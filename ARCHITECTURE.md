# AdVid Server Architecture Documentation

This document provides a comprehensive overview of the AdVid server's architecture, data models, and core functionalities. It is intended for backend engineers and architects to quickly understand the system's design and components.

## 1. System Architecture Diagram

This diagram provides a high-level overview of the system's components and their interactions.

```mermaid
graph TD
    subgraph "User"
        A[User Browser/Client]
    end

    subgraph "AdVid Server (Fastify)"
        B[API Gateway/Router]
        C[Authentication (Auth0)]
        D[Controllers]
        E[Services]
        F[Database (Drizzle ORM)]
        G[Email Queue (BullMQ)]
    end

    subgraph "External Services"
        H[Auth0 Management API]
        I[PostgreSQL Database]
        J[Redis (Session/Queue)]
        K[Google Cloud Storage]
        L[Email Service (Resend)]
    end

    A -- HTTPS --> B
    B -- Middleware --> C
    B -- Routes --> D
    C -- Verifies Token --> H
    D -- Logic --> E
    E -- CRUD --> F
    E -- File Operations --> K
    E -- Enqueues Jobs --> G
    F -- SQL --> I
    G -- Jobs --> J
    G -- Processes Jobs --> L
```

## 2. Data Flow and Architecture Analysis

The architecture is a modern, decoupled Node.js application. Here's a breakdown of the data flow and key architectural decisions:

*   **Entry Point & Routing:** The Fastify server acts as the main entry point. It uses a modular routing system, with each module (e.g., `users`, `accounts`) having its own routes, controllers, and schemas. This promotes separation of concerns and makes the API easier to maintain.
*   **Authentication:** Authentication is handled by Auth0, a third-party identity provider. The server uses the `fastify-auth0-verify` plugin to protect routes. The `users.controller.js` shows that the server also interacts with the Auth0 Management API for tasks like updating user profiles and resetting passwords. This is a common and recommended practice, as it offloads complex and critical security concerns to a specialized service.
*   **Database & ORM:** The application uses PostgreSQL as its database and Drizzle as its ORM. Drizzle is a modern, TypeScript-native ORM that provides type safety and a SQL-like syntax. The use of a `relations.ts` file to explicitly define relationships between tables is a good practice that enhances code clarity and maintainability.
*   **Services Layer:** The services layer (e.g., `google.service.ts`, `emailQueue.service.ts`) encapsulates business logic and interactions with external services. This is a key architectural pattern that promotes code reuse and testability.
*   **Asynchronous Tasks:** The use of BullMQ and Redis for an email queue is a great example of handling asynchronous tasks. This ensures that long-running operations like sending emails don't block the main application thread, improving the server's responsiveness and reliability.
*   **Configuration:** The application uses `dotenv` for managing environment variables, with a `validateEnv.ts` utility to ensure that all required variables are present at startup. This is a robust approach to configuration management.

## 3. Core Data Models (ER Diagram)

The database schema is well-structured and normalized. Here's a Mermaid ER diagram of the core models:

```mermaid
erDiagram
    users {
        uuid id PK
        text auth0Id UK
        text email UK
        text name
        text status
    }
    accounts {
        uuid id PK
        text name
        uuid ownerId FK
        text status
    }
    brands {
        uuid id PK
        uuid accountId FK
        text name
    }
    userAccounts {
        uuid userId FK
        uuid accountId FK
        text role
    }
    brandUsers {
        uuid userId FK
        uuid brandId FK
        text role
    }

    users ||--o{ accounts : "owns"
    users ||--|{ userAccounts : "is a member of"
    accounts ||--|{ userAccounts : "has"
    accounts ||--o{ brands : "has"
    brands ||--|{ brandUsers : "has"
    users ||--|{ brandUsers : "is a member of"
```

## 4. Module-by-Module Breakdown

### 4.1. Accounts Management

The `accounts` module is the central organizational pillar of the application. An account represents a single customer entity (e.g., a company) that owns users, brands, and other resources.

#### API Endpoints
*   `GET /`: Retrieves a list of all accounts accessible to the user.
*   `GET /:id`: Retrieves a single account by its ID.
*   `POST /`: Creates a new account.
*   `PUT /:id`: Updates an existing account.
*   `DELETE /:id`: Soft-deletes an account.
*   `GET /:id/users`: Retrieves all users associated with a specific account.
*   `POST /:id/users`: Adds a user to an account.

#### Controller Insights
*   **Authorization-First Approach:** Nearly every controller function begins by calling the `authService` to check permissions. This is a critical security best practice.
*   **Soft Deletes:** The `deleteAccount` function performs a "soft delete" by setting the account's `status` to `'deleted'`, allowing for data recovery.
*   **Efficient Data Fetching:** The `getAccountMembers` function uses `Promise.all` to fetch related data in parallel, minimizing database round-trips.

### 4.2. Authorization Service (`authorization.service.js`)

This service is the heart of the application's security model, providing a clear and concise implementation of role-based access control (RBAC).

*   **Role-Based Logic:** The service uses the `userAccounts` and `brandUsers` tables to determine a user's role within a specific context (account or brand).
*   **Hierarchical Permissions:** The service implements a hierarchical permission model (e.g., an account admin has implicit permissions on the account's brands).
*   **Singleton Pattern:** The service is exported as a singleton instance, ensuring a single, consistent authorization source throughout the application.
*   **Defensive Programming:** The service includes logic to prevent actions that would leave the system in an inconsistent state (e.g., preventing the last admin from leaving a brand).

### 4.3. Brands Management

This module handles the brands owned by accounts. Brands are the primary containers for products and creative projects.

#### API Endpoints
*   `GET /`: Retrieves all brands.
*   `GET /account/:accountId`: Retrieves all brands for a specific account.
*   `GET /account/:accountId/details`: Retrieves brands with aggregated metrics.
*   `POST /`: Creates a new brand.
*   `PUT /:id`: Updates a brand.
*   `DELETE /:id`: Deletes a brand.
*   `POST /:brandId/users/:userId`: Adds a user to a brand.
*   `DELETE /:brandId/users/:userId`: Removes a user from a brand.
*   `POST /:brandId/guidelines`: Uploads brand guideline assets.

#### Controller Insights
*   **Efficient Data Aggregation:** The `getBrandsDetailsForAccount` function uses `Promise.allSettled` to fetch various metrics for each brand in parallel. This approach is highly resilient to partial failures.
*   **Asset Handling:** The `uploadBrandGuidelines` function shows how the application handles file uploads, using the `google.service.ts` to interact with Google Cloud Storage.

### 4.4. Retailers Management

This module manages the different retail platforms or marketplaces the application integrates with.

*   **API:** The API is read-only for end-users (`GET /`), suggesting that retailer configurations are managed administratively to ensure system stability.
*   **Architectural Insight:** This read-only approach is a deliberate and sound design choice for system-critical data.

### 4.5. Products Management

This module is responsible for managing the products that will be advertised.

*   **API:** The API focuses on retrieval (`GET /:accountId`, `GET /product/:productId`) and deletion. The lack of `create` or `update` endpoints strongly suggests that product data is ingested from an external source (e.g., a retailer's API), which is a common pattern.
*   **Rich Filtering:** The `getProductsByAccountId` function supports powerful server-side filtering by brand, category, and a text-based search.
*   **Data Enrichment:** The controller combines product data with related `imageMetadata` to create a client-friendly "view model".

### 4.6. Generations & Creatives

These tightly coupled modules represent the core functionality of the application: generating ad creatives. A `generation` is a request to create one or more `creatives` for a specific product.

#### API Endpoints
*   **Bulk Operations:** The `generations` module supports bulk creation (`POST /bulk`), a crucial feature for user efficiency.
*   **Versioning:** A full suite of versioning endpoints (`/save`, `/version`, `/version/history`) allows users to track changes to generated content over time.
*   **Project-Based Organization:** Generations are organized into projects, providing a logical grouping for campaigns.
*   **Preview Endpoint:** The `creatives` module has a `/preview` endpoint that fetches all necessary data to render a creative, simplifying client-side logic.

#### Controller Insights
*   **Transactional Integrity:** The `createCreative` function uses a database transaction (`db.transaction`) to ensure data consistency.
*   **Complex Data Retrieval:** The `getProjectKeywordGenerations` function uses an efficient subquery to find the latest version for each generation, demonstrating advanced and performant database interaction.

## 5. Core Workflow Diagram

This diagram illustrates the complete data flow for the application's core functionality.

```mermaid
graph TD
    subgraph "User & Account Setup"
        A[User Signs Up via Auth0] --> B{User record created in DB};
        B --> C{User creates an Account};
        C --> D{User is assigned 'owner' role for the Account};
    end

    subgraph "Brand & Product Setup"
        D --> E{User creates a Brand within the Account};
        E --> F{User is assigned 'admin' role for the Brand};
        G[Products are Ingested] --> H{Product records created};
        H --> E;
    end

    subgraph "Creative Generation Workflow"
        F --> I{User creates a Project for a Brand};
        I --> J{User starts a Generation for a Product and Keyword};
        J --> K{A Generation record is created};
        K --> L{A GenerationVersion record is created};
        L --> M{A Creative record is created, linked to the Generation};
    end

    subgraph "Data Models"
        U[users]
        AC[accounts]
        BU[brandUsers]
        P[products]
        PR[projects]
        GNS[generations]
        GV[generationVersions]
        CR[creatives]
    end

    B --> U;
    C --> AC;
    D --> BU;
    H --> P;
    I --> PR;
    K --> GNS;
    L --> GV;
    M --> CR;
```

## 6. Final Summary

The AdVid server is a sophisticated and well-architected application that prioritizes security, scalability, and maintainability.

*   **Security:** The consistent use of a centralized authorization service and the principle of least privilege are commendable.
*   **Scalability:** The use of asynchronous job queues for emails, pagination for all list-based endpoints, and efficient data fetching patterns will allow the application to scale gracefully.
*   **Maintainability:** The modular structure, clear separation of concerns, and use of a modern ORM make the codebase easy to understand and extend.
