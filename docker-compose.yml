services:
  app:
    build: .
    image: advid-server
    ports:
      - "5005:5005"
    environment:
      - NODE_ENV=production
      - PORT=5005
      - DATABASE_URL=${DATABASE_URL}
      - AUTH0_DOMAIN=${AUTH0_DOMAIN}
      - AUTH0_CLIENT_ID=${AUTH0_CLIENT_ID}
      - AUTH0_CLIENT_SECRET=${AUTH0_CLIENT_SECRET}
      - AUTH0_AUDIENCE=${AUTH0_AUDIENCE}
      - JWT_SECRET=${JWT_SECRET}
      - COOKIE_SECRET=${COOKIE_SECRET}
    volumes:
      - .:/app
      - /app/node_modules
    command: tsx app.ts
