const { Client } = require('pg');
require('dotenv').config();

async function runMigration() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Our migration SQL
    const migrationSQL = `
-- Drop foreign key constraints first
ALTER TABLE "retailer_products" DROP CONSTRAINT IF EXISTS "retailer_products_product_id_products_product_id_fk";
ALTER TABLE "image_metadata" DROP CONSTRAINT IF EXISTS "image_metadata_product_id_products_product_id_fk";

-- Now drop the unique constraint
ALTER TABLE "products" DROP CONSTRAINT IF EXISTS "products_product_id_unique";

-- Make image_metadata.product_id nullable
ALTER TABLE "image_metadata" ALTER COLUMN "product_id" DROP NOT NULL;

-- Add new column
ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "transparent_thumbnail_url" text;
    `;

    console.log('Executing migration...');
    await client.query(migrationSQL);
    console.log('✅ Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
  } finally {
    await client.end();
  }
}

runMigration(); 