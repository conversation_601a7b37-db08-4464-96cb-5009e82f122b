# Dependencies
node_modules
*.tgz
*.tar.gz

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Operating System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE/Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Development/Documentation files
.clinerules
CLAUDE.md
ERD-explanation.md
db_changes.md
erd.mmd
erd.png
memory-bank/
test.md
*.test.local.*

# Database seeder files
src/db/seeder/superUsers.json

# Cursor AI specific
.cursor/

# GitHub specific
.github/

# Taskmaster specific
.taskmaster/

# Trae specific
.trae/

# Scripts (if they contain sensitive data)
scripts/

# Temporary seeder content
src/db/seeder/seeders/content/temp.md

# Build artifacts
dist/
build/
out/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development
.env.test
.env.production
.env.staging

# Parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Lock files (keep package-lock.json for npm projects)
yarn.lock
pnpm-lock.yaml

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
