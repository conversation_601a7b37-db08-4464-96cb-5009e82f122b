# Database Migration Guide: Schema Changes

## Overview

This migration contains **destructive changes** that will affect:
- `accounts` table (6 rows) - changing `type` column from enum to text
- `brand_guidelines` table (13 rows) - changing `status` column from text to enum

## ⚠️ IMPORTANT: Data Will Be Lost

The migration wants to **truncate (empty)** these 2 tables because:
1. **accounts.type**: enum values `['agency', '3p', '1p']` → text with default `'default'`
2. **brand_guidelines.status**: any text values → enum `['active', 'archived']` only

## Safe Migration Process

### Step 1: Backup Your Data
```bash
# Create backup before migration
npm run db:backup
```

### Step 2: Run the Destructive Migration
```bash
# This will empty the 2 tables and change schema
npm run db:push
# OR
npx drizzle-kit push
```

### Step 3: Restore Data with Type Conversion
```bash
# Find your backup file (will be named backup-[timestamp].json)
ls scripts/backup-*.json

# Restore data with automatic type conversion
npm run db:restore backup-**********.json
```

## Data Conversion Rules

### Account Types
- `'agency'` → `'agency'`
- `'3p'` → `'third_party'`
- `'1p'` → `'first_party'`
- Unknown values → `'default'`

### Brand Guideline Status
- `'active'` → `'active'`
- `'draft'` → `'active'`
- `'pending'` → `'active'`
- `'archived'` → `'archived'`
- `'deleted'` → `'archived'`
- `'inactive'` → `'archived'`
- Unknown values → `'active'`

## Alternative: Skip Migration for Now

If you don't want to risk data loss right now:

1. **Select "No, abort"** in the migration prompt
2. The schema changes won't be applied
3. Your app might have type mismatches until you migrate later

## Complete Migration Command Sequence

```bash
# 1. Backup
npm run db:backup

# 2. Migrate (select "Yes, I want to truncate 2 tables")
npm run db:push

# 3. Restore (replace with your actual backup filename)
npm run db:restore backup-**********.json

# 4. Verify data is restored correctly
# Check your database to confirm data looks good
```

## Manual Verification

After migration, verify your data:

```sql
-- Check account types
SELECT type, COUNT(*) FROM accounts GROUP BY type;

-- Check brand guideline statuses
SELECT status, COUNT(*) FROM brand_guidelines GROUP BY status;
```

## Rollback Plan

If something goes wrong:
1. The backup files are saved in `scripts/backup-*.json`
2. You can manually restore from these JSON files
3. Or revert the schema changes by creating a new migration

**Note:** This migration also adds:
- `accounts.deleted_at` column (timestamp)
- `users.is_verified` column (boolean, default false)

These additions are safe and don't cause data loss.