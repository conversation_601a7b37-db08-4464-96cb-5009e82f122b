{"name": "advid-server", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"start": "node dist/app.js", "dev": "tsx watch app.ts", "build": "echo 'Build uses tsx for runtime compilation. Use npm run dev for development.'", "migrate:generate": "drizzle-kit generate", "migrate:run": "npx drizzle-kit up", "db:push": "drizzle-kit push", "dbenv:push": "npx dotenv drizzle-kit push", "db:seed": "tsx src/db/seeder/seed-modular.ts", "db:seed:legacy": "tsx src/db/seeder/seed.ts", "db:seed:modular": "tsx src/db/seeder/seed-modular.ts", "db:seed:validate": "tsx src/db/seeder/validate-seeded-data.ts", "db:seed:no-clear": "SKIP_CLEARING=true tsx src/db/seeder/seed-modular.ts", "db:seed:base": "tsx src/db/seeder/seed-base-only.ts", "db:seed:business": "tsx src/db/seeder/seed-business-only.ts", "db:seed:no-workflow": "tsx src/db/seeder/seed-without-workflow.ts", "db:seed:demo": "tsx src/db/seeder/realistic-demo-seeder.ts", "db:seed:superusers": "tsx src/db/seeder/superUsersEnhanced.ts", "db:seed:custom": "tsx src/db/seeder/seed-parameterized.ts", "db:seed:custom:help": "tsx src/db/seeder/seed-parameterized.ts --help", "db:seed:custom:dry": "tsx src/db/seeder/seed-parameterized.ts --dry-run", "db:seed:user": "tsx src/db/seeder/seed-parameterized.ts --super-user-id", "db:seed:brands": "tsx src/db/seeder/seed-parameterized.ts --brand-count", "db:seed:users": "tsx src/db/seeder/seed-parameterized.ts --user-count", "docker:build": "docker build -t advid-server .", "docker:run": "docker run -p 5005:5005 advid-server", "docker:compose": "docker-compose up", "docker:compose:build": "docker-compose up --build", "test:smoke": "newman run tests/postman/AdFury_Smoke.postman_collection.json --environment tests/postman/local.postman_environment.json --reporters cli,htmlextra --reporter-htmlextra-export tests/report/postman/smoke-report.html --reporter-htmlextra-useLocalAssets"}, "keywords": [], "author": "", "license": "ISC", "description": "A robust Node.js server built with Fastify, featuring Auth0 authentication, PostgreSQL database integration via Drizzle ORM, and Docker support. Used for the ADVID platform.", "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/jwt": "^9.0.3", "@fastify/multipart": "^9.0.3", "@fastify/rate-limit": "^10.3.0", "@fastify/session": "^11.1.0", "@fastify/websocket": "^11.0.2", "@google-cloud/storage": "^7.15.0", "@neondatabase/serverless": "^0.10.4", "auth0": "^4.16.0", "axios": "^1.8.1", "bcrypt": "^5.1.1", "bullmq": "5.56.1", "connect-redis": "^8.0.2", "dotenv": "^16.4.7", "drizzle-orm": "^0.38.3", "drizzle-seed": "^0.3.1", "fastify": "^5.2.1", "fastify-auth0-verify": "^3.0.0", "fastify-plugin": "^5.0.1", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "pg": "^8.16.3", "pino-pretty": "^13.0.0", "resend": "^4.1.1", "uuid": "^11.1.0", "ws": "^8.16.0"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@types/connect-redis": "^0.0.23", "@types/node": "^20.17.12", "chalk": "^5.4.1", "drizzle-kit": "^0.31.4", "newman": "^6.2.0", "newman-reporter-htmlextra": "^1.23.1", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.7.3"}, "directories": {"test": "tests"}, "repository": {"type": "git", "url": "git+https://github.com/AdVid-ai/advid-server.git"}, "bugs": {"url": "https://github.com/AdVid-ai/advid-server/issues"}, "homepage": "https://github.com/AdVid-ai/advid-server#readme", "private": true}