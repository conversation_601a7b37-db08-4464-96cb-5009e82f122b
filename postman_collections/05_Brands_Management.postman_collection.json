{"info": {"_postman_id": "brands-management-collection", "name": "05. Brands Management", "description": "Manage brands including creation, updates, brand guidelines, and brand-specific settings.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "Brand Operations", "item": [{"name": "List Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/brands?accountId={{accountId}}", "host": ["{{baseUrl}}"], "path": ["api", "brands"], "query": [{"key": "accountId", "value": "{{accountId}}", "description": "Filter brands by account"}, {"key": "status", "value": "active", "disabled": true}, {"key": "search", "value": "", "disabled": true}]}, "description": "List all brands accessible to the user."}, "response": [{"name": "Brands List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/brands", "host": ["{{baseUrl}}"], "path": ["api", "brands"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"brand_123\",\n    \"name\": \"Nike\",\n    \"accountId\": \"acc_123\",\n    \"logoUrl\": \"https://example.com/nike-logo.png\",\n    \"website\": \"https://nike.com\",\n    \"industry\": \"Sportswear\",\n    \"status\": \"active\",\n    \"primaryColor\": \"#111111\",\n    \"createdAt\": \"2024-01-01T00:00:00Z\"\n  },\n  {\n    \"id\": \"brand_456\",\n    \"name\": \"Adidas\",\n    \"accountId\": \"acc_123\",\n    \"logoUrl\": \"https://example.com/adidas-logo.png\",\n    \"website\": \"https://adidas.com\",\n    \"industry\": \"Sportswear\",\n    \"status\": \"active\",\n    \"primaryColor\": \"#000000\",\n    \"createdAt\": \"2024-01-02T00:00:00Z\"\n  }\n]"}]}, {"name": "Get Brand Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/brands/{{brandId}}", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{brandId}}"]}, "description": "Get detailed information about a specific brand."}, "response": [{"name": "Brand Details", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/brands/brand_123", "host": ["{{baseUrl}}"], "path": ["api", "brands", "brand_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"brand_123\",\n  \"name\": \"Nike\",\n  \"accountId\": \"acc_123\",\n  \"logoUrl\": \"https://example.com/nike-logo.png\",\n  \"website\": \"https://nike.com\",\n  \"industry\": \"Sportswear\",\n  \"status\": \"active\",\n  \"description\": \"Just Do It - Leading global sportswear brand\",\n  \"brandGuidelines\": {\n    \"colors\": {\n      \"primary\": \"#111111\",\n      \"secondary\": \"#FFFFFF\",\n      \"accent\": \"#FF6900\"\n    },\n    \"fonts\": {\n      \"primary\": \"Futura\",\n      \"secondary\": \"Helvetica Neue\"\n    },\n    \"tone\": \"Inspirational, Athletic, Bold\",\n    \"logoUsage\": \"Minimum clear space of 2x logo height\"\n  },\n  \"socialMedia\": {\n    \"facebook\": \"https://facebook.com/nike\",\n    \"instagram\": \"https://instagram.com/nike\",\n    \"twitter\": \"https://twitter.com/nike\"\n  },\n  \"createdAt\": \"2024-01-01T00:00:00Z\",\n  \"updatedAt\": \"2024-01-15T00:00:00Z\"\n}"}]}, {"name": "Create Brand", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Brand\",\n  \"accountId\": \"{{accountId}}\",\n  \"website\": \"https://newbrand.com\",\n  \"industry\": \"Technology\",\n  \"description\": \"Innovative tech solutions\",\n  \"brandGuidelines\": {\n    \"colors\": {\n      \"primary\": \"#0066CC\",\n      \"secondary\": \"#FFFFFF\"\n    },\n    \"fonts\": {\n      \"primary\": \"Arial\"\n    },\n    \"tone\": \"Professional, Innovative\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/brands", "host": ["{{baseUrl}}"], "path": ["api", "brands"]}, "description": "Create a new brand within an account."}}, {"name": "Update Brand", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Brand Name\",\n  \"logoUrl\": \"https://example.com/new-logo.png\",\n  \"brandGuidelines\": {\n    \"colors\": {\n      \"primary\": \"#FF0000\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/brands/{{brandId}}", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{brandId}}"]}, "description": "Update brand information."}}, {"name": "Delete Brand", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/brands/{{brandId}}", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{brandId}}"]}, "description": "Delete a brand. This will archive all associated campaigns and assets."}}]}, {"name": "Brand Guidelines", "item": [{"name": "Get Brand Guidelines", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/brands/{{brandId}}/guidelines", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{brandId}}", "guidelines"]}, "description": "Get detailed brand guidelines including colors, fonts, tone, and usage rules."}}, {"name": "Update Brand Guidelines", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"colors\": {\n    \"primary\": \"#0066CC\",\n    \"secondary\": \"#FFFFFF\",\n    \"accent\": \"#FF6600\",\n    \"error\": \"#CC0000\",\n    \"success\": \"#00CC00\"\n  },\n  \"fonts\": {\n    \"primary\": \"Helvetica Neue\",\n    \"secondary\": \"Georgia\",\n    \"code\": \"Monaco\"\n  },\n  \"tone\": \"Professional, Friendly, Innovative\",\n  \"voice\": {\n    \"doUse\": [\"Active voice\", \"Clear language\", \"Positive tone\"],\n    \"dontUse\": [\"Jargon\", \"Passive voice\", \"Negative language\"]\n  },\n  \"logoUsage\": {\n    \"minSize\": \"100px\",\n    \"clearSpace\": \"2x logo height\",\n    \"backgrounds\": [\"white\", \"black\", \"brand colors only\"]\n  },\n  \"imagery\": {\n    \"style\": \"Modern, clean, professional\",\n    \"subjects\": \"People in real situations\",\n    \"avoid\": \"Stock photos, clichés\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/brands/{{brandId}}/guidelines", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{brandId}}", "guidelines"]}, "description": "Update brand guidelines. Only provided fields will be updated."}}]}, {"name": "Brand Assets", "item": [{"name": "List Brand Assets", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/brands/{{brandId}}/assets?type=logo", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{brandId}}", "assets"], "query": [{"key": "type", "value": "logo", "description": "Filter by asset type (logo, image, video, document)"}, {"key": "limit", "value": "20", "disabled": true}]}, "description": "List all assets associated with a brand."}}, {"name": "Upload Brand Asset", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": ""}, {"key": "type", "value": "logo", "type": "text"}, {"key": "name", "value": "Primary Logo", "type": "text"}, {"key": "description", "value": "Main brand logo for light backgrounds", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/brands/{{brandId}}/assets", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{brandId}}", "assets"]}, "description": "Upload a new asset for the brand."}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005"}, {"key": "authToken", "value": ""}, {"key": "accountId", "value": "acc_123"}, {"key": "brandId", "value": "brand_123"}]}