{"info": {"_postman_id": "project-management-collection", "name": "11. Project Management", "description": "Complete creative project management including project workflows, creative setup, and project advancement operations.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "adfury-api"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "Creative Projects", "item": [{"name": "List User Projects", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-projects?status=active&limit=20&offset=0", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects"], "query": [{"key": "status", "value": "active", "description": "Filter by project status"}, {"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}}, {"name": "Get Project Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-projects/filters", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "filters"]}}}, {"name": "Search Projects", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-projects/search?q=athletic&brandId={{brandId}}", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "search"], "query": [{"key": "q", "value": "athletic"}, {"key": "brandId", "value": "{{brandId}}"}]}}}, {"name": "Get Projects by Brand", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-projects/brand/{{brandId}}", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "brand", "{{brandId}}"]}}}, {"name": "Get Project by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "{{projectId}}"]}}}, {"name": "Create Project", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Q1 Athletic Shoes Campaign\",\n  \"description\": \"Creative campaign for new athletic shoes line\",\n  \"brandId\": \"{{brandId}}\",\n  \"productId\": \"{{productId}}\",\n  \"type\": \"campaign\",\n  \"priority\": \"high\",\n  \"deadline\": \"2024-03-31T23:59:59.000Z\",\n  \"budget\": 50000,\n  \"objectives\": [\n    \"Increase brand awareness\",\n    \"Drive product sales\",\n    \"Engage target audience\"\n  ],\n  \"targetAudience\": \"Athletes and fitness enthusiasts\",\n  \"deliverables\": [\n    \"Hero images\",\n    \"Social media creatives\",\n    \"Video content\",\n    \"Product descriptions\"\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/creative-projects", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects"]}}}, {"name": "Update Project", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Q1 Athletic Shoes Campaign - Updated\",\n  \"status\": \"in_progress\",\n  \"progress\": 45,\n  \"notes\": \"Updated project scope and timeline\"\n}"}, "url": {"raw": "{{baseUrl}}/api/creative-projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "{{projectId}}"]}}}, {"name": "Advance Project Step", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"step\": \"concept_approval\",\n  \"data\": {\n    \"approvedBy\": \"{{currentUserId}}\",\n    \"feedback\": \"Concepts look great, proceeding to production\",\n    \"attachments\": [\n      \"concept_1_approved.jpg\",\n      \"concept_2_approved.jpg\"\n    ]\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/creative-projects/{{projectId}}/advance-step", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "{{projectId}}", "advance-step"]}}}, {"name": "Get Project Product", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-projects/{{projectId}}/product", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "{{projectId}}", "product"]}}}, {"name": "Get Project Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-projects/{{projectId}}/data", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "{{projectId}}", "data"]}}}, {"name": "Get Project Review Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-projects/{{projectId}}/review", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "{{projectId}}", "review"]}}}, {"name": "Delete Project", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "creative-projects", "{{projectId}}"]}}}]}, {"name": "Creative Setup", "item": [{"name": "Get Accessible Projects", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-setup/projects", "host": ["{{baseUrl}}"], "path": ["api", "creative-setup", "projects"]}}}, {"name": "Get Brands by Retailer", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-setup/brands/retailer/{{retailerId}}", "host": ["{{baseUrl}}"], "path": ["api", "creative-setup", "brands", "retailer", "{{retailerId}}"]}}}, {"name": "Debug Brands by Retailer", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-setup/debug/brands/retailer/{{retailerId}}", "host": ["{{baseUrl}}"], "path": ["api", "creative-setup", "debug", "brands", "retailer", "{{retailerId}}"]}}}, {"name": "Get Products by Brand", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-setup/products/{{brandId}}", "host": ["{{baseUrl}}"], "path": ["api", "creative-setup", "products", "{{brandId}}"]}}}, {"name": "Debug User Access", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creative-setup/debug", "host": ["{{baseUrl}}"], "path": ["api", "creative-setup", "debug"]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "currentUserId", "value": "", "type": "string"}, {"key": "brandId", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}, {"key": "projectId", "value": "", "type": "string"}, {"key": "retailerId", "value": "", "type": "string"}]}