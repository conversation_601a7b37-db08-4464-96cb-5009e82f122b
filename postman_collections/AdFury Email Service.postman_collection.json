{"info": {"_postman_id": "a68f99d7-8c16-45e6-8c08-f78fcb32b784", "name": "AdFury Email Service", "description": "Complete email service endpoints for AdFury - includes all implemented routes and monitoring endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "41170009", "_collection_link": "https://engineering-team-8216.postman.co/workspace/adfury-ai~8118d6c4-f7c2-4f4d-88b9-4230840f478f/collection/41170009-a68f99d7-8c16-45e6-8c08-f78fcb32b784?action=share&source=collection_link&creator=41170009"}, "item": [{"name": "🏥 Health & Monitoring", "item": [{"name": "Service Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/", "host": ["{{email_service_url}}"], "path": [""]}, "description": "Get service information and available endpoints."}, "response": []}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/health", "host": ["{{email_service_url}}"], "path": ["health"]}, "description": "Basic health check - returns service status and uptime."}, "response": []}, {"name": "Readiness Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/health/ready", "host": ["{{email_service_url}}"], "path": ["health", "ready"]}, "description": "Readiness check - validates Redis and queue connectivity."}, "response": []}, {"name": "Liveness Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/health/live", "host": ["{{email_service_url}}"], "path": ["health", "live"]}, "description": "Liveness check - simple alive confirmation for orchestration."}, "response": []}], "description": "Basic health and liveness endpoints for the email service."}, {"name": "📊 Metrics & Monitoring", "item": [{"name": "System Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/metrics", "host": ["{{email_service_url}}"], "path": ["metrics"]}, "description": "Get comprehensive system metrics including queue stats, system info, and performance data."}, "response": []}, {"name": "Queue Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/metrics/queue", "host": ["{{email_service_url}}"], "path": ["metrics", "queue"]}, "description": "Get only email queue statistics without system information."}, "response": []}, {"name": "Performance Monitoring", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/monitoring/performance", "host": ["{{email_service_url}}"], "path": ["monitoring", "performance"]}, "description": "Detailed performance metrics with alerts and health scoring."}, "response": []}, {"name": "System Alerts", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/monitoring/alerts", "host": ["{{email_service_url}}"], "path": ["monitoring", "alerts"]}, "description": "Get current system alerts and warnings."}, "response": []}, {"name": "Detailed Health Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/monitoring/health-detailed", "host": ["{{email_service_url}}"], "path": ["monitoring", "health-detailed"]}, "description": "Comprehensive health status with component-level breakdown."}, "response": []}], "description": "Comprehensive monitoring and metrics endpoints for operational observability."}, {"name": "📧 Email API", "item": [{"name": "Send Email", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Email job created successfully\", function () {", "    pm.expect(pm.response.code).to.equal(202);", "    pm.expect(pm.response.json().jobId).to.exist;", "    pm.environment.set(\"job_id\", pm.response.json().jobId);", "    console.log(\"Job ID saved:\", pm.response.json().jobId);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"custom\",\n  \"to\": \"{{test_email}}\",\n  \"subject\": \"Test Email\",\n  \"heading\": \"Welcome!\",\n  \"content\": \"This is a test email from the AdFury email service.\",\n  \"buttonText\": \"Visit Dashboard\",\n  \"buttonLink\": \"https://app.adfury.ai/dashboard\",\n  \"footerText\": \"AdFury Email Service\",\n  \"fromName\": \"AdFury Team\",\n  \"replyTo\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{email_service_url}}/api/send-email", "host": ["{{email_service_url}}"], "path": ["api", "send-email"]}, "description": "Send an email job to the queue. Supports custom, invitation, and password-reset types."}, "response": []}, {"name": "Send Invitation Email", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Email job created successfully\", function () {", "    pm.expect(pm.response.code).to.equal(202);", "    pm.expect(pm.response.json().jobId).to.exist;", "    pm.environment.set(\"job_id\", pm.response.json().jobId);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"invitation\",\n  \"to\": \"{{test_email}}\",\n  \"invitationType\": \"invite\",\n  \"invitationId\": \"inv_{{$randomUUID}}\",\n  \"organizationName\": \"{{org_name}}\",\n  \"senderName\": \"{{org_name}} Team\"\n}"}, "url": {"raw": "{{email_service_url}}/api/send-email", "host": ["{{email_service_url}}"], "path": ["api", "send-email"]}, "description": "Send an invitation email directly through the email service."}, "response": []}, {"name": "Send Password Reset Email", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Email job created successfully\", function () {", "    pm.expect(pm.response.code).to.equal(202);", "    pm.expect(pm.response.json().jobId).to.exist;", "    pm.environment.set(\"job_id\", pm.response.json().jobId);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"password-reset\",\n  \"to\": \"{{test_email}}\",\n  \"resetUrl\": \"https://app.adfury.ai/reset-password?token={{$randomAlphaNumeric}}\"\n}"}, "url": {"raw": "{{email_service_url}}/api/send-email", "host": ["{{email_service_url}}"], "path": ["api", "send-email"]}, "description": "Send a password reset email directly through the email service."}, "response": []}, {"name": "Send Priority Email", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Priority email job created\", function () {", "    pm.expect(pm.response.code).to.equal(202);", "    pm.expect(pm.response.json().priority).to.equal(10);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"password-reset\",\n  \"to\": \"{{test_email}}\",\n  \"resetUrl\": \"https://app.adfury.ai/reset-password?token={{$randomAlphaNumeric}}\"\n}"}, "url": {"raw": "{{email_service_url}}/api/send-email?priority=10", "host": ["{{email_service_url}}"], "path": ["api", "send-email"], "query": [{"key": "priority", "value": "10"}]}, "description": "Send a high priority email (priority=10) for urgent operations."}, "response": []}, {"name": "Check Job Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Job status retrieved\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "    pm.expect(pm.response.json().jobId).to.exist;", "    pm.expect(pm.response.json().status).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/api/job/{{job_id}}", "host": ["{{email_service_url}}"], "path": ["api", "job", "{{job_id}}"]}, "description": "Check the status of a specific email job."}, "response": []}, {"name": "Remove Job", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{email_service_url}}/api/job/{{job_id}}", "host": ["{{email_service_url}}"], "path": ["api", "job", "{{job_id}}"]}, "description": "Remove a specific job from the email queue."}, "response": []}, {"name": "Get Failed Jobs", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/api/failed-jobs?limit=10", "host": ["{{email_service_url}}"], "path": ["api", "failed-jobs"], "query": [{"key": "limit", "value": "10"}]}, "description": "Get detailed information about failed jobs for debugging."}, "response": []}, {"name": "Retry Failed Jobs", "request": {"method": "POST", "header": [], "url": {"raw": "{{email_service_url}}/api/retry-failed", "host": ["{{email_service_url}}"], "path": ["api", "retry-failed"]}, "description": "<PERSON><PERSON> all failed jobs in the email queue."}, "response": []}], "description": "Core email API endpoints for queue-based email processing."}, {"name": "🔧 Administration", "item": [{"name": "Admin - Retry Failed Jobs", "request": {"method": "POST", "header": [], "url": {"raw": "{{email_service_url}}/admin/retry-failed", "host": ["{{email_service_url}}"], "path": ["admin", "retry-failed"]}, "description": "Administrative endpoint to retry all failed jobs in the queue."}, "response": []}, {"name": "API Documentation", "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/api-docs", "host": ["{{email_service_url}}"], "path": ["api-docs"]}, "description": "Swagger UI documentation for all endpoints."}, "response": []}], "description": "Administrative endpoints for email service management."}, {"name": "🧪 Testing & Development", "item": [{"name": "Test Invitation Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{test_email}}\",\n  \"invitationId\": \"{{invitationId}}\",\n  \"invitationType\": \"invite\",\n  \"organizationName\": \"{{org_name}}\",\n  \"senderName\": \"{{org_name}} Team\"\n}"}, "url": {"raw": "{{base_url}}/api/email/test-invitation", "host": ["{{base_url}}"], "path": ["api", "email", "test-invitation"]}, "description": "Send a test invitation email to verify email service functionality."}, "response": []}, {"name": "Test Password Reset Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{test_email}}\",\n  \"resetUrl\": \"https://app.adfury.ai/reset-password?token=test-token-{{$randomAlphaNumeric}}\"\n}"}, "url": {"raw": "{{base_url}}/api/email/test-password-reset", "host": ["{{base_url}}"], "path": ["api", "email", "test-password-reset"]}, "description": "Send a test password reset email. Reset URL is optional - will generate default if not provided."}, "response": []}, {"name": "Test Custom Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{test_email}}\",\n  \"subject\": \"Test Custom Email from {{org_name}}\"\n}"}, "url": {"raw": "{{base_url}}/api/email/test-custom", "host": ["{{base_url}}"], "path": ["api", "email", "test-custom"]}, "description": "Send a test custom email with sample content to verify custom email functionality."}, "response": []}, {"name": "Send Custom Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{test_email}}\",\n  \"subject\": \"Custom Email from {{org_name}}\",\n  \"heading\": \"Welcome to {{org_name}}!\",\n  \"content\": \"<p>This is a custom email with flexible content.</p><p>You can include HTML formatting, custom buttons, and personalized messages.</p>\",\n  \"buttonText\": \"Visit Dashboard\",\n  \"buttonLink\": \"https://app.adfury.ai\",\n  \"footerText\": \"Custom footer text here\",\n  \"fromName\": \"{{org_name}} Team\",\n  \"replyTo\": \"{{test_email}}\"\n}"}, "url": {"raw": "{{base_url}}/api/email/send-custom", "host": ["{{base_url}}"], "path": ["api", "email", "send-custom"]}, "description": "Send a custom email with flexible content including custom HTML, buttons, and sender information."}, "response": []}, {"name": "Get Email Queue Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/email/queue-status", "host": ["{{base_url}}"], "path": ["api", "email", "queue-status"]}, "description": "Get current email queue statistics including waiting, active, completed, and failed jobs."}, "response": []}, {"name": "Email Service Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/email/health", "host": ["{{base_url}}"], "path": ["api", "email", "health"]}, "description": "Check email service health including queue status and Redis connectivity."}, "response": []}], "description": "Testing endpoints from advid-server for email service integration testing."}, {"name": "👥 Invitations", "item": [{"name": "Get All Invitations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/invitations", "host": ["{{base_url}}"], "path": ["api", "invitations"]}, "description": "Retrieve all invitations with their email status."}, "response": []}, {"name": "Get Invitation by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/invitations/{{invitationId}}", "host": ["{{base_url}}"], "path": ["api", "invitations", "{{invitationId}}"]}, "description": "Get details of a specific invitation by its ID."}, "response": []}, {"name": "Create Account Invitation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"email\": \"<EMAIL>\",\n  \"type\": \"invite\",\n  \"message\": \"Welcome to our AdFury team! Please join our account.\"\n}"}, "url": {"raw": "{{base_url}}/api/invitations", "host": ["{{base_url}}"], "path": ["api", "invitations"]}, "description": "Create a new account invitation. Automatically sends invitation email to the recipient."}, "response": []}, {"name": "Update Invitation", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"accepted\"\n}"}, "url": {"raw": "{{base_url}}/api/invitations/{{invitationId}}", "host": ["{{base_url}}"], "path": ["api", "invitations", "{{invitationId}}"]}, "description": "Update an invitation's status or other properties."}, "response": []}, {"name": "Delete Invitation", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/invitations/{{invitationId}}", "host": ["{{base_url}}"], "path": ["api", "invitations", "{{invitationId}}"]}, "description": "Delete an invitation."}, "response": []}, {"name": "Get Invitations by Account", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/invitations/account/{{accountId}}", "host": ["{{base_url}}"], "path": ["api", "invitations", "account", "{{accountId}}"]}, "description": "Get all invitations for a specific account."}, "response": []}, {"name": "Accept Join Request", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notificationId\": \"optional-notification-id\"\n}"}, "url": {"raw": "{{base_url}}/api/invitations/join-request/{{invitationId}}/accept", "host": ["{{base_url}}"], "path": ["api", "invitations", "join-request", "{{invitationId}}", "accept"]}, "description": "Accept a join request invitation."}, "response": []}, {"name": "Create Join <PERSON>quest", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{toEmail}}\",\n  \"accountId\": \"{{accountId}}\",\n  \"type\": \"request\"\n}"}, "url": {"raw": "{{base_url}}/api/invitations/join-request", "host": ["{{base_url}}"], "path": ["api", "invitations", "join-request"]}, "description": "Create a join request (public endpoint - no auth required)."}, "response": []}, {"name": "Decline Join Request", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/invitations/decline-join-request/{{invitationId}}", "host": ["{{base_url}}"], "path": ["api", "invitations", "decline-join-request", "{{invitationId}}"]}, "description": "Decline a join request invitation."}, "response": []}, {"name": "Resend User Invitation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/users/{{userId}}/resend-invite", "host": ["{{base_url}}"], "path": ["api", "users", "{{userId}}", "resend-invite"]}, "description": "Resend invitation email to an existing user."}, "response": []}], "description": "Invitation management including creation, tracking, and acceptance workflows."}, {"name": "👥 Integration - Invitations", "item": [{"name": "Get All Invitations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/invitations", "host": ["{{base_url}}"], "path": ["api", "invitations"]}, "description": "Retrieve all invitations with their email status."}, "response": []}, {"name": "Create Account Invitation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"email\": \"<EMAIL>\",\n  \"type\": \"invite\",\n  \"message\": \"Welcome to our AdFury team! Please join our account.\"\n}"}, "url": {"raw": "{{base_url}}/api/invitations", "host": ["{{base_url}}"], "path": ["api", "invitations"]}, "description": "Create a new account invitation. Automatically sends invitation email to the recipient."}, "response": []}, {"name": "Resend User Invitation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/users/{{userId}}/resend-invite", "host": ["{{base_url}}"], "path": ["api", "users", "{{userId}}", "resend-invite"]}, "description": "Resend invitation email to an existing user."}, "response": []}], "description": "Integration endpoints from advid-server that trigger email sending."}, {"name": "🔐 Integration - Authentication", "item": [{"name": "Request Password Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{test_email}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/password-reset", "host": ["{{base_url}}"], "path": ["auth", "password-reset"]}, "description": "Request a password reset email for a user. Always returns success message for security."}, "response": []}, {"name": "Admin Reset User Password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/users/{{userId}}/reset-password", "host": ["{{base_url}}"], "path": ["api", "users", "{{userId}}", "reset-password"]}, "description": "Admin action to trigger password reset email for a specific user by their ID."}, "response": []}], "description": "Authentication endpoints from advid-server that trigger password reset emails."}, {"name": "🔄 Email Workflow Testing", "item": [{"name": "1. Send Test Email", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Email sent successfully\", function () {", "    pm.expect(pm.response.code).to.equal(202);", "    const jobId = pm.response.json().jobId;", "    pm.expect(jobId).to.exist;", "    pm.environment.set(\"workflow_job_id\", jobId);", "    console.log(\"Workflow job ID:\", jobId);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"custom\",\n  \"to\": \"{{test_email}}\",\n  \"subject\": \"Workflow Test Email\",\n  \"content\": \"This email is part of a workflow test.\"\n}"}, "url": {"raw": "{{email_service_url}}/api/send-email?priority=5", "host": ["{{email_service_url}}"], "path": ["api", "send-email"], "query": [{"key": "priority", "value": "5"}]}, "description": "Step 1: Send a test email to start the workflow."}, "response": []}, {"name": "2. Check Job Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Job status check successful\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const status = pm.response.json().status;", "    console.log(\"Job status:\", status);", "    pm.expect(status).to.be.oneOf(['waiting', 'active', 'completed', 'failed']);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/api/job/{{workflow_job_id}}", "host": ["{{email_service_url}}"], "path": ["api", "job", "{{workflow_job_id}}"]}, "description": "Step 2: Check job status immediately after sending."}, "response": []}, {"name": "3. <PERSON>s", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Queue metrics retrieved\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const metrics = pm.response.json();", "    pm.expect(metrics.waiting).to.be.a('number');", "    pm.expect(metrics.active).to.be.a('number');", "    pm.expect(metrics.completed).to.be.a('number');", "    pm.expect(metrics.failed).to.be.a('number');", "    console.log('Queue stats:', {", "        waiting: metrics.waiting,", "        active: metrics.active,", "        completed: metrics.completed,", "        failed: metrics.failed", "    });", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/metrics/queue", "host": ["{{email_service_url}}"], "path": ["metrics", "queue"]}, "description": "Step 3: Check current queue statistics."}, "response": []}, {"name": "4. Check Service Health", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Service health check passed\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const health = pm.response.json();", "    pm.expect(health.status).to.equal('healthy');", "    console.log('Service health:', health.status);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{email_service_url}}/health", "host": ["{{email_service_url}}"], "path": ["health"]}, "description": "Step 4: Verify service health after email processing."}, "response": []}], "description": "Complete end-to-end workflow testing for email processing and monitoring."}, {"name": "⚠️ Error Testing", "item": [{"name": "Invalid Email Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Invalid email type rejected\", function () {", "    pm.expect(pm.response.code).to.equal(400);", "    pm.expect(pm.response.json().error).to.include('Invalid email type');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"invalid_type\",\n  \"to\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{email_service_url}}/api/send-email", "host": ["{{email_service_url}}"], "path": ["api", "send-email"]}, "description": "Test error handling for invalid email types."}, "response": []}, {"name": "In<PERSON>id Email Address", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Invalid email address rejected\", function () {", "    pm.expect(pm.response.code).to.equal(400);", "    pm.expect(pm.response.json().error).to.include('Invalid email address');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"invitation\",\n  \"to\": \"invalid-email\",\n  \"invitationType\": \"invite\",\n  \"invitationId\": \"inv_123\"\n}"}, "url": {"raw": "{{email_service_url}}/api/send-email", "host": ["{{email_service_url}}"], "path": ["api", "send-email"]}, "description": "Test error handling for invalid email addresses."}, "response": []}], "description": "Error testing endpoints for the email service."}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-save job ID from email send responses", "if (pm.response.json && pm.response.json().jobId) {", "    pm.environment.set('job_id', pm.response.json().jobId);", "    console.log('Saved job_id:', pm.response.json().jobId);", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:5005", "type": "string"}, {"key": "advid<PERSON><PERSON>ing", "value": "https://advid-server-staging.up.railway.app", "type": "string"}, {"key": "email_service_url", "value": "http://localhost:5555", "type": "string"}, {"key": "email_service_staging", "value": "https://adfury-email-service-stg-staging.up.railway.app", "type": "string"}, {"key": "test_email", "value": "<EMAIL>", "type": "string"}, {"key": "org_name", "value": "AdFury", "type": "string"}, {"key": "access_token", "value": "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}, {"key": "userId", "value": "75d0dda1-07a2-4c36-b394-f6186e62ad31", "type": "string"}, {"key": "accountId", "value": "356b9104-bf47-45ee-a216-b68ac6382749", "type": "string"}, {"key": "invitationId", "value": "9ef3e2bb-8f67-40d4-b4d5-377f589abcbb", "type": "string"}, {"key": "job_id", "value": "job-id-here", "type": "string"}, {"key": "workflow_job_id", "value": "workflow-job-id-here", "type": "string"}]}