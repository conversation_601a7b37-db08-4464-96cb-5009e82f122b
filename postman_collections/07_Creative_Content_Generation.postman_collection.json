{"info": {"_postman_id": "creative-content-generation-collection", "name": "07. Creative Content Generation", "description": "AI-powered creative content generation including generations, creatives, and creative projects workflow management.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "adfury-api"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Ensure auth token is available", "if (!pm.collectionVariables.get('authToken')) {", "    console.warn('No auth token found. Please run Authentication collection first.');", "}"], "type": "text/javascript"}}], "item": [{"name": "AI Generations", "item": [{"name": "List All Generations", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations?limit=20&offset=0", "host": ["{{baseUrl}}"], "path": ["api", "generations"], "query": [{"key": "limit", "value": "20", "description": "Number of generations to return"}, {"key": "offset", "value": "0", "description": "Number of generations to skip"}]}, "description": "Get all AI generations with pagination support."}, "response": [{"name": "Generations List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations", "host": ["{{baseUrl}}"], "path": ["api", "generations"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"gen_123\",\n    \"projectId\": \"proj_456\",\n    \"productId\": \"prod_789\",\n    \"type\": \"image\",\n    \"status\": \"completed\",\n    \"prompt\": \"Create a modern product image for athletic shoes\",\n    \"generatedContent\": {\n      \"imageUrl\": \"https://storage.example.com/generated/image_123.jpg\",\n      \"thumbnailUrl\": \"https://storage.example.com/generated/thumb_123.jpg\"\n    },\n    \"metadata\": {\n      \"model\": \"dalle-3\",\n      \"dimensions\": \"1024x1024\",\n      \"style\": \"photorealistic\"\n    },\n    \"createdAt\": \"2024-01-20T10:00:00.000Z\",\n    \"updatedAt\": \"2024-01-20T10:02:00.000Z\"\n  }\n]"}]}, {"name": "Get Filtered Generations", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has generations array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('generations');", "    pm.expect(jsonData.generations).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"filters\": {\n    \"projectId\": \"{{projectId}}\",\n    \"status\": \"completed\",\n    \"type\": \"image\",\n    \"dateRange\": {\n      \"start\": \"2024-01-01\",\n      \"end\": \"2024-01-31\"\n    }\n  },\n  \"pagination\": {\n    \"limit\": 20,\n    \"offset\": 0\n  },\n  \"sort\": {\n    \"field\": \"createdAt\",\n    \"order\": \"desc\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/generations/getGenerations", "host": ["{{baseUrl}}"], "path": ["api", "generations", "getGenerations"]}, "description": "Get filtered generations with advanced search and filtering capabilities."}, "response": [{"name": "Filtered Generations", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"filters\": {\n    \"projectId\": \"proj_456\",\n    \"status\": \"completed\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/generations/getGenerations", "host": ["{{baseUrl}}"], "path": ["api", "generations", "getGenerations"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"generations\": [\n    {\n      \"id\": \"gen_123\",\n      \"projectId\": \"proj_456\",\n      \"type\": \"image\",\n      \"status\": \"completed\",\n      \"generatedContent\": {\n        \"imageUrl\": \"https://storage.example.com/generated/image_123.jpg\"\n      },\n      \"createdAt\": \"2024-01-20T10:00:00.000Z\"\n    }\n  ],\n  \"pagination\": {\n    \"total\": 1,\n    \"limit\": 20,\n    \"offset\": 0\n  }\n}"}]}, {"name": "Create Generation", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has generation ID', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.collectionVariables.set('generationId', jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{projectId}}\",\n  \"productId\": \"{{productId}}\",\n  \"type\": \"image\",\n  \"prompt\": \"Create a professional product image for athletic running shoes, clean white background, studio lighting\",\n  \"settings\": {\n    \"model\": \"dalle-3\",\n    \"dimensions\": \"1024x1024\",\n    \"style\": \"photorealistic\",\n    \"quality\": \"hd\"\n  },\n  \"metadata\": {\n    \"campaign\": \"Q1 Launch\",\n    \"targetAudience\": \"athletes\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/generations", "host": ["{{baseUrl}}"], "path": ["api", "generations"]}, "description": "Create a new AI generation request."}, "response": [{"name": "Generation Created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"proj_456\",\n  \"type\": \"image\",\n  \"prompt\": \"Create a product image\"\n}"}, "url": {"raw": "{{baseUrl}}/api/generations", "host": ["{{baseUrl}}"], "path": ["api", "generations"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"gen_123\",\n  \"projectId\": \"proj_456\",\n  \"type\": \"image\",\n  \"status\": \"processing\",\n  \"prompt\": \"Create a product image\",\n  \"estimatedCompletionTime\": \"2024-01-20T10:05:00.000Z\",\n  \"createdAt\": \"2024-01-20T10:00:00.000Z\"\n}"}]}, {"name": "Create Bulk Generations", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has generations array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('generations');", "    pm.expect(jsonData.generations).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{projectId}}\",\n  \"generations\": [\n    {\n      \"productId\": \"{{productId}}\",\n      \"type\": \"image\",\n      \"prompt\": \"Athletic shoes on white background\",\n      \"settings\": {\n        \"model\": \"dalle-3\",\n        \"dimensions\": \"1024x1024\"\n      }\n    },\n    {\n      \"productId\": \"{{productId}}\",\n      \"type\": \"copy\",\n      \"prompt\": \"Create engaging product description for athletic shoes\",\n      \"settings\": {\n        \"model\": \"gpt-4\",\n        \"maxTokens\": 200\n      }\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/generations/bulk", "host": ["{{baseUrl}}"], "path": ["api", "generations", "bulk"]}, "description": "Create multiple AI generations in a single request."}, "response": [{"name": "Bulk Generations Created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"proj_456\",\n  \"generations\": [\n    {\n      \"type\": \"image\",\n      \"prompt\": \"Product image 1\"\n    },\n    {\n      \"type\": \"copy\",\n      \"prompt\": \"Product description\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/generations/bulk", "host": ["{{baseUrl}}"], "path": ["api", "generations", "bulk"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"generations\": [\n    {\n      \"id\": \"gen_123\",\n      \"type\": \"image\",\n      \"status\": \"processing\",\n      \"createdAt\": \"2024-01-20T10:00:00.000Z\"\n    },\n    {\n      \"id\": \"gen_124\",\n      \"type\": \"copy\",\n      \"status\": \"processing\",\n      \"createdAt\": \"2024-01-20T10:00:00.000Z\"\n    }\n  ],\n  \"batchId\": \"batch_789\"\n}"}]}, {"name": "Get Generation by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has generation details', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/{{generationId}}", "host": ["{{baseUrl}}"], "path": ["api", "generations", "{{generationId}}"]}, "description": "Get a specific generation by its ID."}, "response": [{"name": "Generation Details", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/gen_123", "host": ["{{baseUrl}}"], "path": ["api", "generations", "gen_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"gen_123\",\n  \"projectId\": \"proj_456\",\n  \"productId\": \"prod_789\",\n  \"type\": \"image\",\n  \"status\": \"completed\",\n  \"prompt\": \"Create a modern product image for athletic shoes\",\n  \"generatedContent\": {\n    \"imageUrl\": \"https://storage.example.com/generated/image_123.jpg\",\n    \"thumbnailUrl\": \"https://storage.example.com/generated/thumb_123.jpg\",\n    \"metadata\": {\n      \"width\": 1024,\n      \"height\": 1024,\n      \"format\": \"jpg\",\n      \"size\": 245760\n    }\n  },\n  \"settings\": {\n    \"model\": \"dalle-3\",\n    \"dimensions\": \"1024x1024\",\n    \"style\": \"photorealistic\",\n    \"quality\": \"hd\"\n  },\n  \"createdAt\": \"2024-01-20T10:00:00.000Z\",\n  \"updatedAt\": \"2024-01-20T10:02:00.000Z\",\n  \"completedAt\": \"2024-01-20T10:02:00.000Z\"\n}"}]}, {"name": "Update Generation", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Generation updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"approved\",\n  \"metadata\": {\n    \"campaign\": \"Q1 Launch - Updated\",\n    \"targetAudience\": \"athletes\",\n    \"approved\": true,\n    \"approvedBy\": \"{{currentUserId}}\",\n    \"approvedAt\": \"2024-01-20T12:00:00.000Z\"\n  },\n  \"tags\": [\"approved\", \"q1-launch\", \"athletic-shoes\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/generations/{{generationId}}", "host": ["{{baseUrl}}"], "path": ["api", "generations", "{{generationId}}"]}, "description": "Update a generation's metadata, status, or tags."}, "response": [{"name": "Generation Updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"approved\",\n  \"metadata\": {\n    \"approved\": true\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/generations/gen_123", "host": ["{{baseUrl}}"], "path": ["api", "generations", "gen_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"gen_123\",\n  \"status\": \"approved\",\n  \"metadata\": {\n    \"approved\": true,\n    \"approvedBy\": \"user_123\",\n    \"approvedAt\": \"2024-01-20T12:00:00.000Z\"\n  },\n  \"updatedAt\": \"2024-01-20T12:00:00.000Z\"\n}"}]}, {"name": "Delete Generation", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 204', function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/{{generationId}}", "host": ["{{baseUrl}}"], "path": ["api", "generations", "{{generationId}}"]}, "description": "Delete a generation and its associated files."}, "response": [{"name": "Generation Deleted", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/gen_123", "host": ["{{baseUrl}}"], "path": ["api", "generations", "gen_123"]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": null, "header": [], "body": null}]}, {"name": "Save Generation Version", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Version saved successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('versionId');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"generationId\": \"{{generationId}}\",\n  \"versionName\": \"Final Approved Version\",\n  \"description\": \"Final version approved for Q1 launch campaign\",\n  \"metadata\": {\n    \"campaign\": \"Q1 Launch\",\n    \"approvedBy\": \"{{currentUserId}}\",\n    \"isFinal\": true\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/generations/save", "host": ["{{baseUrl}}"], "path": ["api", "generations", "save"]}, "description": "Save a specific version of a generation for future reference."}, "response": [{"name": "Version Saved", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"generationId\": \"gen_123\",\n  \"versionName\": \"Final Approved Version\"\n}"}, "url": {"raw": "{{baseUrl}}/api/generations/save", "host": ["{{baseUrl}}"], "path": ["api", "generations", "save"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"versionId\": \"ver_456\",\n  \"generationId\": \"gen_123\",\n  \"versionName\": \"Final Approved Version\",\n  \"versionNumber\": 3,\n  \"createdAt\": \"2024-01-20T12:00:00.000Z\",\n  \"snapshotUrl\": \"https://storage.example.com/versions/ver_456.jpg\"\n}"}]}, {"name": "Get Latest Version", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Latest version returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('versionId');", "    pm.expect(jsonData).to.have.property('versionNumber');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/version?generationId={{generationId}}", "host": ["{{baseUrl}}"], "path": ["api", "generations", "version"], "query": [{"key": "generationId", "value": "{{generationId}}", "description": "ID of the generation to get latest version for"}]}, "description": "Get the latest version of a generation."}, "response": [{"name": "Latest Version", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/version?generationId=gen_123", "host": ["{{baseUrl}}"], "path": ["api", "generations", "version"], "query": [{"key": "generationId", "value": "gen_123"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"versionId\": \"ver_456\",\n  \"generationId\": \"gen_123\",\n  \"versionName\": \"Final Approved Version\",\n  \"versionNumber\": 3,\n  \"isLatest\": true,\n  \"createdAt\": \"2024-01-20T12:00:00.000Z\",\n  \"snapshotUrl\": \"https://storage.example.com/versions/ver_456.jpg\"\n}"}]}, {"name": "Get Version History", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Version history returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('versions');", "    pm.expect(jsonData.versions).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/version/history?generationId={{generationId}}", "host": ["{{baseUrl}}"], "path": ["api", "generations", "version", "history"], "query": [{"key": "generationId", "value": "{{generationId}}", "description": "ID of the generation to get version history for"}]}, "description": "Get the complete version history for a generation."}, "response": [{"name": "Version History", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/version/history?generationId=gen_123", "host": ["{{baseUrl}}"], "path": ["api", "generations", "version", "history"], "query": [{"key": "generationId", "value": "gen_123"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"versions\": [\n    {\n      \"versionId\": \"ver_456\",\n      \"versionName\": \"Final Approved Version\",\n      \"versionNumber\": 3,\n      \"isLatest\": true,\n      \"createdAt\": \"2024-01-20T12:00:00.000Z\"\n    },\n    {\n      \"versionId\": \"ver_455\",\n      \"versionName\": \"Review Version\",\n      \"versionNumber\": 2,\n      \"isLatest\": false,\n      \"createdAt\": \"2024-01-20T11:00:00.000Z\"\n    },\n    {\n      \"versionId\": \"ver_454\",\n      \"versionName\": \"Initial Version\",\n      \"versionNumber\": 1,\n      \"isLatest\": false,\n      \"createdAt\": \"2024-01-20T10:00:00.000Z\"\n    }\n  ],\n  \"totalVersions\": 3\n}"}]}, {"name": "Get Project Generations", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Project generations returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/project/{{projectId}}?status=completed", "host": ["{{baseUrl}}"], "path": ["api", "generations", "project", "{{projectId}}"], "query": [{"key": "status", "value": "completed", "description": "Filter by generation status"}]}, "description": "Get all generations for a specific project."}, "response": [{"name": "Project Generations", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/generations/project/proj_456", "host": ["{{baseUrl}}"], "path": ["api", "generations", "project", "proj_456"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"gen_123\",\n    \"projectId\": \"proj_456\",\n    \"type\": \"image\",\n    \"status\": \"completed\",\n    \"createdAt\": \"2024-01-20T10:00:00.000Z\"\n  },\n  {\n    \"id\": \"gen_124\",\n    \"projectId\": \"proj_456\",\n    \"type\": \"copy\",\n    \"status\": \"completed\",\n    \"createdAt\": \"2024-01-20T10:05:00.000Z\"\n  }\n]"}]}], "description": "AI-powered generation management endpoints"}, {"name": "Creative Management", "item": [{"name": "Get Filtered Creatives", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Creatives returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('creatives');", "    pm.expect(jsonData.creatives).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"filters\": {\n    \"brandId\": \"{{brandId}}\",\n    \"projectId\": \"{{projectId}}\",\n    \"status\": \"active\",\n    \"type\": \"image\",\n    \"tags\": [\"approved\", \"launch-ready\"]\n  },\n  \"pagination\": {\n    \"limit\": 20,\n    \"offset\": 0\n  },\n  \"sort\": {\n    \"field\": \"updatedAt\",\n    \"order\": \"desc\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/creatives/get-creatives", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "get-creatives"]}, "description": "Get filtered creatives with advanced search capabilities."}, "response": [{"name": "Filtered Creatives", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"filters\": {\n    \"brandId\": \"brand_123\",\n    \"status\": \"active\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/creatives/get-creatives", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "get-creatives"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"creatives\": [\n    {\n      \"id\": \"creative_123\",\n      \"brandId\": \"brand_123\",\n      \"projectId\": \"proj_456\",\n      \"name\": \"Athletic Shoes - Hero Image\",\n      \"type\": \"image\",\n      \"status\": \"active\",\n      \"assetUrl\": \"https://storage.example.com/creatives/creative_123.jpg\",\n      \"thumbnailUrl\": \"https://storage.example.com/creatives/thumb_123.jpg\",\n      \"metadata\": {\n        \"dimensions\": \"1080x1080\",\n        \"format\": \"jpg\",\n        \"size\": 512000\n      },\n      \"tags\": [\"approved\", \"launch-ready\"],\n      \"createdAt\": \"2024-01-20T10:00:00.000Z\",\n      \"updatedAt\": \"2024-01-20T12:00:00.000Z\"\n    }\n  ],\n  \"pagination\": {\n    \"total\": 1,\n    \"limit\": 20,\n    \"offset\": 0\n  }\n}"}]}, {"name": "Get Creative Preview", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Preview data returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('previewUrl');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creatives/{{creativeId}}/preview?size=medium", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "{{creativeId}}", "preview"], "query": [{"key": "size", "value": "medium", "description": "Preview size (small, medium, large)"}]}, "description": "Get a preview of a creative asset."}, "response": [{"name": "Creative Preview", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creatives/creative_123/preview", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "creative_123", "preview"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"previewUrl\": \"https://storage.example.com/previews/creative_123_medium.jpg\",\n  \"thumbnailUrl\": \"https://storage.example.com/previews/creative_123_thumb.jpg\",\n  \"metadata\": {\n    \"dimensions\": \"540x540\",\n    \"format\": \"jpg\",\n    \"size\": 128000\n  },\n  \"expiresAt\": \"2024-01-20T18:00:00.000Z\"\n}"}]}, {"name": "Get Creative by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Creative details returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('assetUrl');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creatives/{{creativeId}}", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "{{creativeId}}"]}, "description": "Get detailed information about a specific creative."}, "response": [{"name": "Creative Details", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/creatives/creative_123", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "creative_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"creative_123\",\n  \"brandId\": \"brand_123\",\n  \"projectId\": \"proj_456\",\n  \"name\": \"Athletic Shoes - Hero Image\",\n  \"description\": \"Hero image for athletic shoes product launch\",\n  \"type\": \"image\",\n  \"status\": \"active\",\n  \"assetUrl\": \"https://storage.example.com/creatives/creative_123.jpg\",\n  \"thumbnailUrl\": \"https://storage.example.com/creatives/thumb_123.jpg\",\n  \"metadata\": {\n    \"dimensions\": \"1080x1080\",\n    \"format\": \"jpg\",\n    \"size\": 512000,\n    \"colorProfile\": \"sRGB\",\n    \"dpi\": 300\n  },\n  \"tags\": [\"approved\", \"launch-ready\", \"hero-image\"],\n  \"generatedFrom\": \"gen_123\",\n  \"createdAt\": \"2024-01-20T10:00:00.000Z\",\n  \"updatedAt\": \"2024-01-20T12:00:00.000Z\",\n  \"createdBy\": \"user_123\"\n}"}]}, {"name": "Create Creative", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Creative created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.collectionVariables.set('creativeId', jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"brandId\": \"{{brandId}}\",\n  \"projectId\": \"{{projectId}}\",\n  \"name\": \"Athletic Shoes - Social Media Post\",\n  \"description\": \"Social media post for athletic shoes campaign\",\n  \"type\": \"image\",\n  \"assetUrl\": \"https://storage.example.com/uploads/new_creative.jpg\",\n  \"metadata\": {\n    \"dimensions\": \"1080x1080\",\n    \"format\": \"jpg\",\n    \"targetPlatform\": \"instagram\"\n  },\n  \"tags\": [\"social-media\", \"athletic-shoes\"],\n  \"generatedFrom\": \"gen_124\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/creatives", "host": ["{{baseUrl}}"], "path": ["api", "creatives"]}, "description": "Create a new creative asset."}, "response": [{"name": "Creative Created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"brandId\": \"brand_123\",\n  \"name\": \"Athletic Shoes - Social Media Post\",\n  \"type\": \"image\",\n  \"assetUrl\": \"https://storage.example.com/uploads/new_creative.jpg\"\n}"}, "url": {"raw": "{{baseUrl}}/api/creatives", "host": ["{{baseUrl}}"], "path": ["api", "creatives"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"creative_456\",\n  \"brandId\": \"brand_123\",\n  \"projectId\": \"proj_456\",\n  \"name\": \"Athletic Shoes - Social Media Post\",\n  \"type\": \"image\",\n  \"status\": \"draft\",\n  \"assetUrl\": \"https://storage.example.com/uploads/new_creative.jpg\",\n  \"createdAt\": \"2024-01-20T14:00:00.000Z\",\n  \"createdBy\": \"user_123\"\n}"}]}, {"name": "Update Creative", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Creative updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Athletic Shoes - Final Social Media Post\",\n  \"description\": \"Final approved social media post for athletic shoes campaign\",\n  \"status\": \"approved\",\n  \"tags\": [\"social-media\", \"athletic-shoes\", \"approved\", \"final\"],\n  \"metadata\": {\n    \"dimensions\": \"1080x1080\",\n    \"format\": \"jpg\",\n    \"targetPlatform\": \"instagram\",\n    \"approvedBy\": \"{{currentUserId}}\",\n    \"approvedAt\": \"2024-01-20T15:00:00.000Z\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/creatives/{{creativeId}}", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "{{creativeId}}"]}, "description": "Update a creative asset's metadata and status."}, "response": [{"name": "Creative Updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Athletic Shoes - Final Social Media Post\",\n  \"status\": \"approved\"\n}"}, "url": {"raw": "{{baseUrl}}/api/creatives/creative_456", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "creative_456"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"creative_456\",\n  \"name\": \"Athletic Shoes - Final Social Media Post\",\n  \"status\": \"approved\",\n  \"tags\": [\"social-media\", \"athletic-shoes\", \"approved\", \"final\"],\n  \"updatedAt\": \"2024-01-20T15:00:00.000Z\"\n}"}]}, {"name": "Delete Creative", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 204', function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/creatives/{{creativeId}}", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "{{creativeId}}"]}, "description": "Delete a creative asset and its associated files."}, "response": [{"name": "Creative Deleted", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/creatives/creative_456", "host": ["{{baseUrl}}"], "path": ["api", "creatives", "creative_456"]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": null, "header": [], "body": null}]}], "description": "Creative asset management endpoints"}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "currentUserId", "value": "", "type": "string"}, {"key": "projectId", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}, {"key": "brandId", "value": "", "type": "string"}, {"key": "generationId", "value": "", "type": "string"}, {"key": "creativeId", "value": "", "type": "string"}]}