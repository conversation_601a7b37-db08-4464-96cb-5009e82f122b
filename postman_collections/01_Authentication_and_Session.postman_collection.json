{"info": {"_postman_id": "auth-session-collection", "name": "01. Authentication & Session Management", "description": "Complete authentication flow including login, signup, password reset, session management, and user profile operations. This collection handles all auth-related endpoints with full request/response examples.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "adfury-api"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "test", "script": {"exec": ["// Global test script to save auth token", "if (pm.response.json().token) {", "    pm.collectionVariables.set('authToken', pm.response.json().token);", "}", "", "// Save user ID if present", "if (pm.response.json().user && pm.response.json().user.id) {", "    pm.collectionVariables.set('currentUserId', pm.response.json().user.id);", "}"], "type": "text/javascript"}}], "item": [{"name": "Public Authentication", "item": [{"name": "User Login", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has user object', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData.user).to.have.property('id');", "    pm.expect(jsonData.user).to.have.property('email');", "});", "", "// Save session data", "const jsonData = pm.response.json();", "if (jsonData.user) {", "    pm.collectionVariables.set('currentUserId', jsonData.user.id);", "    pm.collectionVariables.set('currentUserEmail', jsonData.user.email);", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["// Set test credentials if not provided", "if (!pm.variables.get('testEmail')) {", "    pm.variables.set('testEmail', '<EMAIL>');", "    pm.variables.set('testPassword', 'TestPassword123!');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{testEmail}}\",\n  \"password\": \"{{testPassword}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Authenticate a user and establish a session. Rate limited to 5 attempts per 15 minutes.\n\n**Request Body:**\n- `email` (required): User's email address\n- `password` (required): User's password\n\n**Response:**\n- `user`: User object with profile information\n- `accounts`: Array of accounts the user has access to\n- Session cookie is set for subsequent requests\n\n**Rate Limiting:**\n- 5 login attempts per 15 minutes per IP\n- Returns 429 status code when limit exceeded"}, "response": [{"name": "Successful Login", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Set-<PERSON><PERSON>", "value": "sessionId=abc123; Path=/; HttpOnly; Secure"}], "cookie": [], "body": "{\n  \"user\": {\n    \"id\": \"550e8400-e29b-41d4-a716-************\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON>\",\n    \"auth0Id\": \"auth0|*********\",\n    \"profilePictureUrl\": \"https://example.com/profile.jpg\",\n    \"createdAt\": \"2024-01-01T00:00:00.000Z\",\n    \"updatedAt\": \"2024-01-15T12:00:00.000Z\"\n  },\n  \"accounts\": [\n    {\n      \"id\": \"660e8400-e29b-41d4-a716-************\",\n      \"name\": \"Acme Corporation\",\n      \"type\": \"agency\",\n      \"role\": \"admin\",\n      \"status\": \"active\"\n    }\n  ]\n}"}, {"name": "Invalid Credentials", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"error\": \"Invalid credentials\",\n  \"message\": \"The email or password provided is incorrect\"\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Retry-After", "value": "900"}], "cookie": [], "body": "{\n  \"code\": 429,\n  \"error\": \"Too Many Requests\",\n  \"message\": \"Rate limit exceeded. You can make 5 login attempts per 15 minutes. Retry in 900 seconds.\",\n  \"retryAfter\": 900\n}"}]}, {"name": "User Signup", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has user object', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData).to.have.property('account');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"name\": \"New User\",\n  \"accountName\": \"My New Company\",\n  \"betaCode\": \"BETA2024\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/signup?betaCode={{betaCode}}", "host": ["{{baseUrl}}"], "path": ["auth", "signup"], "query": [{"key": "betaCode", "value": "{{betaCode}}", "description": "Optional beta access code"}]}, "description": "Register a new user account. Rate limited to 3 attempts per hour.\n\n**Request Body:**\n- `email` (required): Valid email address\n- `password` (required): Secure password (min 8 chars)\n- `name` (required): User's full name\n- `accountName` (required): Name for the initial account\n- `betaCode` (optional): Beta access code if in beta period\n\n**Response:**\n- `user`: Newly created user object\n- `account`: Initial account created for the user\n- Session is automatically established\n\n**Rate Limiting:**\n- 3 signup attempts per hour per IP"}, "response": [{"name": "Successful Signup", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"name\": \"New User\",\n  \"accountName\": \"My New Company\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/signup", "host": ["{{baseUrl}}"], "path": ["auth", "signup"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"user\": {\n    \"id\": \"770e8400-e29b-41d4-a716-************\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"New User\",\n    \"auth0Id\": \"auth0|*********\",\n    \"createdAt\": \"2024-01-20T10:00:00.000Z\"\n  },\n  \"account\": {\n    \"id\": \"880e8400-e29b-41d4-a716-************\",\n    \"name\": \"My New Company\",\n    \"type\": \"agency\",\n    \"ownerId\": \"770e8400-e29b-41d4-a716-************\",\n    \"status\": \"active\",\n    \"isTrial\": true,\n    \"seats\": 5\n  }\n}"}, {"name": "Email Already Exists", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"name\": \"New User\",\n  \"accountName\": \"My New Company\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/signup", "host": ["{{baseUrl}}"], "path": ["auth", "signup"]}}, "status": "Conflict", "code": 409, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"error\": \"User already exists\",\n  \"message\": \"An account with this email address already exists\"\n}"}]}, {"name": "Request Password Reset", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Success message received', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/password-reset", "host": ["{{baseUrl}}"], "path": ["auth", "password-reset"]}, "description": "Request a password reset email. Rate limited to 2 attempts per hour.\n\n**Request Body:**\n- `email` (required): Email address of the account\n\n**Response:**\n- Always returns success for security (even if email doesn't exist)\n- Password reset email is sent if account exists\n\n**Rate Limiting:**\n- 2 password reset attempts per hour per IP"}, "response": [{"name": "Password Reset Requested", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/password-reset", "host": ["{{baseUrl}}"], "path": ["auth", "password-reset"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"If an account exists with this email, a password reset link has been sent.\"\n}"}]}], "description": "Public authentication endpoints that don't require an existing session"}, {"name": "Authenticated Session Management", "item": [{"name": "Get Current User Profile", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User profile structure is correct', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('email');", "    pm.expect(jsonData).to.have.property('accounts');", "    pm.expect(jsonData.accounts).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}, "description": "Get the current authenticated user's profile information.\n\n**Authorization Required:** Bearer token from login\n\n**Response:**\n- Complete user profile including:\n  - Basic info (id, email, name)\n  - Auth0 metadata\n  - Associated accounts with roles\n  - Profile picture URL\n  - Timestamps"}, "response": [{"name": "Current User Profile", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"550e8400-e29b-41d4-a716-************\",\n  \"email\": \"<EMAIL>\",\n  \"name\": \"<PERSON>\",\n  \"auth0Id\": \"auth0|*********\",\n  \"profilePictureUrl\": \"https://example.com/profile.jpg\",\n  \"phone\": \"+*********0\",\n  \"jobTitle\": \"Marketing Manager\",\n  \"createdAt\": \"2024-01-01T00:00:00.000Z\",\n  \"updatedAt\": \"2024-01-15T12:00:00.000Z\",\n  \"accounts\": [\n    {\n      \"id\": \"660e8400-e29b-41d4-a716-************\",\n      \"name\": \"Acme Corporation\",\n      \"type\": \"agency\",\n      \"role\": \"admin\",\n      \"status\": \"active\",\n      \"logoUrl\": \"https://example.com/logo.png\",\n      \"joinedAt\": \"2024-01-01T00:00:00.000Z\"\n    },\n    {\n      \"id\": \"770e8400-e29b-41d4-a716-************\",\n      \"name\": \"Beta Industries\",\n      \"type\": \"brand\",\n      \"role\": \"member\",\n      \"status\": \"active\",\n      \"joinedAt\": \"2024-01-10T00:00:00.000Z\"\n    }\n  ],\n  \"metadata\": {\n    \"lastLogin\": \"2024-01-20T10:00:00.000Z\",\n    \"loginCount\": 42,\n    \"emailVerified\": true\n  }\n}"}, {"name": "Unauthorized - No Session", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"error\": \"Unauthorized\",\n  \"message\": \"Authentication required\"\n}"}]}, {"name": "User <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Clear stored variables", "pm.collectionVariables.unset('authToken');", "pm.collectionVariables.unset('currentUserId');", "pm.collectionVariables.unset('currentUserEmail');"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}, "description": "Logout the current user and destroy the session.\n\n**Authorization Required:** Bearer token from login\n\n**Effects:**\n- Destroys server-side session\n- Clears session cookie\n- Invalidates current authentication"}, "response": [{"name": "Successful Logout", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Set-<PERSON><PERSON>", "value": "sessionId=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Logged out successfully\"\n}"}]}], "description": "Endpoints that require an authenticated session", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Check if we have an auth token", "if (!pm.collectionVariables.get('authToken')) {", "    console.log('No auth token found. Please login first.');", "}"], "type": "text/javascript"}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "currentUserId", "value": "", "type": "string"}, {"key": "currentUserEmail", "value": "", "type": "string"}, {"key": "testEmail", "value": "<EMAIL>", "type": "string"}, {"key": "testPassword", "value": "TestPassword123!", "type": "string"}, {"key": "betaCode", "value": "", "type": "string"}]}