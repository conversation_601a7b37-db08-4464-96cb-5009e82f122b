{"info": {"_postman_id": "keyword-management-collection", "name": "10. Keyword Management", "description": "Complete keyword management system including keyword operations, product keyword associations, and keyword analytics.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "adfury-api"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Ensure auth token is available", "if (!pm.collectionVariables.get('authToken')) {", "    console.warn('No auth token found. Please run Authentication collection first.');", "}"], "type": "text/javascript"}}], "item": [{"name": "Product Keyword Management", "item": [{"name": "Add Keywords to Product", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Keywords added successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData).to.have.property('addedKeywords');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"keywords\": [\n    {\n      \"keyword\": \"athletic shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\",\n      \"searchVolume\": 45000,\n      \"competition\": \"medium\",\n      \"relevanceScore\": 0.95\n    },\n    {\n      \"keyword\": \"running shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\",\n      \"searchVolume\": 67000,\n      \"competition\": \"high\",\n      \"relevanceScore\": 0.92\n    },\n    {\n      \"keyword\": \"nike air zoom\",\n      \"type\": \"brand\",\n      \"priority\": \"medium\",\n      \"searchVolume\": 12000,\n      \"competition\": \"low\",\n      \"relevanceScore\": 0.88\n    },\n    {\n      \"keyword\": \"comfortable running shoes\",\n      \"type\": \"long-tail\",\n      \"priority\": \"medium\",\n      \"searchVolume\": 8500,\n      \"competition\": \"low\",\n      \"relevanceScore\": 0.85\n    },\n    {\n      \"keyword\": \"marathon shoes\",\n      \"type\": \"secondary\",\n      \"priority\": \"low\",\n      \"searchVolume\": 3200,\n      \"competition\": \"medium\",\n      \"relevanceScore\": 0.78\n    }\n  ],\n  \"metadata\": {\n    \"source\": \"keyword-research-tool\",\n    \"researchDate\": \"2024-01-20\",\n    \"targetAudience\": \"athletes\",\n    \"campaign\": \"Q1 2024 Launch\"\n  },\n  \"options\": {\n    \"overwriteExisting\": false,\n    \"validateRelevance\": true,\n    \"autoOptimize\": true\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/keywords/products/{{productId}}/keywords", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "products", "{{productId}}", "keywords"]}, "description": "Add multiple keywords to a product with comprehensive metadata and analytics data."}, "response": [{"name": "Keywords Added Successfully", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"keywords\": [\n    {\n      \"keyword\": \"athletic shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\"\n    },\n    {\n      \"keyword\": \"running shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/keywords/products/prod_123/keywords", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "products", "prod_123", "keywords"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"productId\": \"prod_123\",\n  \"addedKeywords\": [\n    {\n      \"keywordId\": \"kw_001\",\n      \"keyword\": \"athletic shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\",\n      \"searchVolume\": 45000,\n      \"competition\": \"medium\",\n      \"relevanceScore\": 0.95,\n      \"addedAt\": \"2024-01-20T18:00:00.000Z\"\n    },\n    {\n      \"keywordId\": \"kw_002\",\n      \"keyword\": \"running shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\",\n      \"searchVolume\": 67000,\n      \"competition\": \"high\",\n      \"relevanceScore\": 0.92,\n      \"addedAt\": \"2024-01-20T18:00:00.000Z\"\n    },\n    {\n      \"keywordId\": \"kw_003\",\n      \"keyword\": \"nike air zoom\",\n      \"type\": \"brand\",\n      \"priority\": \"medium\",\n      \"searchVolume\": 12000,\n      \"competition\": \"low\",\n      \"relevanceScore\": 0.88,\n      \"addedAt\": \"2024-01-20T18:00:00.000Z\"\n    },\n    {\n      \"keywordId\": \"kw_004\",\n      \"keyword\": \"comfortable running shoes\",\n      \"type\": \"long-tail\",\n      \"priority\": \"medium\",\n      \"searchVolume\": 8500,\n      \"competition\": \"low\",\n      \"relevanceScore\": 0.85,\n      \"addedAt\": \"2024-01-20T18:00:00.000Z\"\n    },\n    {\n      \"keywordId\": \"kw_005\",\n      \"keyword\": \"marathon shoes\",\n      \"type\": \"secondary\",\n      \"priority\": \"low\",\n      \"searchVolume\": 3200,\n      \"competition\": \"medium\",\n      \"relevanceScore\": 0.78,\n      \"addedAt\": \"2024-01-20T18:00:00.000Z\"\n    }\n  ],\n  \"skippedKeywords\": [],\n  \"totalAdded\": 5,\n  \"optimizationSuggestions\": [\n    {\n      \"type\": \"prioritization\",\n      \"message\": \"Consider focusing on 'running shoes' due to high search volume\",\n      \"impact\": \"high\"\n    },\n    {\n      \"type\": \"long-tail\",\n      \"message\": \"Add more long-tail keywords for better conversion rates\",\n      \"impact\": \"medium\"\n    }\n  ]\n}"}]}, {"name": "Remove Keyword from Product", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Keyword removed successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/keywords/products/{{productId}}/keywords/{{keywordId}}?reason=low-performance", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "products", "{{productId}}", "keywords", "{{keywordId}}"], "query": [{"key": "reason", "value": "low-performance", "description": "Reason for removal (optional)"}]}, "description": "Remove a specific keyword from a product."}, "response": [{"name": "Keyword Removed", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/keywords/products/prod_123/keywords/kw_005", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "products", "prod_123", "keywords", "kw_005"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"productId\": \"prod_123\",\n  \"keywordId\": \"kw_005\",\n  \"keyword\": \"marathon shoes\",\n  \"removedAt\": \"2024-01-20T18:30:00.000Z\",\n  \"reason\": \"low-performance\",\n  \"analytics\": {\n    \"daysActive\": 15,\n    \"impressions\": 127,\n    \"clicks\": 3,\n    \"ctr\": 0.024,\n    \"conversions\": 0\n  }\n}"}]}, {"name": "Get Product Keywords", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product keywords returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('keywords');", "    pm.expect(jsonData.keywords).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/keywords/products/{{productId}}/keywords?includeAnalytics=true&sortBy=performance", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "products", "{{productId}}", "keywords"], "query": [{"key": "includeAnalytics", "value": "true", "description": "Include performance analytics data"}, {"key": "sortBy", "value": "performance", "description": "Sort keywords by (performance, priority, relevance, alphabetical)"}, {"key": "type", "value": "primary", "disabled": true, "description": "Filter by keyword type"}, {"key": "priority", "value": "high", "disabled": true, "description": "Filter by keyword priority"}]}, "description": "Get all keywords associated with a specific product."}, "response": [{"name": "Product Keywords", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/keywords/products/prod_123/keywords", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "products", "prod_123", "keywords"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"productId\": \"prod_123\",\n  \"keywords\": [\n    {\n      \"keywordId\": \"kw_001\",\n      \"keyword\": \"athletic shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\",\n      \"searchVolume\": 45000,\n      \"competition\": \"medium\",\n      \"relevanceScore\": 0.95,\n      \"analytics\": {\n        \"impressions\": 1250,\n        \"clicks\": 89,\n        \"ctr\": 0.071,\n        \"conversions\": 12,\n        \"conversionRate\": 0.135,\n        \"revenue\": 1548.88,\n        \"costPerClick\": 1.45,\n        \"returnOnAdSpend\": 3.2\n      },\n      \"addedAt\": \"2024-01-20T18:00:00.000Z\",\n      \"lastUpdated\": \"2024-01-20T18:00:00.000Z\"\n    },\n    {\n      \"keywordId\": \"kw_002\",\n      \"keyword\": \"running shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\",\n      \"searchVolume\": 67000,\n      \"competition\": \"high\",\n      \"relevanceScore\": 0.92,\n      \"analytics\": {\n        \"impressions\": 2100,\n        \"clicks\": 156,\n        \"ctr\": 0.074,\n        \"conversions\": 18,\n        \"conversionRate\": 0.115,\n        \"revenue\": 2337.82,\n        \"costPerClick\": 2.10,\n        \"returnOnAdSpend\": 2.8\n      },\n      \"addedAt\": \"2024-01-20T18:00:00.000Z\",\n      \"lastUpdated\": \"2024-01-20T18:00:00.000Z\"\n    },\n    {\n      \"keywordId\": \"kw_003\",\n      \"keyword\": \"nike air zoom\",\n      \"type\": \"brand\",\n      \"priority\": \"medium\",\n      \"searchVolume\": 12000,\n      \"competition\": \"low\",\n      \"relevanceScore\": 0.88,\n      \"analytics\": {\n        \"impressions\": 450,\n        \"clicks\": 38,\n        \"ctr\": 0.084,\n        \"conversions\": 6,\n        \"conversionRate\": 0.158,\n        \"revenue\": 779.94,\n        \"costPerClick\": 0.85,\n        \"returnOnAdSpend\": 4.1\n      },\n      \"addedAt\": \"2024-01-20T18:00:00.000Z\",\n      \"lastUpdated\": \"2024-01-20T18:00:00.000Z\"\n    },\n    {\n      \"keywordId\": \"kw_004\",\n      \"keyword\": \"comfortable running shoes\",\n      \"type\": \"long-tail\",\n      \"priority\": \"medium\",\n      \"searchVolume\": 8500,\n      \"competition\": \"low\",\n      \"relevanceScore\": 0.85,\n      \"analytics\": {\n        \"impressions\": 320,\n        \"clicks\": 29,\n        \"ctr\": 0.091,\n        \"conversions\": 5,\n        \"conversionRate\": 0.172,\n        \"revenue\": 649.95,\n        \"costPerClick\": 0.65,\n        \"returnOnAdSpend\": 4.8\n      },\n      \"addedAt\": \"2024-01-20T18:00:00.000Z\",\n      \"lastUpdated\": \"2024-01-20T18:00:00.000Z\"\n    }\n  ],\n  \"summary\": {\n    \"totalKeywords\": 4,\n    \"byType\": {\n      \"primary\": 2,\n      \"brand\": 1,\n      \"long-tail\": 1,\n      \"secondary\": 0\n    },\n    \"byPriority\": {\n      \"high\": 2,\n      \"medium\": 2,\n      \"low\": 0\n    },\n    \"avgRelevanceScore\": 0.90,\n    \"totalSearchVolume\": 132500,\n    \"performance\": {\n      \"totalImpressions\": 4120,\n      \"totalClicks\": 312,\n      \"avgCtr\": 0.076,\n      \"totalConversions\": 41,\n      \"avgConversionRate\": 0.145,\n      \"totalRevenue\": 5316.59\n    }\n  },\n  \"lastUpdated\": \"2024-01-20T18:30:00.000Z\"\n}"}]}], "description": "Product-specific keyword management operations"}, {"name": "Account Keyword Management", "item": [{"name": "Get Keywords by Account", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Account keywords returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('keywords');", "    pm.expect(jsonData.keywords).to.be.an('array');", "    pm.expect(jsonData).to.have.property('summary');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/keywords/accounts/{{accountId}}/keywords?limit=50&offset=0&includeProducts=true&sortBy=performance&order=desc", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "accounts", "{{accountId}}", "keywords"], "query": [{"key": "limit", "value": "50", "description": "Number of keywords to return"}, {"key": "offset", "value": "0", "description": "Number of keywords to skip"}, {"key": "includeProducts", "value": "true", "description": "Include associated product information"}, {"key": "sortBy", "value": "performance", "description": "Sort by (performance, priority, relevance, searchVolume, alphabetical)"}, {"key": "order", "value": "desc", "description": "Sort order (asc, desc)"}, {"key": "brandId", "value": "{{brandId}}", "disabled": true, "description": "Filter by brand"}, {"key": "type", "value": "primary", "disabled": true, "description": "Filter by keyword type"}, {"key": "priority", "value": "high", "disabled": true, "description": "Filter by priority level"}, {"key": "search", "value": "", "disabled": true, "description": "Search keywords by text"}]}, "description": "Get all keywords for an account with comprehensive filtering and analytics."}, "response": [{"name": "Account Keywords", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/keywords/accounts/acc_123/keywords", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "accounts", "acc_123", "keywords"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"accountId\": \"acc_123\",\n  \"keywords\": [\n    {\n      \"keywordId\": \"kw_001\",\n      \"keyword\": \"athletic shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\",\n      \"searchVolume\": 45000,\n      \"competition\": \"medium\",\n      \"relevanceScore\": 0.95,\n      \"products\": [\n        {\n          \"productId\": \"prod_123\",\n          \"productName\": \"Athletic Running Shoes\",\n          \"brandId\": \"brand_123\",\n          \"brandName\": \"Nike\"\n        },\n        {\n          \"productId\": \"prod_124\",\n          \"productName\": \"Training Shoes\",\n          \"brandId\": \"brand_123\",\n          \"brandName\": \"Nike\"\n        }\n      ],\n      \"analytics\": {\n        \"totalImpressions\": 2150,\n        \"totalClicks\": 167,\n        \"avgCtr\": 0.078,\n        \"totalConversions\": 23,\n        \"avgConversionRate\": 0.138,\n        \"totalRevenue\": 2998.77,\n        \"avgCostPerClick\": 1.45,\n        \"totalSpend\": 242.15,\n        \"returnOnAdSpend\": 12.4\n      },\n      \"createdAt\": \"2024-01-20T18:00:00.000Z\",\n      \"lastUpdated\": \"2024-01-20T20:00:00.000Z\"\n    },\n    {\n      \"keywordId\": \"kw_002\",\n      \"keyword\": \"running shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\",\n      \"searchVolume\": 67000,\n      \"competition\": \"high\",\n      \"relevanceScore\": 0.92,\n      \"products\": [\n        {\n          \"productId\": \"prod_123\",\n          \"productName\": \"Athletic Running Shoes\",\n          \"brandId\": \"brand_123\",\n          \"brandName\": \"Nike\"\n        }\n      ],\n      \"analytics\": {\n        \"totalImpressions\": 3200,\n        \"totalClicks\": 245,\n        \"avgCtr\": 0.077,\n        \"totalConversions\": 28,\n        \"avgConversionRate\": 0.114,\n        \"totalRevenue\": 3641.72,\n        \"avgCostPerClick\": 2.10,\n        \"totalSpend\": 514.50,\n        \"returnOnAdSpend\": 7.1\n      },\n      \"createdAt\": \"2024-01-20T18:00:00.000Z\",\n      \"lastUpdated\": \"2024-01-20T20:00:00.000Z\"\n    },\n    {\n      \"keywordId\": \"kw_006\",\n      \"keyword\": \"basketball shoes\",\n      \"type\": \"primary\",\n      \"priority\": \"high\",\n      \"searchVolume\": 38000,\n      \"competition\": \"medium\",\n      \"relevanceScore\": 0.89,\n      \"products\": [\n        {\n          \"productId\": \"prod_125\",\n          \"productName\": \"High-Top Basketball Shoes\",\n          \"brandId\": \"brand_123\",\n          \"brandName\": \"Nike\"\n        }\n      ],\n      \"analytics\": {\n        \"totalImpressions\": 1850,\n        \"totalClicks\": 132,\n        \"avgCtr\": 0.071,\n        \"totalConversions\": 19,\n        \"avgConversionRate\": 0.144,\n        \"totalRevenue\": 2849.81,\n        \"avgCostPerClick\": 1.75,\n        \"totalSpend\": 231.00,\n        \"returnOnAdSpend\": 12.3\n      },\n      \"createdAt\": \"2024-01-18T14:00:00.000Z\",\n      \"lastUpdated\": \"2024-01-20T20:00:00.000Z\"\n    }\n  ],\n  \"pagination\": {\n    \"total\": 47,\n    \"limit\": 50,\n    \"offset\": 0,\n    \"hasMore\": false\n  },\n  \"summary\": {\n    \"totalKeywords\": 47,\n    \"uniqueKeywords\": 47,\n    \"totalProducts\": 8,\n    \"totalBrands\": 3,\n    \"byType\": {\n      \"primary\": 18,\n      \"secondary\": 12,\n      \"brand\": 8,\n      \"long-tail\": 9\n    },\n    \"byPriority\": {\n      \"high\": 15,\n      \"medium\": 22,\n      \"low\": 10\n    },\n    \"totalSearchVolume\": 1245000,\n    \"avgRelevanceScore\": 0.87,\n    \"performance\": {\n      \"totalImpressions\": 45620,\n      \"totalClicks\": 3456,\n      \"avgCtr\": 0.076,\n      \"totalConversions\": 398,\n      \"avgConversionRate\": 0.115,\n      \"totalRevenue\": 51847.35,\n      \"totalSpend\": 7248.90,\n      \"avgReturnOnAdSpend\": 7.2\n    }\n  },\n  \"insights\": [\n    {\n      \"type\": \"top-performer\",\n      \"message\": \"Long-tail keywords showing 35% higher conversion rates\",\n      \"actionable\": true,\n      \"recommendation\": \"Increase focus on long-tail keyword strategy\"\n    },\n    {\n      \"type\": \"opportunity\",\n      \"message\": \"Brand keywords have lowest CPC with high conversion rates\",\n      \"actionable\": true,\n      \"recommendation\": \"Expand brand keyword coverage\"\n    },\n    {\n      \"type\": \"warning\",\n      \"message\": \"3 keywords showing declining performance trend\",\n      \"actionable\": true,\n      \"recommendation\": \"Review and optimize underperforming keywords\"\n    }\n  ],\n  \"lastUpdated\": \"2024-01-20T20:00:00.000Z\"\n}"}]}, {"name": "Keyword Performance Analytics", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Analytics data returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('analytics');", "    pm.expect(jsonData).to.have.property('trends');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"dateRange\": {\n    \"start\": \"2024-01-01\",\n    \"end\": \"2024-01-31\"\n  },\n  \"metrics\": [\n    \"impressions\",\n    \"clicks\",\n    \"ctr\",\n    \"conversions\",\n    \"conversionRate\",\n    \"revenue\",\n    \"costPerClick\",\n    \"returnOnAdSpend\"\n  ],\n  \"groupBy\": [\"keyword\", \"type\", \"priority\"],\n  \"filters\": {\n    \"brandId\": \"{{brandId}}\",\n    \"productIds\": [\"{{productId}}\"],\n    \"keywordTypes\": [\"primary\", \"brand\"],\n    \"minSearchVolume\": 1000,\n    \"minImpressions\": 100\n  },\n  \"aggregations\": {\n    \"includeTrends\": true,\n    \"includeComparisons\": true,\n    \"includeProjections\": true\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/keywords/analytics/performance", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "analytics", "performance"]}, "description": "Get comprehensive keyword performance analytics with trends and insights."}, "response": [{"name": "Keyword Performance Analytics", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"acc_123\",\n  \"dateRange\": {\n    \"start\": \"2024-01-01\",\n    \"end\": \"2024-01-31\"\n  },\n  \"metrics\": [\"impressions\", \"clicks\", \"conversions\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/keywords/analytics/performance", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "analytics", "performance"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"analytics\": {\n    \"dateRange\": {\n      \"start\": \"2024-01-01\",\n      \"end\": \"2024-01-31\"\n    },\n    \"summary\": {\n      \"totalKeywords\": 47,\n      \"activeKeywords\": 45,\n      \"totalImpressions\": 145620,\n      \"totalClicks\": 11234,\n      \"avgCtr\": 0.077,\n      \"totalConversions\": 1287,\n      \"avgConversionRate\": 0.115,\n      \"totalRevenue\": 167423.89,\n      \"totalSpend\": 23654.30,\n      \"avgReturnOnAdSpend\": 7.1\n    },\n    \"byType\": {\n      \"primary\": {\n        \"keywords\": 18,\n        \"impressions\": 89450,\n        \"clicks\": 6789,\n        \"conversions\": 798,\n        \"revenue\": 103845.67,\n        \"spend\": 14256.78,\n        \"roas\": 7.3\n      },\n      \"brand\": {\n        \"keywords\": 8,\n        \"impressions\": 23400,\n        \"clicks\": 2145,\n        \"conversions\": 289,\n        \"revenue\": 37654.22,\n        \"spend\": 4567.89,\n        \"roas\": 8.2\n      },\n      \"long-tail\": {\n        \"keywords\": 9,\n        \"impressions\": 18650,\n        \"clicks\": 1456,\n        \"conversions\": 167,\n        \"revenue\": 21743.56,\n        \"spend\": 2890.12,\n        \"roas\": 7.5\n      },\n      \"secondary\": {\n        \"keywords\": 12,\n        \"impressions\": 14120,\n        \"clicks\": 844,\n        \"conversions\": 33,\n        \"revenue\": 4180.44,\n        \"spend\": 1939.51,\n        \"roas\": 2.2\n      }\n    },\n    \"byPriority\": {\n      \"high\": {\n        \"keywords\": 15,\n        \"impressions\": 98750,\n        \"clicks\": 7234,\n        \"conversions\": 923,\n        \"revenue\": 120456.78,\n        \"roas\": 8.1\n      },\n      \"medium\": {\n        \"keywords\": 22,\n        \"impressions\": 38900,\n        \"clicks\": 3456,\n        \"conversions\": 298,\n        \"revenue\": 38967.11,\n        \"roas\": 6.8\n      },\n      \"low\": {\n        \"keywords\": 10,\n        \"impressions\": 7970,\n        \"clicks\": 544,\n        \"conversions\": 66,\n        \"revenue\": 8000.00,\n        \"roas\": 4.2\n      }\n    }\n  },\n  \"trends\": {\n    \"daily\": [\n      {\n        \"date\": \"2024-01-01\",\n        \"impressions\": 4234,\n        \"clicks\": 321,\n        \"conversions\": 38,\n        \"revenue\": 4956.78\n      },\n      {\n        \"date\": \"2024-01-02\",\n        \"impressions\": 4567,\n        \"clicks\": 345,\n        \"conversions\": 42,\n        \"revenue\": 5467.89\n      }\n    ],\n    \"weekly\": [\n      {\n        \"week\": \"2024-W01\",\n        \"impressions\": 32450,\n        \"clicks\": 2456,\n        \"conversions\": 289,\n        \"revenue\": 37654.33\n      },\n      {\n        \"week\": \"2024-W02\",\n        \"impressions\": 35670,\n        \"clicks\": 2789,\n        \"conversions\": 324,\n        \"revenue\": 42189.56\n      }\n    ]\n  },\n  \"insights\": [\n    {\n      \"type\": \"growth\",\n      \"metric\": \"conversions\",\n      \"change\": \"+23%\",\n      \"period\": \"week-over-week\",\n      \"significance\": \"high\"\n    },\n    {\n      \"type\": \"efficiency\",\n      \"metric\": \"roas\",\n      \"change\": \"+15%\",\n      \"period\": \"month-over-month\",\n      \"significance\": \"medium\"\n    },\n    {\n      \"type\": \"opportunity\",\n      \"metric\": \"long-tail-keywords\",\n      \"message\": \"35% higher conversion rate potential\",\n      \"recommendation\": \"Expand long-tail keyword strategy\"\n    }\n  ],\n  \"projections\": {\n    \"nextMonth\": {\n      \"expectedImpressions\": 158700,\n      \"expectedClicks\": 12100,\n      \"expectedConversions\": 1398,\n      \"expectedRevenue\": 182456.78,\n      \"confidence\": 0.85\n    }\n  },\n  \"generatedAt\": \"2024-01-20T20:30:00.000Z\"\n}"}]}], "description": "Account-level keyword analytics and management"}, {"name": "Keyword Research & Optimization", "item": [{"name": "Keyword Research Suggestions", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Keyword suggestions returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('suggestions');", "    pm.expect(jsonData.suggestions).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"seed\": {\n    \"keywords\": [\"athletic shoes\", \"running\"],\n    \"productId\": \"{{productId}}\",\n    \"brandId\": \"{{brandId}}\",\n    \"category\": \"Footwear\",\n    \"targetAudience\": \"athletes\"\n  },\n  \"parameters\": {\n    \"minSearchVolume\": 1000,\n    \"maxCompetition\": \"medium\",\n    \"includeRelated\": true,\n    \"includeLongTail\": true,\n    \"includeBrandVariations\": true,\n    \"includeSeasonalTrends\": true,\n    \"maxSuggestions\": 50\n  },\n  \"filters\": {\n    \"excludeExisting\": true,\n    \"excludeCompetitors\": false,\n    \"language\": \"en\",\n    \"country\": \"US\",\n    \"deviceType\": \"all\"\n  },\n  \"analysis\": {\n    \"includeIntent\": true,\n    \"includeDifficulty\": true,\n    \"includeOpportunityScore\": true,\n    \"includeTrendData\": true\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/keywords/research/suggestions", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "research", "suggestions"]}, "description": "Get AI-powered keyword suggestions based on seed keywords and product data."}, "response": [{"name": "Keyword Research Suggestions", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"seed\": {\n    \"keywords\": [\"athletic shoes\"],\n    \"productId\": \"prod_123\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/keywords/research/suggestions", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "research", "suggestions"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"suggestions\": [\n    {\n      \"keyword\": \"best athletic shoes\",\n      \"type\": \"informational\",\n      \"searchVolume\": 18500,\n      \"competition\": \"medium\",\n      \"cpc\": 1.85,\n      \"difficulty\": 65,\n      \"opportunityScore\": 0.82,\n      \"intent\": \"research\",\n      \"relevanceScore\": 0.91,\n      \"trend\": \"stable\",\n      \"seasonality\": {\n        \"peak\": \"January\",\n        \"low\": \"December\"\n      }\n    },\n    {\n      \"keyword\": \"athletic shoes for women\",\n      \"type\": \"commercial\",\n      \"searchVolume\": 24000,\n      \"competition\": \"high\",\n      \"cpc\": 2.45,\n      \"difficulty\": 72,\n      \"opportunityScore\": 0.78,\n      \"intent\": \"commercial\",\n      \"relevanceScore\": 0.88,\n      \"trend\": \"growing\",\n      \"seasonality\": {\n        \"peak\": \"March\",\n        \"low\": \"November\"\n      }\n    },\n    {\n      \"keyword\": \"lightweight athletic shoes\",\n      \"type\": \"commercial\",\n      \"searchVolume\": 12300,\n      \"competition\": \"low\",\n      \"cpc\": 1.25,\n      \"difficulty\": 45,\n      \"opportunityScore\": 0.89,\n      \"intent\": \"commercial\",\n      \"relevanceScore\": 0.85,\n      \"trend\": \"growing\",\n      \"seasonality\": {\n        \"peak\": \"April\",\n        \"low\": \"October\"\n      }\n    },\n    {\n      \"keyword\": \"affordable athletic shoes under 100\",\n      \"type\": \"transactional\",\n      \"searchVolume\": 8700,\n      \"competition\": \"medium\",\n      \"cpc\": 1.95,\n      \"difficulty\": 58,\n      \"opportunityScore\": 0.76,\n      \"intent\": \"transactional\",\n      \"relevanceScore\": 0.79,\n      \"trend\": \"stable\",\n      \"seasonality\": {\n        \"peak\": \"May\",\n        \"low\": \"February\"\n      }\n    },\n    {\n      \"keyword\": \"nike athletic shoes review\",\n      \"type\": \"informational\",\n      \"searchVolume\": 6500,\n      \"competition\": \"low\",\n      \"cpc\": 0.85,\n      \"difficulty\": 35,\n      \"opportunityScore\": 0.84,\n      \"intent\": \"research\",\n      \"relevanceScore\": 0.92,\n      \"trend\": \"stable\",\n      \"seasonality\": {\n        \"peak\": \"September\",\n        \"low\": \"July\"\n      }\n    }\n  ],\n  \"categorizedSuggestions\": {\n    \"highOpportunity\": [\n      \"lightweight athletic shoes\",\n      \"nike athletic shoes review\"\n    ],\n    \"lowCompetition\": [\n      \"lightweight athletic shoes\",\n      \"nike athletic shoes review\"\n    ],\n    \"highVolume\": [\n      \"athletic shoes for women\",\n      \"best athletic shoes\"\n    ],\n    \"longTail\": [\n      \"affordable athletic shoes under 100\",\n      \"nike athletic shoes review\"\n    ],\n    \"brandSpecific\": [\n      \"nike athletic shoes review\"\n    ]\n  },\n  \"analysis\": {\n    \"totalSuggestions\": 5,\n    \"avgSearchVolume\": 14000,\n    \"avgCompetition\": \"medium\",\n    \"avgOpportunityScore\": 0.82,\n    \"bestOpportunities\": [\n      {\n        \"keyword\": \"lightweight athletic shoes\",\n        \"reason\": \"High opportunity score with low competition\"\n      },\n      {\n        \"keyword\": \"nike athletic shoes review\",\n        \"reason\": \"Brand-specific with high relevance and low difficulty\"\n      }\n    ],\n    \"recommendations\": [\n      {\n        \"action\": \"prioritize\",\n        \"keywords\": [\"lightweight athletic shoes\"],\n        \"reason\": \"Best balance of opportunity and difficulty\"\n      },\n      {\n        \"action\": \"research\",\n        \"keywords\": [\"athletic shoes for women\"],\n        \"reason\": \"High volume but analyze competition strategy first\"\n      }\n    ]\n  },\n  \"metadata\": {\n    \"researchDate\": \"2024-01-20T21:00:00.000Z\",\n    \"dataSource\": \"keyword-research-api\",\n    \"algorithm\": \"semantic-analysis-v2\",\n    \"confidence\": 0.87\n  }\n}"}]}, {"name": "Optimize Keyword Strategy", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Optimization recommendations returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('recommendations');", "    pm.expect(jsonData.recommendations).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"analysisType\": \"comprehensive\",\n  \"goals\": {\n    \"targetMetric\": \"conversions\",\n    \"budgetConstraint\": 5000,\n    \"timeframe\": \"monthly\",\n    \"riskTolerance\": \"medium\"\n  },\n  \"currentPerformance\": {\n    \"includeAnalytics\": true,\n    \"dateRange\": {\n      \"start\": \"2024-01-01\",\n      \"end\": \"2024-01-20\"\n    }\n  },\n  \"optimizationAreas\": [\n    \"keyword-selection\",\n    \"bid-optimization\",\n    \"negative-keywords\",\n    \"long-tail-expansion\",\n    \"competitor-analysis\",\n    \"seasonal-adjustments\"\n  ],\n  \"constraints\": {\n    \"brandFocus\": true,\n    \"excludeCompetitorBrands\": true,\n    \"maintainBrandSafety\": true\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/keywords/optimize/strategy", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "optimize", "strategy"]}, "description": "Get AI-powered keyword strategy optimization recommendations."}, "response": [{"name": "Keyword Strategy Optimization", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"acc_123\",\n  \"goals\": {\n    \"targetMetric\": \"conversions\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/keywords/optimize/strategy", "host": ["{{baseUrl}}"], "path": ["api", "keywords", "optimize", "strategy"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"recommendations\": [\n    {\n      \"id\": \"rec_001\",\n      \"type\": \"keyword-addition\",\n      \"priority\": \"high\",\n      \"impact\": \"high\",\n      \"effort\": \"low\",\n      \"title\": \"Add High-Converting Long-Tail Keywords\",\n      \"description\": \"Analysis shows long-tail keywords have 35% higher conversion rates with 60% lower CPC\",\n      \"action\": {\n        \"type\": \"add-keywords\",\n        \"keywords\": [\n          {\n            \"keyword\": \"comfortable running shoes for flat feet\",\n            \"estimatedImpact\": {\n              \"conversions\": \"+15/month\",\n              \"revenue\": \"+$1,950/month\",\n              \"roas\": \"8.2x\"\n            }\n          },\n          {\n            \"keyword\": \"best athletic shoes for gym workouts\",\n            \"estimatedImpact\": {\n              \"conversions\": \"+12/month\",\n              \"revenue\": \"+$1,560/month\",\n              \"roas\": \"7.8x\"\n            }\n          }\n        ]\n      },\n      \"timeline\": \"immediate\",\n      \"confidence\": 0.89\n    },\n    {\n      \"id\": \"rec_002\",\n      \"type\": \"bid-optimization\",\n      \"priority\": \"high\",\n      \"impact\": \"medium\",\n      \"effort\": \"low\",\n      \"title\": \"Optimize Bids for Top Performers\",\n      \"description\": \"3 keywords showing strong performance but limited by low bids\",\n      \"action\": {\n        \"type\": \"adjust-bids\",\n        \"adjustments\": [\n          {\n            \"keyword\": \"nike air zoom\",\n            \"currentBid\": 0.85,\n            \"recommendedBid\": 1.25,\n            \"reason\": \"High conversion rate (15.8%) with impression share below 60%\",\n            \"estimatedImpact\": {\n              \"impressions\": \"+40%\",\n              \"conversions\": \"+8/month\",\n              \"revenue\": \"+$1,040/month\"\n            }\n          }\n        ]\n      },\n      \"timeline\": \"immediate\",\n      \"confidence\": 0.92\n    },\n    {\n      \"id\": \"rec_003\",\n      \"type\": \"negative-keywords\",\n      \"priority\": \"medium\",\n      \"impact\": \"medium\",\n      \"effort\": \"low\",\n      \"title\": \"Add Negative Keywords to Reduce Waste\",\n      \"description\": \"Identified irrelevant search terms consuming 12% of budget\",\n      \"action\": {\n        \"type\": \"add-negative-keywords\",\n        \"negativeKeywords\": [\n          \"free\",\n          \"cheap\",\n          \"fake\",\n          \"replica\",\n          \"used\",\n          \"kids\",\n          \"toddler\"\n        ],\n        \"estimatedSavings\": {\n          \"monthlySpend\": \"-$890\",\n          \"wastefulClicks\": \"-156/month\"\n        }\n      },\n      \"timeline\": \"immediate\",\n      \"confidence\": 0.85\n    },\n    {\n      \"id\": \"rec_004\",\n      \"type\": \"seasonal-optimization\",\n      \"priority\": \"medium\",\n      \"impact\": \"high\",\n      \"effort\": \"medium\",\n      \"title\": \"Prepare for Spring Running Season\",\n      \"description\": \"Historical data shows 45% increase in athletic shoe searches in March-May\",\n      \"action\": {\n        \"type\": \"seasonal-strategy\",\n        \"timeline\": \"February 15 - May 31\",\n        \"adjustments\": [\n          {\n            \"period\": \"March\",\n            \"budgetIncrease\": \"+30%\",\n            \"focusKeywords\": [\"spring running shoes\", \"outdoor athletic shoes\"]\n          },\n          {\n            \"period\": \"April-May\",\n            \"budgetIncrease\": \"+45%\",\n            \"focusKeywords\": [\"marathon training shoes\", \"trail running shoes\"]\n          }\n        ],\n        \"estimatedImpact\": {\n          \"conversions\": \"+67/season\",\n          \"revenue\": \"+$8,710/season\"\n        }\n      },\n      \"timeline\": \"plan-for-february\",\n      \"confidence\": 0.78\n    },\n    {\n      \"id\": \"rec_005\",\n      \"type\": \"competitor-analysis\",\n      \"priority\": \"low\",\n      \"impact\": \"medium\",\n      \"effort\": \"high\",\n      \"title\": \"Capitalize on Competitor Keyword Gaps\",\n      \"description\": \"Found 12 high-value keywords where competitors have reduced presence\",\n      \"action\": {\n        \"type\": \"opportunity-keywords\",\n        \"keywords\": [\n          {\n            \"keyword\": \"eco-friendly athletic shoes\",\n            \"competitorGap\": \"Adidas reduced bids by 40%\",\n            \"opportunity\": \"Low competition window for 3-6 months\"\n          },\n          {\n            \"keyword\": \"vegan running shoes\",\n            \"competitorGap\": \"Major competitors not targeting\",\n            \"opportunity\": \"Emerging niche with growing search volume\"\n          }\n        ]\n      },\n      \"timeline\": \"1-2-weeks\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"summary\": {\n    \"totalRecommendations\": 5,\n    \"highPriority\": 2,\n    \"estimatedImpact\": {\n      \"additionalConversions\": \"+102/month\",\n      \"additionalRevenue\": \"+$13,260/month\",\n      \"budgetSavings\": \"-$890/month\",\n      \"netRoasImprovement\": \"+18%\"\n    },\n    \"implementationEffort\": \"medium\",\n    \"timeToSeeResults\": \"2-4 weeks\"\n  },\n  \"nextSteps\": [\n    {\n      \"step\": 1,\n      \"action\": \"Implement high-priority recommendations (rec_001, rec_002, rec_003)\",\n      \"timeline\": \"Week 1\"\n    },\n    {\n      \"step\": 2,\n      \"action\": \"Monitor performance and adjust bids based on initial results\",\n      \"timeline\": \"Week 2-3\"\n    },\n    {\n      \"step\": 3,\n      \"action\": \"Prepare seasonal strategy implementation\",\n      \"timeline\": \"Week 4\"\n    },\n    {\n      \"step\": 4,\n      \"action\": \"Research and implement competitor gap opportunities\",\n      \"timeline\": \"Month 2\"\n    }\n  ],\n  \"generatedAt\": \"2024-01-20T21:30:00.000Z\",\n  \"validUntil\": \"2024-02-20T21:30:00.000Z\"\n}"}]}], "description": "AI-powered keyword research and optimization tools"}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "currentUserId", "value": "", "type": "string"}, {"key": "accountId", "value": "", "type": "string"}, {"key": "brandId", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}, {"key": "keywordId", "value": "", "type": "string"}]}