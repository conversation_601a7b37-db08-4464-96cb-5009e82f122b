{"info": {"_postman_id": "email-service-collection", "name": "03. Email Service", "description": "Email service endpoints for sending various types of emails including invitations, notifications, and custom emails.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "Send Invitation Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"<EMAIL>\",\n  \"inviterName\": \"<PERSON>\",\n  \"accountName\": \"Acme Corporation\",\n  \"invitationLink\": \"https://app.adfury.com/invite/abc123\",\n  \"role\": \"member\",\n  \"personalMessage\": \"Looking forward to working with you!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/email/send-invitation", "host": ["{{baseUrl}}"], "path": ["api", "email", "send-invitation"]}, "description": "Send an invitation email to a new user.\n\n**Request Body:**\n- `to`: Recipient email address\n- `inviterName`: Name of the person sending the invitation\n- `accountName`: Name of the account/organization\n- `invitationLink`: Unique invitation URL\n- `role`: Role being offered\n- `personalMessage`: Optional custom message"}, "response": [{"name": "<PERSON><PERSON> Successfully", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"<EMAIL>\",\n  \"inviterName\": \"<PERSON>\",\n  \"accountName\": \"Acme Corporation\",\n  \"invitationLink\": \"https://app.adfury.com/invite/abc123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/email/send-invitation", "host": ["{{baseUrl}}"], "path": ["api", "email", "send-invitation"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"message\": \"Invitation email sent successfully\",\n  \"emailId\": \"msg_2XtY8z4bVQ3eLKNHLbKgHf\"\n}"}]}, {"name": "Send Password Reset Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"<EMAIL>\",\n  \"resetLink\": \"https://app.adfury.com/reset-password?token=xyz789\",\n  \"userName\": \"<PERSON>\",\n  \"expiryTime\": \"24 hours\"\n}"}, "url": {"raw": "{{baseUrl}}/api/email/send-password-reset", "host": ["{{baseUrl}}"], "path": ["api", "email", "send-password-reset"]}, "description": "Send a password reset email.\n\n**Request Body:**\n- `to`: User's email address\n- `resetLink`: Password reset URL with token\n- `userName`: User's name for personalization\n- `expiryTime`: How long the link is valid"}, "response": [{"name": "Password Reset Email <PERSON>", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"<EMAIL>\",\n  \"resetLink\": \"https://app.adfury.com/reset-password?token=xyz789\",\n  \"userName\": \"<PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/email/send-password-reset", "host": ["{{baseUrl}}"], "path": ["api", "email", "send-password-reset"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"message\": \"Password reset email sent successfully\",\n  \"emailId\": \"msg_3YuZ9a5cWR4fMLOIMcLhIg\"\n}"}]}, {"name": "Send Welcome Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"<EMAIL>\",\n  \"userName\": \"<PERSON>\",\n  \"accountName\": \"Acme Corporation\",\n  \"loginUrl\": \"https://app.adfury.com/login\",\n  \"gettingStartedUrl\": \"https://app.adfury.com/getting-started\"\n}"}, "url": {"raw": "{{baseUrl}}/api/email/send-welcome", "host": ["{{baseUrl}}"], "path": ["api", "email", "send-welcome"]}, "description": "Send a welcome email to new users after signup.\n\n**Request Body:**\n- `to`: New user's email\n- `userName`: User's name\n- `accountName`: Account they joined\n- `loginUrl`: URL to login page\n- `gettingStartedUrl`: URL to getting started guide"}}, {"name": "Send Notification Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": [\"<EMAIL>\", \"<EMAIL>\"],\n  \"subject\": \"New Campaign Ready for Review\",\n  \"type\": \"campaign_ready\",\n  \"data\": {\n    \"campaignName\": \"Summer Sale 2024\",\n    \"campaignId\": \"camp_123\",\n    \"reviewUrl\": \"https://app.adfury.com/campaigns/camp_123/review\",\n    \"deadline\": \"2024-02-01T17:00:00Z\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/email/send-notification", "host": ["{{baseUrl}}"], "path": ["api", "email", "send-notification"]}, "description": "Send notification emails for various events.\n\n**Request Body:**\n- `to`: Single email or array of emails\n- `subject`: Email subject line\n- `type`: Notification type (campaign_ready, comment_added, etc.)\n- `data`: Type-specific data for the email template"}}, {"name": "Send Custom Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"<EMAIL>\",\n  \"subject\": \"Custom Email Subject\",\n  \"html\": \"<h1>Hello {{name}}</h1><p>This is a custom email.</p>\",\n  \"text\": \"Hello {{name}}, This is a custom email.\",\n  \"templateData\": {\n    \"name\": \"<PERSON>\"\n  },\n  \"attachments\": [\n    {\n      \"filename\": \"report.pdf\",\n      \"content\": \"base64_encoded_content\",\n      \"contentType\": \"application/pdf\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/email/send-custom", "host": ["{{baseUrl}}"], "path": ["api", "email", "send-custom"]}, "description": "Send a custom email with HTML/text content.\n\n**Request Body:**\n- `to`: Recipient email(s)\n- `subject`: Email subject\n- `html`: HTML content (can include template variables)\n- `text`: Plain text fallback\n- `templateData`: Variables to replace in content\n- `attachments`: Optional file attachments"}}, {"name": "Get Email Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/email/status/{{emailId}}", "host": ["{{baseUrl}}"], "path": ["api", "email", "status", "{{emailId}}"]}, "description": "Check the delivery status of a sent email.\n\n**Path Parameters:**\n- `emailId`: The email ID returned when sending\n\n**Response includes:**\n- Delivery status\n- Open/click tracking\n- Bounce information"}, "response": [{"name": "Email Status", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/email/status/msg_2XtY8z4bVQ3eLKNHLbKgHf", "host": ["{{baseUrl}}"], "path": ["api", "email", "status", "msg_2XtY8z4bVQ3eLKNHLbKgHf"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"emailId\": \"msg_2XtY8z4bVQ3eLKNHLbKgHf\",\n  \"status\": \"delivered\",\n  \"to\": \"<EMAIL>\",\n  \"subject\": \"Welcome to AdFury\",\n  \"sentAt\": \"2024-01-20T10:00:00Z\",\n  \"deliveredAt\": \"2024-01-20T10:00:15Z\",\n  \"openedAt\": \"2024-01-20T10:05:00Z\",\n  \"clicks\": [\n    {\n      \"url\": \"https://app.adfury.com/login\",\n      \"clickedAt\": \"2024-01-20T10:05:30Z\"\n    }\n  ]\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005"}, {"key": "authToken", "value": ""}, {"key": "emailId", "value": "msg_2XtY8z4bVQ3eLKNHLbKgHf"}]}