{"info": {"_postman_id": "notifications-management-collection", "name": "12. Notifications Management", "description": "Complete notification system management including creating, reading, updating notification status, and user-specific notifications.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "adfury-api"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "Notification Operations", "item": [{"name": "Mark Notifications as Read", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notificationIds\": [\"{{notificationId}}\"],\n  \"markAll\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/notifications/mark-as-read", "host": ["{{baseUrl}}"], "path": ["api", "notifications", "mark-as-read"]}}}, {"name": "List All Notifications", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/notifications?limit=20&offset=0&status=unread", "host": ["{{baseUrl}}"], "path": ["api", "notifications"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}, {"key": "status", "value": "unread"}]}}}, {"name": "Get Notification by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/notifications/{{notificationId}}", "host": ["{{baseUrl}}"], "path": ["api", "notifications", "{{notificationId}}"]}}}, {"name": "Create Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"project_update\",\n  \"title\": \"Project Milestone Completed\",\n  \"message\": \"Your Q1 Athletic Shoes Campaign has reached the concept approval stage\",\n  \"recipientId\": \"{{currentUserId}}\",\n  \"priority\": \"medium\",\n  \"actionUrl\": \"/projects/{{projectId}}\",\n  \"metadata\": {\n    \"projectId\": \"{{projectId}}\",\n    \"milestone\": \"concept_approval\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/notifications", "host": ["{{baseUrl}}"], "path": ["api", "notifications"]}}}, {"name": "Update Notification Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"read\"\n}"}, "url": {"raw": "{{baseUrl}}/api/notifications/{{notificationId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "notifications", "{{notificationId}}", "status"]}}}, {"name": "Delete Notification", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/notifications/{{notificationId}}", "host": ["{{baseUrl}}"], "path": ["api", "notifications", "{{notificationId}}"]}}}, {"name": "Get User Notifications", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/notifications/user/{{currentUserId}}?limit=50&includeRead=false", "host": ["{{baseUrl}}"], "path": ["api", "notifications", "user", "{{currentUserId}}"], "query": [{"key": "limit", "value": "50"}, {"key": "includeRead", "value": "false"}]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "currentUserId", "value": "", "type": "string"}, {"key": "notificationId", "value": "", "type": "string"}, {"key": "projectId", "value": "", "type": "string"}]}