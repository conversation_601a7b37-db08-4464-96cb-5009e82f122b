{"info": {"_postman_id": "retailers-management-collection", "name": "06. Retailers Management", "description": "Manage retailers and retail platforms including Amazon, Walmart, Target, and others.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "Retailer Operations", "item": [{"name": "List Retailers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/retailers?status=active", "host": ["{{baseUrl}}"], "path": ["api", "retailers"], "query": [{"key": "status", "value": "active", "description": "Filter by status (active, inactive)"}, {"key": "category", "value": "marketplace", "disabled": true, "description": "Filter by category (marketplace, direct, social)"}]}, "description": "List all available retailers in the system."}, "response": [{"name": "Retailers List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/retailers", "host": ["{{baseUrl}}"], "path": ["api", "retailers"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"ret_amazon\",\n    \"name\": \"Amazon\",\n    \"slug\": \"amazon\",\n    \"logoUrl\": \"https://example.com/amazon-logo.png\",\n    \"category\": \"marketplace\",\n    \"status\": \"active\",\n    \"adFormats\": [\"sponsored_products\", \"sponsored_brands\", \"display\"],\n    \"requiredCredentials\": [\"api_key\", \"secret_key\", \"seller_id\"],\n    \"regions\": [\"US\", \"UK\", \"DE\", \"FR\", \"IT\", \"ES\", \"JP\"]\n  },\n  {\n    \"id\": \"ret_walmart\",\n    \"name\": \"Walmart\",\n    \"slug\": \"walmart\",\n    \"logoUrl\": \"https://example.com/walmart-logo.png\",\n    \"category\": \"marketplace\",\n    \"status\": \"active\",\n    \"adFormats\": [\"sponsored_products\", \"display\"],\n    \"requiredCredentials\": [\"client_id\", \"client_secret\"],\n    \"regions\": [\"US\", \"CA\"]\n  }\n]"}]}, {"name": "Get Retailer Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/retailers/{{retailerId}}", "host": ["{{baseUrl}}"], "path": ["api", "retailers", "{{retailerId}}"]}, "description": "Get detailed information about a specific retailer."}, "response": [{"name": "Retailer Details", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/retailers/ret_amazon", "host": ["{{baseUrl}}"], "path": ["api", "retailers", "ret_amazon"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"ret_amazon\",\n  \"name\": \"Amazon\",\n  \"slug\": \"amazon\",\n  \"logoUrl\": \"https://example.com/amazon-logo.png\",\n  \"category\": \"marketplace\",\n  \"status\": \"active\",\n  \"description\": \"World's largest online marketplace\",\n  \"adFormats\": [\n    {\n      \"id\": \"sponsored_products\",\n      \"name\": \"Sponsored Products\",\n      \"description\": \"Promote individual product listings\",\n      \"minBudget\": 1,\n      \"bidTypes\": [\"manual\", \"auto\"]\n    },\n    {\n      \"id\": \"sponsored_brands\",\n      \"name\": \"Sponsored Brands\",\n      \"description\": \"Showcase your brand and product portfolio\",\n      \"minBudget\": 100,\n      \"requirements\": [\"brand_registry\"]\n    }\n  ],\n  \"requiredCredentials\": [\n    {\n      \"key\": \"api_key\",\n      \"label\": \"API Key\",\n      \"type\": \"string\",\n      \"required\": true\n    },\n    {\n      \"key\": \"secret_key\",\n      \"label\": \"Secret Key\",\n      \"type\": \"password\",\n      \"required\": true\n    },\n    {\n      \"key\": \"seller_id\",\n      \"label\": \"Seller ID\",\n      \"type\": \"string\",\n      \"required\": true\n    }\n  ],\n  \"regions\": [\n    {\n      \"code\": \"US\",\n      \"name\": \"United States\",\n      \"endpoint\": \"https://advertising-api.amazon.com\"\n    },\n    {\n      \"code\": \"UK\",\n      \"name\": \"United Kingdom\",\n      \"endpoint\": \"https://advertising-api-eu.amazon.com\"\n    }\n  ],\n  \"features\": {\n    \"bulkOperations\": true,\n    \"reporting\": true,\n    \"automation\": true,\n    \"dayparting\": false\n  }\n}"}]}]}, {"name": "Retailer Credentials", "item": [{"name": "List Account Retailer Credentials", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/retailers/credentials?accountId={{accountId}}", "host": ["{{baseUrl}}"], "path": ["api", "retailers", "credentials"], "query": [{"key": "accountId", "value": "{{accountId}}", "description": "Filter credentials by account"}]}, "description": "List all retailer credentials for an account."}, "response": [{"name": "Credentials List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/retailers/credentials?accountId=acc_123", "host": ["{{baseUrl}}"], "path": ["api", "retailers", "credentials"], "query": [{"key": "accountId", "value": "acc_123"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"cred_123\",\n    \"accountId\": \"acc_123\",\n    \"retailerId\": \"ret_amazon\",\n    \"retailerName\": \"Amazon\",\n    \"name\": \"Amazon US Production\",\n    \"region\": \"US\",\n    \"status\": \"active\",\n    \"lastVerified\": \"2024-01-20T10:00:00Z\",\n    \"createdAt\": \"2024-01-01T00:00:00Z\"\n  },\n  {\n    \"id\": \"cred_456\",\n    \"accountId\": \"acc_123\",\n    \"retailerId\": \"ret_walmart\",\n    \"retailerName\": \"Walmart\",\n    \"name\": \"Walmart Main Account\",\n    \"region\": \"US\",\n    \"status\": \"active\",\n    \"lastVerified\": \"2024-01-19T15:00:00Z\",\n    \"createdAt\": \"2024-01-05T00:00:00Z\"\n  }\n]"}]}, {"name": "Add Retailer Credentials", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"retailerId\": \"ret_amazon\",\n  \"name\": \"Amazon UK Account\",\n  \"region\": \"UK\",\n  \"credentials\": {\n    \"api_key\": \"your-api-key\",\n    \"secret_key\": \"your-secret-key\",\n    \"seller_id\": \"your-seller-id\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/retailers/credentials", "host": ["{{baseUrl}}"], "path": ["api", "retailers", "credentials"]}, "description": "Add new retailer credentials for an account."}}, {"name": "Update Retailer Credentials", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Credential Name\",\n  \"credentials\": {\n    \"api_key\": \"new-api-key\",\n    \"secret_key\": \"new-secret-key\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/retailers/credentials/{{credentialId}}", "host": ["{{baseUrl}}"], "path": ["api", "retailers", "credentials", "{{credentialId}}"]}, "description": "Update existing retailer credentials."}}, {"name": "Test Retailer Credentials", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/retailers/credentials/{{credentialId}}/test", "host": ["{{baseUrl}}"], "path": ["api", "retailers", "credentials", "{{credentialId}}", "test"]}, "description": "Test if retailer credentials are valid and working."}, "response": [{"name": "Credentials Valid", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/retailers/credentials/cred_123/test", "host": ["{{baseUrl}}"], "path": ["api", "retailers", "credentials", "cred_123", "test"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"valid\": true,\n  \"message\": \"Credentials verified successfully\",\n  \"accountInfo\": {\n    \"accountName\": \"My Seller Account\",\n    \"accountId\": \"A1B2C3D4E5F6\",\n    \"marketplaces\": [\"US\", \"CA\", \"MX\"]\n  }\n}"}]}, {"name": "Delete Retailer Credentials", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/retailers/credentials/{{credentialId}}", "host": ["{{baseUrl}}"], "path": ["api", "retailers", "credentials", "{{credentialId}}"]}, "description": "Delete retailer credentials."}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005"}, {"key": "authToken", "value": ""}, {"key": "accountId", "value": "acc_123"}, {"key": "retailerId", "value": "ret_amazon"}, {"key": "credentialId", "value": "cred_123"}]}