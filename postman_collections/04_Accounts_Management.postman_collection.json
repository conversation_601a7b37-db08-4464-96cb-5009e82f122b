{"info": {"_postman_id": "accounts-management-collection", "name": "04. Accounts Management", "description": "Manage accounts (organizations/agencies) including creation, updates, user management, and billing.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "Account Operations", "item": [{"name": "List Accounts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts?type=agency&status=active", "host": ["{{baseUrl}}"], "path": ["api", "accounts"], "query": [{"key": "type", "value": "agency", "description": "Filter by account type (agency, brand)"}, {"key": "status", "value": "active", "description": "Filter by status (active, suspended, trial)"}, {"key": "limit", "value": "20", "disabled": true}, {"key": "offset", "value": "0", "disabled": true}]}, "description": "List all accounts the user has access to."}, "response": [{"name": "Accounts List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts", "host": ["{{baseUrl}}"], "path": ["api", "accounts"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"acc_123\",\n    \"name\": \"Acme Corporation\",\n    \"type\": \"agency\",\n    \"status\": \"active\",\n    \"logoUrl\": \"https://example.com/logo.png\",\n    \"ownerId\": \"user_123\",\n    \"seats\": 10,\n    \"usedSeats\": 7,\n    \"plan\": \"professional\",\n    \"billingEmail\": \"<EMAIL>\",\n    \"createdAt\": \"2024-01-01T00:00:00Z\",\n    \"userRole\": \"admin\"\n  }\n]"}]}, {"name": "Get Account Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}"]}, "description": "Get detailed information about a specific account."}, "response": [{"name": "Account Details", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts/acc_123", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "acc_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"acc_123\",\n  \"name\": \"Acme Corporation\",\n  \"type\": \"agency\",\n  \"status\": \"active\",\n  \"logoUrl\": \"https://example.com/logo.png\",\n  \"website\": \"https://acme.com\",\n  \"industry\": \"Technology\",\n  \"size\": \"50-100\",\n  \"ownerId\": \"user_123\",\n  \"seats\": 10,\n  \"usedSeats\": 7,\n  \"plan\": \"professional\",\n  \"billingEmail\": \"<EMAIL>\",\n  \"address\": {\n    \"street\": \"123 Main St\",\n    \"city\": \"San Francisco\",\n    \"state\": \"CA\",\n    \"zip\": \"94105\",\n    \"country\": \"USA\"\n  },\n  \"settings\": {\n    \"requireTwoFactor\": true,\n    \"allowGuestAccess\": false,\n    \"dataRetentionDays\": 90\n  },\n  \"createdAt\": \"2024-01-01T00:00:00Z\",\n  \"updatedAt\": \"2024-01-15T00:00:00Z\"\n}"}]}, {"name": "Create Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Agency\",\n  \"type\": \"agency\",\n  \"website\": \"https://newagency.com\",\n  \"industry\": \"Marketing\",\n  \"size\": \"10-50\",\n  \"billingEmail\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/accounts", "host": ["{{baseUrl}}"], "path": ["api", "accounts"]}, "description": "Create a new account. User becomes the owner."}}, {"name": "Update Account", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Agency Name\",\n  \"website\": \"https://updated-agency.com\",\n  \"logoUrl\": \"https://example.com/new-logo.png\",\n  \"billingEmail\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}"]}, "description": "Update account information. Requires admin role."}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}"]}, "description": "Delete an account. Requires owner role. This will delete all associated data."}}]}, {"name": "Account Users", "item": [{"name": "List Account Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}/users", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}", "users"]}, "description": "List all users in an account with their roles."}, "response": [{"name": "Account Users", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts/acc_123/users", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "acc_123", "users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"user_123\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON>\",\n    \"role\": \"admin\",\n    \"status\": \"active\",\n    \"joinedAt\": \"2024-01-01T00:00:00Z\",\n    \"lastActiveAt\": \"2024-01-20T10:00:00Z\"\n  },\n  {\n    \"id\": \"user_456\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON>\",\n    \"role\": \"member\",\n    \"status\": \"active\",\n    \"joinedAt\": \"2024-01-05T00:00:00Z\",\n    \"lastActiveAt\": \"2024-01-20T09:00:00Z\"\n  }\n]"}]}, {"name": "Add User to Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"user_789\",\n  \"role\": \"member\"\n}"}, "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}/users", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}", "users"]}, "description": "Add an existing user to the account."}}, {"name": "Update User Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"role\": \"admin\"\n}"}, "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}", "users", "{{userId}}"]}, "description": "Update a user's role in the account."}}, {"name": "Remove User from Account", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}", "users", "{{userId}}"]}, "description": "Remove a user from the account."}}]}, {"name": "Account <PERSON><PERSON>", "item": [{"name": "Get Account <PERSON><PERSON>s", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}/settings", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}", "settings"]}, "description": "Get account-wide settings and preferences."}}, {"name": "Update Account <PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requireTwoFactor\": true,\n  \"allowGuestAccess\": false,\n  \"dataRetentionDays\": 90,\n  \"defaultUserRole\": \"member\",\n  \"features\": {\n    \"advancedAnalytics\": true,\n    \"customBranding\": true\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}/settings", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}", "settings"]}, "description": "Update account settings. Requires admin role."}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005"}, {"key": "authToken", "value": ""}, {"key": "accountId", "value": "acc_123"}, {"key": "userId", "value": "user_456"}]}