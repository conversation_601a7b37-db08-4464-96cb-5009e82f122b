{"info": {"_postman_id": "asset-management-collection", "name": "09. Asset Management", "description": "Complete asset management including file uploads, asset organization, metadata management, and asset workflow operations.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "adfury-api"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Ensure auth token is available", "if (!pm.collectionVariables.get('authToken')) {", "    console.warn('No auth token found. Please run Authentication collection first.');", "}"], "type": "text/javascript"}}], "item": [{"name": "Asset Operations", "item": [{"name": "List All Assets", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array of assets', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    if (jsonData.length > 0) {", "        pm.expect(jsonData[0]).to.have.property('id');", "        pm.expect(jsonData[0]).to.have.property('filename');", "        pm.expect(jsonData[0]).to.have.property('url');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assets?limit=50&offset=0&bucket={{assetBucket}}&type=image", "host": ["{{baseUrl}}"], "path": ["api", "assets"], "query": [{"key": "limit", "value": "50", "description": "Number of assets to return"}, {"key": "offset", "value": "0", "description": "Number of assets to skip"}, {"key": "bucket", "value": "{{assetBucket}}", "description": "Filter by asset bucket (e.g., products, creatives, brands)"}, {"key": "type", "value": "image", "description": "Filter by asset type (image, video, document, audio)"}, {"key": "brandId", "value": "{{brandId}}", "disabled": true, "description": "Filter by brand"}, {"key": "status", "value": "active", "disabled": true, "description": "Filter by asset status"}, {"key": "search", "value": "", "disabled": true, "description": "Search assets by filename or tags"}]}, "description": "Get all assets with filtering and pagination capabilities."}, "response": [{"name": "Assets List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assets", "host": ["{{baseUrl}}"], "path": ["api", "assets"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"asset_123\",\n    \"filename\": \"athletic-shoes-hero.jpg\",\n    \"originalFilename\": \"nike-air-zoom-pegasus-main.jpg\",\n    \"url\": \"https://storage.example.com/assets/products/asset_123.jpg\",\n    \"thumbnailUrl\": \"https://storage.example.com/assets/products/thumbs/asset_123_thumb.jpg\",\n    \"bucket\": \"products\",\n    \"type\": \"image\",\n    \"mimeType\": \"image/jpeg\",\n    \"size\": 2048576,\n    \"dimensions\": {\n      \"width\": 1200,\n      \"height\": 1200\n    },\n    \"metadata\": {\n      \"brandId\": \"brand_123\",\n      \"productId\": \"prod_123\",\n      \"description\": \"Hero image for athletic running shoes\",\n      \"tags\": [\"product\", \"hero\", \"shoes\", \"athletic\"],\n      \"colorProfile\": \"sRGB\",\n      \"dpi\": 300,\n      \"hasTransparency\": false\n    },\n    \"status\": \"active\",\n    \"uploadedBy\": \"user_123\",\n    \"createdAt\": \"2024-01-20T10:00:00.000Z\",\n    \"updatedAt\": \"2024-01-20T12:00:00.000Z\"\n  },\n  {\n    \"id\": \"asset_124\",\n    \"filename\": \"brand-guidelines.pdf\",\n    \"originalFilename\": \"Nike_Brand_Guidelines_2024.pdf\",\n    \"url\": \"https://storage.example.com/assets/brands/asset_124.pdf\",\n    \"bucket\": \"brands\",\n    \"type\": \"document\",\n    \"mimeType\": \"application/pdf\",\n    \"size\": 5242880,\n    \"metadata\": {\n      \"brandId\": \"brand_123\",\n      \"description\": \"Official brand guidelines document\",\n      \"tags\": [\"guidelines\", \"brand\", \"document\"],\n      \"pages\": 24,\n      \"version\": \"2024.1\"\n    },\n    \"status\": \"active\",\n    \"uploadedBy\": \"user_456\",\n    \"createdAt\": \"2024-01-18T14:00:00.000Z\",\n    \"updatedAt\": \"2024-01-18T14:00:00.000Z\"\n  }\n]"}]}, {"name": "Get Asset by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Asset details returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('url');", "    pm.expect(jsonData).to.have.property('metadata');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assets/{{assetId}}", "host": ["{{baseUrl}}"], "path": ["api", "assets", "{{assetId}}"]}, "description": "Get detailed information about a specific asset."}, "response": [{"name": "Asset Details", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assets/asset_123", "host": ["{{baseUrl}}"], "path": ["api", "assets", "asset_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"asset_123\",\n  \"filename\": \"athletic-shoes-hero.jpg\",\n  \"originalFilename\": \"nike-air-zoom-pegasus-main.jpg\",\n  \"url\": \"https://storage.example.com/assets/products/asset_123.jpg\",\n  \"thumbnailUrl\": \"https://storage.example.com/assets/products/thumbs/asset_123_thumb.jpg\",\n  \"downloadUrl\": \"https://storage.example.com/assets/products/downloads/asset_123.jpg?expires=1705776000\",\n  \"bucket\": \"products\",\n  \"type\": \"image\",\n  \"mimeType\": \"image/jpeg\",\n  \"size\": 2048576,\n  \"dimensions\": {\n    \"width\": 1200,\n    \"height\": 1200,\n    \"aspectRatio\": \"1:1\"\n  },\n  \"metadata\": {\n    \"brandId\": \"brand_123\",\n    \"productId\": \"prod_123\",\n    \"description\": \"Hero image for athletic running shoes - main product view with studio lighting\",\n    \"tags\": [\"product\", \"hero\", \"shoes\", \"athletic\", \"nike\"],\n    \"colorProfile\": \"sRGB\",\n    \"dpi\": 300,\n    \"hasTransparency\": false,\n    \"dominantColors\": [\"#FFFFFF\", \"#000000\", \"#FF6B35\"],\n    \"usage\": \"product-hero\",\n    \"photographer\": \"Studio Team\",\n    \"location\": \"Main Studio\",\n    \"equipment\": \"Canon EOS R5\"\n  },\n  \"processing\": {\n    \"status\": \"completed\",\n    \"thumbnailGenerated\": true,\n    \"optimizedVersions\": [\n      {\n        \"size\": \"small\",\n        \"dimensions\": \"300x300\",\n        \"url\": \"https://storage.example.com/assets/products/small/asset_123_300x300.jpg\"\n      },\n      {\n        \"size\": \"medium\",\n        \"dimensions\": \"600x600\",\n        \"url\": \"https://storage.example.com/assets/products/medium/asset_123_600x600.jpg\"\n      },\n      {\n        \"size\": \"large\",\n        \"dimensions\": \"1200x1200\",\n        \"url\": \"https://storage.example.com/assets/products/large/asset_123_1200x1200.jpg\"\n      }\n    ]\n  },\n  \"usage\": {\n    \"usedInProjects\": [\n      {\n        \"projectId\": \"proj_456\",\n        \"projectName\": \"Q1 Athletic Shoes Campaign\",\n        \"usage\": \"hero-image\"\n      }\n    ],\n    \"usedInCreatives\": [\n      {\n        \"creativeId\": \"creative_789\",\n        \"creativeName\": \"Instagram Hero Post\",\n        \"usage\": \"main-image\"\n      }\n    ],\n    \"downloadCount\": 15,\n    \"lastUsed\": \"2024-01-20T12:00:00.000Z\"\n  },\n  \"status\": \"active\",\n  \"uploadedBy\": \"user_123\",\n  \"createdAt\": \"2024-01-20T10:00:00.000Z\",\n  \"updatedAt\": \"2024-01-20T12:00:00.000Z\"\n}"}]}, {"name": "Upload Asset", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Asset uploaded successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('url');", "    pm.collectionVariables.set('assetId', jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/your/image.jpg", "description": "The file to upload"}, {"key": "metadata", "value": "{\n  \"brandId\": \"{{brandId}}\",\n  \"productId\": \"{{productId}}\",\n  \"description\": \"Product image for athletic shoes\",\n  \"tags\": [\"product\", \"shoes\", \"athletic\"],\n  \"usage\": \"product-gallery\",\n  \"generateThumbnail\": true,\n  \"generateOptimizedVersions\": true\n}", "type": "text", "description": "Asset metadata as JSON string"}]}, "url": {"raw": "{{baseUrl}}/api/assets/{{assetBucket}}", "host": ["{{baseUrl}}"], "path": ["api", "assets", "{{assetBucket}}"]}, "description": "Upload a new asset to a specific bucket with metadata."}, "response": [{"name": "Asset Uploaded", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "image.jpg"}, {"key": "metadata", "value": "{\"brandId\": \"brand_123\"}", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/assets/products", "host": ["{{baseUrl}}"], "path": ["api", "assets", "products"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"asset_125\",\n  \"filename\": \"product-image-1705774800.jpg\",\n  \"originalFilename\": \"image.jpg\",\n  \"url\": \"https://storage.example.com/assets/products/asset_125.jpg\",\n  \"thumbnailUrl\": \"https://storage.example.com/assets/products/thumbs/asset_125_thumb.jpg\",\n  \"bucket\": \"products\",\n  \"type\": \"image\",\n  \"mimeType\": \"image/jpeg\",\n  \"size\": 1536000,\n  \"dimensions\": {\n    \"width\": 1000,\n    \"height\": 1000\n  },\n  \"metadata\": {\n    \"brandId\": \"brand_123\",\n    \"productId\": \"prod_123\",\n    \"description\": \"Product image for athletic shoes\",\n    \"tags\": [\"product\", \"shoes\", \"athletic\"]\n  },\n  \"processing\": {\n    \"status\": \"processing\",\n    \"thumbnailGenerated\": false,\n    \"optimizedVersions\": [],\n    \"estimatedCompletionTime\": \"2024-01-20T17:05:00.000Z\"\n  },\n  \"status\": \"processing\",\n  \"uploadedBy\": \"user_123\",\n  \"createdAt\": \"2024-01-20T17:00:00.000Z\"\n}"}]}, {"name": "Update Asset", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Asset updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('updatedAt');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"filename\": \"athletic-shoes-hero-updated.jpg\",\n  \"metadata\": {\n    \"description\": \"Updated hero image for athletic running shoes - Q1 2024 campaign\",\n    \"tags\": [\"product\", \"hero\", \"shoes\", \"athletic\", \"q1-2024\", \"campaign\"],\n    \"usage\": \"hero-image\",\n    \"campaign\": \"Q1 2024 Launch\",\n    \"targetAudience\": \"Athletes and fitness enthusiasts\",\n    \"approvedBy\": \"{{currentUserId}}\",\n    \"approvedAt\": \"2024-01-20T17:30:00.000Z\",\n    \"version\": \"2.0\"\n  },\n  \"status\": \"approved\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assets/{{assetId}}", "host": ["{{baseUrl}}"], "path": ["api", "assets", "{{assetId}}"]}, "description": "Update asset metadata, filename, or status."}, "response": [{"name": "Asset Updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"filename\": \"athletic-shoes-hero-updated.jpg\",\n  \"metadata\": {\n    \"description\": \"Updated hero image\",\n    \"tags\": [\"product\", \"hero\", \"updated\"]\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/assets/asset_123", "host": ["{{baseUrl}}"], "path": ["api", "assets", "asset_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"asset_123\",\n  \"filename\": \"athletic-shoes-hero-updated.jpg\",\n  \"metadata\": {\n    \"description\": \"Updated hero image for athletic running shoes - Q1 2024 campaign\",\n    \"tags\": [\"product\", \"hero\", \"shoes\", \"athletic\", \"q1-2024\", \"campaign\"],\n    \"version\": \"2.0\",\n    \"approvedBy\": \"user_123\",\n    \"approvedAt\": \"2024-01-20T17:30:00.000Z\"\n  },\n  \"status\": \"approved\",\n  \"updatedAt\": \"2024-01-20T17:30:00.000Z\",\n  \"updatedBy\": \"user_123\"\n}"}]}, {"name": "Delete Asset", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 204', function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/assets/{{assetId}}?permanent=false", "host": ["{{baseUrl}}"], "path": ["api", "assets", "{{assetId}}"], "query": [{"key": "permanent", "value": "false", "description": "Whether to permanently delete (true) or soft delete (false)"}]}, "description": "Delete an asset (supports both soft and permanent deletion)."}, "response": [{"name": "Asset Deleted", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/assets/asset_125", "host": ["{{baseUrl}}"], "path": ["api", "assets", "asset_125"]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": null, "header": [], "body": null}]}], "description": "Basic asset management operations"}, {"name": "Asset Workflows", "item": [{"name": "Bulk Upload Assets", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Bulk upload initiated', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('batchId');", "    pm.expect(jsonData).to.have.property('uploadedAssets');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["/path/to/image1.jpg", "/path/to/image2.jpg", "/path/to/image3.jpg"], "description": "Multiple files to upload"}, {"key": "commonMetadata", "value": "{\n  \"brandId\": \"{{brandId}}\",\n  \"productId\": \"{{productId}}\",\n  \"tags\": [\"product\", \"bulk-upload\", \"campaign\"],\n  \"usage\": \"product-gallery\",\n  \"campaign\": \"Q1 2024 Launch\"\n}", "type": "text", "description": "Common metadata applied to all uploaded files"}, {"key": "options", "value": "{\n  \"generateThumbnails\": true,\n  \"generateOptimizedVersions\": true,\n  \"autoTag\": true,\n  \"notifyOnCompletion\": true\n}", "type": "text", "description": "Upload processing options"}]}, "url": {"raw": "{{baseUrl}}/api/assets/{{assetBucket}}/bulk", "host": ["{{baseUrl}}"], "path": ["api", "assets", "{{assetBucket}}", "bulk"]}, "description": "Upload multiple assets in a single operation."}, "response": [{"name": "Bulk Upload Initiated", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["image1.jpg", "image2.jpg"]}]}, "url": {"raw": "{{baseUrl}}/api/assets/products/bulk", "host": ["{{baseUrl}}"], "path": ["api", "assets", "products", "bulk"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"batchId\": \"batch_789\",\n  \"uploadedAssets\": [\n    {\n      \"id\": \"asset_126\",\n      \"filename\": \"product-image-1-1705774800.jpg\",\n      \"originalFilename\": \"image1.jpg\",\n      \"status\": \"processing\",\n      \"url\": \"https://storage.example.com/assets/products/asset_126.jpg\"\n    },\n    {\n      \"id\": \"asset_127\",\n      \"filename\": \"product-image-2-1705774801.jpg\",\n      \"originalFilename\": \"image2.jpg\",\n      \"status\": \"processing\",\n      \"url\": \"https://storage.example.com/assets/products/asset_127.jpg\"\n    }\n  ],\n  \"totalFiles\": 2,\n  \"successfulUploads\": 2,\n  \"failedUploads\": 0,\n  \"estimatedProcessingTime\": \"5 minutes\",\n  \"createdAt\": \"2024-01-20T17:00:00.000Z\"\n}"}]}, {"name": "Process Asset Batch", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Batch processing status returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('batchId');", "    pm.expect(jsonData).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assets/batch/{{batchId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "assets", "batch", "{{batchId}}", "status"]}, "description": "Check the processing status of a bulk upload batch."}, "response": [{"name": "Batch Processing Status", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assets/batch/batch_789/status", "host": ["{{baseUrl}}"], "path": ["api", "assets", "batch", "batch_789", "status"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"batchId\": \"batch_789\",\n  \"status\": \"completed\",\n  \"totalAssets\": 2,\n  \"processedAssets\": 2,\n  \"failedAssets\": 0,\n  \"assets\": [\n    {\n      \"assetId\": \"asset_126\",\n      \"status\": \"completed\",\n      \"processing\": {\n        \"thumbnailGenerated\": true,\n        \"optimizedVersions\": 3,\n        \"autoTagsGenerated\": [\"shoes\", \"athletic\", \"product\"]\n      }\n    },\n    {\n      \"assetId\": \"asset_127\",\n      \"status\": \"completed\",\n      \"processing\": {\n        \"thumbnailGenerated\": true,\n        \"optimizedVersions\": 3,\n        \"autoTagsGenerated\": [\"shoes\", \"running\", \"sport\"]\n      }\n    }\n  ],\n  \"completedAt\": \"2024-01-20T17:05:00.000Z\",\n  \"processingTime\": \"5 minutes\"\n}"}]}, {"name": "Search Assets", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Search results returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('results');", "    pm.expect(jsonData.results).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"athletic shoes\",\n  \"filters\": {\n    \"bucket\": \"products\",\n    \"type\": \"image\",\n    \"brandId\": \"{{brandId}}\",\n    \"tags\": [\"product\", \"shoes\"],\n    \"status\": \"active\",\n    \"dateRange\": {\n      \"start\": \"2024-01-01\",\n      \"end\": \"2024-01-31\"\n    },\n    \"sizeRange\": {\n      \"min\": 100000,\n      \"max\": 5000000\n    }\n  },\n  \"search\": {\n    \"fields\": [\"filename\", \"description\", \"tags\"],\n    \"fuzzy\": true,\n    \"boost\": {\n      \"filename\": 2.0,\n      \"tags\": 1.5,\n      \"description\": 1.0\n    }\n  },\n  \"pagination\": {\n    \"limit\": 20,\n    \"offset\": 0\n  },\n  \"sort\": {\n    \"field\": \"relevance\",\n    \"order\": \"desc\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assets/search", "host": ["{{baseUrl}}"], "path": ["api", "assets", "search"]}, "description": "Search assets using advanced filters and full-text search."}, "response": [{"name": "Asset Search Results", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"athletic shoes\",\n  \"filters\": {\n    \"type\": \"image\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/assets/search", "host": ["{{baseUrl}}"], "path": ["api", "assets", "search"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"results\": [\n    {\n      \"id\": \"asset_123\",\n      \"filename\": \"athletic-shoes-hero.jpg\",\n      \"url\": \"https://storage.example.com/assets/products/asset_123.jpg\",\n      \"thumbnailUrl\": \"https://storage.example.com/assets/products/thumbs/asset_123_thumb.jpg\",\n      \"relevanceScore\": 0.95,\n      \"matchedFields\": [\"filename\", \"tags\"],\n      \"highlightedText\": {\n        \"filename\": \"<mark>athletic</mark>-<mark>shoes</mark>-hero.jpg\",\n        \"tags\": [\"product\", \"<mark>athletic</mark>\", \"<mark>shoes</mark>\"]\n      }\n    }\n  ],\n  \"pagination\": {\n    \"total\": 1,\n    \"limit\": 20,\n    \"offset\": 0,\n    \"hasMore\": false\n  },\n  \"searchMetadata\": {\n    \"query\": \"athletic shoes\",\n    \"searchTime\": \"15ms\",\n    \"totalMatches\": 1,\n    \"appliedFilters\": {\n      \"type\": \"image\"\n    }\n  },\n  \"facets\": {\n    \"buckets\": {\n      \"products\": 1,\n      \"creatives\": 0\n    },\n    \"types\": {\n      \"image\": 1,\n      \"video\": 0\n    },\n    \"brands\": {\n      \"brand_123\": 1\n    }\n  }\n}"}]}, {"name": "Generate Asset Variants", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Variants generation initiated', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('variantGenerationId');", "    pm.expect(jsonData).to.have.property('variants');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assetId\": \"{{assetId}}\",\n  \"variants\": [\n    {\n      \"name\": \"instagram-square\",\n      \"dimensions\": {\n        \"width\": 1080,\n        \"height\": 1080\n      },\n      \"format\": \"jpg\",\n      \"quality\": 90,\n      \"optimizeForPlatform\": \"instagram\"\n    },\n    {\n      \"name\": \"facebook-cover\",\n      \"dimensions\": {\n        \"width\": 1200,\n        \"height\": 630\n      },\n      \"format\": \"jpg\",\n      \"quality\": 85,\n      \"optimizeForPlatform\": \"facebook\"\n    },\n    {\n      \"name\": \"web-thumbnail\",\n      \"dimensions\": {\n        \"width\": 300,\n        \"height\": 300\n      },\n      \"format\": \"webp\",\n      \"quality\": 80,\n      \"optimizeForWeb\": true\n    }\n  ],\n  \"options\": {\n    \"maintainAspectRatio\": false,\n    \"cropStrategy\": \"smart\",\n    \"applyWatermark\": false,\n    \"generateMetadata\": true\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assets/{{assetId}}/variants", "host": ["{{baseUrl}}"], "path": ["api", "assets", "{{assetId}}", "variants"]}, "description": "Generate multiple variants of an asset for different platforms and use cases."}, "response": [{"name": "Variants Generation Initiated", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assetId\": \"asset_123\",\n  \"variants\": [\n    {\n      \"name\": \"instagram-square\",\n      \"dimensions\": {\n        \"width\": 1080,\n        \"height\": 1080\n      }\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/assets/asset_123/variants", "host": ["{{baseUrl}}"], "path": ["api", "assets", "asset_123", "variants"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"variantGenerationId\": \"variant_gen_456\",\n  \"parentAssetId\": \"asset_123\",\n  \"variants\": [\n    {\n      \"variantId\": \"variant_001\",\n      \"name\": \"instagram-square\",\n      \"status\": \"processing\",\n      \"dimensions\": {\n        \"width\": 1080,\n        \"height\": 1080\n      },\n      \"estimatedCompletionTime\": \"2024-01-20T18:05:00.000Z\"\n    },\n    {\n      \"variantId\": \"variant_002\",\n      \"name\": \"facebook-cover\",\n      \"status\": \"queued\",\n      \"dimensions\": {\n        \"width\": 1200,\n        \"height\": 630\n      },\n      \"estimatedCompletionTime\": \"2024-01-20T18:07:00.000Z\"\n    },\n    {\n      \"variantId\": \"variant_003\",\n      \"name\": \"web-thumbnail\",\n      \"status\": \"queued\",\n      \"dimensions\": {\n        \"width\": 300,\n        \"height\": 300\n      },\n      \"estimatedCompletionTime\": \"2024-01-20T18:08:00.000Z\"\n    }\n  ],\n  \"totalVariants\": 3,\n  \"estimatedTotalTime\": \"8 minutes\",\n  \"createdAt\": \"2024-01-20T18:00:00.000Z\"\n}"}]}], "description": "Advanced asset workflow operations and processing"}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "currentUserId", "value": "", "type": "string"}, {"key": "brandId", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}, {"key": "assetId", "value": "", "type": "string"}, {"key": "assetBucket", "value": "products", "type": "string"}, {"key": "batchId", "value": "", "type": "string"}]}