{"info": {"_postman_id": "settings-credentials-collection", "name": "13. Settings & Credentials Management", "description": "Complete settings and credentials management including user settings, account credentials, API secrets, and credential sets management.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "adfury-api"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "User Settings", "item": [{"name": "Get Settings", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/settings", "host": ["{{baseUrl}}"], "path": ["api", "settings"]}}}, {"name": "Update Settings", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notifications\": {\n    \"email\": true,\n    \"push\": false,\n    \"projectUpdates\": true,\n    \"marketingEmails\": false\n  },\n  \"preferences\": {\n    \"theme\": \"dark\",\n    \"language\": \"en\",\n    \"timezone\": \"America/New_York\",\n    \"dateFormat\": \"MM/DD/YYYY\"\n  },\n  \"privacy\": {\n    \"profileVisibility\": \"team\",\n    \"activityTracking\": true,\n    \"analyticsOptOut\": false\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/settings", "host": ["{{baseUrl}}"], "path": ["api", "settings"]}}}, {"name": "Get User Settings (Admin)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/settings/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "settings", "users", "{{userId}}"]}}}, {"name": "Update User Settings (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"permissions\": {\n    \"canCreateProjects\": true,\n    \"canManageUsers\": false,\n    \"canAccessAnalytics\": true\n  },\n  \"limits\": {\n    \"maxProjects\": 10,\n    \"maxStorageGB\": 50,\n    \"maxApiCalls\": 10000\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/settings/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "settings", "users", "{{userId}}"]}}}]}, {"name": "Credential Sets", "item": [{"name": "Update Credential Set", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Amazon Advertising API - Updated\",\n  \"description\": \"Updated credentials for Amazon Advertising API integration\",\n  \"fields\": {\n    \"clientId\": \"updated_client_id\",\n    \"clientSecret\": \"updated_client_secret\",\n    \"refreshToken\": \"updated_refresh_token\",\n    \"region\": \"US\"\n  },\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/settings/credentialSets/{{credentialSetId}}", "host": ["{{baseUrl}}"], "path": ["api", "settings", "credentialSets", "{{credentialSetId}}"]}}}, {"name": "Delete Credential Fields", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fields\": [\"oldA<PERSON><PERSON>ey\", \"deprecatedToken\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/settings/credentialSets/{{credentialSetId}}/fields", "host": ["{{baseUrl}}"], "path": ["api", "settings", "credentialSets", "{{credentialSetId}}", "fields"]}}}, {"name": "Get Secret by Partner ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/settings/secret/{{partnerId}}", "host": ["{{baseUrl}}"], "path": ["api", "settings", "secret", "{{partnerId}}"]}}}]}, {"name": "Account Credentials", "item": [{"name": "Get Account Credentials", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/settings/accounts/{{accountId}}/credentials?type=api&active=true", "host": ["{{baseUrl}}"], "path": ["api", "settings", "accounts", "{{accountId}}", "credentials"], "query": [{"key": "type", "value": "api", "description": "Filter by credential type"}, {"key": "active", "value": "true", "description": "Filter by active status"}]}}}, {"name": "Create Account Credential", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Google Ads API\",\n  \"type\": \"api\",\n  \"provider\": \"google\",\n  \"description\": \"Google Ads API credentials for campaign management\",\n  \"credentials\": {\n    \"clientId\": \"google_client_id\",\n    \"clientSecret\": \"google_client_secret\",\n    \"refreshToken\": \"google_refresh_token\",\n    \"developerToken\": \"google_developer_token\",\n    \"customerId\": \"google_customer_id\"\n  },\n  \"scopes\": [\"campaigns\", \"keywords\", \"ads\"],\n  \"isActive\": true,\n  \"expiresAt\": \"2025-01-20T00:00:00.000Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/settings/accounts/{{accountId}}/credentials", "host": ["{{baseUrl}}"], "path": ["api", "settings", "accounts", "{{accountId}}", "credentials"]}}}, {"name": "Update Account Credential", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Google Ads API - Updated\",\n  \"credentials\": {\n    \"refreshToken\": \"new_google_refresh_token\",\n    \"customerId\": \"updated_customer_id\"\n  },\n  \"isActive\": true,\n  \"lastValidated\": \"2024-01-20T21:00:00.000Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/settings/accounts/{{accountId}}/credentials/{{credentialId}}", "host": ["{{baseUrl}}"], "path": ["api", "settings", "accounts", "{{accountId}}", "credentials", "{{credentialId}}"]}}}, {"name": "Delete Account Credential", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/settings/accounts/{{accountId}}/credentials/{{credentialId}}", "host": ["{{baseUrl}}"], "path": ["api", "settings", "accounts", "{{accountId}}", "credentials", "{{credentialId}}"]}}}, {"name": "Test Credential Connection", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"credentialId\": \"{{credentialId}}\",\n  \"testType\": \"connection\",\n  \"options\": {\n    \"timeout\": 30000,\n    \"validatePermissions\": true\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/settings/accounts/{{accountId}}/credentials/{{credentialId}}/test", "host": ["{{baseUrl}}"], "path": ["api", "settings", "accounts", "{{accountId}}", "credentials", "{{credentialId}}", "test"]}}}]}, {"name": "Beta Features", "item": [{"name": "Fetch Beta Lead by <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/beta/leads/fetch", "host": ["{{baseUrl}}"], "path": ["api", "beta", "leads", "fetch"]}}}, {"name": "Create Beta Lead", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"New Beta User\",\n  \"company\": \"Example Corp\",\n  \"role\": \"Marketing Manager\",\n  \"useCase\": \"Creative campaign management\",\n  \"source\": \"website\"\n}"}, "url": {"raw": "{{baseUrl}}/api/beta/leads", "host": ["{{baseUrl}}"], "path": ["api", "beta", "leads"]}}}, {"name": "Update Beta Lead", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"status\": \"approved\",\n  \"notes\": \"Beta access approved for Q1 program\"\n}"}, "url": {"raw": "{{baseUrl}}/api/beta/leads", "host": ["{{baseUrl}}"], "path": ["api", "beta", "leads"]}}}, {"name": "Send Beta Invitation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"Invited Beta User\",\n  \"inviteCode\": \"BETA2024Q1\",\n  \"personalMessage\": \"Welcome to our beta program! We're excited to have you try our new features.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/beta/invitations", "host": ["{{baseUrl}}"], "path": ["api", "beta", "invitations"]}}}]}, {"name": "Debug Endpoints", "item": [{"name": "Get All Sessions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/debug/sessions", "host": ["{{baseUrl}}"], "path": ["debug", "sessions"]}}}, {"name": "Delete All Sessions", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/debug/sessions", "host": ["{{baseUrl}}"], "path": ["debug", "sessions"]}}}, {"name": "Get Current Session Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/debug/my-session", "host": ["{{baseUrl}}"], "path": ["debug", "my-session"]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "currentUserId", "value": "", "type": "string"}, {"key": "accountId", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "credentialSetId", "value": "", "type": "string"}, {"key": "credentialId", "value": "", "type": "string"}, {"key": "partnerId", "value": "", "type": "string"}]}