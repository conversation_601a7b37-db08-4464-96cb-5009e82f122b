{"info": {"_postman_id": "user-management-collection", "name": "02. User Management", "description": "Complete user management endpoints including CRUD operations, user search, profile updates, and user settings. Requires authentication for all operations.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "adfury-api"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Ensure auth token is available", "if (!pm.collectionVariables.get('authToken')) {", "    console.warn('No auth token found. Please run Authentication collection first.');", "}"], "type": "text/javascript"}}], "item": [{"name": "User Listing & Search", "item": [{"name": "List All Users", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array of users', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    if (jsonData.length > 0) {", "        pm.expect(jsonData[0]).to.have.property('id');", "        pm.expect(jsonData[0]).to.have.property('email');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users?limit=20&offset=0&sort=createdAt&order=desc", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "limit", "value": "20", "description": "Number of users to return (default: 20, max: 100)"}, {"key": "offset", "value": "0", "description": "Number of users to skip for pagination"}, {"key": "sort", "value": "createdAt", "description": "Field to sort by (createdAt, updatedAt, name, email)"}, {"key": "order", "value": "desc", "description": "Sort order (asc or desc)"}, {"key": "accountId", "value": "{{currentAccountId}}", "description": "Filter users by account ID", "disabled": true}, {"key": "search", "value": "", "description": "Search users by name or email", "disabled": true}]}, "description": "Get a paginated list of users with optional filtering and sorting.\n\n**Query Parameters:**\n- `limit`: Number of results per page (max 100)\n- `offset`: Skip N results for pagination\n- `sort`: Sort field (createdAt, updatedAt, name, email)\n- `order`: Sort order (asc/desc)\n- `accountId`: Filter by specific account\n- `search`: Search by name or email\n\n**Response:**\n- Array of user objects\n- Each user includes basic profile info\n- Does not include sensitive data"}, "response": [{"name": "User List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users?limit=2&offset=0", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "limit", "value": "2"}, {"key": "offset", "value": "0"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-************\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON>\",\n    \"profilePictureUrl\": \"https://example.com/profile1.jpg\",\n    \"jobTitle\": \"Marketing Manager\",\n    \"phone\": \"+**********\",\n    \"isActive\": true,\n    \"createdAt\": \"2024-01-01T00:00:00.000Z\",\n    \"updatedAt\": \"2024-01-15T12:00:00.000Z\"\n  },\n  {\n    \"id\": \"660e8400-e29b-41d4-a716-************\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON>\",\n    \"profilePictureUrl\": \"https://example.com/profile2.jpg\",\n    \"jobTitle\": \"Creative Director\",\n    \"phone\": \"+*********1\",\n    \"isActive\": true,\n    \"createdAt\": \"2024-01-02T00:00:00.000Z\",\n    \"updatedAt\": \"2024-01-16T12:00:00.000Z\"\n  }\n]"}]}, {"name": "Search Users", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Search results match query', function () {", "    const jsonData = pm.response.json();", "    const searchTerm = pm.request.url.query.get('search').toLowerCase();", "    ", "    jsonData.forEach(user => {", "        const matchesName = user.name && user.name.toLowerCase().includes(searchTerm);", "        const matchesEmail = user.email && user.email.toLowerCase().includes(searchTerm);", "        pm.expect(matchesName || matchesEmail).to.be.true;", "    });", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users?search=john", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "search", "value": "john", "description": "Search term to find in user names or emails"}]}, "description": "Search for users by name or email.\n\n**Search Behavior:**\n- Case-insensitive partial matching\n- Searches in both name and email fields\n- Returns all matching users\n- Can be combined with pagination parameters"}, "response": [{"name": "Search Results", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users?search=john", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "search", "value": "john"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-************\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON>\",\n    \"profilePictureUrl\": \"https://example.com/profile1.jpg\",\n    \"jobTitle\": \"Marketing Manager\",\n    \"isActive\": true\n  },\n  {\n    \"id\": \"770e8400-e29b-41d4-a716-446655440002\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON>\",\n    \"profilePictureUrl\": \"https://example.com/profile3.jpg\",\n    \"jobTitle\": \"Sales Manager\",\n    \"isActive\": true\n  }\n]"}]}]}, {"name": "User CRUD Operations", "item": [{"name": "Get User by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User object has required fields', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('email');", "    pm.expect(jsonData).to.have.property('name');", "    pm.expect(jsonData).to.have.property('accounts');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}, "description": "Get detailed information about a specific user by their ID.\n\n**Path Parameters:**\n- `userId`: UUID of the user\n\n**Response:**\n- Complete user profile\n- Associated accounts with roles\n- Settings and preferences\n- Activity metadata"}, "response": [{"name": "User Details", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/550e8400-e29b-41d4-a716-************", "host": ["{{baseUrl}}"], "path": ["api", "users", "550e8400-e29b-41d4-a716-************"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"550e8400-e29b-41d4-a716-************\",\n  \"email\": \"<EMAIL>\",\n  \"name\": \"<PERSON>\",\n  \"auth0Id\": \"auth0|*********\",\n  \"profilePictureUrl\": \"https://example.com/profile.jpg\",\n  \"phone\": \"+**********\",\n  \"jobTitle\": \"Marketing Manager\",\n  \"bio\": \"Experienced marketing professional with 10+ years in digital advertising.\",\n  \"isActive\": true,\n  \"emailVerified\": true,\n  \"createdAt\": \"2024-01-01T00:00:00.000Z\",\n  \"updatedAt\": \"2024-01-15T12:00:00.000Z\",\n  \"accounts\": [\n    {\n      \"id\": \"660e8400-e29b-41d4-a716-************\",\n      \"name\": \"Acme Corporation\",\n      \"type\": \"agency\",\n      \"role\": \"admin\",\n      \"permissions\": [\"create:campaigns\", \"edit:campaigns\", \"delete:campaigns\", \"manage:users\"],\n      \"joinedAt\": \"2024-01-01T00:00:00.000Z\"\n    }\n  ],\n  \"settings\": {\n    \"notifications\": {\n      \"email\": true,\n      \"push\": false,\n      \"sms\": false\n    },\n    \"preferences\": {\n      \"language\": \"en\",\n      \"timezone\": \"America/New_York\",\n      \"dateFormat\": \"MM/DD/YYYY\"\n    }\n  },\n  \"metadata\": {\n    \"lastLogin\": \"2024-01-20T10:00:00.000Z\",\n    \"loginCount\": 42,\n    \"lastActivityAt\": \"2024-01-20T15:30:00.000Z\"\n  }\n}"}, {"name": "User Not Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/invalid-user-id", "host": ["{{baseUrl}}"], "path": ["api", "users", "invalid-user-id"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"error\": \"Not Found\",\n  \"message\": \"User not found\"\n}"}]}, {"name": "Update User Profile", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User was updated', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('updatedAt');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>e\",\n  \"phone\": \"+**********\",\n  \"jobTitle\": \"Senior Marketing Manager\",\n  \"bio\": \"Updated bio with new achievements and experience.\",\n  \"profilePictureUrl\": \"https://example.com/new-profile.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}, "description": "Update a user's profile information.\n\n**Path Parameters:**\n- `userId`: UUID of the user to update\n\n**Request Body (all fields optional):**\n- `name`: User's full name\n- `phone`: Phone number\n- `jobTitle`: Professional title\n- `bio`: User biography\n- `profilePictureUrl`: URL to profile picture\n\n**Note:** Users can only update their own profile unless they have admin permissions"}, "response": [{"name": "Updated User", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"jobTitle\": \"Senior Marketing Manager\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/550e8400-e29b-41d4-a716-************", "host": ["{{baseUrl}}"], "path": ["api", "users", "550e8400-e29b-41d4-a716-************"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"550e8400-e29b-41d4-a716-************\",\n  \"email\": \"<EMAIL>\",\n  \"name\": \"<PERSON>\",\n  \"jobTitle\": \"Senior Marketing Manager\",\n  \"updatedAt\": \"2024-01-20T16:00:00.000Z\",\n  \"message\": \"User profile updated successfully\"\n}"}]}, {"name": "Delete User", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 204 or 200', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 204]);", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}, "description": "Delete a user account. This is a soft delete that deactivates the account.\n\n**Path Parameters:**\n- `userId`: UUID of the user to delete\n\n**Authorization:**\n- Only admins or the user themselves can delete an account\n- Account owner cannot delete their own account if they own active accounts\n\n**Effects:**\n- User is marked as inactive\n- User is removed from all accounts\n- User cannot login\n- Data is retained for audit purposes"}, "response": [{"name": "User Deleted", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/users/550e8400-e29b-41d4-a716-************", "host": ["{{baseUrl}}"], "path": ["api", "users", "550e8400-e29b-41d4-a716-************"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"User account has been deactivated\"\n}"}, {"name": "<PERSON><PERSON> Delete - Account Owner", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/users/550e8400-e29b-41d4-a716-************", "host": ["{{baseUrl}}"], "path": ["api", "users", "550e8400-e29b-41d4-a716-************"]}}, "status": "Conflict", "code": 409, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"error\": \"Cannot delete user\",\n  \"message\": \"User owns active accounts. Transfer ownership before deleting.\",\n  \"ownedAccounts\": [\n    {\n      \"id\": \"660e8400-e29b-41d4-a716-************\",\n      \"name\": \"Acme Corporation\"\n    }\n  ]\n}"}]}]}, {"name": "User Settings & Preferences", "item": [{"name": "Get User Settings", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Settings structure is correct', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('notifications');", "    pm.expect(jsonData).to.have.property('preferences');", "    pm.expect(jsonData).to.have.property('privacy');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/settings", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "settings"]}, "description": "Get user-specific settings and preferences.\n\n**Path Parameters:**\n- `userId`: UUID of the user\n\n**Response includes:**\n- Notification preferences\n- UI preferences\n- Privacy settings\n- Feature flags"}, "response": [{"name": "User Settings", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/550e8400-e29b-41d4-a716-************/settings", "host": ["{{baseUrl}}"], "path": ["api", "users", "550e8400-e29b-41d4-a716-************", "settings"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"notifications\": {\n    \"email\": {\n      \"campaigns\": true,\n      \"updates\": true,\n      \"marketing\": false,\n      \"digest\": \"weekly\"\n    },\n    \"push\": {\n      \"enabled\": false,\n      \"campaigns\": false,\n      \"mentions\": true\n    },\n    \"sms\": {\n      \"enabled\": false,\n      \"critical\": true\n    }\n  },\n  \"preferences\": {\n    \"language\": \"en\",\n    \"timezone\": \"America/New_York\",\n    \"dateFormat\": \"MM/DD/YYYY\",\n    \"timeFormat\": \"12h\",\n    \"currency\": \"USD\",\n    \"theme\": \"light\"\n  },\n  \"privacy\": {\n    \"profileVisibility\": \"team\",\n    \"showEmail\": false,\n    \"showPhone\": false,\n    \"activityTracking\": true\n  },\n  \"features\": {\n    \"betaProgram\": true,\n    \"advancedAnalytics\": true,\n    \"aiSuggestions\": true\n  }\n}"}]}, {"name": "Update User Settings", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Settings were updated', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.include('updated');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notifications\": {\n    \"email\": {\n      \"campaigns\": false,\n      \"digest\": \"daily\"\n    }\n  },\n  \"preferences\": {\n    \"theme\": \"dark\",\n    \"language\": \"es\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/settings", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "settings"]}, "description": "Update user settings. Only the settings provided in the request body will be updated.\n\n**Path Parameters:**\n- `userId`: UUID of the user\n\n**Request Body:**\n- Partial settings object\n- Only include settings you want to change\n- Nested properties are merged"}, "response": [{"name": "Settings Updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"preferences\": {\n    \"theme\": \"dark\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/users/550e8400-e29b-41d4-a716-************/settings", "host": ["{{baseUrl}}"], "path": ["api", "users", "550e8400-e29b-41d4-a716-************", "settings"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"User settings updated successfully\",\n  \"updatedFields\": [\"preferences.theme\"]\n}"}]}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "userId", "value": "550e8400-e29b-41d4-a716-************", "type": "string"}, {"key": "currentAccountId", "value": "", "type": "string"}]}