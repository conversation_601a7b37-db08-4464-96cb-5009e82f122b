{"info": {"_postman_id": "product-management-collection", "name": "08. Product Management", "description": "Complete product management workflow including product operations, batch processing, metadata management, and product setup workflows.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "adfury-api"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Ensure auth token is available", "if (!pm.collectionVariables.get('authToken')) {", "    console.warn('No auth token found. Please run Authentication collection first.');", "}"], "type": "text/javascript"}}], "item": [{"name": "Product Operations", "item": [{"name": "Get Products by Account", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array of products', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    if (jsonData.length > 0) {", "        pm.expect(jsonData[0]).to.have.property('id');", "        pm.expect(jsonData[0]).to.have.property('name');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/{{accountId}}?limit=50&offset=0&status=active", "host": ["{{baseUrl}}"], "path": ["api", "products", "{{accountId}}"], "query": [{"key": "limit", "value": "50", "description": "Number of products to return"}, {"key": "offset", "value": "0", "description": "Number of products to skip"}, {"key": "status", "value": "active", "description": "Filter by product status"}, {"key": "brandId", "value": "{{brandId}}", "disabled": true, "description": "Filter by brand"}, {"key": "search", "value": "", "disabled": true, "description": "Search products by name or SKU"}]}, "description": "Get all products for a specific account with filtering and pagination."}, "response": [{"name": "Products List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/acc_123", "host": ["{{baseUrl}}"], "path": ["api", "products", "acc_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"prod_123\",\n    \"accountId\": \"acc_123\",\n    \"brandId\": \"brand_123\",\n    \"name\": \"Athletic Running Shoes\",\n    \"sku\": \"ARS-001\",\n    \"description\": \"High-performance running shoes for athletes\",\n    \"category\": \"Footwear\",\n    \"subcategory\": \"Running Shoes\",\n    \"price\": {\n      \"amount\": 129.99,\n      \"currency\": \"USD\"\n    },\n    \"images\": [\n      {\n        \"url\": \"https://storage.example.com/products/prod_123_main.jpg\",\n        \"type\": \"main\",\n        \"alt\": \"Athletic Running Shoes - Main View\"\n      },\n      {\n        \"url\": \"https://storage.example.com/products/prod_123_side.jpg\",\n        \"type\": \"side\",\n        \"alt\": \"Athletic Running Shoes - Side View\"\n      }\n    ],\n    \"attributes\": {\n      \"color\": \"Black/White\",\n      \"size\": \"Multiple\",\n      \"material\": \"Mesh/Synthetic\",\n      \"weight\": \"280g\"\n    },\n    \"inventory\": {\n      \"inStock\": true,\n      \"quantity\": 150,\n      \"lowStockThreshold\": 10\n    },\n    \"keywords\": [\"running\", \"athletic\", \"shoes\", \"performance\"],\n    \"status\": \"active\",\n    \"createdAt\": \"2024-01-15T10:00:00.000Z\",\n    \"updatedAt\": \"2024-01-20T14:00:00.000Z\"\n  }\n]"}]}, {"name": "Get Product by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product details returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('name');", "    pm.expect(jsonData).to.have.property('images');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/product/{{productId}}", "host": ["{{baseUrl}}"], "path": ["api", "products", "product", "{{productId}}"]}, "description": "Get detailed information about a specific product."}, "response": [{"name": "Product Details", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/product/prod_123", "host": ["{{baseUrl}}"], "path": ["api", "products", "product", "prod_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"prod_123\",\n  \"accountId\": \"acc_123\",\n  \"brandId\": \"brand_123\",\n  \"name\": \"Athletic Running Shoes\",\n  \"sku\": \"ARS-001\",\n  \"description\": \"High-performance running shoes designed for serious athletes. Features advanced cushioning and breathable mesh upper.\",\n  \"category\": \"Footwear\",\n  \"subcategory\": \"Running Shoes\",\n  \"price\": {\n    \"amount\": 129.99,\n    \"currency\": \"USD\",\n    \"msrp\": 159.99,\n    \"costPrice\": 65.00\n  },\n  \"images\": [\n    {\n      \"url\": \"https://storage.example.com/products/prod_123_main.jpg\",\n      \"type\": \"main\",\n      \"alt\": \"Athletic Running Shoes - Main View\",\n      \"dimensions\": \"1200x1200\"\n    },\n    {\n      \"url\": \"https://storage.example.com/products/prod_123_side.jpg\",\n      \"type\": \"side\",\n      \"alt\": \"Athletic Running Shoes - Side View\",\n      \"dimensions\": \"1200x1200\"\n    },\n    {\n      \"url\": \"https://storage.example.com/products/prod_123_back.jpg\",\n      \"type\": \"back\",\n      \"alt\": \"Athletic Running Shoes - Back View\",\n      \"dimensions\": \"1200x1200\"\n    }\n  ],\n  \"attributes\": {\n    \"color\": \"Black/White\",\n    \"sizes\": [\"7\", \"7.5\", \"8\", \"8.5\", \"9\", \"9.5\", \"10\", \"10.5\", \"11\", \"11.5\", \"12\"],\n    \"material\": \"Mesh/Synthetic\",\n    \"weight\": \"280g\",\n    \"gender\": \"Unisex\",\n    \"brand\": \"Nike\",\n    \"model\": \"Air Zoom Pegasus\"\n  },\n  \"inventory\": {\n    \"inStock\": true,\n    \"quantity\": 150,\n    \"lowStockThreshold\": 10,\n    \"reservedQuantity\": 5,\n    \"availableQuantity\": 145\n  },\n  \"keywords\": [\"running\", \"athletic\", \"shoes\", \"performance\", \"nike\", \"pegasus\"],\n  \"seo\": {\n    \"metaTitle\": \"Nike Air Zoom Pegasus - Athletic Running Shoes\",\n    \"metaDescription\": \"High-performance Nike Air Zoom Pegasus running shoes for athletes. Advanced cushioning and breathable design.\",\n    \"slug\": \"nike-air-zoom-pegasus-running-shoes\"\n  },\n  \"shipping\": {\n    \"weight\": 1.2,\n    \"dimensions\": {\n      \"length\": 32,\n      \"width\": 20,\n      \"height\": 12\n    },\n    \"fragile\": false\n  },\n  \"status\": \"active\",\n  \"createdAt\": \"2024-01-15T10:00:00.000Z\",\n  \"updatedAt\": \"2024-01-20T14:00:00.000Z\",\n  \"createdBy\": \"user_123\"\n}"}]}, {"name": "Delete Product", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product deleted successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"reason\": \"Product discontinued\",\n  \"softDelete\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/products/delete", "host": ["{{baseUrl}}"], "path": ["api", "products", "delete"]}, "description": "Delete a product (supports both soft and hard delete)."}, "response": [{"name": "Product Deleted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"prod_123\",\n  \"softDelete\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/products/delete", "host": ["{{baseUrl}}"], "path": ["api", "products", "delete"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"message\": \"Product deleted successfully\",\n  \"productId\": \"prod_123\",\n  \"deletedAt\": \"2024-01-20T16:00:00.000Z\",\n  \"softDelete\": true\n}"}]}], "description": "Basic product operations and queries"}, {"name": "Product Management Workflows", "item": [{"name": "Fetch All Products", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Products fetched successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('products');", "    pm.expect(jsonData.products).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"filters\": {\n    \"brandId\": \"{{brandId}}\",\n    \"status\": \"active\",\n    \"category\": \"Footwear\",\n    \"priceRange\": {\n      \"min\": 50,\n      \"max\": 200\n    },\n    \"inStock\": true\n  },\n  \"pagination\": {\n    \"limit\": 100,\n    \"offset\": 0\n  },\n  \"sort\": {\n    \"field\": \"updatedAt\",\n    \"order\": \"desc\"\n  },\n  \"includeMetadata\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products/all", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products", "all"]}, "description": "Fetch all products with advanced filtering and metadata."}, "response": [{"name": "All Products Fetched", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"acc_123\",\n  \"filters\": {\n    \"status\": \"active\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products/all", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products", "all"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"products\": [\n    {\n      \"id\": \"prod_123\",\n      \"name\": \"Athletic Running Shoes\",\n      \"sku\": \"ARS-001\",\n      \"brandId\": \"brand_123\",\n      \"category\": \"Footwear\",\n      \"status\": \"active\",\n      \"price\": {\n        \"amount\": 129.99,\n        \"currency\": \"USD\"\n      },\n      \"inventory\": {\n        \"inStock\": true,\n        \"quantity\": 150\n      }\n    }\n  ],\n  \"pagination\": {\n    \"total\": 1,\n    \"limit\": 100,\n    \"offset\": 0,\n    \"hasMore\": false\n  },\n  \"filters\": {\n    \"appliedFilters\": {\n      \"status\": \"active\"\n    }\n  },\n  \"fetchedAt\": \"2024-01-20T16:00:00.000Z\"\n}"}]}, {"name": "Fetch Products", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Products fetched with pagination', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('products');", "    pm.expect(jsonData).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"brandId\": \"{{brandId}}\",\n  \"pagination\": {\n    \"limit\": 20,\n    \"offset\": 0\n  },\n  \"search\": {\n    \"query\": \"running shoes\",\n    \"fields\": [\"name\", \"description\", \"keywords\"]\n  },\n  \"filters\": {\n    \"status\": \"active\",\n    \"hasImages\": true,\n    \"inStock\": true\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products"]}, "description": "Fetch products with search and pagination capabilities."}, "response": [{"name": "Products Fetched", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"acc_123\",\n  \"search\": {\n    \"query\": \"running shoes\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"products\": [\n    {\n      \"id\": \"prod_123\",\n      \"name\": \"Athletic Running Shoes\",\n      \"relevanceScore\": 0.95,\n      \"matchedFields\": [\"name\", \"keywords\"]\n    }\n  ],\n  \"pagination\": {\n    \"total\": 1,\n    \"limit\": 20,\n    \"offset\": 0\n  },\n  \"searchMetadata\": {\n    \"query\": \"running shoes\",\n    \"searchTime\": \"12ms\",\n    \"totalMatches\": 1\n  }\n}"}]}, {"name": "Product Setup Workflow", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Setup workflow completed', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('setupId');", "    pm.expect(jsonData).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"brandId\": \"{{brandId}}\",\n  \"products\": [\n    {\n      \"id\": \"{{productId}}\",\n      \"setupTasks\": [\n        \"generateKeywords\",\n        \"optimizeImages\",\n        \"createSEOMetadata\",\n        \"setupInventoryTracking\"\n      ]\n    }\n  ],\n  \"options\": {\n    \"generateContent\": true,\n    \"optimizeForRetailers\": [\"amazon\", \"walmart\"],\n    \"autoPublish\": false\n  },\n  \"priority\": \"high\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products/setup", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products", "setup"]}, "description": "Initiate a comprehensive product setup workflow."}, "response": [{"name": "Setup Workflow Initiated", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"products\": [\n    {\n      \"id\": \"prod_123\",\n      \"setupTasks\": [\"generateKeywords\", \"optimizeImages\"]\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products/setup", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products", "setup"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"setupId\": \"setup_789\",\n  \"status\": \"processing\",\n  \"products\": [\n    {\n      \"productId\": \"prod_123\",\n      \"tasks\": [\n        {\n          \"taskId\": \"task_001\",\n          \"type\": \"generateKeywords\",\n          \"status\": \"queued\",\n          \"estimatedCompletionTime\": \"2024-01-20T16:05:00.000Z\"\n        },\n        {\n          \"taskId\": \"task_002\",\n          \"type\": \"optimizeImages\",\n          \"status\": \"queued\",\n          \"estimatedCompletionTime\": \"2024-01-20T16:10:00.000Z\"\n        }\n      ]\n    }\n  ],\n  \"estimatedTotalTime\": \"15 minutes\",\n  \"createdAt\": \"2024-01-20T16:00:00.000Z\"\n}"}]}, {"name": "Batch Process Products", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Batch processing initiated', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('batchId');", "    pm.expect(jsonData).to.have.property('processedProducts');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"brandId\": \"{{brandId}}\",\n  \"productIds\": [\"{{productId}}\"],\n  \"operations\": [\n    {\n      \"type\": \"updateStatus\",\n      \"parameters\": {\n        \"status\": \"active\"\n      }\n    },\n    {\n      \"type\": \"updatePricing\",\n      \"parameters\": {\n        \"priceAdjustment\": {\n          \"type\": \"percentage\",\n          \"value\": 10\n        }\n      }\n    },\n    {\n      \"type\": \"updateKeywords\",\n      \"parameters\": {\n        \"action\": \"append\",\n        \"keywords\": [\"sale\", \"discount\", \"limited-time\"]\n      }\n    }\n  ],\n  \"options\": {\n    \"validateBeforeUpdate\": true,\n    \"notifyOnCompletion\": true,\n    \"rollbackOnError\": true\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products/batch-process", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products", "batch-process"]}, "description": "Process multiple products with batch operations."}, "response": [{"name": "Batch Processing Initiated", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productIds\": [\"prod_123\"],\n  \"operations\": [\n    {\n      \"type\": \"updateStatus\",\n      \"parameters\": {\n        \"status\": \"active\"\n      }\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products/batch-process", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products", "batch-process"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"batchId\": \"batch_456\",\n  \"status\": \"processing\",\n  \"processedProducts\": 0,\n  \"totalProducts\": 1,\n  \"operations\": [\n    {\n      \"operationId\": \"op_001\",\n      \"type\": \"updateStatus\",\n      \"status\": \"queued\"\n    }\n  ],\n  \"estimatedCompletionTime\": \"2024-01-20T16:05:00.000Z\",\n  \"createdAt\": \"2024-01-20T16:00:00.000Z\"\n}"}]}, {"name": "Update Product Metadata", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('<PERSON><PERSON><PERSON> updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"metadata\": {\n    \"seo\": {\n      \"metaTitle\": \"Updated Nike Air Zoom Pegasus - Athletic Running Shoes\",\n      \"metaDescription\": \"High-performance Nike Air Zoom Pegasus running shoes with advanced cushioning. Perfect for athletes and runners.\",\n      \"slug\": \"nike-air-zoom-pegasus-athletic-running-shoes\",\n      \"keywords\": [\"nike\", \"running\", \"shoes\", \"athletic\", \"pegasus\", \"air zoom\"]\n    },\n    \"attributes\": {\n      \"season\": \"All Season\",\n      \"targetAudience\": \"Athletes, Runners\",\n      \"ageGroup\": \"Adult\",\n      \"occasion\": \"Sports, Exercise, Running\"\n    },\n    \"marketing\": {\n      \"tags\": [\"bestseller\", \"new-arrival\", \"featured\"],\n      \"promotions\": [\n        {\n          \"type\": \"discount\",\n          \"value\": 15,\n          \"startDate\": \"2024-02-01\",\n          \"endDate\": \"2024-02-14\"\n        }\n      ]\n    },\n    \"compliance\": {\n      \"certifications\": [\"CE\", \"ISO 9001\"],\n      \"safetyStandards\": [\"ASTM F2913\"]\n    }\n  },\n  \"updateOptions\": {\n    \"validateMetadata\": true,\n    \"updateSearchIndex\": true,\n    \"notifyChannels\": true\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products/metadata", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products", "metadata"]}, "description": "Update comprehensive product metadata including SEO, attributes, and marketing data."}, "response": [{"name": "Metadata Updated", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"prod_123\",\n  \"metadata\": {\n    \"seo\": {\n      \"metaTitle\": \"Updated Nike Air Zoom Pegasus\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/product-mgmt/products/metadata", "host": ["{{baseUrl}}"], "path": ["api", "product-mgmt", "products", "metadata"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"productId\": \"prod_123\",\n  \"updatedFields\": [\n    \"seo.metaTitle\",\n    \"seo.metaDescription\",\n    \"seo.keywords\",\n    \"attributes.season\",\n    \"marketing.tags\"\n  ],\n  \"validationResults\": {\n    \"metadataValid\": true,\n    \"seoOptimized\": true,\n    \"complianceChecked\": true\n  },\n  \"searchIndexUpdated\": true,\n  \"updatedAt\": \"2024-01-20T16:30:00.000Z\"\n}"}]}], "description": "Advanced product management workflows and batch operations"}], "variable": [{"key": "baseUrl", "value": "http://localhost:5005", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "accountId", "value": "", "type": "string"}, {"key": "brandId", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}, {"key": "currentUserId", "value": "", "type": "string"}]}