# AdFury API - Complete Postman Collections

## 🎉 Overview

This directory contains **13 comprehensive Postman collections** that provide complete coverage of the AdFury API ecosystem. These collections include **100+ endpoints** covering all aspects of the creative advertising management platform.

## 📚 Collection List

### Core Collections
1. **01_Authentication_and_Session** - User authentication, login, logout, and session management
2. **02_User_Management** - Complete user operations, profiles, and user administration
3. **03_Email_Service** - Email service integration and email operations
4. **04_Accounts_Management** - Account operations, member management, and billing
5. **05_Brands_Management** - Brand operations, guidelines, and brand-specific settings
6. **06_Retailers_Management** - Retailer platform management and integrations

### Advanced Workflow Collections
7. **07_Creative_Content_Generation** - AI-powered content generation and creative workflows
8. **08_Product_Management** - Product operations, batch processing, and metadata management
9. **09_Asset_Management** - File uploads, asset organization, and asset workflows
10. **10_Keyword_Management** - Keyword operations, analytics, and optimization
11. **11_Project_Management** - Creative project workflows and project advancement
12. **12_Notifications_Management** - Notification system and user alerts
13. **13_Settings_Credentials** - Settings, credentials, and API key management

### Legacy Collections (Reference)
- **AdFury Email Service.postman_collection.json** - Original email service collection
- **AdFury REST API.postman_collection.json** - Original REST API collection

## 🚀 Quick Start

### 1. Import Collections
1. Open Postman
2. Click **Import** 
3. Select **Folder** and choose the `postman_collections` directory
4. All 13 collections will be imported automatically

### 2. Environment Setup
Create a new environment in Postman with these variables:

```
baseUrl = http://localhost:5005
email_service_url = http://localhost:5555
authToken = [Your JWT token from login]
adminToken = [Admin JWT token if needed]
n8nSecret = [N8N workflow secret if needed]

# Resource IDs (will be auto-populated)
currentUserId = 
accountId = 
brandId = 
productId = 
projectId = 
creativeId = 
assetId = 
keywordId = 
notificationId = 
credentialId = 

# Test Data
testEmail = <EMAIL>
testPassword = TestPassword123!
betaCode = BETA2024
```

### 3. Authentication Flow
1. **Start with Collection 01**: Run `User Login` to get your auth token
2. **Set Environment**: Copy the returned token to your `authToken` variable
3. **Verify Access**: Run `Get Current User Profile` to confirm authentication
4. **Explore APIs**: All other collections will now work with your token

## 📋 Collection Details

### 🔐 Authentication & Session (Collection 01)
- **Endpoints**: 4 endpoints
- **Features**: Login, signup, logout, profile management
- **Rate Limiting**: Built-in rate limiting documentation
- **Security**: Bearer token authentication patterns

### 👥 User Management (Collection 02)  
- **Endpoints**: 20+ endpoints
- **Features**: User CRUD, profile updates, plugin connections
- **Admin Functions**: User administration and role management
- **Enhanced Features**: Brand assignments, retailer connections

### 📧 Email Service (Collection 03)
- **Endpoints**: 15+ endpoints
- **Features**: Email sending, templates, testing
- **Integration**: Main API + dedicated email service
- **Testing**: Email queue status and health checks

### 🏢 Accounts Management (Collection 04)
- **Endpoints**: 12+ endpoints  
- **Features**: Account operations, member management
- **Billing**: Stripe integration and subscription management
- **Team Management**: Role-based access control

### 🎨 Brands Management (Collection 05)
- **Endpoints**: 15+ endpoints
- **Features**: Brand operations, guidelines upload
- **Asset Management**: Brand-specific asset organization
- **Team Access**: Brand-level user assignments

### 🛒 Retailers Management (Collection 06)
- **Endpoints**: 5+ endpoints
- **Features**: Retailer platform integrations
- **Platforms**: Amazon, Walmart, Target, and more
- **Credentials**: Retailer-specific API management

### ⚡ Creative Content Generation (Collection 07)
- **Endpoints**: 15+ endpoints
- **Features**: AI-powered content generation
- **Workflows**: Generation → Creative → Approval
- **Versioning**: Generation version control and history
- **Analytics**: Performance tracking and optimization

### 📦 Product Management (Collection 08)
- **Endpoints**: 8+ endpoints
- **Features**: Product operations, batch processing
- **Workflows**: Product setup automation
- **Metadata**: Comprehensive product data management
- **SEO**: Search optimization and keyword integration

### 📁 Asset Management (Collection 09)
- **Endpoints**: 12+ endpoints
- **Features**: File uploads, asset organization
- **Processing**: Automatic thumbnail and variant generation
- **Search**: Advanced asset search and filtering
- **Workflows**: Bulk upload and batch processing

### 🔑 Keyword Management (Collection 10)
- **Endpoints**: 8+ endpoints
- **Features**: Keyword operations, analytics
- **AI Research**: Keyword suggestion and optimization
- **Analytics**: Performance tracking and insights
- **Strategy**: AI-powered optimization recommendations

### 📊 Project Management (Collection 11)
- **Endpoints**: 12+ endpoints
- **Features**: Creative project workflows
- **Setup**: Creative setup utilities and access management
- **Advancement**: Project step progression and approval
- **Data**: Project analytics and review systems

### 🔔 Notifications Management (Collection 12)
- **Endpoints**: 7+ endpoints
- **Features**: Notification system management
- **Real-time**: User alerts and status updates
- **Management**: Read/unread status and notification cleanup
- **Targeting**: User-specific notification delivery

### ⚙️ Settings & Credentials (Collection 13)
- **Endpoints**: 15+ endpoints
- **Features**: Settings and credential management
- **API Keys**: Secure credential storage and management
- **Beta Program**: Beta user management and invitations
- **Debug**: Development and debugging utilities

## Usage Instructions

1. **Import Collections**: Import the JSON files into Postman
2. **Configure Environment**: Set up environment variables:
   - `baseUrl`: Your API base URL (default: `http://localhost:5005`)
   - `authToken`: Will be automatically set after login
   - Other IDs will be populated as you use the collections

3. **Authentication Flow**:
   - Start with the Authentication collection
   - Run "Sign Up" or "Login" to get an auth token
   - The token will be automatically used in subsequent requests

4. **Collection Variables**: Each collection includes its own variables for common IDs

## Environment Setup

Create a Postman environment with these variables:
```json
{
  "baseUrl": "http://localhost:5005",
  "authToken": "",
  "userId": "",
  "accountId": "",
  "brandId": "",
  "retailerId": "",
  "projectId": "",
  "creativeId": ""
}
```

## Best Practices

1. **Run Authentication First**: Always authenticate before using other collections
2. **Use Collection Runner**: Test entire workflows using Postman's Collection Runner
3. **Check Response Examples**: Each request includes example responses for reference
4. **Update Variables**: Keep environment variables updated as you create resources
5. **Test Scripts**: Requests include test scripts to validate responses

## Contributing

When adding new endpoints:
1. Place them in the appropriate collection
2. Include detailed descriptions
3. Add request/response examples
4. Include test scripts
5. Document any required variables

## Original Collections Reference

- **AdFury Email Service.postman_collection.json**: Original email service collection
- **AdFury REST API.postman_collection.json**: Original comprehensive API collection

These original collections served as reference for creating the organized, detailed collections above.