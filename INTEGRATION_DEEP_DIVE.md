# Integration Deep Dive

This document provides a thorough, code‑level breakdown of how the four related codebases interact:
- **advid-app** ↔ **advid-server**
- **advid-app** ↔ **asset-generator**
- **advid-(app & server)** ↔ **advid-wm-service**
- **advid-server** ↔ **adfury-email-service**

For each flow, we list environment variables, API proxy routes (in advid-app), service endpoints (in the target repo), and the key code snippets wiring them together.

---

## 1. advid-app ↔ advid-server

### 1.1 Environment Variables
In `advid-app/.env.local`, advid-app calls the backend API:
```properties
NEXT_PUBLIC_API_URL='http://localhost:3000'
NEXT_PUBLIC_WS_URL='ws://localhost:5005'
```
【F:advid-app/.env.local†L17-L20】

### 1.2 HTTP Proxies in advid-app
Under `app/api/`, Next.js route files forward client requests to advid-server:

| Feature           | Proxy route file                                             | advid-server endpoint             |
|-------------------|--------------------------------------------------------------|------------------------------------|
| Authentication    | `app/api/auth/.../route.ts`                                  | `/api/auth/*`                      |
| Users             | `app/api/users/.../route.ts`                                 | `/api/users/*`                     |
| Brands            | `app/api/brands/.../route.ts`                                | `/api/brands/*`                    |
| Creatives         | `app/api/creatives/.../route.ts`                             | `/api/creatives/*`                 |
| WMT Display (to WM)| `app/api/wmt-display/creatives/*/route.ts`                  | `/display/creatives/*` (WMT svc)   |
| ...               | (many other modules)                                         | `/api/...`                         |

Example fetching creatives:
```ts
// advid-app/app/actions/creative.actions.ts
const response = await fetch(
  `${process.env.NEXT_PUBLIC_API_URL}/api/creatives/get-creatives`,
  { method: 'POST', headers: {...}, body: JSON.stringify(params) }
);
```
【F:advid-app/app/actions/creative.actions.ts†L103-L110】

### 1.3 Route Definitions in advid-server
In the server, each module registers its own routes.  E.g. creatives:
```js
// advid-server/src/routes/creatives/creatives.routes.js
fastify.post('/api/creatives/get-creatives', {
  onRequest: [fastify.verifyAuth], handler: getCreatives
});
```
【F:src/routes/creatives/creatives.routes.js†L1-L8】【F:src/routes/creatives/creatives.routes.js†L19-L27】

Controller logic lives in `creatives.controllers.js` alongside the route.

---

## 2. advid-app ↔ asset-generator

### 2.1 Environment Variables
```properties
BASE_ASSET_URL='http://localhost:3001'
NEXT_PUBLIC_BASE_ASSET_URL='http://localhost:3001'
```
【F:advid-app/.env.local†L11-L14】

### 2.2 OpenAI‑Based Copy Generation

```ts
// advid-app/app/api/create-headline/route.js
export async function POST(request) {
  const { product_title, keyword } = await request.json();
  const response = await fetch(
    `${process.env.BASE_ASSET_URL}/api/openai/generate-headline`,
    { method: 'POST', headers: {'Content-Type':'application/json'}, body: JSON.stringify({product_title,keyword}) }
  );
  return NextResponse.json(await response.json());
}
```
【F:advid-app/app/api/create-headline/route.js†L6-L17】

```js
// asset-generator/routes/openaiRoutes.js
router.post('/generate-headline', async (req,res) => { … });
router.post('/generate-content', async (req,res) => { … });
router.post('/generate-subheadline', async (req,res) => { … });
```
【F:asset-generator/routes/openaiRoutes.js†L1-L45】

### 2.3 Image & Template Processing

```ts
// advid-app/app/api/generate/route.ts
const response = await fetch(
  `${process.env.BASE_ASSET_URL}/api/images/process-templates`,
  { method: 'POST', body: processFormData }
);
// stream SSE back to client…
```
【F:advid-app/app/api/generate/route.ts†L27-L41】

```js
// asset-generator/routes/imageRoutes.js
router.post('/process-templates', …);
router.post('/crop-image', …);
router.post('/remove-background', …);
```
【F:asset-generator/routes/imageRoutes.js†L1-L20】

### 2.4 Real‑Time “Express Generation” Workflow

```ts
// advid-app/app/api/express-generation/[sessionId]/start/route.ts
await fetch(
  `${process.env.BASE_ASSET_URL}/api/expressGeneration/express-generation`,
  { method: 'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({sessionId,sessionData}) }
);
```
【F:advid-app/app/api/express-generation/[sessionId]/start/route.ts†L32-L46】

```js
// asset-generator/routes/expressGenerationRoutes.js
router.post('/express-generation', async (req,res)=>{ … });
router.post('/callback/:sessionId', async (req,res)=>{ … });
```
【F:asset-generator/routes/expressGenerationRoutes.js†L1-L29】【F:asset-generator/routes/expressGenerationRoutes.js†L59-L83】

---

## 3. advid-(app & server) ↔ advid-wm-service (Walmart)

### 3.1 Environment Variables
```properties
# advid-app/.env.local (proxy)
WALMART_MANAGER_API_URL=http://localhost:3006

# advid-server/.env
WALMART_MANAGER_API_URL=http://localhost:3006
```
【F:advid-app/.env.local†L31-L34】【F:src/routes/settings/settings.controllers.js†L12-L20】

### 3.2 Proxy Routes in advid-app

Under `app/api/wmt-display/` the frontend proxies calls to advid-wm-service:
```bash
app/api/wmt-display/creatives/get-creatives/route.ts
app/api/wmt-display/creatives/create/route.ts
app/api/wmt-display/campaigns/create/route.ts
app/api/wmt-display/ad-groups/create/route.ts
…
```
【F:advid-app/app/api/wmt-display/creatives/get-creatives/route.ts†L1-L20】

Example get‑creatives proxy logic with custom 0‑case handling:
```ts
// …/get-creatives/route.ts
const response = await fetch(
  `${process.env.WALMART_MANAGER_API_URL}/display/creatives/list`,
  { method:'POST',headers:{Authorization, 'Content-Type':'application/json'},body:JSON.stringify({advertiserId, Filter[creativeId]:creativeIdFilter}) }
);
// handle UNABLE_TO_FETCH_CREATIVE_DETAIL → empty array on 200
```
【F:advid-app/app/api/wmt-display/creatives/get-creatives/route.ts†L34-L91】

### 3.3 advid-server Direct Calls to Walmart

In `settings.controllers.js`, the server validates and stores brands via Walmart’s API:
```js
// Validate a Walmart advertiser ID
const response = await axios.post(
  `${process.env.WALMART_MANAGER_API_URL}/display/catalog/brands/list/`,
  { advertiserId: wmAdvertiserId },
  { headers:{Authorization, 'x-account-id':accountId} }
);
```
【F:src/routes/settings/settings.controllers.js†L12-L20】【F:src/routes/settings/settings.controllers.js†L55-L69】

And in the same file, store and link brands/credentials:
```js
await db.insert(brands).values({accountId, name:brandData.brandName,…});
await db.insert(brandRetailers).values({brandId, retailerId});
await db.insert(credentialSetRetailers).values({credentialSetId, retailerId});
```
【F:src/routes/settings/settings.controllers.js†L90-L140】

---

## 4. advid-server ↔ adfury-email-service (Email Queue)

### 4.1 Environment Variables
```properties
QUEUE_REDIS_URL=<redis://email-queue-instance>
RESEND_API_KEY=<your-resend-key>
```
【F:src/services/emailQueue.service.ts†L22-L30】【F:adfury-email-service/README.md†L30-L55】

### 4.2 Producer: enqueuing jobs in advid-server
```ts
// src/routes/email/email.routes.js
await emailQueue.addInvitationEmail({ to, invitationId, … });
await emailQueue.addPasswordResetEmail({ to, resetUrl, … });
await emailQueue.addCustomEmail({ to, subject, content, … });
```
【F:src/routes/email/email.routes.js†L85-L100】【F:src/routes/email/email.routes.js†L137-L154】

```ts
// src/services/emailQueue.service.ts (queue init)
const redis = new IORedis(process.env.QUEUE_REDIS_URL);
this.queue = new Queue('email-processing',{connection:redis,defaultJobOptions:{attempts:3,backoff:{type:'exponential',delay:5000}}});
```
【F:src/services/emailQueue.service.ts†L18-L26】【F:src/services/emailQueue.service.ts†L105-L113】

### 4.3 Consumer: adfury-email-service
```js
// email.service.js (processing jobs)
emailQueue.process(async (job) => {
  await resendClient.send({to:job.data.to,subject:job.data.subject,html:job.data.content});
});
```
【F:adfury-email-service/dist/services/email.service.js†L45-L60】

**Health & Metrics** exposed:
```http
GET /health
GET /health/ready
GET /metrics
GET /metrics/queue
```
【F:adfury-email-service/README.md†L60-L75】

---

This completes the deep dive—up to the exact files, lines, and code snippets wiring each service pair together.  Let me know if you need deeper calls‑stack or protocol analysis! 