# ADVID Server

# AdVid Server

A robust Node.js server built with Fastify, featuring Auth0 authentication, PostgreSQL database integration via Drizzle ORM, and Docker support. Used for the ADVID platform.

This server is the backend for the AdVid application, handling user authentication, data persistence, and the core business logic for generating ad creatives.

-   [Architecture Overview](./ARCHITECTURE.md)
-   [API Module Documentation](./src/routes/)

---

## 🚀 Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

-   [Node.js](https://nodejs.org/) (v20.x or later recommended)
-   [Docker](https://www.docker.com/) and Docker Compose
-   A running PostgreSQL database
-   A running Redis instance

While you can run Postgres and Redis natively, the simplest way to get started is to use Docker. A `docker-compose.yml` is provided for this purpose.

### 1. Clone the repository

```bash
git clone https://github.com/AdVid-ai/advid-server.git
cd advid-server
```

### 2. Install dependencies

```bash
npm install
```

### 3. Set up environment variables

Create a `.env` file in the root of the project by copying the example below. You will need to fill in the values for your local environment and from your Auth0, Google Cloud, and other service configurations.

```bash
cp .env.example .env
# Now, edit .env with your specific values
```

### 4. Start dependent services

If you are using Docker for your database and Redis, start them now:

```bash
docker-compose up -d
```
*(Note: The provided `docker-compose.yml` only defines the `app` service. You will need to add `postgres` and `redis` services to it or run them separately.)*

### 5. Run database migrations

This will apply the latest database schema to your Postgres instance.

```bash
npm run migrate:run
```

### 6. Seed the database (optional but recommended)

This will populate your database with initial data for testing and development.

```bash
npm run db:seed
```

### 7. Run the development server

This will start the Fastify server with hot-reloading on file changes.

```bash
npm run dev
```

The server should now be running on `http://localhost:3000` (or the port specified in your `.env` file).

---

## ⚙️ Configuration

The server is configured using environment variables defined in a `.env` file.

### Required Variables

-   `DATABASE_URL`: The connection string for your PostgreSQL database. (e.g., `postgres://user:password@localhost:5432/advid_db`)
-   `REDIS_URL`: The connection string for your primary Redis instance (used for sessions). (e.g., `redis://localhost:6379`)
-   `AUTH0_DOMAIN`: Your Auth0 domain.
-   `AUTH0_CLIENT_ID`: The Client ID of your Auth0 application.
-   `AUTH0_CLIENT_SECRET`: The Client Secret of your Auth0 application.
-   `AUTH0_AUDIENCE`: The audience identifier for your Auth0 API.
-   `JWT_SECRET`: A long, random string used to sign JWTs.
-   `SESSION_SECRET`: A long, random string used to sign session cookies.

### Optional Variables

-   `PORT`: The port the server will run on. Defaults to `3000`.
-   `NODE_ENV`: The environment mode. Defaults to `development`. Set to `production` for production builds.
-   `LOG_LEVEL`: The logging level. Defaults to `INFO`.
-   `QUEUE_REDIS_URL`: The connection string for the Redis instance used by the BullMQ email queue. Can be the same as `REDIS_URL` but a separate instance is recommended.
-   `GOOGLE_CLOUD_PROJECT_ID`: Project ID for Google Cloud Storage.
-   `GOOGLE_CLOUD_CLIENT_EMAIL`: Service account email for GCP.
-   `GOOGLE_CLOUD_PRIVATE_KEY`: Private key for the GCP service account.
-   `WALMART_MANAGER_API_URL`: The base URL for the external Walmart Manager API.
-   `AUTH0_PASSWORD_RESET_REDIRECT_URL`: The URL users are redirected to after a password reset.
-   `AUTH0_DB_CONNECTION_ID`: The ID of your Auth0 database connection.
-   `RESEND_API_KEY`: API key for the Resend email service.

---

## 🧪 Running Tests

This project uses Postman/Newman for API smoke testing.

To run the test suite:

```bash
npm run test:smoke
```

This command will execute the Postman collection defined in `tests/postman/` and generate an HTML report in `tests/report/postman/`. 

## Features

- 🔐 **Security Hardened Auth0 Authentication** - Strict token validation with Redis session management
- 📦 **Optimized PostgreSQL Database** - Drizzle ORM with connection pooling and performance indexes
- 🚀 **High-Performance Fastify Server** - Rate limiting, structured logging, and comprehensive monitoring  
- 🐳 **Docker Support** - Full containerization with development and production configurations
- 🔄 **TypeScript Support** - Full type safety with comprehensive JSDoc documentation
- 📝 **Database Migrations & Optimization** - Automated migrations with 25+ performance indexes
- 🔑 **Enhanced JWT Handling** - Secure token management with Redis-backed sessions
- 🍪 **Secure Cookie Management** - HttpOnly cookies with proper security headers
- 📧 **Queue-Based Email Service** - Redis queue integration with comprehensive error handling
- 📊 **Performance Monitoring** - Built-in metrics, alerting, and health scoring system

## Prerequisites

- Node.js 20+
- PostgreSQL database (or Neon.tech account)
- Auth0 account
- Docker (optional)

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```bash
DATABASE_URL=<your_database_url>
AUTH0_DOMAIN=<your_auth0_domain>
AUTH0_CLIENT_ID=<your_auth0_client_id>
AUTH0_CLIENT_SECRET=<your_auth0_client_secret>
AUTH0_AUDIENCE=<your_auth0_audience>
JWT_SECRET=<your_jwt_secret>
COOKIE_SECRET=<your_cookie_secret>
PORT=5005

# Security Configuration (Production Required)
SESSION_SECRET=<strong_session_secret_32_chars_minimum>
JWT_SECRET=<strong_jwt_secret>

# Email Service Configuration
QUEUE_REDIS_URL=<redis_url_for_email_queue>
RESEND_API_KEY=<your_resend_api_key>
AUTH0_PASSWORD_RESET_REDIRECT_URL=<password_reset_redirect_url>
AUTH0_DB_CONNECTION_ID=<auth0_database_connection_id>

# Performance & Monitoring (Optional)
LOG_LEVEL=info
REDIS_URL=<session_redis_url>
```

## Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Run database migrations:

```bash
npm run migrate
```

4. Apply performance optimizations (recommended for production):

```bash
# Apply database indexes for optimal performance
# Run this SQL file against your PostgreSQL database
psql $DATABASE_URL -f src/db/indexes.sql
```

5. Seed the database with demo data:

```bash
npm run db:seed
```

## Database Seeding

After running migrations, you can seed the database with comprehensive demo data:

```bash
# Seed database with realistic demo data (recommended)
npm run db:seed

# Seed without clearing existing data
npm run db:seed:no-clear

# View all seeding options and commands
npm run db:seed:custom:help
```

The seeder creates realistic brands, products, projects, and workflows with:
- Professional brand names and descriptions
- Contextual product categories and specifications
- Realistic campaign structures and timelines
- Meaningful creative review workflows

For detailed seeding documentation, see [src/db/seeder/README.md](src/db/seeder/README.md).

## Development

Start the development server:

```bash
npm run dev
```

## Docker

### Build and run with Docker

```bash
npm run docker:build
npm run docker:run
```

## API Routes

### Authentication

- `POST /auth/signup` - Register a new user
- `POST /auth/login` - Login user
- `POST /auth/logout` - Logout user
- `GET /auth/me` - Get current user info (protected)

### Users

- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Accounts
- `GET /api/accounts` - Get all accounts
- `GET /api/accounts/:id` - Get account by ID
- `POST /api/accounts` - Create new account
- `PUT /api/accounts/:id` - Update account
- `PUT /api/accounts/:id/delete` - Soft delete account
- `DELETE /api/accounts/:id` - Hard delete account

### Organizations

- `GET /api/organizations` - Get all organizations
- `GET /api/organizations/:id` - Get organization by ID
- `POST /api/organizations` - Create organization
- `PUT /api/organizations/:id` - Update organization
- `DELETE /api/organizations/:id` - Delete organization

### Brands

- `GET /api/brands` - Get all brands
- `GET /api/brands/:id` - Get brand by ID
- `POST /api/brands` - Create brand
- `PUT /api/brands/:id` - Update brand
- `DELETE /api/brands/:id` - Delete brand

### Ad Groups
- `GET /api/ad-groups` - Get all ad groups
- `GET /api/ad-groups/:id` - Get ad group by ID
- `POST /api/ad-groups` - Create new ad group
- `PUT /api/ad-groups/:id` - Update ad group
- `DELETE /api/ad-groups/:id` - Delete ad group

### Email Service

The ADVID server integrates with the AdFury Email Service through a Redis queue-based architecture for reliable, scalable email processing.

#### Email System Architecture

```mermaid
graph LR
    A[advid-app] --> B[advid-server]
    B --> C[Redis Queue]
    C --> D[adfury-email-service]
    D --> E[Resend API]
    E --> F[User Email]
    
    subgraph "ADVID Frontend"
        A
    end
    
    subgraph "ADVID Backend"
        B
    end
    
    subgraph "Email Infrastructure"
        C
        D
        E
    end
```

#### Email Flow Process

```mermaid
sequenceDiagram
    participant App as advid-app
    participant Server as advid-server
    participant Queue as Redis Queue
    participant Email as adfury-email-service
    participant User as User Email
    
    App->>Server: POST /api/email/send-custom
    Server->>Server: Authenticate & validate
    Server->>Queue: Publish email job
    Server->>App: {"success": true, "message": "Email queued"}
    
    Note over Queue,Email: Background Processing
    Email->>Queue: Poll for jobs
    Queue->>Email: Email job data
    Email->>Email: Build HTML template
    Email->>Email: Send via Resend API
    Email->>User: Deliver email
    Email->>Queue: Mark job complete
```

#### Email API Endpoints

- `POST /api/email/send-custom` - Send custom email with flexible content
- `POST /api/email/test-custom` - Send test custom email
- `POST /api/email/test-invitation` - Send test invitation email
- `POST /api/email/test-password-reset` - Send test password reset email
- `POST /api/email/test-beta-invitation` - Send test beta invitation email
- `GET /api/email/queue-status` - Get email queue statistics
- `GET /api/email/health` - Check email service health

#### Email Types Supported

**1. Custom Emails**
Flexible emails with custom subject, content, buttons, and styling
```typescript
POST /api/email/send-custom
{
  "to": "<EMAIL>",
  "subject": "Welcome to ADVID",
  "content": "<p>Welcome to our platform!</p>",
  "heading": "Get Started Today",
  "buttonText": "Login Now",
  "buttonLink": "https://app.adfury.ai/login",
  "footerText": "Questions? Reply to this email",
  "fromName": "ADVID Team",
  "replyTo": "<EMAIL>"
}
```

**2. Invitation Emails**
Team member invitations and join requests
```typescript
POST /api/email/test-invitation
{
  "to": "<EMAIL>",
  "invitationType": "invite",
  "invitationId": "inv-uuid-123",
  "organizationName": "Acme Corp",
  "senderName": "John Doe"
}
```

**3. Password Reset Emails**
Secure password reset links
```typescript
POST /api/email/test-password-reset
{
  "to": "<EMAIL>",
  "resetUrl": "https://app.adfury.ai/reset-password?token=secure-token"
}
```

**4. Beta Invitation Emails**
Beta program invitations with personal messages
```typescript
POST /api/email/test-beta-invitation
{
  "to": "<EMAIL>",
  "senderName": "Jane Smith",
  "senderEmail": "<EMAIL>",
  "betaCode": "BETA2024",
  "personalMessage": "Excited to have you try our new features!"
}
```

#### Integration with advid-app

The React frontend integrates with the email system through authenticated API calls:

```typescript
// In advid-app: hooks/useEmailService.ts
export function useEmailService() {
  const { getAccessToken } = useAuth0();
  
  const sendInvitation = async (invitationData: InvitationData) => {
    const token = await getAccessToken();
    
    const response = await fetch('/api/email/test-invitation', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(invitationData)
    });
    
    if (!response.ok) {
      throw new Error('Failed to send invitation');
    }
    
    return response.json();
  };
  
  const sendCustomEmail = async (emailData: CustomEmailData) => {
    const token = await getAccessToken();
    
    const response = await fetch('/api/email/send-custom', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(emailData)
    });
    
    if (!response.ok) {
      throw new Error('Failed to send email');
    }
    
    return response.json();
  };
  
  return { sendInvitation, sendCustomEmail };
}

// Example component usage
function InviteTeamMember() {
  const { sendInvitation } = useEmailService();
  const [isLoading, setIsLoading] = useState(false);
  
  const handleInvite = async (email: string) => {
    setIsLoading(true);
    try {
      await sendInvitation({
        to: email,
        invitationType: 'invite',
        organizationName: 'My Company',
        senderName: 'John Doe'
      });
      toast.success('Invitation sent successfully!');
    } catch (error) {
      toast.error('Failed to send invitation');
    } finally {
      setIsLoading(false);
    }
  };
  
  return <InviteForm onSubmit={handleInvite} loading={isLoading} />;
}
```

#### Queue-Based Architecture Benefits

**Performance**
- API responses return immediately (~1ms)
- Email processing happens in background
- No blocking operations in main server

**Reliability**
- Jobs persist in Redis until processed
- Automatic retries with exponential backoff
- Failed jobs are preserved for debugging

**Scalability**
- Email service can be scaled independently
- Handle high email volumes without affecting main server
- Queue acts as buffer during traffic spikes

#### Custom Email Parameters

- `to` (required): Recipient email address
- `subject` (required): Email subject line
- `content` (required): Main email content (supports HTML)
- `heading` (optional): Main heading in email
- `buttonText` (optional): Call-to-action button text
- `buttonLink` (optional): Call-to-action button URL
- `footerText` (optional): Custom footer text
- `fromName` (optional): Custom sender name (default: "AdFury Team")
- `replyTo` (optional): Reply-to email address

#### Email Service Configuration

**Required Environment Variables:**
```bash
# Email Queue Configuration
QUEUE_REDIS_URL=redis://email-queue-redis:6379  # Shared with adfury-email-service
RESEND_API_KEY=your_resend_api_key               # For direct API calls (if needed)

# Auth0 Configuration (for password reset)
AUTH0_PASSWORD_RESET_REDIRECT_URL=https://app.adfury.ai/reset-password
AUTH0_DB_CONNECTION_ID=your_auth0_database_connection_id
```

#### Email Queue Monitoring

Monitor email queue health and performance:

```typescript
// Check queue status
GET /api/email/queue-status
Response: {
  "waiting": 0,
  "active": 1,
  "completed": 152,
  "failed": 2,
  "delayed": 0
}

// Check email service health
GET /api/email/health
Response: {
  "status": "healthy",
  "redis": "connected",
  "queue": "active",
  "worker": "running"
}
```

#### Email Templates

The email service uses responsive HTML templates with:
- Professional styling with AdFury branding
- Mobile-responsive design
- Customizable content areas
- Call-to-action buttons
- Footer with unsubscribe links

#### Error Handling

The system includes comprehensive error handling:
- **Validation errors**: Invalid email data returns 400 with details
- **Authentication errors**: Missing/invalid JWT returns 401
- **Queue errors**: Redis connection issues return 500
- **Rate limiting**: Built-in protection against spam

#### Testing Email Integration

```bash
# Test invitation email
curl -X POST https://your-server.railway.app/api/email/test-invitation \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "invitationType": "invite",
    "organizationName": "Test Company",
    "senderName": "Test User"
  }'

# Test custom email
curl -X POST https://your-server.railway.app/api/email/send-custom \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Welcome to ADVID",
    "content": "<p>Welcome to our platform!</p>",
    "buttonText": "Get Started",
    "buttonLink": "https://app.adfury.ai"
  }'
```

## Database Schema

### Users
- id (UUID, primary key)
- auth0_id (text, unique)
- email (text, unique)
- name (text)
- account_id (UUID, foreign key)
- created_at (timestamp)
- updated_at (timestamp)

### Organizations
- id (UUID, primary key)
- account_id (UUID, foreign key)
- name (text)
- created_at (timestamp)
- updated_at (timestamp)

### Accounts
- id (UUID, primary key)
- name (text)
- created_at (timestamp)
- updated_at (timestamp)

### Brands
- id (UUID, primary key)
- organization_id (UUID, foreign key)
- name (text)
- created_at (timestamp)
- updated_at (timestamp)

### User Organizations (Junction Table)
- user_id (UUID, foreign key)
- organization_id (UUID, foreign key)
- Primary Key: (user_id, organization_id)

## Scripts

```json
{
  "start": "node dist/app.js",
  "dev": "tsx watch app.ts",
  "build": "tsc",
  "migrate": "drizzle-kit generate:sqlite && node src/db/migrations/run.js",
  "migrate:generate": "drizzle-kit generate",
  "migrate:run": "npx drizzle-kit up",
  "db:push": "drizzle-kit push",
  "docker:build": "docker build -t advid-server .",
  "docker:run": "docker run -p 5005:5005 advid-server",
  "docker:compose": "docker-compose up",
  "docker:compose:build": "docker-compose up --build"
}
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Performance Optimizations

### Database Performance
- **Connection Pooling**: Configured Neon HTTP client with optimized connection settings
- **25+ Strategic Indexes**: See `src/db/indexes.sql` for foreign keys, composite indexes, and query-specific optimizations
- **Query Optimization**: Identified and documented N+1 query patterns and batch optimization opportunities

### Security Hardening
- **Removed Hardcoded Secrets**: Eliminated dangerous secret bypass in authentication
- **Enhanced Rate Limiting**: Multi-level rate limiting on authentication and API endpoints
- **Input Sanitization**: Comprehensive validation and sanitization across all inputs
- **Structured Logging**: Security-focused logging with PII masking and audit trails

### Monitoring & Reliability
- **Environment Validation**: Comprehensive startup validation with meaningful error messages
- **Connection Monitoring**: Enhanced Redis connection management with retry strategies
- **Performance Metrics**: Built-in request tracking and performance monitoring
- **Health Checks**: Multi-level health checking for external dependencies

### Key Files
- `src/db/indexes.sql` - Performance indexes for production deployment
- `src/utils/validateEnv.ts` - Environment validation with security checks
- `src/utils/logger.ts` - Structured logging with PII protection
- `src/plugins/auth.plugin.js` - Hardened authentication with token validation

## License

ISC
