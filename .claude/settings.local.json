{"permissions": {"allow": ["Bash(npm run build:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(npm run:*)", "Bash(npx tsx:*)", "<PERSON><PERSON>(jq:*)", "Bash(rg:*)", "Bash(npm audit:*)", "Bash(find:*)", "Bash(npm uninstall:*)", "mcp__sequential-thinking__sequentialthinking", "WebFetch(domain:docs.railway.com)", "<PERSON><PERSON>(mkdir:*)", "Bash(bun run:*)", "Bash(npx tsc:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm init:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(pkill:*)", "Bash(npx depcheck:*)", "Bash(npm install:*)", "Bash(npm ls:*)", "Bash(git add:*)", "Bash(timeout 10s npm run dev:*)", "Bash(git commit -m \"$(cat <<''EOF''\nfeat: enhance logging infrastructure and code quality\n\n- Replace console.log with structured logger across codebase\n- Fix import path inconsistencies (.js/.ts extensions)\n- Optimize database connection pooling and monitoring\n- Add health check functionality for database connections\n- Improve seeder logging with structured format\n- Enhance environment validation with security checks\n- Update log levels for better development experience\n\nPerformance improvements:\n- Enhanced Neon connection configuration\n- Query monitoring and debugging support\n- Optimized connection timeouts and pooling\n\nCode quality improvements:\n- Consistent import paths across route handlers\n- Structured logging with context and metadata\n- Better error handling and monitoring\nEOF\n)\")", "Bash(npm test)", "Bash(git fetch:*)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(python3:*)", "Bash(for:*)", "Bash(do echo \"=== $file ===\")", "Bash(/dev/null)", "Bash(done)"], "deny": []}}