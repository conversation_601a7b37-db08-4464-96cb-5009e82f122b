{"sessionId": "d4699606-b2f3-4f67-af6a-04159b4c86a9", "currentStep": 0, "projectId": "d4699606-b2f3-4f67-af6a-04159b4c86a9", "brands": [{"id": "d05a7873-93df-4c93-bb3a-5b0d0c7a120b", "name": "UrbanPro Style", "description": "Modern urban fashion and accessories", "colors": ["#84CC16", "#65A30D", "#BEF264"], "logoAssetUrl": "https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_10_26%20AM.png", "productCount": 111, "userRole": "owner", "createdAt": "2025-07-08T20:50:27.452Z", "updatedAt": "2025-07-08T20:50:27.452Z", "retailer": {"id": "f8b45195-507d-41e6-93c2-ef49aa5a3487", "name": "Walmart", "slug": "walmart", "logoUrl": "https://loremflickr.com/537/3831/business?lock=4770750670789307", "url": "https://walmart.com"}, "brandGuidelines": null}], "selectedKeywords": [], "selectedCampaignAdGroups": [], "isReadOnly": false, "projectData": {"id": "d4699606-b2f3-4f67-af6a-04159b4c86a9", "brandId": null, "retailerId": "f8b45195-507d-41e6-93c2-ef49aa5a3487", "name": "Test Express One", "wmCampaigns": {}, "currentStep": 0, "createdAt": "2025-07-13T03:24:38.096Z", "updatedAt": "2025-07-13T03:24:38.096Z", "creator": {"id": "681d85e5-3294-4c70-82f3-57c2b4265e27", "name": "<PERSON>", "email": "<EMAIL>"}, "brand": null, "retailer": {"id": "f8b45195-507d-41e6-93c2-ef49aa5a3487", "name": "Walmart", "slug": "walmart", "logoUrl": "https://loremflickr.com/537/3831/business?lock=4770750670789307", "url": "https://walmart.com"}, "credentialSet": null, "generationsCount": 0}, "existingProjectData": {"project": {"id": "d4699606-b2f3-4f67-af6a-04159b4c86a9", "name": "Test Express One", "currentStep": 0, "wmCampaigns": {}}, "brand": null, "product": null, "keywords": [], "campaigns": [], "adGroups": []}, "selectedBrandId": "d05a7873-93df-4c93-bb3a-5b0d0c7a120b", "selectedProductId": "db61925e-2a50-4c87-ab82-26c7667cc288", "selectedProduct": {"id": "db61925e-2a50-4c87-ab82-26c7667cc288", "productId": "SIEH0DFU", "productTitle": "Organic Cotton Reusable Bags - Premium", "productType": "physical", "category": "Home & Kitchen", "thumbnailUrl": "https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_1_product_1.png", "shortDescription": "Airtight food storage solution", "longDescription": "Airtight food storage solution. <PERSON><PERSON>r tener nulla tero tantillus thymbra eaque. Speciosus quam talis assentator.", "genAiDescription": "Airtight food storage solution Perfect for modern consumers who value quality and functionality.", "specifications": [{}, {}], "productHighlights": [{}, {}, {}, {}], "classType": "B", "upc": "************", "images": [{}], "customImages": [], "pdpSummary": "Airtight food storage solution", "brand": {"id": "d05a7873-93df-4c93-bb3a-5b0d0c7a120b", "name": "UrbanPro Style", "description": "Modern urban fashion and accessories", "logoAssetUrl": "https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_10_26%20AM.png"}, "account": {"id": "2ab6c703-8605-4ed1-98ec-cc553cdfd519", "name": "<PERSON>'s content paradigms"}}, "isExpress": true, "hasExistingGenerations": false, "brandImage": "https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_10_26%20AM.png", "createdAt": "2025-07-13T03:35:57.226Z", "updatedAt": "2025-07-13T03:35:57.268Z", "timestamp": *************}