# Fixing Google Cloud Authentication ERR_OSSL_UNSUPPORTED Error

## Problem Description

You're encountering this error when trying to use Google Cloud Storage:

```
Error: error:1E08010C:DECODER routines::unsupported
```

This error occurs during JWT signing for Google service account authentication, typically due to private key format incompatibility with newer Node.js/OpenSSL versions.

## Quick Fix Solutions

### Solution 1: Use Service Account JSON File (Recommended)

This is the most reliable approach as it eliminates environment variable parsing issues:

1. **Download your service account JSON file** from Google Cloud Console:
   - Go to Google Cloud Console → IAM & Admin → Service Accounts
   - Select your service account → Keys → Add Key → Create New Key → JSON
   - Download the JSON file

2. **Place the file in your project root** and name it `service-account-key.json`

3. **Set the environment variable** (optional):
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="./service-account-key.json"
   ```

4. **The updated code will automatically detect and use this file**

### Solution 2: Fix Environment Variables

If you prefer to keep using environment variables:

1. **Run the diagnostic script** to identify the issue:
   ```bash
   npm run fix:google-auth
   ```

2. **Ensure your private key is properly formatted**:
   - Must include `-----BEGIN PRIVATE KEY-----` and `-----END PRIVATE KEY-----`
   - Should be in PKCS#8 format (not PKCS#1)
   - Newlines should be escaped as `\\n` in environment variables

3. **Example of correct format**:
   ```bash
   GOOGLE_CLOUD_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\\n-----END PRIVATE KEY-----\\n"
   ```

### Solution 3: Convert PKCS#1 to PKCS#8

If your private key starts with `-----BEGIN RSA PRIVATE KEY-----`, it needs conversion:

1. **Install OpenSSL** (if not already installed):
   ```bash
   # macOS
   brew install openssl

   # Ubuntu/Debian
   sudo apt-get install openssl
   ```

2. **Convert the key**:
   ```bash
   openssl pkcs8 -topk8 -inform PEM -outform PEM -nocrypt -in old_key.pem -out new_key.pem
   ```

3. **Use the converted key** in your environment variables

## Automated Diagnosis

Run our diagnostic script to identify and fix the issue automatically:

```bash
npm run fix:google-auth
```

This script will:
- ✅ Check for service account JSON files
- ✅ Validate environment variables
- ✅ Test private key format compatibility
- ✅ Attempt automatic PKCS#1 to PKCS#8 conversion
- ✅ Test the actual Google Cloud connection

## Environment Variables Reference

Required environment variables for Google Cloud authentication:

```bash
# Google Cloud Project Configuration
GOOGLE_CLOUD_PROJECT_ID="your-project-id"

# Service Account Credentials
GOOGLE_CLOUD_CLIENT_EMAIL="<EMAIL>"
GOOGLE_CLOUD_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\\nYOUR_PRIVATE_KEY_HERE\\n-----END PRIVATE KEY-----\\n"

# Optional: Path to service account JSON file (takes precedence over env vars)
GOOGLE_APPLICATION_CREDENTIALS="./service-account-key.json"
```

## Testing the Fix

After implementing one of the solutions:

1. **Run the diagnostic script**:
   ```bash
   npm run fix:google-auth
   ```

2. **Test your application**:
   ```bash
   npm run dev
   ```

3. **Try the failing endpoint** that was causing the error (e.g., asset upload)

## Common Issues and Solutions

### Issue: "Invalid private key format"
**Solution**: Ensure the private key includes proper PEM headers and is in PKCS#8 format.

### Issue: "Missing required environment variables"
**Solution**: Verify all required environment variables are set in your `.env` file.

### Issue: "Service account file is malformed"
**Solution**: Re-download the service account JSON file from Google Cloud Console.

### Issue: "Connection test failed"
**Solution**:
- Verify the service account has the necessary permissions
- Check that the project ID is correct
- Ensure the service account key hasn't expired

## Security Best Practices

1. **Never commit service account files to version control**
   - Add `service-account-key.json` to `.gitignore`
   - Use environment variables in production

2. **Rotate service account keys regularly**
   - Generate new keys periodically
   - Delete old unused keys

3. **Use least privilege principle**
   - Only grant necessary permissions to the service account
   - Regularly audit service account permissions

## Additional Resources

- [Google Cloud Service Account Documentation](https://cloud.google.com/iam/docs/service-accounts)
- [Node.js Crypto Documentation](https://nodejs.org/api/crypto.html)
- [OpenSSL Key Format Conversion](https://www.openssl.org/docs/man1.1.1/man1/openssl-pkcs8.html)

## Still Having Issues?

If you're still experiencing problems after trying these solutions:

1. Run the diagnostic script with verbose output
2. Check the Google Cloud Console for any permission issues
3. Verify your service account has the necessary roles:
   - Storage Admin (for full bucket access)
   - Storage Object Admin (for object operations)
   - Or custom roles with specific permissions

3. Consider creating a new service account and key to eliminate any corruption issues

---

**Last Updated**: January 2025
**Tested with**: Node.js 18+, @google-cloud/storage 7.15.0+