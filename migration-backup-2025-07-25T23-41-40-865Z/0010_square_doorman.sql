ALTER TABLE "projects" DROP CONSTRAINT "projects_retailer_id_retailers_id_fk";
--> statement-breakpoint
ALTER TABLE "brands" ALTER COLUMN "colors" SET DEFAULT '["#e3e3e3","#e6e6e6"]'::jsonb;--> statement-breakpoint
ALTER TABLE "credential_sets" ALTER COLUMN "is_shared" SET DEFAULT true;--> statement-breakpoint
ALTER TABLE "projects" ADD CONSTRAINT "projects_retailer_id_retailers_id_fk" FOREIGN KEY ("retailer_id") REFERENCES "public"."retailers"("id") ON DELETE set null ON UPDATE no action;