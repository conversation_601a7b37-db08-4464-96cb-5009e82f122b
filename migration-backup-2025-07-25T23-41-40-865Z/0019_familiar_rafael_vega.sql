ALTER TABLE "generations" DROP CONSTRAINT "generations_product_id_products_product_id_fk";
--> statement-breakpoint
-- Clear any existing data to avoid foreign key issues
TRUNCATE TABLE "generations" CASCADE;
--> statement-breakpoint
-- Drop the old varchar column
ALTER TABLE "generations" DROP COLUMN "product_id";
--> statement-breakpoint
-- Add the new uuid column
ALTER TABLE "generations" ADD COLUMN "product_id" uuid NOT NULL;
--> statement-breakpoint
ALTER TABLE "generations" ADD CONSTRAINT "generations_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE no action ON UPDATE no action;