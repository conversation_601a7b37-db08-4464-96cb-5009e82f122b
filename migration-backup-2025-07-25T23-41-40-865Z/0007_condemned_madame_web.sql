CREATE TABLE "credential_set_retailers" (
	"credential_set_id" uuid NOT NULL,
	"retailer_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "credential_set_retailers_credential_set_id_retailer_id_pk" PRIMARY KEY("credential_set_id","retailer_id")
);
--> statement-breakpoint
CREATE TABLE "external_reviewers" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"account_id" uuid NOT NULL,
	"job_title" text NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"requested_by_user_id" uuid NOT NULL,
	"retailer_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "generation_versions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"generation_id" uuid NOT NULL,
	"session_id" text,
	"unit_fields" jsonb DEFAULT '{}',
	"has_completed" boolean DEFAULT false,
	"created_by" uuid NOT NULL,
	"version" integer DEFAULT 1,
	"ad_previews" jsonb DEFAULT '[]',
	"preview_image_positions" jsonb DEFAULT '{}',
	"apply_to_all" boolean DEFAULT false,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "proofs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"generation_version_id" uuid NOT NULL,
	"title" text NOT NULL,
	"email_description" text,
	"ad_unit_ids" text[],
	"reviewers" jsonb DEFAULT '{ "internal": [], "external": [] }',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "proof_comments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"proof_id" uuid NOT NULL,
	"reviewer_id" uuid NOT NULL,
	"comment" text NOT NULL,
	"reply_to_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "reviewers" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"proof_id" uuid,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"user_id" uuid,
	"is_external" boolean DEFAULT false,
	"status" text DEFAULT 'pending',
	"requested_by" uuid NOT NULL,
	"last_requested_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "invitations" RENAME COLUMN "brand_id" TO "brand_ids";--> statement-breakpoint
ALTER TABLE "generations" DROP CONSTRAINT "generations_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "invitations" DROP CONSTRAINT "invitations_brand_id_brands_id_fk";
--> statement-breakpoint
ALTER TABLE "brand_guidelines" ALTER COLUMN "asset_url" SET DEFAULT '';--> statement-breakpoint
ALTER TABLE "brand_users" ADD CONSTRAINT "brand_users_brand_id_user_id_pk" PRIMARY KEY("brand_id","user_id");--> statement-breakpoint
ALTER TABLE "accounts" ADD COLUMN "onboarding_step" integer DEFAULT 0 NOT NULL;--> statement-breakpoint
ALTER TABLE "brand_guidelines" ADD COLUMN "label" text;--> statement-breakpoint
ALTER TABLE "brand_guidelines" ADD COLUMN "file_name" text DEFAULT 'guidelines' NOT NULL;--> statement-breakpoint
ALTER TABLE "brand_guidelines" ADD COLUMN "uploaded_by" uuid;--> statement-breakpoint
ALTER TABLE "brands" ADD COLUMN "primary_credential_id" uuid;--> statement-breakpoint
ALTER TABLE "brands" ADD COLUMN "product_count" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "projects" ADD COLUMN "wm_campaigns" jsonb DEFAULT '{}'::jsonb;--> statement-breakpoint
ALTER TABLE "credential_set_retailers" ADD CONSTRAINT "credential_set_retailers_credential_set_id_credential_sets_id_fk" FOREIGN KEY ("credential_set_id") REFERENCES "public"."credential_sets"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "credential_set_retailers" ADD CONSTRAINT "credential_set_retailers_retailer_id_retailers_id_fk" FOREIGN KEY ("retailer_id") REFERENCES "public"."retailers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "external_reviewers" ADD CONSTRAINT "external_reviewers_account_id_accounts_id_fk" FOREIGN KEY ("account_id") REFERENCES "public"."accounts"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "external_reviewers" ADD CONSTRAINT "external_reviewers_requested_by_user_id_users_id_fk" FOREIGN KEY ("requested_by_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "external_reviewers" ADD CONSTRAINT "external_reviewers_retailer_id_retailers_id_fk" FOREIGN KEY ("retailer_id") REFERENCES "public"."retailers"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "generation_versions" ADD CONSTRAINT "generation_versions_generation_id_generations_id_fk" FOREIGN KEY ("generation_id") REFERENCES "public"."generations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "generation_versions" ADD CONSTRAINT "generation_versions_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "proofs" ADD CONSTRAINT "proofs_generation_version_id_generation_versions_id_fk" FOREIGN KEY ("generation_version_id") REFERENCES "public"."generation_versions"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "proof_comments" ADD CONSTRAINT "proof_comments_proof_id_proofs_id_fk" FOREIGN KEY ("proof_id") REFERENCES "public"."proofs"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "proof_comments" ADD CONSTRAINT "proof_comments_reviewer_id_reviewers_id_fk" FOREIGN KEY ("reviewer_id") REFERENCES "public"."reviewers"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "proof_comments" ADD CONSTRAINT "proof_comments_reply_to_id_proof_comments_id_fk" FOREIGN KEY ("reply_to_id") REFERENCES "public"."proof_comments"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviewers" ADD CONSTRAINT "reviewers_proof_id_proofs_id_fk" FOREIGN KEY ("proof_id") REFERENCES "public"."proofs"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviewers" ADD CONSTRAINT "reviewers_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviewers" ADD CONSTRAINT "reviewers_requested_by_users_id_fk" FOREIGN KEY ("requested_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "brand_guidelines" ADD CONSTRAINT "brand_guidelines_uploaded_by_users_id_fk" FOREIGN KEY ("uploaded_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "brands" ADD CONSTRAINT "brands_primary_credential_id_credential_sets_id_fk" FOREIGN KEY ("primary_credential_id") REFERENCES "public"."credential_sets"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "brands" DROP COLUMN "guidelines";--> statement-breakpoint
ALTER TABLE "brands" DROP COLUMN "guideline_files";--> statement-breakpoint
ALTER TABLE "creatives" DROP COLUMN "version";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "session_id";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "wm_campaign_id";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "wm_ad_group_id";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "unit_fields";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "has_completed";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "created_by";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "version";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "ad_previews";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "preview_image_positions";--> statement-breakpoint
ALTER TABLE "generations" DROP COLUMN "apply_to_all";