CREATE TYPE "public"."brand_guideline_status" AS ENUM('active', 'archived');--> statement-breakpoint
ALTER TABLE "accounts" ALTER COLUMN "type" SET DATA TYPE text;--> statement-breakpoint
ALTER TABLE "accounts" ALTER COLUMN "type" SET DEFAULT 'default';--> statement-breakpoint
ALTER TABLE "brand_guidelines" ALTER COLUMN "status" SET DATA TYPE brand_guideline_status;--> statement-breakpoint
ALTER TABLE "creatives" ALTER COLUMN "preview_urls" SET DEFAULT '[]'::jsonb;--> statement-breakpoint
ALTER TABLE "accounts" ADD COLUMN "deleted_at" timestamp;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "is_verified" boolean DEFAULT false NOT NULL;--> statement-breakpoint
DROP TYPE "public"."account_type";