CREATE TABLE "queued_marketplace_items" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"product_id" uuid NOT NULL,
	"credential_set_id" uuid,
	"status" text DEFAULT 'queued' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "products" DROP CONSTRAINT "products_product_id_unique";--> statement-breakpoint
ALTER TABLE "image_metadata" DROP CONSTRAINT "image_metadata_product_id_products_product_id_fk";
--> statement-breakpoint
ALTER TABLE "retailer_products" DROP CONSTRAINT "retailer_products_product_id_products_product_id_fk";
--> statement-breakpoint
ALTER TABLE "image_metadata" ALTER COLUMN "product_id" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "transparent_thumbnail_url" text;--> statement-breakpoint
ALTER TABLE "queued_marketplace_items" ADD CONSTRAINT "queued_marketplace_items_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "queued_marketplace_items" ADD CONSTRAINT "queued_marketplace_items_credential_set_id_credential_sets_id_fk" FOREIGN KEY ("credential_set_id") REFERENCES "public"."credential_sets"("id") ON DELETE set null ON UPDATE no action;