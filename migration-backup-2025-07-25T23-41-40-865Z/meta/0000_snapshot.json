{"id": "4591198c-8b22-4cbf-8e60-706d7c51abfe", "prevId": "********-0000-0000-0000-********0000", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "account_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'3p'"}, "is_trial": {"name": "is_trial", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "account_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "valid_subscription": {"name": "valid_subscription", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "has_payment_method": {"name": "has_payment_method", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"accounts_owner_id_users_id_fk": {"name": "accounts_owner_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.account_settings": {"name": "account_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"account_settings_account_id_accounts_id_fk": {"name": "account_settings_account_id_accounts_id_fk", "tableFrom": "account_settings", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"account_settings_account_id_unique": {"name": "account_settings_account_id_unique", "nullsNotDistinct": false, "columns": ["account_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brand_credential_sets": {"name": "brand_credential_sets", "schema": "", "columns": {"brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "credential_set_id": {"name": "credential_set_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_credential_sets_brand_id_brands_id_fk": {"name": "brand_credential_sets_brand_id_brands_id_fk", "tableFrom": "brand_credential_sets", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "brand_credential_sets_credential_set_id_credential_sets_id_fk": {"name": "brand_credential_sets_credential_set_id_credential_sets_id_fk", "tableFrom": "brand_credential_sets", "tableTo": "credential_sets", "columnsFrom": ["credential_set_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"brand_credential_sets_brand_id_credential_set_id_pk": {"name": "brand_credential_sets_brand_id_credential_set_id_pk", "columns": ["brand_id", "credential_set_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brand_categories": {"name": "brand_categories", "schema": "", "columns": {"brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_categories_brand_id_brands_id_fk": {"name": "brand_categories_brand_id_brands_id_fk", "tableFrom": "brand_categories", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "brand_categories_category_id_categories_id_fk": {"name": "brand_categories_category_id_categories_id_fk", "tableFrom": "brand_categories", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brands": {"name": "brands", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "colors": {"name": "colors", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"primary\":\"#000000\",\"secondary\":\"#000000\"}'::jsonb"}, "logo_asset_url": {"name": "logo_asset_url", "type": "text", "primaryKey": false, "notNull": false}, "guidelines": {"name": "guidelines", "type": "text", "primaryKey": false, "notNull": false}, "guideline_files": {"name": "guideline_files", "type": "text[]", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brands_account_id_accounts_id_fk": {"name": "brands_account_id_accounts_id_fk", "tableFrom": "brands", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brand_users": {"name": "brand_users", "schema": "", "columns": {"brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_users_brand_id_brands_id_fk": {"name": "brand_users_brand_id_brands_id_fk", "tableFrom": "brand_users", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "brand_users_user_id_users_id_fk": {"name": "brand_users_user_id_users_id_fk", "tableFrom": "brand_users", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"categories_account_id_accounts_id_fk": {"name": "categories_account_id_accounts_id_fk", "tableFrom": "categories", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.creatives": {"name": "creatives", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "wm_ad_group_id": {"name": "wm_ad_group_id", "type": "text", "primaryKey": false, "notNull": false}, "wm_campaign_id": {"name": "wm_campaign_id", "type": "text", "primaryKey": false, "notNull": false}, "wm_creative_id": {"name": "wm_creative_id", "type": "text", "primaryKey": false, "notNull": false}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "generation_id": {"name": "generation_id", "type": "uuid", "primaryKey": false, "notNull": false}, "preview_urls": {"name": "preview_urls", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "preview_status": {"name": "preview_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'PENDING'"}, "folder_id": {"name": "folder_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'DRAFT'"}, "keyword": {"name": "keyword", "type": "text", "primaryKey": false, "notNull": false}, "ad_units": {"name": "ad_units", "type": "jsonb", "primaryKey": false, "notNull": true}, "review_comments": {"name": "review_comments", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"creatives_brand_id_brands_id_fk": {"name": "creatives_brand_id_brands_id_fk", "tableFrom": "creatives", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "creatives_user_id_users_id_fk": {"name": "creatives_user_id_users_id_fk", "tableFrom": "creatives", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "creatives_generation_id_generations_id_fk": {"name": "creatives_generation_id_generations_id_fk", "tableFrom": "creatives", "tableTo": "generations", "columnsFrom": ["generation_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credential_sets": {"name": "credential_sets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "default": "'Default Credential Set'"}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "default": "'default'"}, "credentials": {"name": "credentials", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"credential_sets_account_id_accounts_id_fk": {"name": "credential_sets_account_id_accounts_id_fk", "tableFrom": "credential_sets", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.generations": {"name": "generations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "keyword_id": {"name": "keyword_id", "type": "uuid", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "wm_campaign_id": {"name": "wm_campaign_id", "type": "text", "primaryKey": false, "notNull": false}, "wm_ad_group_id": {"name": "wm_ad_group_id", "type": "text", "primaryKey": false, "notNull": false}, "unit_fields": {"name": "unit_fields", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'"}, "has_completed": {"name": "has_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "ad_previews": {"name": "ad_previews", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "preview_image_positions": {"name": "preview_image_positions", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'"}, "apply_to_all": {"name": "apply_to_all", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"generations_product_id_products_product_id_fk": {"name": "generations_product_id_products_product_id_fk", "tableFrom": "generations", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["product_id"], "onDelete": "no action", "onUpdate": "no action"}, "generations_keyword_id_keywords_id_fk": {"name": "generations_keyword_id_keywords_id_fk", "tableFrom": "generations", "tableTo": "keywords", "columnsFrom": ["keyword_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "generations_project_id_projects_id_fk": {"name": "generations_project_id_projects_id_fk", "tableFrom": "generations", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "generations_brand_id_brands_id_fk": {"name": "generations_brand_id_brands_id_fk", "tableFrom": "generations", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "generations_created_by_users_id_fk": {"name": "generations_created_by_users_id_fk", "tableFrom": "generations", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.image_metadata": {"name": "image_metadata", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "image_text": {"name": "image_text", "type": "text", "primaryKey": false, "notNull": false}, "classification": {"name": "classification", "type": "image_classification", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"image_metadata_product_id_products_product_id_fk": {"name": "image_metadata_product_id_products_product_id_fk", "tableFrom": "image_metadata", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["product_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"image_metadata_product_id_image_url_unique": {"name": "image_metadata_product_id_image_url_unique", "nullsNotDistinct": false, "columns": ["product_id", "image_url"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "auth0_id": {"name": "auth0_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_beta_user": {"name": "is_beta_user", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "beta_key_id": {"name": "beta_key_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"users_permission_id_permissions_id_fk": {"name": "users_permission_id_permissions_id_fk", "tableFrom": "users", "tableTo": "permissions", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_auth0_id_unique": {"name": "users_auth0_id_unique", "nullsNotDistinct": false, "columns": ["auth0_id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "invitation_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "invitation_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"invitations_account_id_accounts_id_fk": {"name": "invitations_account_id_accounts_id_fk", "tableFrom": "invitations", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invitations_brand_id_brands_id_fk": {"name": "invitations_brand_id_brands_id_fk", "tableFrom": "invitations", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.keywords": {"name": "keywords", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "keyword": {"name": "keyword", "type": "text", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "include": {"name": "include", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\n    \"tone\": null,\n    \"word\": null,\n    \"consideration\": null\n  }'"}, "exclude": {"name": "exclude", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\n    \"tone\": null,\n    \"word\": null,\n    \"consideration\": null\n  }'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"keywords_account_id_accounts_id_fk": {"name": "keywords_account_id_accounts_id_fk", "tableFrom": "keywords", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"keywords_keyword_unique": {"name": "keywords_keyword_unique", "nullsNotDistinct": false, "columns": ["keyword"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "product_title": {"name": "product_title", "type": "text", "primaryKey": false, "notNull": true}, "product_type": {"name": "product_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "short_description": {"name": "short_description", "type": "text", "primaryKey": false, "notNull": false}, "long_description": {"name": "long_description", "type": "text", "primaryKey": false, "notNull": false}, "gen_ai_description": {"name": "gen_ai_description", "type": "text", "primaryKey": false, "notNull": false}, "specifications": {"name": "specifications", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "product_highlights": {"name": "product_highlights", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "class_type": {"name": "class_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "upc": {"name": "upc", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "images": {"name": "images", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "custom_images": {"name": "custom_images", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": false}, "pdp_summary": {"name": "pdp_summary", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"products_account_id_accounts_id_fk": {"name": "products_account_id_accounts_id_fk", "tableFrom": "products", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "products_brand_id_brands_id_fk": {"name": "products_brand_id_brands_id_fk", "tableFrom": "products", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"products_product_id_unique": {"name": "products_product_id_unique", "nullsNotDistinct": false, "columns": ["product_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permissions": {"name": "permissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "permission_data": {"name": "permission_data", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_settings_user_id_unique": {"name": "user_settings_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brand_retailers": {"name": "brand_retailers", "schema": "", "columns": {"brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "retailer_id": {"name": "retailer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_retailers_brand_id_brands_id_fk": {"name": "brand_retailers_brand_id_brands_id_fk", "tableFrom": "brand_retailers", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "brand_retailers_retailer_id_retailers_id_fk": {"name": "brand_retailers_retailer_id_retailers_id_fk", "tableFrom": "brand_retailers", "tableTo": "retailers", "columnsFrom": ["retailer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.retailer_products": {"name": "retailer_products", "schema": "", "columns": {"retailer_id": {"name": "retailer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"retailer_products_retailer_id_retailers_id_fk": {"name": "retailer_products_retailer_id_retailers_id_fk", "tableFrom": "retailer_products", "tableTo": "retailers", "columnsFrom": ["retailer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "retailer_products_product_id_products_product_id_fk": {"name": "retailer_products_product_id_products_product_id_fk", "tableFrom": "retailer_products", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["product_id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.retailers": {"name": "retailers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"retailers_slug_unique": {"name": "retailers_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "message": {"name": "message", "type": "jsonb", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'unread'"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "notifications_account_id_accounts_id_fk": {"name": "notifications_account_id_accounts_id_fk", "tableFrom": "notifications", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.leads": {"name": "leads", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "beta_id": {"name": "beta_id", "type": "uuid", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "organization": {"name": "organization", "type": "text", "primaryKey": false, "notNull": true}, "dss_spend": {"name": "dss_spend", "type": "integer", "primaryKey": false, "notNull": false}, "org_size": {"name": "org_size", "type": "integer", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "invite_status": {"name": "invite_status", "type": "invite_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "beta_keys": {"name": "beta_keys", "type": "uuid[]", "primaryKey": false, "notNull": false}, "sent_keys": {"name": "sent_keys", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"projects_brand_id_brands_id_fk": {"name": "projects_brand_id_brands_id_fk", "tableFrom": "projects", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "projects_created_by_users_id_fk": {"name": "projects_created_by_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_accounts": {"name": "user_accounts", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_accounts_user_id_users_id_fk": {"name": "user_accounts_user_id_users_id_fk", "tableFrom": "user_accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_accounts_account_id_accounts_id_fk": {"name": "user_accounts_account_id_accounts_id_fk", "tableFrom": "user_accounts", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_accounts_user_id_account_id_pk": {"name": "user_accounts_user_id_account_id_pk", "columns": ["user_id", "account_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.account_status": {"name": "account_status", "schema": "public", "values": ["active", "inactive", "deleted"]}, "public.account_type": {"name": "account_type", "schema": "public", "values": ["agency", "3p", "1p"]}, "public.image_classification": {"name": "image_classification", "schema": "public", "values": ["product", "lifestyle", "other", "invalid"]}, "public.beta_status": {"name": "beta_status", "schema": "public", "values": ["pending", "accepted", "rejected"]}, "public.invitation_status": {"name": "invitation_status", "schema": "public", "values": ["pending", "accepted", "rejected"]}, "public.invitation_type": {"name": "invitation_type", "schema": "public", "values": ["request", "invite"]}, "public.invite_status": {"name": "invite_status", "schema": "public", "values": ["pending", "accepted", "rejected"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}