import 'dotenv/config'
import { migrate } from 'drizzle-orm/neon-http/migrator'
import { db } from '../config.js'

async function runMigrations() {
  try {
    console.log('Starting migrations...')

    await migrate(db, {
      migrationsFolder: './src/db/migrations'
    })

    console.log('Migrations completed successfully')
    process.exit(0)
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  }
}

runMigrations()