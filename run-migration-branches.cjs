const { Client } = require('pg');
require('dotenv').config();

async function runMigration(databaseUrl) {
  const connectionString = databaseUrl || process.env.DATABASE_URL;
  
  if (!connectionString) {
    console.error('❌ No database URL provided. Use: node run-migration-branches.cjs <DATABASE_URL> or set DATABASE_URL environment variable');
    process.exit(1);
  }

  // Extract branch info from URL for logging
  const urlMatch = connectionString.match(/ep-([^-]+)-([^-]+)-/);
  const branchInfo = urlMatch ? `${urlMatch[1]}-${urlMatch[2]}` : 'unknown';
  
  const client = new Client({ connectionString });

  try {
    await client.connect();
    console.log(`🔗 Connected to database branch: ${branchInfo}`);

    // Our migration SQL
    const migrationSQL = `
-- Drop foreign key constraints first
ALTER TABLE "retailer_products" DROP CONSTRAINT IF EXISTS "retailer_products_product_id_products_product_id_fk";
ALTER TABLE "image_metadata" DROP CONSTRAINT IF EXISTS "image_metadata_product_id_products_product_id_fk";

-- Now drop the unique constraint
ALTER TABLE "products" DROP CONSTRAINT IF EXISTS "products_product_id_unique";

-- Make image_metadata.product_id nullable
ALTER TABLE "image_metadata" ALTER COLUMN "product_id" DROP NOT NULL;

-- Add new column
ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "transparent_thumbnail_url" text;
    `;

    console.log(`⚙️  Executing migration on ${branchInfo}...`);
    await client.query(migrationSQL);
    console.log(`✅ Migration completed successfully on ${branchInfo}!`);

  } catch (error) {
    console.error(`❌ Migration failed on ${branchInfo}:`, error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Get database URL from command line argument or environment
const databaseUrl = process.argv[2];
runMigration(databaseUrl); 