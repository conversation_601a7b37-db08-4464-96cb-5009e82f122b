import "dotenv/config";
import Fastify from "fastify";
import jwt from "@fastify/jwt";
import cookie from "@fastify/cookie";
import session from "@fastify/session";
import Redis from "ioredis";
import { createRequire } from "module";
import { validateEnvironment } from "./src/utils/validateEnv.js";
import { logger } from "./src/utils/logger.js";
import dbPlugin from "./src/plugins/db.plugin.js";
import authPlugin from "./src/plugins/auth.plugin.js";
import authRoutes from "./src/routes/auth/auth.routes.js";
import userRoutes from "./src/routes/users/users.routes.js";
import accountRoutes from "./src/routes/accounts/accounts.routes.js";
import brandRoutes from "./src/routes/brands/brands.routes.js";
import retailerRoutes from "./src/routes/retailers/retailers.routes.js";
import adGroupRoutes from './src/routes/adGroups/adGroups.routes.js'
import invitationRoutes from "./src/routes/invitations/invitations.routes.js";
import notificationRoutes from "./src/routes/notifications/notifications.routes.js";
import assetRoutes from "./src/routes/assets/assets.routes.js";
import settingsRoutes from "./src/routes/settings/settings.routes.js";

import productMgmtRoutes from "./src/routes/product-mgmt/product-mgmt.routes.js";
import productRoutes from "./src/routes/products/products.routes.js";
import keywordRoutes from "./src/routes/keywords/keywords.routes.js";
import generationRoutes from "./src/routes/generations/generations.routes.js";
import creativeRoutes from "./src/routes/creatives/creatives.routes.js";
import creativeProjectRoutes from "./src/routes/creativeProjects/creativeProjects.routes.js";
import creativeSetupRoutes from "./src/routes/creativeSetup/creativeSetup.routes.js";
import proofRoutes from "./src/routes/proofs/proofs.routes.js";
import proofCommentRoutes from "./src/routes/proofComments/proofComments.routes.js";
import reviewerRoutes from "./src/routes/reviewers/reviewers.routes.js";
import betaRoutes from "./src/routes/beta/beta.routes.js";
import debugRoutes from "./src/routes/debug/debug.routes.js";
import emailRoutes from "./src/routes/email/email.routes.js";
import multipart from "@fastify/multipart";
import websocket from "@fastify/websocket";
import rateLimit from "@fastify/rate-limit";
import { notificationService } from "./src/services/notifications";

const fastify = Fastify({
  logger: {
    transport: {
      target: "pino-pretty",
      options: {
        translateTime: "HH:MM:ss Z",
        ignore: "pid,hostname",
        colorize: true,
      },
    },
  },
  pluginTimeout: 20000,
});
const port = Number(process.env.PORT) || 3000;

// Create a require function
const require = createRequire(import.meta.url);
// Use require to import connect-redis and extract RedisStore class
const { RedisStore } = require("connect-redis");

// Register all plugins before routes
async function registerPlugins() {
  const redisClient = new Redis(process.env.REDIS_URL || "");
  redisClient.on("error", (err) =>
    fastify.log.error("Redis Client Error", err),
  );

  // RedisStore is already imported directly, no need to call connectRedis(session)

  fastify.decorate("redis", redisClient);
  await fastify.register(cookie);

  // Create the store instance
  const redisStoreInstance = new RedisStore({
    client: redisClient,
    prefix: "sess:", // Use default prefix for clarity
  });

  // Add debug info
  logger.debug("Registering session store", {
    storeType: redisStoreInstance.constructor.name || "RedisStore",
    redisConnected: redisClient.status === "ready" || redisClient.status,
    sessionSecret: process.env.SESSION_SECRET ? "Set from env" : "Using fallback"
  });

  // Validate session secret exists
  if (!process.env.SESSION_SECRET) {
    throw new Error('SESSION_SECRET environment variable is required for production security');
  }

  await fastify.register(session, {
    secret: process.env.SESSION_SECRET,
    store: redisStoreInstance,
    cookie: {
      secure: process.env.NODE_ENV === "production",
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // Default 24 hours, but will be overridden by Auth0 token expiration
    },
    saveUninitialized: false,
  });

  // Log successful session registration
  logger.info("Session plugin registered successfully");

  // Validate JWT secret exists
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET environment variable is required for production security');
  }

  // Core plugins
  await fastify.register(jwt, {
    secret: process.env.JWT_SECRET,
    sign: {
      expiresIn: "1d",
    },
    verify: {
      allowedAud: process.env.AUTH0_AUDIENCE,
    },
  });

  // Rate limiting configuration
  // await fastify.register(rateLimit, {
  //   max: 100, // Maximum 100 requests
  //   timeWindow: '1 minute', // Per minute
  //   redis: redisClient, // Use Redis for distributed rate limiting
  //   keyGenerator: (request) => {
  //     // Use IP address and user ID if available for more granular limiting
  //     const user = request.user as { id?: string } | undefined;
  //     return `${request.ip}:${user?.id || 'anonymous'}`;
  //   },
  //   errorResponseBuilder: (request, context) => {
  //     return {
  //       code: 429,
  //       error: 'Too Many Requests',
  //       message: `Rate limit exceeded, retry in ${context.ttl} seconds`,
  //       retryAfter: context.ttl
  //     };
  //   }
  // });

  // Application plugins
  await fastify.register(dbPlugin);
  await fastify.register(authPlugin);
  await fastify.register(multipart, {
    attachFieldsToBody: true,
    limits: {
      fileSize: 10_737_418_240, // 10GB in bytes
    },
  });
  await fastify.register(websocket);
  await fastify.register(notificationService);
}

/**
 * Registers all API and debug routes with the Fastify server instance.
 *
 * This function attaches route modules for authentication, user management, accounts, brands, retailers, invitations, notifications, assets, settings, product management, products, keywords, generations, creatives, creative projects, creative setup, beta features, and debug endpoints under their respective URL prefixes.
 */
async function registerRoutes() {
  await fastify.register(authRoutes, { prefix: "/auth" });
  await fastify.register(userRoutes, { prefix: "/api/users" });
  await fastify.register(accountRoutes, { prefix: "/api/accounts" });
  await fastify.register(brandRoutes, { prefix: "/api/brands" });
  await fastify.register(retailerRoutes, { prefix: "/api/retailers" });
  await fastify.register(adGroupRoutes, { prefix: '/api/ad-groups' })
  await fastify.register(invitationRoutes, { prefix: "/api/invitations" });
  await fastify.register(notificationRoutes, { prefix: "/api/notifications" });
  await fastify.register(assetRoutes, { prefix: "/api/assets" });
  await fastify.register(settingsRoutes, { prefix: "/api/settings" });

  await fastify.register(productMgmtRoutes, { prefix: "/api/product-mgmt" });
  await fastify.register(productRoutes, { prefix: "/api/products" });
  await fastify.register(keywordRoutes, { prefix: "/api/keywords" });
  await fastify.register(generationRoutes, { prefix: "/api/generations" });
  await fastify.register(creativeRoutes, { prefix: "/api/creatives" });
  await fastify.register(creativeProjectRoutes, { prefix: "/api/creative-projects" });
  await fastify.register(creativeSetupRoutes, { prefix: "/api/creative-setup" });
  await fastify.register(proofRoutes, { prefix: "/api/proofs" });
  await fastify.register(proofCommentRoutes, { prefix: "/api/proof-comments" });
  await fastify.register(reviewerRoutes, { prefix: "/api/reviewers" });
  await fastify.register(betaRoutes, { prefix: "/api/beta" });
  await fastify.register(emailRoutes, { prefix: "/api/email" });

  // Register debug routes (will only add routes if not in production)
  await fastify.register(debugRoutes, { prefix: "/debug" });
}
// Start server
const start = async () => {
  try {
    // Validate environment variables first
    const envConfig = validateEnvironment();
    logger.info('Starting ADVID Server', {
      environment: envConfig.NODE_ENV,
      port: envConfig.PORT,
      nodeVersion: process.version
    });

    await registerPlugins();
    await registerRoutes();

    await fastify.listen({ port, host: "0.0.0.0" });
    logger.info('Server started successfully', {
      port,
      host: "0.0.0.0",
      environment: envConfig.NODE_ENV
    });
  } catch (err) {
    logger.fatal('Server startup failed', err);
    process.exit(1);
  }
};

start();
