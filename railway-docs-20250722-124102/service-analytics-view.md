# Service: analytics-view

**Generated:** Tue Jul 22 12:41:21 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** analytics-view
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "GOOGLE_CLOUD_AUTH_PROVIDER_X509_CERT_URL": "https://www.googleapis.com/oauth2/v1/certs",
  "GOOGLE_CLOUD_AUTH_URI": "https://accounts.google.com/o/oauth2/auth",
  "GOOGLE_CLOUD_CLIENT_EMAIL": "<EMAIL>",
  "GOOGLE_CLOUD_CLIENT_ID": "115713899329077171498",
  "GOOGLE_CLOUD_CLIENT_X509_CERT_URL": "https://www.googleapis.com/robot/v1/metadata/x509/storage-service-account%40advid-gradient-bgs.iam.gserviceaccount.com",
  "GOOGLE_CLOUD_PRIVATE_KEY": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCpiixubmGadr+R\nn00Ve9JJ1e8ySMmpAyCmmut9DlnyrkQwygyDnx7hgMKJCg0L4fSJovoVHxyZOShu\nX3UR/RzLUwjoESJHIwW2LK7TxNfrcnEhdhKZFF5RJa+nqMIPvA3837BG32i9PySJ\nS/Zp3VJZPuiXxwdce5VHPvOIu2wXVhCswffm8i9H63c0zc/fn+KDFlJsjgiH/lBI\nlfYd7pzBkk3h3vJWbwKIfpeLVM3Rbvb+Z8p+OHpH907KZCwJU6/uiCMtg8afw5Q3\nppqd+G1WEylCC2zkd2x+YDT2p/NPiookb9XQZLzFazx1PRh2kL9aaK+TFH+U9l9E\nt6cmg09tAgMBAAECggEAASrXo/oEF4duSb8mTXnRCJudvkOkidKg8Ky75txIr8RV\n6V0WyXg4wvY7QKzOSrDnWWRDIw6YCJENb3ceS/2pa1f6xXhKu2EpGqhDmuct0mvz\nDfctaRrsDFR9WWLu1SNnGEDlCpPiuXzcEEry2ydAOCeX++WNriwWqssq65vYwm9d\nJG2tdHlCFdooRePhCBPuotKdM2Z+e2GUkGsb2aSqJsKbQkfywt3xraytHO6JNIoG\nvtOHiImvwYCGUzmqh5abT5LmmSpSJZrD7JbdfcdNawkTVMiuNGZE5W/CDue2SwJ5\nkWZu9cq6rlIkoKiipb3eAPlSm7u6jsxB6ichTQ6PIQKBgQDiyzlaDX+ATKGmPRQS\nKkMow9IsD/giFLCdPaDDXaJ7LzQxfIBhXgsymH0Cu7VxykXFWHSvCK23ieiyi78o\nCdWoxoRatk+YyyY9juDq1ZL2PwALIamwkfQICS3NXMO4hhRAVusklzieRr3J0mri\nzgjbW/WDYKTkgac734DAfNSj4QKBgQC/X3ABL2q1KNHCMMvMMwrpQb+G8EKSasIr\n4ZZySgEUJAVQy15CN/RPO2ZCEFp+u7f+0MSGgFaLCer3uVS88IXivvEbHTBQepp8\nU4LW/NkV40+ct5BnUmLjRwPjUxuSc+3ygexOYg2kNeCtUtKabRizJ0DG5l3amXEO\ndKX6jfadDQKBgQDONeQm2mEVAhh089I9FgpMlYiTIc4HBvLzdean3IpxISF+4WpF\nneHmljoMcypulXFNF7m4sNZrLMzVEzWzLQYFWMNdS6kiD4zsqe4d+CWRGwVqGaiV\n8I+HgvkPYgpfzCG7wow5j/oeB8pnKJihMp9g/MNGOmiTkMPDL9x8qMQbYQKBgBnD\niHmwX4ZKG4swNI+mI4bpohZRjDTYCJINycpTKjgC7XBGIMd7bvmnvomQWA6NySSW\nCI8vAfev+yEko0LYgr3XBPCoilObXeb0+z1kRvmKY7JuiBNQ5R1B90UIcELUNqma\nUizvSHTeCARtjQrbOyMLWs4sP2Bpne3bSGkl6ZiVAoGAHLHTpAaoTqzSkOd6qWe1\n3M9qESu2K2LakLueQgtfyjh5Rf6VvqoBasWsRgiM9+eyaUQWP5YIqojX45+vB/6p\n2Ojnd+KBb3pnjrv/AuVuOX58LHt0eXqgGtrKsn2jT98sIDhKG0sNktB6c/OVNJ4b\nPubELLPpgaGkfMRiaqkVbmI=\n-----END PRIVATE KEY-----\n",
  "GOOGLE_CLOUD_PRIVATE_KEY_ID": "754231d842f8becfe3db8b1ae7b12408d22b5175",
  "GOOGLE_CLOUD_PROJECT_ID": "advid-gradient-bgs",
  "GOOGLE_CLOUD_TOKEN_URI": "https://oauth2.googleapis.com/token",
  "GOOGLE_CLOUD_TYPE": "service_account",
  "GOOGLE_CLOUD_UNIVERSE_DOMAIN": "googleapis.com",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "analytics-view.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "analytics-view",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "metrics.adfury.ai"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| GOOGLE_CLOUD_AUTH_PROVIDER_X509_CERT_URL | https://www.googleapis.com/oauth2/v1/certs |
| GOOGLE_CLOUD_AUTH_URI | https://accounts.google.com/o/oauth2/auth |
| GOOGLE_CLOUD_CLIENT_EMAIL | <EMAIL> |
| GOOGLE_CLOUD_CLIENT_ID | 115713899329077171498 |
| GOOGLE_CLOUD_CLIENT_X509_CERT_URL | https://www.googleapis.com/robot/v1/metadata/x509/storage-service-account%40advid-gradient-bgs.iam.gserviceaccount.com |
| GOOGLE_CLOUD_PRIVATE_KEY | ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************ |
| GOOGLE_CLOUD_PRIVATE_KEY_ID | 754231d842f8becfe3db8b1ae7b12408d22b5175 |
| GOOGLE_CLOUD_PROJECT_ID | advid-gradient-bgs |
| GOOGLE_CLOUD_TOKEN_URI | https://oauth2.googleapis.com/token |
| GOOGLE_CLOUD_TYPE | service_account |
| GOOGLE_CLOUD_UNIVERSE_DOMAIN | googleapis.com |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | analytics-view.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | metrics.adfury.ai |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | analytics-view |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | metrics.adfury.ai |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| e5d90686-6785-4fa7-929b-8d846780655c | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "analytics-view",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "e5d90686-6785-4fa7-929b-8d846780655c",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "8184b358-f403-49d1-a44b-bbe31d747bee",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "",
              "commitHash": "337a7554efc31759e5b23ae0ec2cc9f911d732a8",
              "commitMessage": "new repo",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "isPublicRepoDeploy": false,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "patchId": "179d63b8-f957-4595-88ab-4fd2fda43a5a",
              "propertyFileMapping": {},
              "reason": "deploy",
              "repo": "AdVid-ai/analytics-view",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/analytics-view",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "8184b358-f403-49d1-a44b-bbe31d747bee",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "",
    "commitHash": "337a7554efc31759e5b23ae0ec2cc9f911d732a8",
    "commitMessage": "new repo",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "isPublicRepoDeploy": false,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "patchId": "179d63b8-f957-4595-88ab-4fd2fda43a5a",
    "propertyFileMapping": {},
    "reason": "deploy",
    "repo": "AdVid-ai/analytics-view",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service analytics-view

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "analytics-view" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
