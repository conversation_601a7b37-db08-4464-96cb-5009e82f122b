{"id": "1ca54bbb-e741-4196-8b36-a9a41288434f", "name": "advid-server", "serviceInstances": {"edges": [{"node": {"id": "c78091c5-da1f-4d7d-89e8-ffcec769701b", "serviceId": "1ca54bbb-e741-4196-8b36-a9a41288434f", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "b347928d-c403-48a0-90ef-cca9f3164a2d", "meta": {"branch": "main", "buildOnly": false, "commitAuthor": "alpendergrast", "commitHash": "cee42720a546ff98e29bf7f0bcd5f2e497b50ab3", "commitMessage": "Merge pull request #116 from AdVid-ai/ADV-354\n\nADV-354 - Fix credential update", "fileServiceManifest": {}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {}, "reason": "deploy", "repo": "AdVid-ai/advid-server", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "nixpacksConfigPath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 3}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/advid-server", "image": null}}}]}}