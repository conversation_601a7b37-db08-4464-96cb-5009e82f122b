# Service: Redis-mXH1

**Generated:** Tue Jul 22 12:41:46 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Redis-mXH1
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "redis-mxh1.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_RUN_AS_ROOT": "true",
  "RAILWAY_RUN_UID": "0",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "Redis-mXH1",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_TCP_APPLICATION_PORT": "6379",
  "RAILWAY_TCP_PROXY_DOMAIN": "ballast.proxy.rlwy.net",
  "RAILWAY_TCP_PROXY_PORT": "13185",
  "RAILWAY_VOLUME_ID": "************************************",
  "RAILWAY_VOLUME_MOUNT_PATH": "/bitnami",
  "RAILWAY_VOLUME_NAME": "overflowing-volume",
  "REDISHOST": "redis-mxh1.railway.internal",
  "REDISPASSWORD": "YtZiaCCiPBWyKzZaargxcAuzVpxRiKea",
  "REDISPORT": "6379",
  "REDISUSER": "default",
  "REDIS_AOF_ENABLED": "no",
  "REDIS_PASSWORD": "YtZiaCCiPBWyKzZaargxcAuzVpxRiKea",
  "REDIS_PUBLIC_URL": "redis://default:<EMAIL>:13185",
  "REDIS_RDB_POLICY": "3600#1 300#100 60#10000",
  "REDIS_URL": "redis://default:<EMAIL>:6379"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | redis-mxh1.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_RUN_AS_ROOT | true |
| RAILWAY_RUN_UID | 0 |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | Redis-mXH1 |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_TCP_APPLICATION_PORT | 6379 |
| RAILWAY_TCP_PROXY_DOMAIN | ballast.proxy.rlwy.net |
| RAILWAY_TCP_PROXY_PORT | 13185 |
| RAILWAY_VOLUME_ID | ************************************ |
| RAILWAY_VOLUME_MOUNT_PATH | /bitnami |
| RAILWAY_VOLUME_NAME | overflowing-volume |
| REDISHOST | redis-mxh1.railway.internal |
| REDISPASSWORD | YtZiaCCiPBWyKzZaargxcAuzVpxRiKea |
| REDISPORT | 6379 |
| REDISUSER | default |
| REDIS_AOF_ENABLED | no |
| REDIS_PASSWORD | YtZiaCCiPBWyKzZaargxcAuzVpxRiKea |
| REDIS_PUBLIC_URL | redis://default:<EMAIL>:13185 |
| REDIS_RDB_POLICY | 3600#1 300#100 60#10000 |
| REDIS_URL | redis://default:<EMAIL>:6379 |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 8fc25e34-58c7-49d8-946f-9d425dd9ecd8 | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "Redis-mXH1",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "8fc25e34-58c7-49d8-946f-9d425dd9ecd8",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "18b17783-61bc-464b-919c-e0b707f4787d",
            "meta": {
              "image": "bitnami/redis:7.2.5",
              "logsV2": true,
              "patchId": "5edb47dd-1ffd-401c-a845-bd81b95a7128",
              "reason": "deploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": "/bitnami",
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/bitnami"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "bitnami/redis:7.2.5"
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "18b17783-61bc-464b-919c-e0b707f4787d",
  "meta": {
    "image": "bitnami/redis:7.2.5",
    "logsV2": true,
    "patchId": "5edb47dd-1ffd-401c-a845-bd81b95a7128",
    "reason": "deploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": "/bitnami",
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/bitnami"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Redis-mXH1

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "Redis-mXH1" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
