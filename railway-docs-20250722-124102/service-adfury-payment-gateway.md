# Service: adfury-payment-gateway

**Generated:** Tue Jul 22 12:41:26 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** adfury-payment-gateway
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "AUTH0_AUDIENCE": "http://localhost:5000",
  "AUTH0_CALLBACK_URL": "http://localhost:5000/callback",
  "AUTH0_CLIENT_ID": "O98PyUH72xNTjJo3Fswn7FgEwhNJuLle",
  "AUTH0_CLIENT_SECRET": "****************************************************************",
  "AUTH0_DB_CONNECTION_ID": "con_tbGgZwtsbQciTp86",
  "AUTH0_DOMAIN": "dev-zjqwcrjrkyr2dfsm.us.auth0.com",
  "AUTH0_PASSWORD_RESET_REDIRECT_URL": "http://localhost:3000/settings",
  "COOKIE_SECRET": "****************************************************************",
  "DATABASE_URL": "postgresql://AdVid_owner:<EMAIL>/AdVid?sslmode=require",
  "JWT_SECRET": "****************************************************************",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "sweet-miracle.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "adfury-payment-gateway",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "adfury-payment-gateway-production.up.railway.app",
  "STRIPE_SECRET_KEY": "sk_test_51QjtU1AYoRIs6MbFVHdVRxfdDN6bmbJiKineZRlq3u1SFN3j8VJXjk2eXEFUllJBRE3tl8dK091ZusxCgw8ZPxb500zaSJqN9y",
  "STRIPE_WEBHOOK_SECRET": "whsec_VyPfBlYOlvvsvN7cXRuZYhbQB43qJUNG"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| AUTH0_AUDIENCE | http://localhost:5000 |
| AUTH0_CALLBACK_URL | http://localhost:5000/callback |
| AUTH0_CLIENT_ID | O98PyUH72xNTjJo3Fswn7FgEwhNJuLle |
| AUTH0_CLIENT_SECRET | **************************************************************** |
| AUTH0_DB_CONNECTION_ID | con_tbGgZwtsbQciTp86 |
| AUTH0_DOMAIN | dev-zjqwcrjrkyr2dfsm.us.auth0.com |
| AUTH0_PASSWORD_RESET_REDIRECT_URL | http://localhost:3000/settings |
| COOKIE_SECRET | **************************************************************** |
| DATABASE_URL | postgresql://AdVid_owner:<EMAIL>/AdVid?sslmode=require |
| JWT_SECRET | **************************************************************** |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | sweet-miracle.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | adfury-payment-gateway |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | adfury-payment-gateway-production.up.railway.app |
| STRIPE_SECRET_KEY | sk_test_51QjtU1AYoRIs6MbFVHdVRxfdDN6bmbJiKineZRlq3u1SFN3j8VJXjk2eXEFUllJBRE3tl8dK091ZusxCgw8ZPxb500zaSJqN9y |
| STRIPE_WEBHOOK_SECRET | whsec_VyPfBlYOlvvsvN7cXRuZYhbQB43qJUNG |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| a22ae4a8-88dc-47e0-94f2-046e67594f77 | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "adfury-payment-gateway",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "a22ae4a8-88dc-47e0-94f2-046e67594f77",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "8d3e95d4-1225-4379-8265-702e7bc5a166",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "alpendergrast",
              "commitHash": "9512fca373a9088ddf951e0d55763cab3c73c067",
              "commitMessage": ".",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {},
              "reason": "migrate",
              "repo": "AdVid-ai/payment-gateway",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/payment-gateway",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "8d3e95d4-1225-4379-8265-702e7bc5a166",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "alpendergrast",
    "commitHash": "9512fca373a9088ddf951e0d55763cab3c73c067",
    "commitMessage": ".",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {},
    "reason": "migrate",
    "repo": "AdVid-ai/payment-gateway",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service adfury-payment-gateway

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "adfury-payment-gateway" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
