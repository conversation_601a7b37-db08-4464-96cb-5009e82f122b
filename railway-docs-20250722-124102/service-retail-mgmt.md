# Service: retail-mgmt

**Generated:** Tue Jul 22 12:41:39 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** retail-mgmt
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "APIFY_ACTOR_ID": "dKylGAj0fF0pCjGeW",
  "APIFY_TOKEN": "**********************************************",
  "AUTH0_AUDIENCE": "http://localhost:5005",
  "AUTH0_CALLBACK_URL": "http://localhost:5000/callback",
  "AUTH0_CLIENT_ID": "O98PyUH72xNTjJo3Fswn7FgEwhNJuLle",
  "AUTH0_CLIENT_SECRET": "****************************************************************",
  "AUTH0_DOMAIN": "dev-zjqwcrjrkyr2dfsm.us.auth0.com",
  "COOKIE_SECRET": "****************************************************************",
  "DATABASE_URL": "postgresql://AdVid_owner:<EMAIL>/AdVid?sslmode=require",
  "JWT_SECRET": "****************************************************************",
  "NODE_ENV": "production",
  "PORT": "5005",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "retail-mgmt.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "retail-mgmt",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "retail-mgmt-production.up.railway.app"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| APIFY_ACTOR_ID | dKylGAj0fF0pCjGeW |
| APIFY_TOKEN | ********************************************** |
| AUTH0_AUDIENCE | http://localhost:5005 |
| AUTH0_CALLBACK_URL | http://localhost:5000/callback |
| AUTH0_CLIENT_ID | O98PyUH72xNTjJo3Fswn7FgEwhNJuLle |
| AUTH0_CLIENT_SECRET | **************************************************************** |
| AUTH0_DOMAIN | dev-zjqwcrjrkyr2dfsm.us.auth0.com |
| COOKIE_SECRET | **************************************************************** |
| DATABASE_URL | postgresql://AdVid_owner:<EMAIL>/AdVid?sslmode=require |
| JWT_SECRET | **************************************************************** |
| NODE_ENV | production |
| PORT | 5005 |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | retail-mgmt.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | retail-mgmt |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | retail-mgmt-production.up.railway.app |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| ce4c1c6d-2081-4729-afaf-f7589e081581 | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "retail-mgmt",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "ce4c1c6d-2081-4729-afaf-f7589e081581",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "8a91d03c-ba43-43f9-a4f7-240650c8b273",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "9e369b30593d03a2d625d9d6345990d4a91e1570",
              "commitMessage": "strip duplicates from final images array",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "builder": "NIXPACKS"
                },
                "deploy": {
                  "healthcheckPath": "/health",
                  "healthcheckTimeout": 100,
                  "restartPolicyType": "ON_FAILURE",
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.buildCommand": "$.build.buildCommand",
                "build.builder": "$.build.builder",
                "deploy.envs": "$.deploy.envs",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "migrate",
              "repo": "AdVid-ai/retail-mgmt",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": 100,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/retail-mgmt",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "8a91d03c-ba43-43f9-a4f7-240650c8b273",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "9e369b30593d03a2d625d9d6345990d4a91e1570",
    "commitMessage": "strip duplicates from final images array",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "buildCommand": "npm install",
        "builder": "NIXPACKS"
      },
      "deploy": {
        "healthcheckPath": "/health",
        "healthcheckTimeout": 100,
        "restartPolicyType": "ON_FAILURE",
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.buildCommand": "$.build.buildCommand",
      "build.builder": "$.build.builder",
      "deploy.envs": "$.deploy.envs",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.restartPolicyType": "$.deploy.restartPolicyType",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "migrate",
    "repo": "AdVid-ai/retail-mgmt",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": "npm install",
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": 100,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service retail-mgmt

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "retail-mgmt" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
