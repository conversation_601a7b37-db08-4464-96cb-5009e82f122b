{"id": "11bc08a2-0d5e-4495-a27a-94444a59d72d", "name": "marketplace-item-scheduler", "serviceInstances": {"edges": [{"node": {"id": "c0b5b915-e941-4ccd-a834-34fee879a9fd", "serviceId": "11bc08a2-0d5e-4495-a27a-94444a59d72d", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "45f59db2-d681-4908-80d9-19672a00a875", "meta": {"duration": 0, "image": "ghcr.io/railwayapp/function-bun:1.2.16", "logsV2": true, "patchId": "91d672dd-abd0-44c5-a0ee-2a48bfee16f3", "reason": "deploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": "0 * * * *", "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": null, "restartPolicyType": "NEVER", "runtime": "V2", "sleepApplication": false, "startCommand": "./run.sh Ly8gaW5kZXgudHN4IChCdW4gdjEuMiBydW50aW1lKQppbXBvcnQgeyB6IH0gZnJvbSAiem9kQDMiOwoKY29uc3QgZmFjdCA9IHoub2JqZWN0KHsKICBpZDogei5udW1iZXIoKSwKICBmYWN0OiB6LnN0cmluZygpLAp9KTsKCi8vIEZ1biBmYWN0IQovLyBUaGlzIEFQSSBpcyBob3N0ZWQgb24gUmFpbHdheSBGdW5jdGlvbnM6Ci8vICAgaHR0cHM6Ly9yYWlsd2F5LmNvbS9wcm9qZWN0LzAxNjJlMjgwLTFlMzYtNDU4Mi1iNjcxLTAxMTc1YmI2YmI2Zi9zZXJ2aWNlLzQxODEyOTEzLTgzY2YtNDQyMC04YWFkLThhZDVmZDc0MDMzMi9zb3VyY2UtY29kZT9lbnZpcm9ubWVudElkPTgxY2QxMjJhLWNmYmEtNDVjOC05MWU0LWE5NmVlM2U0YTA1OApjb25zdCByZXMgPSBhd2FpdCBmZXRjaCgiaHR0cHM6Ly9yYW5kb20tZmFjdHMudXAucmFpbHdheS5hcHAvZmFjdCIpOwpjb25zdCBkYXRhID0gZmFjdC5wYXJzZShhd2FpdCByZXMuanNvbigpKTsKY29uc3QgZGlhbG9nU2l6ZSA9IE1hdGgubWF4KGRhdGEuZmFjdC5sZW5ndGggKyAyLCAzMik7Cgpjb25zb2xlLmxvZyhgCiR7Ii0iLnJlcGVhdChkaWFsb2dTaXplKX0KPCAke2RhdGEuZmFjdH0gPgokeyItIi5yZXBlYXQoZGlhbG9nU2l6ZSl9CiAgICAgICAgICBcXCAgICBfX1/ilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITila4KICAgICAgICAgICAgLC3ilbxfX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19ffAogICAgICAgICAgLC9fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19ffAogICAgICAgICAoXyAgICAgICAgICzilITilITilITilITilIQuICAgICAgICAgICAgICAgICAgICAgICzilITilITilITilITilITilITilITilIQuICB8Cl9fX19fX19fX19fLS5fX19fXy4nKF8pKF8pXF9fX19fX19fX19fX19fX19fX19fX19fLihfKT0oXylcX18vCmApOwoK"}}, "volumeMounts": []}}, "source": {"repo": null, "image": "ghcr.io/railwayapp/function-bun:1.2.16"}}}]}}