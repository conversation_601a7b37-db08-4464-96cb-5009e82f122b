{"DB_POSTGRESDB_DATABASE": "railway", "DB_POSTGRESDB_HOST": "postgres.railway.internal", "DB_POSTGRESDB_PASSWORD": "IeZrAr-MN*2e67a0~pc!Lb3ADW5sS4cA", "DB_POSTGRESDB_PORT": "5432", "DB_POSTGRESDB_USER": "railway", "DB_TYPE": "postgresdb", "ENABLE_ALPINE_PRIVATE_NETWORKING": "true", "EXECUTIONS_MODE": "queue", "N8N_ENCRYPTION_KEY": "6ijSb5x*FOdhRa7_eg9SuLiKtLLkdKF7", "N8N_LISTEN_ADDRESS": "::", "NODE_OPTIONS": "--max_old_space_size=8192", "PORT": "5678", "QUEUE_BULL_REDIS_HOST": "interchange.proxy.rlwy.net", "QUEUE_BULL_REDIS_PASSWORD": "Sy0fDf94wRW0tap17sAWJaLYLh2evk*S", "QUEUE_BULL_REDIS_PORT": "41842", "QUEUE_BULL_REDIS_USERNAME": "default", "RAILWAY_ENVIRONMENT": "production", "RAILWAY_ENVIRONMENT_ID": "************************************", "RAILWAY_ENVIRONMENT_NAME": "production", "RAILWAY_PRIVATE_DOMAIN": "worker.railway.internal", "RAILWAY_PROJECT_ID": "************************************", "RAILWAY_PROJECT_NAME": "advid", "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai", "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app", "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app", "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai", "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app", "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai", "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app", "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai", "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app", "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app", "RAILWAY_SERVICE_ID": "************************************", "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app", "RAILWAY_SERVICE_NAME": "Worker", "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app", "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app", "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app", "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app", "WEBHOOK_URL": "https://primary-production-35c9.up.railway.app"}