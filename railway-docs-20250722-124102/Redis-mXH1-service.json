{"id": "c400c30d-b6ab-4b43-bc3a-c34276a33021", "name": "Redis-mXH1", "serviceInstances": {"edges": [{"node": {"id": "8fc25e34-58c7-49d8-946f-9d425dd9ecd8", "serviceId": "c400c30d-b6ab-4b43-bc3a-c34276a33021", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "18b17783-61bc-464b-919c-e0b707f4787d", "meta": {"image": "bitnami/redis:7.2.5", "logsV2": true, "patchId": "5edb47dd-1ffd-401c-a845-bd81b95a7128", "reason": "deploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": "/bitnami", "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": ["/bitnami"]}}, "source": {"repo": null, "image": "bitnami/redis:7.2.5"}}}]}}