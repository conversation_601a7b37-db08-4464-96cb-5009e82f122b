{"id": "463cd6d1-a8e9-4832-9a4c-7a70229542b3", "name": "adfury-email-service-stg", "serviceInstances": {"edges": [{"node": {"id": "66a90e81-0484-474d-888f-41474c791bc6", "serviceId": "463cd6d1-a8e9-4832-9a4c-7a70229542b3", "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562", "latestDeployment": {"canRedeploy": true, "id": "4a6a9a63-c3ee-41fc-8e39-7c3bdab8e227", "meta": {"branch": "staging", "buildOnly": false, "commitAuthor": "bryce27", "commitHash": "da1661e45f95bc46d7f9c33c56d3245bfdb0044c", "commitMessage": "Merge pull request #4 from AdVid-ai/staging\n\nfix: resolve Gmail spam warnings and improve email deliverability", "configFile": "railway.json", "fileServiceManifest": {"build": {"buildCommand": "npm run build", "builder": "NIXPACKS"}, "deploy": {"healthcheckPath": "/ready", "healthcheckTimeout": 60, "numReplicas": 1, "restartPolicyMaxRetries": 3, "restartPolicyType": "ON_FAILURE", "startCommand": "npm start"}}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {"build.buildCommand": "$.build.buildCommand", "build.builder": "$.build.builder", "deploy.healthcheckPath": "$.deploy.healthcheckPath", "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout", "deploy.numReplicas": "$.deploy.numReplicas", "deploy.restartPolicyMaxRetries": "$.deploy.restartPolicyMaxRetries", "deploy.restartPolicyType": "$.deploy.restartPolicyType", "deploy.startCommand": "$.deploy.startCommand"}, "reason": "deploy", "repo": "AdVid-ai/adfury-email-service", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": "npm run build", "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": "/ready", "healthcheckTimeout": 60, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 3, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "npm start"}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/adfury-email-service", "image": null}}}]}}