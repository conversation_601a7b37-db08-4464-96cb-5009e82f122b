# Service: Redis

**Generated:** Tue Jul 22 12:41:43 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Redis
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "redis.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_RUN_UID": "0",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "Redis",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_TCP_APPLICATION_PORT": "6379",
  "RAILWAY_TCP_PROXY_DOMAIN": "interchange.proxy.rlwy.net",
  "RAILWAY_TCP_PROXY_PORT": "41842",
  "RAILWAY_VOLUME_ID": "************************************",
  "RAILWAY_VOLUME_MOUNT_PATH": "/bitnami",
  "RAILWAY_VOLUME_NAME": "blade-volume",
  "REDISHOST": "interchange.proxy.rlwy.net",
  "REDISHOST_PRIVATE": "redis.railway.internal",
  "REDISPORT": "41842",
  "REDISPORT_PRIVATE": "6379",
  "REDISUSER": "default",
  "REDIS_PASSWORD": "Sy0fDf94wRW0tap17sAWJaLYLh2evk*S",
  "REDIS_PRIVATE_URL": "redis://default:Sy0fDf94wRW0tap17sAWJaLYLh2evk*<EMAIL>:6379",
  "REDIS_URL": "redis://default:Sy0fDf94wRW0tap17sAWJaLYLh2evk*<EMAIL>:41842"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | redis.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_RUN_UID | 0 |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | Redis |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_TCP_APPLICATION_PORT | 6379 |
| RAILWAY_TCP_PROXY_DOMAIN | interchange.proxy.rlwy.net |
| RAILWAY_TCP_PROXY_PORT | 41842 |
| RAILWAY_VOLUME_ID | ************************************ |
| RAILWAY_VOLUME_MOUNT_PATH | /bitnami |
| RAILWAY_VOLUME_NAME | blade-volume |
| REDISHOST | interchange.proxy.rlwy.net |
| REDISHOST_PRIVATE | redis.railway.internal |
| REDISPORT | 41842 |
| REDISPORT_PRIVATE | 6379 |
| REDISUSER | default |
| REDIS_PASSWORD | Sy0fDf94wRW0tap17sAWJaLYLh2evk*S |
| REDIS_PRIVATE_URL | redis://default:Sy0fDf94wRW0tap17sAWJaLYLh2evk*<EMAIL>:6379 |
| REDIS_URL | redis://default:Sy0fDf94wRW0tap17sAWJaLYLh2evk*<EMAIL>:41842 |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 775ebe1d-1914-4b46-9be2-4d4ce11c36da | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "Redis",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "775ebe1d-1914-4b46-9be2-4d4ce11c36da",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "57b83c66-c020-4068-9831-9522dcd106b3",
            "meta": {
              "ignoreWatchPatterns": true,
              "image": "bitnami/redis",
              "logsV2": true,
              "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
              "reason": "redeploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/bitnami"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "bitnami/redis"
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "57b83c66-c020-4068-9831-9522dcd106b3",
  "meta": {
    "ignoreWatchPatterns": true,
    "image": "bitnami/redis",
    "logsV2": true,
    "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
    "reason": "redeploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/bitnami"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Redis

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "Redis" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
