# Service: adfury-app

**Generated:** Tue Jul 22 12:41:07 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** adfury-app
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "AUTH0_BASE_URL": "http://localhost:3000",
  "AUTH0_CLIENT_ID": "gccMKTvZWZqYaV7W1mBm3OUMHaTx6MNx",
  "AUTH0_CLIENT_SECRET": "****************************************************************",
  "AUTH0_ISSUER_BASE_URL": "https://dev-zjqwcrjrkyr2dfsm.us.auth0.com",
  "AUTH0_MGMT_BASE_URL": "https://dev-zjqwcrjrkyr2dfsm.us.auth0.com",
  "AUTH0_MGMT_CLIENT_ID": "tZjxCv7MtpbCQC4FauN0WCbg9aHUz4fs",
  "AUTH0_MGMT_CLIENT_SECRET": "****************************************************************",
  "AUTH0_SECRET": "****************************************************************",
  "AUTH_API_URL": "https://advid-server-production.up.railway.app",
  "BASE_ASSET_URL": "https://asset-generator-production.up.railway.app",
  "BILLING_API_URL": "https://adfury-payment-gateway-production.up.railway.app",
  "CANVA_CLIENT_ID": "OC-AZc2zx9dmZKe",
  "CANVA_CLIENT_SECRET": "cnvca8rJzsisQ9I4djyAsz4JM9mbhnLkaGbFfc7-9y2F58lU35d1b5d2",
  "CANVA_REDIRECT_URI": "https://app.adfury.ai/creative-builder/canva-callback",
  "CANVA_RETURN_URL": "https://app.adfury.ai/creative-builder/canva-return",
  "NEXT_PUBLIC_ADOBE_API_KEY": "5ce6c3d286064925ac65c72608e75a42",
  "NEXT_PUBLIC_ADOBE_EXPRESS_CLIENT_ID": "5ce6c3d286064925ac65c72608e75a42",
  "NEXT_PUBLIC_ADOBE_IMS_ORG_ID": "249C1E91676DAA8A0A495FCC@AdobeOrg",
  "NEXT_PUBLIC_ADOBE_ORG": "apendergrast-advid.ai",
  "NEXT_PUBLIC_API_URL": "https://app.adfury.ai",
  "NEXT_PUBLIC_APP_URL": "https://app.adfury.ai",
  "NEXT_PUBLIC_CANVA_CLIENT_ID": "OC-AZc2zx9dmZKe",
  "NEXT_PUBLIC_CANVA_REDIRECT_URI": "https://app.adfury.ai/creative-builder/canva-callback",
  "NEXT_PUBLIC_CANVA_RETURN_URL": "https://app.adfury.ai/creative-builder/canva-return",
  "NEXT_PUBLIC_POSTHOG_HOST": "https://us.i.posthog.com",
  "NEXT_PUBLIC_POSTHOG_KEY": "phc_q2f92mb0ZyG8aviZVjycI4jUrv9ReWjeZFpjNe4uxOe",
  "NEXT_PUBLIC_PUSHER_CLUSTER": "mt1",
  "NEXT_PUBLIC_PUSHER_FORCE_TLS": "true",
  "NEXT_PUBLIC_PUSHER_HOST": "https://soketi-production-7b57.up.railway.app/",
  "NEXT_PUBLIC_PUSHER_KEY": "kydfr6qwm6yirqzmkv29wl9srt7a88oh",
  "NEXT_PUBLIC_PUSHER_PORT": "6001",
  "NEXT_PUBLIC_PUSHER_WSS_PORT": "443",
  "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "pk_test_51QjtU1AYoRIs6MbFE5t7t7AoLtnMuOFIBHTm5gBCzpe5srGCf4XEaBSAaVt3E8Ab9XKnosrVfnEnPc53sgCj7Bqx008qxGSyn0",
  "NEXT_PUBLIC_SUPPLIER_ONE_CLIENT_ID": "0f01081e-8017-4f75-aea0-33fa3e093654",
  "NEXT_PUBLIC_WALMART_INTERNAL_ID": "4335884c-6ee7-430d-a0f9-992710a04de4",
  "NEXT_PUBLIC_WMT_SELLER_CLIENT_ID": "81b179dd-8a85-4373-9bc6-cad4c607a17b",
  "NOTION_API_KEY": "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",
  "NOTION_DATABASE_ID": "21e9dc78969f801b8779d2cf5d34aa9e",
  "NOTION_FEATURE_REQUESTS_DATABASE_ID": "1c59dc78969f8011b724e61eb9a895dd",
  "NOTION_FEEDBACK_DATABASE_ID": "2309dc78969f8062b049dd35da90d560",
  "NOTION_ISSUE_TRACKING_DATABASE_ID": "1c59dc78969f8016bc05fa9c44ec95c4",
  "NOTION_NEWSLETTER_DATABASE_ID": "2319dc78969f80e88cc9fb2800ac53f7",
  "NOTION_TICKETS_DATABASE_ID": "2259dc78969f80e98a88f1f90810c6fe",
  "NO_CACHE": "1",
  "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
  "OPENROUTER_API_KEY": "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",
  "PUSHER_APP_ID": "RCYPSexV",
  "PUSHER_CLUSTER": "mt1",
  "PUSHER_HOST": "https://soketi-production-7b57.up.railway.app/",
  "PUSHER_KEY": "kydfr6qwm6yirqzmkv29wl9srt7a88oh",
  "PUSHER_PORT": "443",
  "PUSHER_SECRET": "oi3si7s0m7xk56huvly5h3x0i04xugyf",
  "PUSHER_USE_TLS": "true",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "advid-app.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "adfury-app",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "app.adfury.ai",
  "REDIS_URL": "redis://default:<EMAIL>:38358",
  "STRIPE_SUBSCRIPTION_PRICE_ID": "price_1QzqN0AYoRIs6MbFQswWUOdl",
  "USE_NOTION_TICKETS": "true",
  "WALMART_MANAGER_API_URL": "https://advid-wm-service-production.up.railway.app",
  "WALMART_MANAGER_PROD_API_URL": "https://advid-wm-service-production.up.railway.app"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| AUTH0_BASE_URL | http://localhost:3000 |
| AUTH0_CLIENT_ID | gccMKTvZWZqYaV7W1mBm3OUMHaTx6MNx |
| AUTH0_CLIENT_SECRET | **************************************************************** |
| AUTH0_ISSUER_BASE_URL | https://dev-zjqwcrjrkyr2dfsm.us.auth0.com |
| AUTH0_MGMT_BASE_URL | https://dev-zjqwcrjrkyr2dfsm.us.auth0.com |
| AUTH0_MGMT_CLIENT_ID | tZjxCv7MtpbCQC4FauN0WCbg9aHUz4fs |
| AUTH0_MGMT_CLIENT_SECRET | **************************************************************** |
| AUTH0_SECRET | **************************************************************** |
| AUTH_API_URL | https://advid-server-production.up.railway.app |
| BASE_ASSET_URL | https://asset-generator-production.up.railway.app |
| BILLING_API_URL | https://adfury-payment-gateway-production.up.railway.app |
| CANVA_CLIENT_ID | OC-AZc2zx9dmZKe |
| CANVA_CLIENT_SECRET | cnvca8rJzsisQ9I4djyAsz4JM9mbhnLkaGbFfc7-9y2F58lU35d1b5d2 |
| CANVA_REDIRECT_URI | https://app.adfury.ai/creative-builder/canva-callback |
| CANVA_RETURN_URL | https://app.adfury.ai/creative-builder/canva-return |
| NEXT_PUBLIC_ADOBE_API_KEY | 5ce6c3d286064925ac65c72608e75a42 |
| NEXT_PUBLIC_ADOBE_EXPRESS_CLIENT_ID | 5ce6c3d286064925ac65c72608e75a42 |
| NEXT_PUBLIC_ADOBE_IMS_ORG_ID | 249C1E91676DAA8A0A495FCC@AdobeOrg |
| NEXT_PUBLIC_ADOBE_ORG | apendergrast-advid.ai |
| NEXT_PUBLIC_API_URL | https://app.adfury.ai |
| NEXT_PUBLIC_APP_URL | https://app.adfury.ai |
| NEXT_PUBLIC_CANVA_CLIENT_ID | OC-AZc2zx9dmZKe |
| NEXT_PUBLIC_CANVA_REDIRECT_URI | https://app.adfury.ai/creative-builder/canva-callback |
| NEXT_PUBLIC_CANVA_RETURN_URL | https://app.adfury.ai/creative-builder/canva-return |
| NEXT_PUBLIC_POSTHOG_HOST | https://us.i.posthog.com |
| NEXT_PUBLIC_POSTHOG_KEY | phc_q2f92mb0ZyG8aviZVjycI4jUrv9ReWjeZFpjNe4uxOe |
| NEXT_PUBLIC_PUSHER_CLUSTER | mt1 |
| NEXT_PUBLIC_PUSHER_FORCE_TLS | true |
| NEXT_PUBLIC_PUSHER_HOST | https://soketi-production-7b57.up.railway.app/ |
| NEXT_PUBLIC_PUSHER_KEY | kydfr6qwm6yirqzmkv29wl9srt7a88oh |
| NEXT_PUBLIC_PUSHER_PORT | 6001 |
| NEXT_PUBLIC_PUSHER_WSS_PORT | 443 |
| NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY | pk_test_51QjtU1AYoRIs6MbFE5t7t7AoLtnMuOFIBHTm5gBCzpe5srGCf4XEaBSAaVt3E8Ab9XKnosrVfnEnPc53sgCj7Bqx008qxGSyn0 |
| NEXT_PUBLIC_SUPPLIER_ONE_CLIENT_ID | 0f01081e-8017-4f75-aea0-33fa3e093654 |
| NEXT_PUBLIC_WALMART_INTERNAL_ID | 4335884c-6ee7-430d-a0f9-992710a04de4 |
| NEXT_PUBLIC_WMT_SELLER_CLIENT_ID | 81b179dd-8a85-4373-9bc6-cad4c607a17b |
| NOTION_API_KEY | 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 |
| NOTION_DATABASE_ID | 21e9dc78969f801b8779d2cf5d34aa9e |
| NOTION_FEATURE_REQUESTS_DATABASE_ID | 1c59dc78969f8011b724e61eb9a895dd |
| NOTION_FEEDBACK_DATABASE_ID | 2309dc78969f8062b049dd35da90d560 |
| NOTION_ISSUE_TRACKING_DATABASE_ID | 1c59dc78969f8016bc05fa9c44ec95c4 |
| NOTION_NEWSLETTER_DATABASE_ID | 2319dc78969f80e88cc9fb2800ac53f7 |
| NOTION_TICKETS_DATABASE_ID | 2259dc78969f80e98a88f1f90810c6fe |
| NO_CACHE | 1 |
| OPENAI_API_KEY | ******************************************************************************************************************************************************************** |
| OPENROUTER_API_KEY | 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 |
| PUSHER_APP_ID | RCYPSexV |
| PUSHER_CLUSTER | mt1 |
| PUSHER_HOST | https://soketi-production-7b57.up.railway.app/ |
| PUSHER_KEY | kydfr6qwm6yirqzmkv29wl9srt7a88oh |
| PUSHER_PORT | 443 |
| PUSHER_SECRET | oi3si7s0m7xk56huvly5h3x0i04xugyf |
| PUSHER_USE_TLS | true |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | advid-app.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | adfury-app |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | app.adfury.ai |
| REDIS_URL | redis://default:<EMAIL>:38358 |
| STRIPE_SUBSCRIPTION_PRICE_ID | price_1QzqN0AYoRIs6MbFQswWUOdl |
| USE_NOTION_TICKETS | true |
| WALMART_MANAGER_API_URL | https://advid-wm-service-production.up.railway.app |
| WALMART_MANAGER_PROD_API_URL | https://advid-wm-service-production.up.railway.app |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| ab39dd90-b997-4a37-be1b-6246232532ff | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "adfury-app",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "ab39dd90-b997-4a37-be1b-6246232532ff",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "fbc0d7fe-a715-4b19-b951-3ed80ca55f98",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "alpendergrast",
              "commitHash": "b08ca1993fec98ea936e3c7db9cc1b2266bb6836",
              "commitMessage": "fix advertiser id detection from builder",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {},
              "reason": "deploy",
              "repo": "AdVid-ai/advid-app",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-app",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "fbc0d7fe-a715-4b19-b951-3ed80ca55f98",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "alpendergrast",
    "commitHash": "b08ca1993fec98ea936e3c7db9cc1b2266bb6836",
    "commitMessage": "fix advertiser id detection from builder",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {},
    "reason": "deploy",
    "repo": "AdVid-ai/advid-app",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service adfury-app

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "adfury-app" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
