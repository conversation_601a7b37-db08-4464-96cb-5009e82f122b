{"id": "8222e98b-23d3-4062-a0d8-7362bb04f90b", "name": "adfury-app-demo", "serviceInstances": {"edges": [{"node": {"id": "47c05e1e-6691-4cc9-a67a-985cc245f16e", "serviceId": "8222e98b-23d3-4062-a0d8-7362bb04f90b", "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562", "latestDeployment": {"canRedeploy": true, "id": "2761f58a-9ad2-4130-8bc1-1b9af2ea76d2", "meta": {"branch": "staging", "buildOnly": false, "commitAuthor": "alpendergrast", "commitHash": "4ed2c1ef1b6fb4f42de9b126f38eded3be8dda40", "commitMessage": "Merge branch 'main' of github.com:AdVid-ai/advid-app into staging", "fileServiceManifest": {}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {}, "reason": "deploy", "repo": "AdVid-ai/advid-app", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-west2": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/advid-app", "image": null}}}, {"node": {"id": "a744000a-a192-406e-a0cf-0c42277878c0", "serviceId": "8222e98b-23d3-4062-a0d8-7362bb04f90b", "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42", "latestDeployment": {"canRedeploy": true, "id": "d0d2268f-3d63-4669-a1b3-f8a187704fb2", "meta": {"branch": "staging", "buildOnly": false, "commitAuthor": "alpendergrast", "commitHash": "4ed2c1ef1b6fb4f42de9b126f38eded3be8dda40", "commitMessage": "Merge branch 'main' of github.com:AdVid-ai/advid-app into staging", "fileServiceManifest": {}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {}, "reason": "deploy", "repo": "AdVid-ai/advid-app", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-west2": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/advid-app", "image": null}}}]}}