# Service: asset-generator-stg

**Generated:** Tue Jul 22 12:41:35 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** asset-generator-stg
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "asset-generator-stg",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | asset-generator-stg |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |


## Service Instances

**Total Instances:** 2

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| bbde1137-0507-449e-951a-73ac991f1172 | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |
| c712444f-cc5d-4ff2-b7db-4272dd61cdc5 | Unknown | 89aed380-ce64-4a52-b022-20e17d3a5562 |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "asset-generator-stg",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "bbde1137-0507-449e-951a-73ac991f1172",
          "serviceId": "************************************",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "9907055f-8340-4814-9ae6-0915ada8e42e",
            "meta": {
              "branch": "staging",
              "buildOnly": false,
              "commitAuthor": "JRippelmeyer",
              "commitHash": "3eba527548d4d24d2b291f6dfad6ef2555f7c8e0",
              "commitMessage": "Merge pull request #14 from AdVid-ai/main\n\nMerge main into staging",
              "configFile": "railway.toml",
              "deprecatedRegions": [
                {
                  "region": "us-west1",
                  "replacementRegion": "us-west2"
                }
              ],
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile"
                },
                "deploy": {
                  "healthcheckPath": "/api/health",
                  "healthcheckTimeout": 100,
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.builder": "$.build.builder",
                "build.dockerfilePath": "$.build.dockerfilePath",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "deploy",
              "repo": "AdVid-ai/asset-generator",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": "/api/health",
                  "healthcheckTimeout": 100,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west1": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/asset-generator",
            "image": null
          }
        }
      },
      {
        "node": {
          "id": "c712444f-cc5d-4ff2-b7db-4272dd61cdc5",
          "serviceId": "************************************",
          "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "75f8f61f-4ac4-4539-a0dd-9582b57f46db",
            "meta": {
              "branch": "staging",
              "buildOnly": false,
              "commitAuthor": "JRippelmeyer",
              "commitHash": "3eba527548d4d24d2b291f6dfad6ef2555f7c8e0",
              "commitMessage": "Merge pull request #14 from AdVid-ai/main\n\nMerge main into staging",
              "configFile": "railway.toml",
              "deprecatedRegions": [
                {
                  "region": "us-west1",
                  "replacementRegion": "us-west2"
                }
              ],
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile"
                },
                "deploy": {
                  "healthcheckPath": "/api/health",
                  "healthcheckTimeout": 100,
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.builder": "$.build.builder",
                "build.dockerfilePath": "$.build.dockerfilePath",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "deploy",
              "repo": "AdVid-ai/asset-generator",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": "/api/health",
                  "healthcheckTimeout": 100,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west1": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/asset-generator",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "9907055f-8340-4814-9ae6-0915ada8e42e",
  "meta": {
    "branch": "staging",
    "buildOnly": false,
    "commitAuthor": "JRippelmeyer",
    "commitHash": "3eba527548d4d24d2b291f6dfad6ef2555f7c8e0",
    "commitMessage": "Merge pull request #14 from AdVid-ai/main\n\nMerge main into staging",
    "configFile": "railway.toml",
    "deprecatedRegions": [
      {
        "region": "us-west1",
        "replacementRegion": "us-west2"
      }
    ],
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile"
      },
      "deploy": {
        "healthcheckPath": "/api/health",
        "healthcheckTimeout": 100,
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.builder": "$.build.builder",
      "build.dockerfilePath": "$.build.dockerfilePath",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "deploy",
    "repo": "AdVid-ai/asset-generator",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": "/api/health",
        "healthcheckTimeout": 100,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west1": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
{
  "canRedeploy": true,
  "id": "75f8f61f-4ac4-4539-a0dd-9582b57f46db",
  "meta": {
    "branch": "staging",
    "buildOnly": false,
    "commitAuthor": "JRippelmeyer",
    "commitHash": "3eba527548d4d24d2b291f6dfad6ef2555f7c8e0",
    "commitMessage": "Merge pull request #14 from AdVid-ai/main\n\nMerge main into staging",
    "configFile": "railway.toml",
    "deprecatedRegions": [
      {
        "region": "us-west1",
        "replacementRegion": "us-west2"
      }
    ],
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile"
      },
      "deploy": {
        "healthcheckPath": "/api/health",
        "healthcheckTimeout": 100,
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.builder": "$.build.builder",
      "build.dockerfilePath": "$.build.dockerfilePath",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "deploy",
    "repo": "AdVid-ai/asset-generator",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": "/api/health",
        "healthcheckTimeout": 100,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west1": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service asset-generator-stg

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "asset-generator-stg" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
