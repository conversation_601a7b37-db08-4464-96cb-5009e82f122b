# Service: asset-generator

**Generated:** Tue Jul 22 12:41:47 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** asset-generator
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "CALLBACK_URL": "https://asset-generator-production.up.railway.app/api/expressGeneration/callback",
  "CLAID_API_KEY": "78a8f798bcd74891931c1687398bd321",
  "CLOUDINARY_API_KEY": "***************",
  "CLOUDINARY_API_SECRET": "YCoggaRVo6t9xm5DFn3zJfe4Q8w",
  "CLOUDINARY_CLOUD_NAME": "ddqdw7gvi",
  "EDEN_AI_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZWNmNzI3MTQtY2IzYS00MTEwLTk2MjctZTA5ZTFkYTQ2MTc1IiwidHlwZSI6ImFwaV90b2tlbiJ9.KlZv4aNTiQHxu6vPX3KbXeMxp1Nkpvx6mTMOh2exLg0",
  "GOOGLE_CLOUD_AUTH_PROVIDER_X509_CERT_URL": "https://www.googleapis.com/oauth2/v1/certs",
  "GOOGLE_CLOUD_AUTH_URI": "https://accounts.google.com/o/oauth2/auth",
  "GOOGLE_CLOUD_CLIENT_EMAIL": "<EMAIL>",
  "GOOGLE_CLOUD_CLIENT_ID": "115713899329077171498",
  "GOOGLE_CLOUD_CLIENT_X509_CERT_URL": "https://www.googleapis.com/robot/v1/metadata/x509/storage-service-account%40advid-gradient-bgs.iam.gserviceaccount.com",
  "GOOGLE_CLOUD_PRIVATE_KEY": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCpiixubmGadr+R\nn00Ve9JJ1e8ySMmpAyCmmut9DlnyrkQwygyDnx7hgMKJCg0L4fSJovoVHxyZOShu\nX3UR/RzLUwjoESJHIwW2LK7TxNfrcnEhdhKZFF5RJa+nqMIPvA3837BG32i9PySJ\nS/Zp3VJZPuiXxwdce5VHPvOIu2wXVhCswffm8i9H63c0zc/fn+KDFlJsjgiH/lBI\nlfYd7pzBkk3h3vJWbwKIfpeLVM3Rbvb+Z8p+OHpH907KZCwJU6/uiCMtg8afw5Q3\nppqd+G1WEylCC2zkd2x+YDT2p/NPiookb9XQZLzFazx1PRh2kL9aaK+TFH+U9l9E\nt6cmg09tAgMBAAECggEAASrXo/oEF4duSb8mTXnRCJudvkOkidKg8Ky75txIr8RV\n6V0WyXg4wvY7QKzOSrDnWWRDIw6YCJENb3ceS/2pa1f6xXhKu2EpGqhDmuct0mvz\nDfctaRrsDFR9WWLu1SNnGEDlCpPiuXzcEEry2ydAOCeX++WNriwWqssq65vYwm9d\nJG2tdHlCFdooRePhCBPuotKdM2Z+e2GUkGsb2aSqJsKbQkfywt3xraytHO6JNIoG\nvtOHiImvwYCGUzmqh5abT5LmmSpSJZrD7JbdfcdNawkTVMiuNGZE5W/CDue2SwJ5\nkWZu9cq6rlIkoKiipb3eAPlSm7u6jsxB6ichTQ6PIQKBgQDiyzlaDX+ATKGmPRQS\nKkMow9IsD/giFLCdPaDDXaJ7LzQxfIBhXgsymH0Cu7VxykXFWHSvCK23ieiyi78o\nCdWoxoRatk+YyyY9juDq1ZL2PwALIamwkfQICS3NXMO4hhRAVusklzieRr3J0mri\nzgjbW/WDYKTkgac734DAfNSj4QKBgQC/X3ABL2q1KNHCMMvMMwrpQb+G8EKSasIr\n4ZZySgEUJAVQy15CN/RPO2ZCEFp+u7f+0MSGgFaLCer3uVS88IXivvEbHTBQepp8\nU4LW/NkV40+ct5BnUmLjRwPjUxuSc+3ygexOYg2kNeCtUtKabRizJ0DG5l3amXEO\ndKX6jfadDQKBgQDONeQm2mEVAhh089I9FgpMlYiTIc4HBvLzdean3IpxISF+4WpF\nneHmljoMcypulXFNF7m4sNZrLMzVEzWzLQYFWMNdS6kiD4zsqe4d+CWRGwVqGaiV\n8I+HgvkPYgpfzCG7wow5j/oeB8pnKJihMp9g/MNGOmiTkMPDL9x8qMQbYQKBgBnD\niHmwX4ZKG4swNI+mI4bpohZRjDTYCJINycpTKjgC7XBGIMd7bvmnvomQWA6NySSW\nCI8vAfev+yEko0LYgr3XBPCoilObXeb0+z1kRvmKY7JuiBNQ5R1B90UIcELUNqma\nUizvSHTeCARtjQrbOyMLWs4sP2Bpne3bSGkl6ZiVAoGAHLHTpAaoTqzSkOd6qWe1\n3M9qESu2K2LakLueQgtfyjh5Rf6VvqoBasWsRgiM9+eyaUQWP5YIqojX45+vB/6p\n2Ojnd+KBb3pnjrv/AuVuOX58LHt0eXqgGtrKsn2jT98sIDhKG0sNktB6c/OVNJ4b\nPubELLPpgaGkfMRiaqkVbmI=\n-----END PRIVATE KEY-----\n",
  "GOOGLE_CLOUD_PRIVATE_KEY_ID": "754231d842f8becfe3db8b1ae7b12408d22b5175",
  "GOOGLE_CLOUD_PROJECT_ID": "advid-gradient-bgs",
  "GOOGLE_CLOUD_TOKEN_URI": "https://oauth2.googleapis.com/token",
  "GOOGLE_CLOUD_TYPE": "service_account",
  "GOOGLE_CLOUD_UNIVERSE_DOMAIN": "googleapis.com",
  "LANGFLOW_API_KEY": "sk-prPaNmnt1xmXF9jy7vKz2TkNP6Or0UYzoTWf9cyz3xM",
  "LANGFLOW_API_TOKEN": "AstraCS:lLZmcbIQaqBatmYvEIOaEjci:404c3cc0b04aac0d31873dd481b024c928cbc6e1db8734e296bf3f91c5450cc5",
  "LANGFLOW_BASE_URL": "https://langflowailangflowlatest-production-01c9.up.railway.app",
  "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
  "PUSHER_APP_ID": "RCYPSexV",
  "PUSHER_CLUSTER": "mt1",
  "PUSHER_HOST": "https://soketi-production-7b57.up.railway.app/",
  "PUSHER_KEY": "kydfr6qwm6yirqzmkv29wl9srt7a88oh",
  "PUSHER_PORT": "443",
  "PUSHER_SECRET": "oi3si7s0m7xk56huvly5h3x0i04xugyf",
  "PUSHER_USE_TLS": "true",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "asset-generator.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "asset-generator",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "asset-generator-production.up.railway.app",
  "REDIS_URL": "redis://default:<EMAIL>:38358",
  "REMOVE_BG_API_KEY": "3kfEVGVVJcBZYEAxp8y4fHWA",
  "UPLOADTHING_APP_ID": "upl-1234567890",
  "UPLOADTHING_SECRET": "************************************************************************",
  "UPLOAD_THING_API_TOKEN": "UPLOADTHING_TOKEN='eyJhcGlLZXkiOiJza19saXZlX2UyMjhjZjUxNTY5OGQyMGMzMDA4NWZjODQyYzg3NjZjZmE2NGM2MGIxODMyOGQxODlmNjZkMjU2MGNlNDBkYTgiLCJhcHBJZCI6InlnM2g2d3p6MWkiLCJyZWdpb25zIjpbInNlYTEiXX0='"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| CALLBACK_URL | https://asset-generator-production.up.railway.app/api/expressGeneration/callback |
| CLAID_API_KEY | 78a8f798bcd74891931c1687398bd321 |
| CLOUDINARY_API_KEY | *************** |
| CLOUDINARY_API_SECRET | YCoggaRVo6t9xm5DFn3zJfe4Q8w |
| CLOUDINARY_CLOUD_NAME | ddqdw7gvi |
| EDEN_AI_API_KEY | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZWNmNzI3MTQtY2IzYS00MTEwLTk2MjctZTA5ZTFkYTQ2MTc1IiwidHlwZSI6ImFwaV90b2tlbiJ9.KlZv4aNTiQHxu6vPX3KbXeMxp1Nkpvx6mTMOh2exLg0 |
| GOOGLE_CLOUD_AUTH_PROVIDER_X509_CERT_URL | https://www.googleapis.com/oauth2/v1/certs |
| GOOGLE_CLOUD_AUTH_URI | https://accounts.google.com/o/oauth2/auth |
| GOOGLE_CLOUD_CLIENT_EMAIL | <EMAIL> |
| GOOGLE_CLOUD_CLIENT_ID | 115713899329077171498 |
| GOOGLE_CLOUD_CLIENT_X509_CERT_URL | https://www.googleapis.com/robot/v1/metadata/x509/storage-service-account%40advid-gradient-bgs.iam.gserviceaccount.com |
| GOOGLE_CLOUD_PRIVATE_KEY | ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************ |
| GOOGLE_CLOUD_PRIVATE_KEY_ID | 754231d842f8becfe3db8b1ae7b12408d22b5175 |
| GOOGLE_CLOUD_PROJECT_ID | advid-gradient-bgs |
| GOOGLE_CLOUD_TOKEN_URI | https://oauth2.googleapis.com/token |
| GOOGLE_CLOUD_TYPE | service_account |
| GOOGLE_CLOUD_UNIVERSE_DOMAIN | googleapis.com |
| LANGFLOW_API_KEY | sk-prPaNmnt1xmXF9jy7vKz2TkNP6Or0UYzoTWf9cyz3xM |
| LANGFLOW_API_TOKEN | AstraCS:lLZmcbIQaqBatmYvEIOaEjci:404c3cc0b04aac0d31873dd481b024c928cbc6e1db8734e296bf3f91c5450cc5 |
| LANGFLOW_BASE_URL | https://langflowailangflowlatest-production-01c9.up.railway.app |
| OPENAI_API_KEY | ******************************************************************************************************************************************************************** |
| PUSHER_APP_ID | RCYPSexV |
| PUSHER_CLUSTER | mt1 |
| PUSHER_HOST | https://soketi-production-7b57.up.railway.app/ |
| PUSHER_KEY | kydfr6qwm6yirqzmkv29wl9srt7a88oh |
| PUSHER_PORT | 443 |
| PUSHER_SECRET | oi3si7s0m7xk56huvly5h3x0i04xugyf |
| PUSHER_USE_TLS | true |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | asset-generator.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | asset-generator |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | asset-generator-production.up.railway.app |
| REDIS_URL | redis://default:<EMAIL>:38358 |
| REMOVE_BG_API_KEY | 3kfEVGVVJcBZYEAxp8y4fHWA |
| UPLOADTHING_APP_ID | upl-1234567890 |
| UPLOADTHING_SECRET | ************************************************************************ |
| UPLOAD_THING_API_TOKEN | UPLOADTHING_TOKEN='eyJhcGlLZXkiOiJza19saXZlX2UyMjhjZjUxNTY5OGQyMGMzMDA4NWZjODQyYzg3NjZjZmE2NGM2MGIxODMyOGQxODlmNjZkMjU2MGNlNDBkYTgiLCJhcHBJZCI6InlnM2g2d3p6MWkiLCJyZWdpb25zIjpbInNlYTEiXX0=' |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 30d141c1-e085-40c5-87a4-df9c2dd005e8 | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "asset-generator",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "30d141c1-e085-40c5-87a4-df9c2dd005e8",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "cc010d3f-24bf-4daf-8993-8809a1abc1bb",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "JRippelmeyer",
              "commitHash": "8f1f18536fea90c1f7ac71cc524f272f63bc17fb",
              "commitMessage": "Added more error handling for pusher events",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile"
                },
                "deploy": {
                  "healthcheckPath": "/api/health",
                  "healthcheckTimeout": 100,
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.builder": "$.build.builder",
                "build.dockerfilePath": "$.build.dockerfilePath",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "deploy",
              "repo": "AdVid-ai/asset-generator",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": "/api/health",
                  "healthcheckTimeout": 100,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 3
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/asset-generator",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "cc010d3f-24bf-4daf-8993-8809a1abc1bb",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "JRippelmeyer",
    "commitHash": "8f1f18536fea90c1f7ac71cc524f272f63bc17fb",
    "commitMessage": "Added more error handling for pusher events",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile"
      },
      "deploy": {
        "healthcheckPath": "/api/health",
        "healthcheckTimeout": 100,
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.builder": "$.build.builder",
      "build.dockerfilePath": "$.build.dockerfilePath",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "deploy",
    "repo": "AdVid-ai/asset-generator",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": "/api/health",
        "healthcheckTimeout": 100,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 3
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service asset-generator

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "asset-generator" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
