# Railway Ecosystem Documentation

**Generated:** Tue Jul 22 12:41:54 CDT 2025
**Project:** advid
**Environment:** production
**Railway CLI Version:** Unknown
**Authenticated User:** Logged in as <PERSON> (<EMAIL>) 👋

## Project Overview

**Project Name:** advid
**Current Environment:** production
**Total Services:**       40

## Project Status

```json
{
  "id": "8a3a1706-985f-4920-a955-5d21ffeef856",
  "name": "advid",
  "deletedAt": null,
  "team": {
    "name": "advid-ai"
  },
  "environments": {
    "edges": [
      {
        "node": {
          "id": "304cee46-c13a-48a2-8feb-2a8be4ba237c",
          "name": "pv",
          "deletedAt": null
        }
      },
      {
        "node": {
          "id": "52147228-dbf8-4586-9d28-59509668b4a8",
          "name": "production",
          "deletedAt": null
        }
      },
      {
        "node": {
          "id": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "name": "staging",
          "deletedAt": null
        }
      },
      {
        "node": {
          "id": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "name": "demo",
          "deletedAt": null
        }
      }
    ]
  },
  "services": {
    "edges": [
      {
        "node": {
          "id": "02dc9b92-589f-4736-acd1-78aa5fc2978a",
          "name": "Worker-fS_a",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "18cc439d-c515-4a3b-b928-fa3910df5672",
                  "serviceId": "02dc9b92-589f-4736-acd1-78aa5fc2978a",
                  "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "3450a75f-4be8-4360-a7d5-a85083eca05a",
                    "meta": {
                      "image": "n8nio/n8n",
                      "logsV2": true,
                      "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d",
                      "queuedReason": "Waiting for dependencies to deploy",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "n8n worker"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "n8nio/n8n"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "0c861b92-6665-48f2-a392-8c1942ba89e3",
          "name": "adfury-app",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "ab39dd90-b997-4a37-be1b-6246232532ff",
                  "serviceId": "0c861b92-6665-48f2-a392-8c1942ba89e3",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "fbc0d7fe-a715-4b19-b951-3ed80ca55f98",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "alpendergrast",
                      "commitHash": "b08ca1993fec98ea936e3c7db9cc1b2266bb6836",
                      "commitMessage": "fix advertiser id detection from builder",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "deploy",
                      "repo": "AdVid-ai/advid-app",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-app",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "0e93f435-2291-4820-b176-715fe50fa6e4",
          "name": "Redis-Auth",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "a0657a45-0977-4a75-a88d-0382e24a99e2",
                  "serviceId": "0e93f435-2291-4820-b176-715fe50fa6e4",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "1197ab63-74c1-480b-8077-7fdff57e4037",
                    "meta": {
                      "ignoreWatchPatterns": true,
                      "image": "bitnami/redis:7.2.5",
                      "logsV2": true,
                      "patchId": "17bf9a42-14c3-4701-a049-b88eee9dbab2",
                      "reason": "migrate",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/bitnami"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "bitnami/redis:7.2.5"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "0fa43be0-b6bc-47bd-bae3-e489eb8a49fa",
          "name": "Redis-Sessions",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "d22925e2-c0ed-4853-9dbb-f70518a401bd",
                  "serviceId": "0fa43be0-b6bc-47bd-bae3-e489eb8a49fa",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "54e6ae8f-a113-4127-b04c-2f7ea23eabfc",
                    "meta": {
                      "duration": 1773042,
                      "image": "bitnami/redis:7.2.5",
                      "logsV2": true,
                      "patchId": "f7da1d75-24ca-4ed0-818f-960d187a28df",
                      "reason": "rollback",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": "/bitnami",
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/bitnami"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "bitnami/redis:7.2.5"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "11267c52-9831-4e48-a751-654fcc1b32f2",
          "name": "Flowise AI (Agents + Datastore + Flows)",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "ebb4a4e0-6770-45c1-840f-60209ace4e70",
                  "serviceId": "11267c52-9831-4e48-a751-654fcc1b32f2",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "4450fd59-c073-48f0-8968-3d213caef549",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "chungyau97",
                      "commitHash": "6baec938601da61cb709e4ec61af2fcf058ba71f",
                      "commitMessage": "Optimize export import (#4795)\n\n* feat: add saveBatch and optimize duplication id handling\n\n* feat: improve lookup performance by using Set",
                      "fileServiceManifest": {},
                      "isPublicRepoDeploy": true,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "deploy",
                      "repo": "FlowiseAI/Flowise",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "FlowiseAI/Flowise",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "11bc08a2-0d5e-4495-a27a-94444a59d72d",
          "name": "marketplace-item-scheduler",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "c0b5b915-e941-4ccd-a834-34fee879a9fd",
                  "serviceId": "11bc08a2-0d5e-4495-a27a-94444a59d72d",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "45f59db2-d681-4908-80d9-19672a00a875",
                    "meta": {
                      "duration": 0,
                      "image": "ghcr.io/railwayapp/function-bun:1.2.16",
                      "logsV2": true,
                      "patchId": "91d672dd-abd0-44c5-a0ee-2a48bfee16f3",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": "0 * * * *",
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": null,
                          "restartPolicyType": "NEVER",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "./run.sh Ly8gaW5kZXgudHN4IChCdW4gdjEuMiBydW50aW1lKQppbXBvcnQgeyB6IH0gZnJvbSAiem9kQDMiOwoKY29uc3QgZmFjdCA9IHoub2JqZWN0KHsKICBpZDogei5udW1iZXIoKSwKICBmYWN0OiB6LnN0cmluZygpLAp9KTsKCi8vIEZ1biBmYWN0IQovLyBUaGlzIEFQSSBpcyBob3N0ZWQgb24gUmFpbHdheSBGdW5jdGlvbnM6Ci8vICAgaHR0cHM6Ly9yYWlsd2F5LmNvbS9wcm9qZWN0LzAxNjJlMjgwLTFlMzYtNDU4Mi1iNjcxLTAxMTc1YmI2YmI2Zi9zZXJ2aWNlLzQxODEyOTEzLTgzY2YtNDQyMC04YWFkLThhZDVmZDc0MDMzMi9zb3VyY2UtY29kZT9lbnZpcm9ubWVudElkPTgxY2QxMjJhLWNmYmEtNDVjOC05MWU0LWE5NmVlM2U0YTA1OApjb25zdCByZXMgPSBhd2FpdCBmZXRjaCgiaHR0cHM6Ly9yYW5kb20tZmFjdHMudXAucmFpbHdheS5hcHAvZmFjdCIpOwpjb25zdCBkYXRhID0gZmFjdC5wYXJzZShhd2FpdCByZXMuanNvbigpKTsKY29uc3QgZGlhbG9nU2l6ZSA9IE1hdGgubWF4KGRhdGEuZmFjdC5sZW5ndGggKyAyLCAzMik7Cgpjb25zb2xlLmxvZyhgCiR7Ii0iLnJlcGVhdChkaWFsb2dTaXplKX0KPCAke2RhdGEuZmFjdH0gPgokeyItIi5yZXBlYXQoZGlhbG9nU2l6ZSl9CiAgICAgICAgICBcXCAgICBfX1/ilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITilITila4KICAgICAgICAgICAgLC3ilbxfX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19ffAogICAgICAgICAgLC9fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19ffAogICAgICAgICAoXyAgICAgICAgICzilITilITilITilITilIQuICAgICAgICAgICAgICAgICAgICAgICzilITilITilITilITilITilITilITilIQuICB8Cl9fX19fX19fX19fLS5fX19fXy4nKF8pKF8pXF9fX19fX19fX19fX19fX19fX19fX19fLihfKT0oXylcX18vCmApOwoK"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "ghcr.io/railwayapp/function-bun:1.2.16"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "1ca54bbb-e741-4196-8b36-a9a41288434f",
          "name": "advid-server",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "c78091c5-da1f-4d7d-89e8-ffcec769701b",
                  "serviceId": "1ca54bbb-e741-4196-8b36-a9a41288434f",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "b347928d-c403-48a0-90ef-cca9f3164a2d",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "alpendergrast",
                      "commitHash": "cee42720a546ff98e29bf7f0bcd5f2e497b50ab3",
                      "commitMessage": "Merge pull request #116 from AdVid-ai/ADV-354\n\nADV-354 - Fix credential update",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "deploy",
                      "repo": "AdVid-ai/advid-server",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 3
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-server",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "31dd5c7b-c1ae-43a6-bf72-c06c2a9d8c14",
          "name": "Redis-M2_I",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "58ec488f-618f-4668-af33-1d93cb50c4ce",
                  "serviceId": "31dd5c7b-c1ae-43a6-bf72-c06c2a9d8c14",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "fe49e186-3b84-44e7-a9f3-38bbfeb9a6fe",
                    "meta": {
                      "image": "bitnami/redis:7.2.5",
                      "logsV2": true,
                      "patchId": "1bcd540a-7927-46f8-9b30-49e1db3e8f67",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": "/bitnami",
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/bitnami"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "bitnami/redis:7.2.5"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "3360d2c6-78de-4fb3-bf80-bfee41f0685a",
          "name": "payment-gateway",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "62747196-fd40-4e72-bbfc-a19e356ee0a1",
                  "serviceId": "3360d2c6-78de-4fb3-bf80-bfee41f0685a",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "3814227e-e6d6-46d8-9f3d-3092b532bae9",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "",
                      "commitHash": "6d34742f84f1c8f220adcf58e134c57d6cacf6e5",
                      "commitMessage": "add stripe routes",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "isPublicRepoDeploy": false,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "patchId": "fad79739-7b39-4a53-ac5d-a81f09688f68",
                      "propertyFileMapping": {},
                      "reason": "migrate",
                      "repo": "AdVid-ai/payment-gateway",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/payment-gateway",
                    "image": null
                  }
                }
              },
              {
                "node": {
                  "id": "9aa2967b-94eb-4b5c-94f5-670fa2b7b82a",
                  "serviceId": "3360d2c6-78de-4fb3-bf80-bfee41f0685a",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "2d7ed75a-6a59-44e5-a467-1d00ba35ad46",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "",
                      "commitHash": "6d34742f84f1c8f220adcf58e134c57d6cacf6e5",
                      "commitMessage": "add stripe routes",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "isPublicRepoDeploy": false,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "patchId": "23fbae35-4f89-47d2-84e0-204c977e18c3",
                      "propertyFileMapping": {},
                      "reason": "migrate",
                      "repo": "AdVid-ai/payment-gateway",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/payment-gateway",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "3d163159-581d-46f8-856c-b1a3e4a6d704",
          "name": "Postgres",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "1bb37637-389e-473b-9f19-68b9cd81fd1c",
                  "serviceId": "3d163159-581d-46f8-856c-b1a3e4a6d704",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "d8ecc9d1-2b78-4313-8842-48a8c2f18dd3",
                    "meta": {
                      "deprecatedRegions": [
                        {
                          "region": "us-west1",
                          "replacementRegion": "us-west2"
                        }
                      ],
                      "ignoreWatchPatterns": true,
                      "image": "ghcr.io/railwayapp-templates/postgres-ssl",
                      "logsV2": true,
                      "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
                      "reason": "redeploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west1": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""
                        }
                      },
                      "volumeMounts": [
                        "/var/lib/postgresql/data"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "ghcr.io/railwayapp-templates/postgres-ssl"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "3f4d0bdf-202e-4a46-8f9f-10b248fdb152",
          "name": "advid-wm-service",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "3f3640f5-53c9-4ee1-b263-87fcb50fe5da",
                  "serviceId": "3f4d0bdf-202e-4a46-8f9f-10b248fdb152",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "bc89cae0-5b23-4129-9083-4a5a7c0054c9",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "bryce27",
                      "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
                      "commitMessage": "stuff",
                      "configFile": "railway.toml",
                      "fileServiceManifest": {
                        "build": {
                          "builder": "DOCKERFILE"
                        }
                      },
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {
                        "build.builder": "$.build.builder"
                      },
                      "reason": "deploy",
                      "repo": "AdVid-ai/advid-wm-service",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-wm-service",
                    "image": null
                  }
                }
              },
              {
                "node": {
                  "id": "61fbd61d-76d3-4cbc-8303-73247f1ef5bb",
                  "serviceId": "3f4d0bdf-202e-4a46-8f9f-10b248fdb152",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "1fcf9d3a-85aa-44c4-87c0-146a4b6efdb5",
                    "meta": {
                      "branch": "demo",
                      "buildOnly": false,
                      "commitAuthor": "bryce27",
                      "commitHash": "b40b6b6367398a253007d6d9c6d9dfa506b0d46f",
                      "commitMessage": "Merge pull request #6 from AdVid-ai/staging\n\nremove limit on creative id filtering",
                      "configFile": "railway.toml",
                      "fileServiceManifest": {
                        "build": {
                          "builder": "DOCKERFILE"
                        }
                      },
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "isPublicRepoDeploy": false,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "patchId": "bd7ae71f-4a92-455a-9de8-3c3d25d2c4c5",
                      "propertyFileMapping": {
                        "build.builder": "$.build.builder"
                      },
                      "reason": "migrate",
                      "repo": "AdVid-ai/advid-wm-service",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-wm-service",
                    "image": null
                  }
                }
              },
              {
                "node": {
                  "id": "e179d54c-7201-4b8b-93e9-1e77eea85dd9",
                  "serviceId": "3f4d0bdf-202e-4a46-8f9f-10b248fdb152",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "40b50223-7a1f-42cd-a962-c8ce35027512",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "bryce27",
                      "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
                      "commitMessage": "stuff",
                      "configFile": "railway.toml",
                      "fileServiceManifest": {
                        "build": {
                          "builder": "DOCKERFILE"
                        }
                      },
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {
                        "build.builder": "$.build.builder"
                      },
                      "reason": "deploy",
                      "repo": "AdVid-ai/advid-wm-service",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 3
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-wm-service",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "463cd6d1-a8e9-4832-9a4c-7a70229542b3",
          "name": "adfury-email-service-stg",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "66a90e81-0484-474d-888f-41474c791bc6",
                  "serviceId": "463cd6d1-a8e9-4832-9a4c-7a70229542b3",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "4a6a9a63-c3ee-41fc-8e39-7c3bdab8e227",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "bryce27",
                      "commitHash": "da1661e45f95bc46d7f9c33c56d3245bfdb0044c",
                      "commitMessage": "Merge pull request #4 from AdVid-ai/staging\n\nfix: resolve Gmail spam warnings and improve email deliverability",
                      "configFile": "railway.json",
                      "fileServiceManifest": {
                        "build": {
                          "buildCommand": "npm run build",
                          "builder": "NIXPACKS"
                        },
                        "deploy": {
                          "healthcheckPath": "/ready",
                          "healthcheckTimeout": 60,
                          "numReplicas": 1,
                          "restartPolicyMaxRetries": 3,
                          "restartPolicyType": "ON_FAILURE",
                          "startCommand": "npm start"
                        }
                      },
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {
                        "build.buildCommand": "$.build.buildCommand",
                        "build.builder": "$.build.builder",
                        "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                        "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                        "deploy.numReplicas": "$.deploy.numReplicas",
                        "deploy.restartPolicyMaxRetries": "$.deploy.restartPolicyMaxRetries",
                        "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                        "deploy.startCommand": "$.deploy.startCommand"
                      },
                      "reason": "deploy",
                      "repo": "AdVid-ai/adfury-email-service",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": "npm run build",
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": "/ready",
                          "healthcheckTimeout": 60,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 3,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "npm start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/adfury-email-service",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "5afd7862-c46c-4904-8a0d-129007ede9b6",
          "name": "Postgres-C5Ge",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "10aa8a7d-8b75-4759-9ecd-a45b2aef81ff",
                  "serviceId": "5afd7862-c46c-4904-8a0d-129007ede9b6",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "25c3aca4-2967-4740-acd8-b62e512c9364",
                    "meta": {
                      "ignoreWatchPatterns": true,
                      "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest",
                      "logsV2": true,
                      "patchId": "e4769826-016f-4d40-84d5-a63f51acd20d",
                      "reason": "migrate",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/var/lib/postgresql/data"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "5c52fa2a-848e-4f8f-84b9-f7ca251ef894",
          "name": "analytics-view",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "e5d90686-6785-4fa7-929b-8d846780655c",
                  "serviceId": "5c52fa2a-848e-4f8f-84b9-f7ca251ef894",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "8184b358-f403-49d1-a44b-bbe31d747bee",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "",
                      "commitHash": "337a7554efc31759e5b23ae0ec2cc9f911d732a8",
                      "commitMessage": "new repo",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "isPublicRepoDeploy": false,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "patchId": "179d63b8-f957-4595-88ab-4fd2fda43a5a",
                      "propertyFileMapping": {},
                      "reason": "deploy",
                      "repo": "AdVid-ai/analytics-view",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/analytics-view",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "5cb303d9-3b6c-4945-b3a8-7e24a1980d85",
          "name": "Redis-yTjt",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "1421b278-225b-4aa0-8d43-d4b519bf19f3",
                  "serviceId": "5cb303d9-3b6c-4945-b3a8-7e24a1980d85",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "cbdbe06d-17ba-43a9-8575-5a998d4b1af3",
                    "meta": {
                      "image": "bitnami/redis",
                      "logsV2": true,
                      "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": "/bitnami",
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/bitnami"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "bitnami/redis"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "7311f8ab-2bea-4c27-ad70-d2c4b7e16548",
          "name": "advid-server-stg",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "11e73551-f8f2-4bc8-bd55-78cb40302fbd",
                  "serviceId": "7311f8ab-2bea-4c27-ad70-d2c4b7e16548",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "167dbaed-3826-4571-b6eb-ad5e2266a105",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "alpendergrast",
                      "commitHash": "7f6597cff162671d5661e35738e5c5cd04288465",
                      "commitMessage": "Merge pull request #115 from AdVid-ai/development\n\nDevelopment->Staging",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "deploy",
                      "repo": "AdVid-ai/advid-server",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-server",
                    "image": null
                  }
                }
              },
              {
                "node": {
                  "id": "11f15535-06da-431d-ad9d-e3bf186013e6",
                  "serviceId": "7311f8ab-2bea-4c27-ad70-d2c4b7e16548",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "fa1cbf0d-5a2d-4386-ae53-88ad305e5acb",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "alpendergrast",
                      "commitHash": "7f6597cff162671d5661e35738e5c5cd04288465",
                      "commitMessage": "Merge pull request #115 from AdVid-ai/development\n\nDevelopment->Staging",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "deploy",
                      "repo": "AdVid-ai/advid-server",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-server",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "73790433-b9ca-4a8a-854e-9faf553990eb",
          "name": "pdf-scraper",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "c0850753-8c68-4690-80b8-170aaf9ce876",
                  "serviceId": "73790433-b9ca-4a8a-854e-9faf553990eb",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "2dace867-c6c8-42b0-92fb-572db35e6159",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "JRippelmeyer",
                      "commitHash": "56c54fc5229b76b6aa560fe763ca338e940c3b0d",
                      "commitMessage": "Change any type",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "isPublicRepoDeploy": false,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "patchId": "aece8ff4-e158-40d2-a2ed-a8b0ca760322",
                      "propertyFileMapping": {},
                      "reason": "migrate",
                      "repo": "AdVid-ai/pdf-scraper",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/pdf-scraper",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "7ae6349f-4f26-46c0-8149-0b7b4ccbccd6",
          "name": "Postgres-yzsh",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "25b58d17-4f07-4e2a-90c1-0b1e55667e5c",
                  "serviceId": "7ae6349f-4f26-46c0-8149-0b7b4ccbccd6",
                  "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "b2f26052-870e-487a-921b-aa3c8d1b97b7",
                    "meta": {
                      "image": "ghcr.io/railwayapp-templates/postgres-ssl",
                      "logsV2": true,
                      "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": "/var/lib/postgresql/data",
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""
                        }
                      },
                      "volumeMounts": [
                        "/var/lib/postgresql/data"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "ghcr.io/railwayapp-templates/postgres-ssl"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "821f9a7f-eeee-4bbd-9144-1ea9264cbd77",
          "name": "adfury-payment-gateway",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "a22ae4a8-88dc-47e0-94f2-046e67594f77",
                  "serviceId": "821f9a7f-eeee-4bbd-9144-1ea9264cbd77",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "8d3e95d4-1225-4379-8265-702e7bc5a166",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "alpendergrast",
                      "commitHash": "9512fca373a9088ddf951e0d55763cab3c73c067",
                      "commitMessage": ".",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "migrate",
                      "repo": "AdVid-ai/payment-gateway",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/payment-gateway",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "8222e98b-23d3-4062-a0d8-7362bb04f90b",
          "name": "adfury-app-demo",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "47c05e1e-6691-4cc9-a67a-985cc245f16e",
                  "serviceId": "8222e98b-23d3-4062-a0d8-7362bb04f90b",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "2761f58a-9ad2-4130-8bc1-1b9af2ea76d2",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "alpendergrast",
                      "commitHash": "4ed2c1ef1b6fb4f42de9b126f38eded3be8dda40",
                      "commitMessage": "Merge branch 'main' of github.com:AdVid-ai/advid-app into staging",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "deploy",
                      "repo": "AdVid-ai/advid-app",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-app",
                    "image": null
                  }
                }
              },
              {
                "node": {
                  "id": "a744000a-a192-406e-a0cf-0c42277878c0",
                  "serviceId": "8222e98b-23d3-4062-a0d8-7362bb04f90b",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "d0d2268f-3d63-4669-a1b3-f8a187704fb2",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "alpendergrast",
                      "commitHash": "4ed2c1ef1b6fb4f42de9b126f38eded3be8dda40",
                      "commitMessage": "Merge branch 'main' of github.com:AdVid-ai/advid-app into staging",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "deploy",
                      "repo": "AdVid-ai/advid-app",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-app",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "83517162-ebde-4f95-8a09-3ec95ca2f0f4",
          "name": "Worker",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "4ac792ca-97fe-4e5a-af46-780ce24ef6ff",
                  "serviceId": "83517162-ebde-4f95-8a09-3ec95ca2f0f4",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "a6bacea5-9e4b-468a-a7fc-b48989ad0862",
                    "meta": {
                      "ignoreWatchPatterns": true,
                      "image": "n8nio/n8n",
                      "logsV2": true,
                      "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
                      "queuedReason": "Waiting for dependencies to deploy",
                      "reason": "redeploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "n8n worker"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "n8nio/n8n"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "8d6fd41f-bfca-4b9e-9a78-d51987db8592",
          "name": "Worker-tjT0",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "c1411af9-44b4-4241-969d-f3b3aa2e1e8f",
                  "serviceId": "8d6fd41f-bfca-4b9e-9a78-d51987db8592",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "e436027d-02d2-4995-a17c-aef0823562ae",
                    "meta": {
                      "image": "n8nio/n8n",
                      "logsV2": true,
                      "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
                      "queuedReason": "Waiting for dependencies to deploy",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "n8n worker"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "n8nio/n8n"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "90b1e1f1-123c-4053-bb1e-fb7e3c9b6b88",
          "name": "Primary",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "f91b943d-8bc6-48ee-bcca-e807588e1d62",
                  "serviceId": "90b1e1f1-123c-4053-bb1e-fb7e3c9b6b88",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "98d96ec3-414a-4369-b373-3922529c8061",
                    "meta": {
                      "image": "n8nio/n8n",
                      "logsV2": true,
                      "patchId": "d031b9af-dcd6-4d2c-8403-60ff11f0846f",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": "/healthz",
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "n8n start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "n8nio/n8n"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "93758c48-deac-4f56-917b-04f42091437c",
          "name": "Primary-TvKP",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "644fbe0d-0c35-46d8-ba4a-a70b05332bb7",
                  "serviceId": "93758c48-deac-4f56-917b-04f42091437c",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "0bdf7cca-84dc-4629-9694-3ae55c4919c8",
                    "meta": {
                      "image": "n8nio/n8n",
                      "logsV2": true,
                      "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
                      "queuedReason": "Waiting for dependencies to deploy",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": "/healthz",
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "n8n start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "n8nio/n8n"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "948dfff7-bba7-4ba2-84a2-a2cf4c7b5353",
          "name": "langflowai/langflow:latest",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "cd326eeb-1fa5-4f53-b7e1-7eb96f59ea29",
                  "serviceId": "948dfff7-bba7-4ba2-84a2-a2cf4c7b5353",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "6462d13d-5a55-4b5e-8ae4-c7c4307a4bff",
                    "meta": {
                      "ignoreWatchPatterns": true,
                      "image": "langflowai/langflow:latest",
                      "logsV2": true,
                      "patchId": "bc70da66-ac5a-42ce-b10a-a928e53d528b",
                      "reason": "migrate",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "python -m langflow run --host 0.0.0.0"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "langflowai/langflow:latest"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "a5c5a05e-a068-40d3-99ad-f1e74c08ebfb",
          "name": "asset-generator-stg",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "bbde1137-0507-449e-951a-73ac991f1172",
                  "serviceId": "a5c5a05e-a068-40d3-99ad-f1e74c08ebfb",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "9907055f-8340-4814-9ae6-0915ada8e42e",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "JRippelmeyer",
                      "commitHash": "3eba527548d4d24d2b291f6dfad6ef2555f7c8e0",
                      "commitMessage": "Merge pull request #14 from AdVid-ai/main\n\nMerge main into staging",
                      "configFile": "railway.toml",
                      "deprecatedRegions": [
                        {
                          "region": "us-west1",
                          "replacementRegion": "us-west2"
                        }
                      ],
                      "fileServiceManifest": {
                        "build": {
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile"
                        },
                        "deploy": {
                          "healthcheckPath": "/api/health",
                          "healthcheckTimeout": 100,
                          "startCommand": "npm start"
                        }
                      },
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {
                        "build.builder": "$.build.builder",
                        "build.dockerfilePath": "$.build.dockerfilePath",
                        "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                        "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                        "deploy.startCommand": "$.deploy.startCommand"
                      },
                      "reason": "deploy",
                      "repo": "AdVid-ai/asset-generator",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": "/api/health",
                          "healthcheckTimeout": 100,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west1": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "npm start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/asset-generator",
                    "image": null
                  }
                }
              },
              {
                "node": {
                  "id": "c712444f-cc5d-4ff2-b7db-4272dd61cdc5",
                  "serviceId": "a5c5a05e-a068-40d3-99ad-f1e74c08ebfb",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "75f8f61f-4ac4-4539-a0dd-9582b57f46db",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "JRippelmeyer",
                      "commitHash": "3eba527548d4d24d2b291f6dfad6ef2555f7c8e0",
                      "commitMessage": "Merge pull request #14 from AdVid-ai/main\n\nMerge main into staging",
                      "configFile": "railway.toml",
                      "deprecatedRegions": [
                        {
                          "region": "us-west1",
                          "replacementRegion": "us-west2"
                        }
                      ],
                      "fileServiceManifest": {
                        "build": {
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile"
                        },
                        "deploy": {
                          "healthcheckPath": "/api/health",
                          "healthcheckTimeout": 100,
                          "startCommand": "npm start"
                        }
                      },
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {
                        "build.builder": "$.build.builder",
                        "build.dockerfilePath": "$.build.dockerfilePath",
                        "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                        "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                        "deploy.startCommand": "$.deploy.startCommand"
                      },
                      "reason": "deploy",
                      "repo": "AdVid-ai/asset-generator",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": "/api/health",
                          "healthcheckTimeout": 100,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west1": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "npm start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/asset-generator",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "a7d244ed-0030-4e59-b62e-14e48505bca1",
          "name": "Soketi",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "0235ace9-06be-477d-b6f6-937c0fce801d",
                  "serviceId": "a7d244ed-0030-4e59-b62e-14e48505bca1",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "b11743ed-9d84-4cf6-b5a2-974df9471517",
                    "meta": {
                      "image": "quay.io/soketi/soketi:1.6.1-16-debian",
                      "logsV2": true,
                      "patchId": "bc5748a4-c7cc-4ee7-8248-0a399835d50a",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": "/ready",
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "/bin/sh -c \"export AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1; node /app/bin/server.js start\""
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "quay.io/soketi/soketi:1.6.1-16-debian"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "ad7cc5f3-c9e4-430f-8523-519cfaf6bd60",
          "name": "Postgres-56l-",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "8c9a6a48-a005-470b-bf0c-c5d060ea5e29",
                  "serviceId": "ad7cc5f3-c9e4-430f-8523-519cfaf6bd60",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "7485ee67-7c70-4d9f-920b-d9d26c43c486",
                    "meta": {
                      "image": "ghcr.io/railwayapp-templates/postgres-ssl",
                      "logsV2": true,
                      "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": "/var/lib/postgresql/data",
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""
                        }
                      },
                      "volumeMounts": [
                        "/var/lib/postgresql/data"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "ghcr.io/railwayapp-templates/postgres-ssl"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "ae24a0e5-3bc5-4c98-a3d4-1177262e1217",
          "name": "retail-mgmt",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "ce4c1c6d-2081-4729-afaf-f7589e081581",
                  "serviceId": "ae24a0e5-3bc5-4c98-a3d4-1177262e1217",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "8a91d03c-ba43-43f9-a4f7-240650c8b273",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "bryce27",
                      "commitHash": "9e369b30593d03a2d625d9d6345990d4a91e1570",
                      "commitMessage": "strip duplicates from final images array",
                      "configFile": "railway.toml",
                      "fileServiceManifest": {
                        "build": {
                          "buildCommand": "npm install",
                          "builder": "NIXPACKS"
                        },
                        "deploy": {
                          "healthcheckPath": "/health",
                          "healthcheckTimeout": 100,
                          "restartPolicyType": "ON_FAILURE",
                          "startCommand": "npm start"
                        }
                      },
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {
                        "build.buildCommand": "$.build.buildCommand",
                        "build.builder": "$.build.builder",
                        "deploy.envs": "$.deploy.envs",
                        "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                        "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                        "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                        "deploy.startCommand": "$.deploy.startCommand"
                      },
                      "reason": "migrate",
                      "repo": "AdVid-ai/retail-mgmt",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": "npm install",
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": 100,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "npm start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/retail-mgmt",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "b8730e7f-b1f6-4ac0-9a5f-bc76aa3ecf88",
          "name": "Redis-HVJ1",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "105fdb75-c19f-4be7-81d8-6da52e9a6164",
                  "serviceId": "b8730e7f-b1f6-4ac0-9a5f-bc76aa3ecf88",
                  "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "31acf8eb-dd50-4fd1-bbad-0e58c9c0f9b4",
                    "meta": {
                      "image": "bitnami/redis",
                      "logsV2": true,
                      "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": "/bitnami",
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/bitnami"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "bitnami/redis"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "bc5cf161-1545-49bc-b66f-eec5348ab37e",
          "name": "advid-client-sandbox",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "209f910f-3a37-46b8-9bd5-698cde8669ff",
                  "serviceId": "bc5cf161-1545-49bc-b66f-eec5348ab37e",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "fbe77ad6-1f08-4fcb-a29f-08454460c03e",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "alpendergrast",
                      "commitHash": "6bc777c60845c84956321e9e2036dbbe292a91c3",
                      "commitMessage": "adjust layout for url sub and detail pages",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "migrate",
                      "repo": "AdVid-ai/advid-client-sandbox",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-client-sandbox",
                    "image": null
                  }
                }
              },
              {
                "node": {
                  "id": "cb0a8259-8dab-4d3b-b89a-cef256a3fa62",
                  "serviceId": "bc5cf161-1545-49bc-b66f-eec5348ab37e",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": null,
                  "source": null
                }
              },
              {
                "node": {
                  "id": "dc848756-198b-4fb0-8219-99778f46d541",
                  "serviceId": "bc5cf161-1545-49bc-b66f-eec5348ab37e",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": null,
                  "source": null
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "bfaaa506-b8ae-4abf-aa10-eee051b96dc0",
          "name": "Redis",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "775ebe1d-1914-4b46-9be2-4d4ce11c36da",
                  "serviceId": "bfaaa506-b8ae-4abf-aa10-eee051b96dc0",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "57b83c66-c020-4068-9831-9522dcd106b3",
                    "meta": {
                      "ignoreWatchPatterns": true,
                      "image": "bitnami/redis",
                      "logsV2": true,
                      "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
                      "reason": "redeploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/bitnami"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "bitnami/redis"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "c3ee66f5-ee6e-4808-8a1f-bdf353bc6a22",
          "name": "adfury-email-service",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "f1bdbead-7d00-40b8-b51e-6f49dcd80a82",
                  "serviceId": "c3ee66f5-ee6e-4808-8a1f-bdf353bc6a22",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "ba67300a-99d2-4158-a893-5d5ecc00fb67",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "bryce27",
                      "commitHash": "da1661e45f95bc46d7f9c33c56d3245bfdb0044c",
                      "commitMessage": "Merge pull request #4 from AdVid-ai/staging\n\nfix: resolve Gmail spam warnings and improve email deliverability",
                      "configFile": "railway.json",
                      "fileServiceManifest": {
                        "build": {
                          "buildCommand": "npm run build",
                          "builder": "NIXPACKS"
                        },
                        "deploy": {
                          "healthcheckPath": "/ready",
                          "healthcheckTimeout": 60,
                          "numReplicas": 1,
                          "restartPolicyMaxRetries": 3,
                          "restartPolicyType": "ON_FAILURE",
                          "startCommand": "npm start"
                        }
                      },
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {
                        "build.buildCommand": "$.build.buildCommand",
                        "build.builder": "$.build.builder",
                        "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                        "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                        "deploy.numReplicas": "$.deploy.numReplicas",
                        "deploy.restartPolicyMaxRetries": "$.deploy.restartPolicyMaxRetries",
                        "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                        "deploy.startCommand": "$.deploy.startCommand"
                      },
                      "reason": "deploy",
                      "repo": "AdVid-ai/adfury-email-service",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": "npm run build",
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": "/ready",
                          "healthcheckTimeout": 60,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 3,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "npm start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/adfury-email-service",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "c400c30d-b6ab-4b43-bc3a-c34276a33021",
          "name": "Redis-mXH1",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "8fc25e34-58c7-49d8-946f-9d425dd9ecd8",
                  "serviceId": "c400c30d-b6ab-4b43-bc3a-c34276a33021",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "18b17783-61bc-464b-919c-e0b707f4787d",
                    "meta": {
                      "image": "bitnami/redis:7.2.5",
                      "logsV2": true,
                      "patchId": "5edb47dd-1ffd-401c-a845-bd81b95a7128",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": "/bitnami",
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/bitnami"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "bitnami/redis:7.2.5"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "c574bc61-5697-460d-b21a-7d3321d34895",
          "name": "asset-generator",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "30d141c1-e085-40c5-87a4-df9c2dd005e8",
                  "serviceId": "c574bc61-5697-460d-b21a-7d3321d34895",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "cc010d3f-24bf-4daf-8993-8809a1abc1bb",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "JRippelmeyer",
                      "commitHash": "8f1f18536fea90c1f7ac71cc524f272f63bc17fb",
                      "commitMessage": "Added more error handling for pusher events",
                      "configFile": "railway.toml",
                      "fileServiceManifest": {
                        "build": {
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile"
                        },
                        "deploy": {
                          "healthcheckPath": "/api/health",
                          "healthcheckTimeout": 100,
                          "startCommand": "npm start"
                        }
                      },
                      "githubDeploymentId": **********,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {
                        "build.builder": "$.build.builder",
                        "build.dockerfilePath": "$.build.dockerfilePath",
                        "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                        "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                        "deploy.startCommand": "$.deploy.startCommand"
                      },
                      "reason": "deploy",
                      "repo": "AdVid-ai/asset-generator",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckPath": "/api/health",
                          "healthcheckTimeout": 100,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 3
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "npm start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/asset-generator",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "c9ba0a6c-0acf-4cda-8b50-5e07e59032b2",
          "name": "retail-mgmt-stg",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "8908257e-e032-4ae7-bd34-84651bb01208",
                  "serviceId": "c9ba0a6c-0acf-4cda-8b50-5e07e59032b2",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "79a4565b-2154-4961-b4ae-20f4cd44a089",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "bryce27",
                      "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
                      "commitMessage": "remove ignored files from git tracking",
                      "configFile": "railway.toml",
                      "fileServiceManifest": {
                        "build": {
                          "buildCommand": "npm install",
                          "builder": "NIXPACKS"
                        },
                        "deploy": {
                          "healthcheckPath": "/health",
                          "healthcheckTimeout": 100,
                          "restartPolicyType": "ON_FAILURE",
                          "startCommand": "npm start"
                        }
                      },
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {
                        "build.buildCommand": "$.build.buildCommand",
                        "build.builder": "$.build.builder",
                        "deploy.envs": "$.deploy.envs",
                        "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                        "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                        "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                        "deploy.startCommand": "$.deploy.startCommand"
                      },
                      "reason": "migrate",
                      "repo": "AdVid-ai/retail-mgmt",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": "npm install",
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckTimeout": 100,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "npm start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/retail-mgmt",
                    "image": null
                  }
                }
              },
              {
                "node": {
                  "id": "c2329538-5a47-4dc7-be27-1541264cc9bd",
                  "serviceId": "c9ba0a6c-0acf-4cda-8b50-5e07e59032b2",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "f7fc88a2-05e1-49fd-babb-b6ee8ddf3815",
                    "meta": {
                      "branch": "staging",
                      "buildOnly": false,
                      "commitAuthor": "",
                      "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
                      "commitMessage": "remove ignored files from git tracking",
                      "configFile": "railway.toml",
                      "fileServiceManifest": {
                        "build": {
                          "buildCommand": "npm install",
                          "builder": "NIXPACKS"
                        },
                        "deploy": {
                          "healthcheckPath": "/health",
                          "healthcheckTimeout": 100,
                          "restartPolicyType": "ON_FAILURE",
                          "startCommand": "npm start"
                        }
                      },
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "isPublicRepoDeploy": false,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "patchId": "85f81d41-9d73-4265-9722-e040640b6d1a",
                      "propertyFileMapping": {
                        "build.buildCommand": "$.build.buildCommand",
                        "build.builder": "$.build.builder",
                        "deploy.envs": "$.deploy.envs",
                        "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                        "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                        "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                        "deploy.startCommand": "$.deploy.startCommand"
                      },
                      "reason": "migrate",
                      "repo": "AdVid-ai/retail-mgmt",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": "npm install",
                          "buildEnvironment": null,
                          "builder": "DOCKERFILE",
                          "dockerfilePath": "Dockerfile",
                          "nixpacksConfigPath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": 100,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "npm start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/retail-mgmt",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "dc33e660-d3df-46c5-b2db-f3d56d619df4",
          "name": "ash-assistant",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "73c719cc-30bb-4bac-b959-a4fdceb709f8",
                  "serviceId": "dc33e660-d3df-46c5-b2db-f3d56d619df4",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "55949f22-08ea-41a1-9e0b-eb0af7208ee1",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "",
                      "commitHash": "ed74283a56d8c1c1d1f96880c520606c395e3874",
                      "commitMessage": "ai ideas folder (potential features/enhancements/considerations)",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "isPublicRepoDeploy": false,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "patchId": "d32b2948-77f8-48ad-94ba-57874285a05d",
                      "propertyFileMapping": {},
                      "reason": "migrate",
                      "repo": "AdVid-ai/ash-assistant",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/ash-assistant",
                    "image": null
                  }
                }
              },
              {
                "node": {
                  "id": "e973e8f3-d9a8-44e0-8517-8bcc788c6373",
                  "serviceId": "dc33e660-d3df-46c5-b2db-f3d56d619df4",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "d1f07e17-7abd-41a6-9349-5aa777d827ea",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "bryce27",
                      "commitHash": "ed74283a56d8c1c1d1f96880c520606c395e3874",
                      "commitMessage": "ai ideas folder (potential features/enhancements/considerations)",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "propertyFileMapping": {},
                      "reason": "migrate",
                      "repo": "AdVid-ai/ash-assistant",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/ash-assistant",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "ef43fc0e-01ac-47d4-be06-a63dd627e3d3",
          "name": "Primary-1k-6",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "cb6ad666-ae04-4c73-a2f5-fccea3f1ac02",
                  "serviceId": "ef43fc0e-01ac-47d4-be06-a63dd627e3d3",
                  "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "8eccd9d2-c6b9-4e6e-93c1-f060f12ec537",
                    "meta": {
                      "image": "n8nio/n8n",
                      "logsV2": true,
                      "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d",
                      "queuedReason": "Waiting for dependencies to deploy",
                      "reason": "deploy",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckPath": "/healthz",
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": "n8n start"
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "n8nio/n8n"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "ef63a6d7-63ac-4802-a7ec-7b02a707bd9c",
          "name": "Redis-S0jv",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "a4b8a2a3-b67b-4caf-86a2-df83999a0858",
                  "serviceId": "ef63a6d7-63ac-4802-a7ec-7b02a707bd9c",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "b0d99880-45e2-459e-943e-1209898006e6",
                    "meta": {
                      "ignoreWatchPatterns": true,
                      "image": "bitnami/redis:7.2.5",
                      "logsV2": true,
                      "patchId": "fad79739-7b39-4a53-ac5d-a81f09688f68",
                      "reason": "migrate",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/bitnami"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "bitnami/redis:7.2.5"
                  }
                }
              },
              {
                "node": {
                  "id": "c043b5f3-74c1-43eb-ae03-a7b65b97b129",
                  "serviceId": "ef63a6d7-63ac-4802-a7ec-7b02a707bd9c",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "5a8b9289-572f-455a-ae40-68a858452a13",
                    "meta": {
                      "ignoreWatchPatterns": true,
                      "image": "bitnami/redis:7.2.5",
                      "logsV2": true,
                      "patchId": "f2cd465e-5cb5-4286-a583-2afa84ae59c9",
                      "reason": "migrate",
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "drainingSeconds": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-east4-eqdc4a": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "overlapSeconds": null,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": [
                        "/bitnami"
                      ]
                    }
                  },
                  "source": {
                    "repo": null,
                    "image": "bitnami/redis:7.2.5"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "id": "f0be02d6-581d-404c-a2bd-9a3627f30343",
          "name": "advid-marketing-page",
          "serviceInstances": {
            "edges": [
              {
                "node": {
                  "id": "c0571ac9-c675-4142-bce6-f5949f0147e8",
                  "serviceId": "f0be02d6-581d-404c-a2bd-9a3627f30343",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "latestDeployment": {
                    "canRedeploy": true,
                    "id": "e9495bbd-814f-45a8-8201-17df1d1b514c",
                    "meta": {
                      "branch": "main",
                      "buildOnly": false,
                      "commitAuthor": "",
                      "commitHash": "6ecd25d5e197741ce0f7994f2e9cbed9eefa9a6a",
                      "commitMessage": "replace favicon",
                      "fileServiceManifest": {},
                      "githubDeploymentId": **********,
                      "ignoreWatchPatterns": true,
                      "isPublicRepoDeploy": false,
                      "logsV2": true,
                      "nixpacksProviders": [
                        "node"
                      ],
                      "patchId": "d4c85156-5769-48e1-aec2-920834ab5ebd",
                      "propertyFileMapping": {},
                      "reason": "migrate",
                      "repo": "AdVid-ai/advid-marketing-page",
                      "rootDirectory": null,
                      "runtime": "V2",
                      "serviceManifest": {
                        "build": {
                          "buildCommand": null,
                          "buildEnvironment": null,
                          "builder": "NIXPACKS",
                          "dockerfilePath": null,
                          "nixpacksPlan": null,
                          "watchPatterns": []
                        },
                        "deploy": {
                          "cronSchedule": null,
                          "healthcheckTimeout": null,
                          "limitOverride": null,
                          "multiRegionConfig": {
                            "us-west2": {
                              "numReplicas": 1
                            }
                          },
                          "numReplicas": 1,
                          "preDeployCommand": null,
                          "region": null,
                          "registryCredentials": null,
                          "requiredMountPath": null,
                          "restartPolicyMaxRetries": 10,
                          "restartPolicyType": "ON_FAILURE",
                          "runtime": "V2",
                          "sleepApplication": false,
                          "startCommand": null
                        }
                      },
                      "volumeMounts": []
                    }
                  },
                  "source": {
                    "repo": "AdVid-ai/advid-marketing-page",
                    "image": null
                  }
                }
              }
            ]
          }
        }
      }
    ]
  },
  "volumes": {
    "edges": [
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "ad7cc5f3-c9e4-430f-8523-519cfaf6bd60",
                  "mountPath": "/var/lib/postgresql/data",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "currentSizeMB": 1132.859392,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "governor-volume",
                    "id": "04f0a304-6386-4804-98c8-9dd4cbf8f64d"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "31dd5c7b-c1ae-43a6-bf72-c06c2a9d8c14",
                  "mountPath": "/bitnami",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "currentSizeMB": 1060.483072,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "beautiful-volume",
                    "id": "197d9832-3018-4c7c-b060-bbe7dbb0af5e"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "3d163159-581d-46f8-856c-b1a3e4a6d704",
                  "mountPath": "/var/lib/postgresql/data",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "currentSizeMB": 3867.594752,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "work-volume",
                    "id": "1f557351-a5c5-4bed-88a5-e10fe5ce70ee"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "0fa43be0-b6bc-47bd-bae3-e489eb8a49fa",
                  "mountPath": "/bitnami",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "currentSizeMB": 1060.192256,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "jelly-volume",
                    "id": "20f2c1f3-373d-4dfc-85d3-960e8d1e9d8e"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "c400c30d-b6ab-4b43-bc3a-c34276a33021",
                  "mountPath": "/bitnami",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "currentSizeMB": 1060.43392,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "overflowing-volume",
                    "id": "279c80a1-8f29-4f97-b297-76cfe8583fef"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "7ae6349f-4f26-46c0-8149-0b7b4ccbccd6",
                  "mountPath": "/var/lib/postgresql/data",
                  "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c",
                  "currentSizeMB": 1132.777472,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "needle-volume",
                    "id": "3197cf90-a1ba-4d2f-aac3-96e5ce266f1b"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "bfaaa506-b8ae-4abf-aa10-eee051b96dc0",
                  "mountPath": "/bitnami",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "currentSizeMB": 1066.377216,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "blade-volume",
                    "id": "572218db-799b-4252-8f82-37b16bec7aa1"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "5afd7862-c46c-4904-8a0d-129007ede9b6",
                  "mountPath": "/var/lib/postgresql/data",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "currentSizeMB": 1224.855552,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "tomatoes-volume",
                    "id": "75f3e629-840e-433d-85fb-22ab935301bc"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "b8730e7f-b1f6-4ac0-9a5f-bc76aa3ecf88",
                  "mountPath": "/bitnami",
                  "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c",
                  "currentSizeMB": 1075.421184,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "paper-volume",
                    "id": "77535218-e7b6-4bed-83ea-e3d77f25771d"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": null,
                  "mountPath": "/tmp",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "currentSizeMB": 0.0,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "noise-volume",
                    "id": "8b24b2a6-ee4a-49d1-a871-823663d4f73c"
                  }
                }
              },
              {
                "node": {
                  "serviceId": null,
                  "mountPath": "/data",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "currentSizeMB": 274.80064,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "noise-volume",
                    "id": "8b24b2a6-ee4a-49d1-a871-823663d4f73c"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "5cb303d9-3b6c-4945-b3a8-7e24a1980d85",
                  "mountPath": "/bitnami",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "currentSizeMB": 1076.006912,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "anger-volume",
                    "id": "9b946aee-166a-4aa8-9f2e-994eef36464c"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "0e93f435-2291-4820-b176-715fe50fa6e4",
                  "mountPath": "/bitnami",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "currentSizeMB": 1060.3315200000002,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "nation-volume",
                    "id": "b1ba33fc-10eb-4c2e-b413-28ea27598289"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": null,
                  "mountPath": "/tmp",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "currentSizeMB": 0.0,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "seed-volume",
                    "id": "b5045578-27d6-4e5a-9920-678db03782c2"
                  }
                }
              },
              {
                "node": {
                  "serviceId": null,
                  "mountPath": "/var/lib/postgresql/data",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "currentSizeMB": 1127.919616,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "seed-volume",
                    "id": "b5045578-27d6-4e5a-9920-678db03782c2"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": "ef63a6d7-63ac-4802-a7ec-7b02a707bd9c",
                  "mountPath": "/bitnami",
                  "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
                  "currentSizeMB": 1060.442112,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "cent-volume",
                    "id": "b832715f-f38d-42e3-8fa2-b2e4cc486d79"
                  }
                }
              },
              {
                "node": {
                  "serviceId": "ef63a6d7-63ac-4802-a7ec-7b02a707bd9c",
                  "mountPath": "/bitnami",
                  "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
                  "currentSizeMB": 1060.110336,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "cent-volume",
                    "id": "b832715f-f38d-42e3-8fa2-b2e4cc486d79"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "node": {
          "volumeInstances": {
            "edges": [
              {
                "node": {
                  "serviceId": null,
                  "mountPath": "/var/lib/postgresql/data",
                  "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
                  "currentSizeMB": 0.0,
                  "sizeMB": 50000,
                  "volume": {
                    "name": "canvas-volume",
                    "id": "c43ddd75-66ab-4f92-937a-e9551101ffe8"
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }
}
```

## Services Architecture

| Service Name | Status | Service ID | Variables Retrieved |
|--------------|--------|------------|---------------------|
| [Worker-fS_a](./service-Worker-fS_a.md) | Unknown | 02dc9b92-589f-4736-acd1-78aa5fc2978a | ✅ Yes (22 vars) |
| [adfury-app](./service-adfury-app.md) | Unknown | 0c861b92-6665-48f2-a392-8c1942ba89e3 | ✅ Yes (83 vars) |
| [Redis-Auth](./service-Redis-Auth.md) | Unknown | 0e93f435-2291-4820-b176-715fe50fa6e4 | ✅ Yes (40 vars) |
| [Redis-Sessions](./service-Redis-Sessions.md) | Unknown | 0fa43be0-b6bc-47bd-bae3-e489eb8a49fa | ✅ Yes (40 vars) |
| [Flowise AI (Agents + Datastore + Flows)](./service-Flowise_AI_(Agents_+_Datastore_+_Flows).md) | Unknown | 11267c52-9831-4e48-a751-654fcc1b32f2 | ✅ Yes (25 vars) |
| [marketplace-item-scheduler](./service-marketplace-item-scheduler.md) | Unknown | 11bc08a2-0d5e-4495-a27a-94444a59d72d | ✅ Yes (23 vars) |
| [advid-server](./service-advid-server.md) | Unknown | 1ca54bbb-e741-4196-8b36-a9a41288434f | ✅ Yes (57 vars) |
| [Redis-M2_I](./service-Redis-M2_I.md) | Unknown | 31dd5c7b-c1ae-43a6-bf72-c06c2a9d8c14 | ✅ Yes (22 vars) |
| [payment-gateway](./service-payment-gateway.md) | Unknown | 3360d2c6-78de-4fb3-bf80-bfee41f0685a | ✅ Yes (22 vars) |
| [Postgres](./service-Postgres.md) | Unknown | 3d163159-581d-46f8-856c-b1a3e4a6d704 | ✅ Yes (37 vars) |
| [advid-wm-service](./service-advid-wm-service.md) | Unknown | 3f4d0bdf-202e-4a46-8f9f-10b248fdb152 | ✅ Yes (66 vars) |
| [adfury-email-service-stg](./service-adfury-email-service-stg.md) | Unknown | 463cd6d1-a8e9-4832-9a4c-7a70229542b3 | ✅ Yes (22 vars) |
| [Postgres-C5Ge](./service-Postgres-C5Ge.md) | Unknown | 5afd7862-c46c-4904-8a0d-129007ede9b6 | ✅ Yes (42 vars) |
| [analytics-view](./service-analytics-view.md) | Unknown | 5c52fa2a-848e-4f8f-84b9-f7ca251ef894 | ✅ Yes (36 vars) |
| [Redis-yTjt](./service-Redis-yTjt.md) | Unknown | 5cb303d9-3b6c-4945-b3a8-7e24a1980d85 | ✅ Yes (22 vars) |
| [advid-server-stg](./service-advid-server-stg.md) | Unknown | 7311f8ab-2bea-4c27-ad70-d2c4b7e16548 | ✅ Yes (22 vars) |
| [pdf-scraper](./service-pdf-scraper.md) | Unknown | 73790433-b9ca-4a8a-854e-9faf553990eb | ✅ Yes (26 vars) |
| [Postgres-yzsh](./service-Postgres-yzsh.md) | Unknown | 7ae6349f-4f26-46c0-8149-0b7b4ccbccd6 | ✅ Yes (22 vars) |
| [adfury-payment-gateway](./service-adfury-payment-gateway.md) | Unknown | 821f9a7f-eeee-4bbd-9144-1ea9264cbd77 | ✅ Yes (37 vars) |
| [adfury-app-demo](./service-adfury-app-demo.md) | Unknown | 8222e98b-23d3-4062-a0d8-7362bb04f90b | ✅ Yes (22 vars) |
| [Worker](./service-Worker.md) | Unknown | 83517162-ebde-4f95-8a09-3ec95ca2f0f4 | ✅ Yes (40 vars) |
| [Worker-tjT0](./service-Worker-tjT0.md) | Unknown | 8d6fd41f-bfca-4b9e-9a78-d51987db8592 | ✅ Yes (22 vars) |
| [Primary](./service-Primary.md) | Unknown | 90b1e1f1-123c-4053-bb1e-fb7e3c9b6b88 | ✅ Yes (44 vars) |
| [Primary-TvKP](./service-Primary-TvKP.md) | Unknown | 93758c48-deac-4f56-917b-04f42091437c | ✅ Yes (22 vars) |
| [langflowai/langflow:latest](./service-langflowai-langflow-latest.md) | Unknown | 948dfff7-bba7-4ba2-84a2-a2cf4c7b5353 | ✅ Yes (30 vars) |
| [asset-generator-stg](./service-asset-generator-stg.md) | Unknown | a5c5a05e-a068-40d3-99ad-f1e74c08ebfb | ✅ Yes (22 vars) |
| [Soketi](./service-Soketi.md) | Unknown | a7d244ed-0030-4e59-b62e-14e48505bca1 | ✅ Yes (33 vars) |
| [Postgres-56l-](./service-Postgres-56l-.md) | Unknown | ad7cc5f3-c9e4-430f-8523-519cfaf6bd60 | ✅ Yes (22 vars) |
| [retail-mgmt](./service-retail-mgmt.md) | Unknown | ae24a0e5-3bc5-4c98-a3d4-1177262e1217 | ✅ Yes (37 vars) |
| [Redis-HVJ1](./service-Redis-HVJ1.md) | Unknown | b8730e7f-b1f6-4ac0-9a5f-bc76aa3ecf88 | ✅ Yes (22 vars) |
| [advid-client-sandbox](./service-advid-client-sandbox.md) | Unknown | bc5cf161-1545-49bc-b66f-eec5348ab37e | ✅ Yes (26 vars) |
| [Redis](./service-Redis.md) | Unknown | bfaaa506-b8ae-4abf-aa10-eee051b96dc0 | ✅ Yes (38 vars) |
| [adfury-email-service](./service-adfury-email-service.md) | Unknown | c3ee66f5-ee6e-4808-8a1f-bdf353bc6a22 | ✅ Yes (31 vars) |
| [Redis-mXH1](./service-Redis-mXH1.md) | Unknown | c400c30d-b6ab-4b43-bc3a-c34276a33021 | ✅ Yes (40 vars) |
| [asset-generator](./service-asset-generator.md) | Unknown | c574bc61-5697-460d-b21a-7d3321d34895 | ✅ Yes (58 vars) |
| [retail-mgmt-stg](./service-retail-mgmt-stg.md) | Unknown | c9ba0a6c-0acf-4cda-8b50-5e07e59032b2 | ✅ Yes (22 vars) |
| [ash-assistant](./service-ash-assistant.md) | Unknown | dc33e660-d3df-46c5-b2db-f3d56d619df4 | ✅ Yes (22 vars) |
| [Primary-1k-6](./service-Primary-1k-6.md) | Unknown | ef43fc0e-01ac-47d4-be06-a63dd627e3d3 | ✅ Yes (22 vars) |
| [Redis-S0jv](./service-Redis-S0jv.md) | Unknown | ef63a6d7-63ac-4802-a7ec-7b02a707bd9c | ✅ Yes (22 vars) |
| [advid-marketing-page](./service-advid-marketing-page.md) | Unknown | f0be02d6-581d-404c-a2bd-9a3627f30343 | ✅ Yes (32 vars) |

## Variables Summary

This section provides an overview of environment variables across all services.

### Worker-fS_a (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### adfury-app (83 variables)

**Variable Names:**
- AUTH0_BASE_URL
- AUTH0_CLIENT_ID
- AUTH0_CLIENT_SECRET
- AUTH0_ISSUER_BASE_URL
- AUTH0_MGMT_BASE_URL
- AUTH0_MGMT_CLIENT_ID
- AUTH0_MGMT_CLIENT_SECRET
- AUTH0_SECRET
- AUTH_API_URL
- BASE_ASSET_URL
- BILLING_API_URL
- CANVA_CLIENT_ID
- CANVA_CLIENT_SECRET
- CANVA_REDIRECT_URI
- CANVA_RETURN_URL
- NEXT_PUBLIC_ADOBE_API_KEY
- NEXT_PUBLIC_ADOBE_EXPRESS_CLIENT_ID
- NEXT_PUBLIC_ADOBE_IMS_ORG_ID
- NEXT_PUBLIC_ADOBE_ORG
- NEXT_PUBLIC_API_URL
- NEXT_PUBLIC_APP_URL
- NEXT_PUBLIC_CANVA_CLIENT_ID
- NEXT_PUBLIC_CANVA_REDIRECT_URI
- NEXT_PUBLIC_CANVA_RETURN_URL
- NEXT_PUBLIC_POSTHOG_HOST
- NEXT_PUBLIC_POSTHOG_KEY
- NEXT_PUBLIC_PUSHER_CLUSTER
- NEXT_PUBLIC_PUSHER_FORCE_TLS
- NEXT_PUBLIC_PUSHER_HOST
- NEXT_PUBLIC_PUSHER_KEY
- NEXT_PUBLIC_PUSHER_PORT
- NEXT_PUBLIC_PUSHER_WSS_PORT
- NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
- NEXT_PUBLIC_SUPPLIER_ONE_CLIENT_ID
- NEXT_PUBLIC_WALMART_INTERNAL_ID
- NEXT_PUBLIC_WMT_SELLER_CLIENT_ID
- NOTION_API_KEY
- NOTION_DATABASE_ID
- NOTION_FEATURE_REQUESTS_DATABASE_ID
- NOTION_FEEDBACK_DATABASE_ID
- NOTION_ISSUE_TRACKING_DATABASE_ID
- NOTION_NEWSLETTER_DATABASE_ID
- NOTION_TICKETS_DATABASE_ID
- NO_CACHE
- OPENAI_API_KEY
- OPENROUTER_API_KEY
- PUSHER_APP_ID
- PUSHER_CLUSTER
- PUSHER_HOST
- PUSHER_KEY
- PUSHER_PORT
- PUSHER_SECRET
- PUSHER_USE_TLS
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL
- REDIS_URL
- STRIPE_SUBSCRIPTION_PRICE_ID
- USE_NOTION_TICKETS
- WALMART_MANAGER_API_URL
- WALMART_MANAGER_PROD_API_URL

### Redis-Auth (40 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_RUN_AS_ROOT
- RAILWAY_RUN_UID
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_TCP_APPLICATION_PORT
- RAILWAY_TCP_PROXY_DOMAIN
- RAILWAY_TCP_PROXY_PORT
- RAILWAY_VOLUME_ID
- RAILWAY_VOLUME_MOUNT_PATH
- RAILWAY_VOLUME_NAME
- REDISHOST
- REDISPASSWORD
- REDISPORT
- REDISUSER
- REDIS_AOF_ENABLED
- REDIS_PASSWORD
- REDIS_PUBLIC_URL
- REDIS_RDB_POLICY
- REDIS_URL

### Redis-Sessions (40 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_RUN_AS_ROOT
- RAILWAY_RUN_UID
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_TCP_APPLICATION_PORT
- RAILWAY_TCP_PROXY_DOMAIN
- RAILWAY_TCP_PROXY_PORT
- RAILWAY_VOLUME_ID
- RAILWAY_VOLUME_MOUNT_PATH
- RAILWAY_VOLUME_NAME
- REDISHOST
- REDISPASSWORD
- REDISPORT
- REDISUSER
- REDIS_AOF_ENABLED
- REDIS_PASSWORD
- REDIS_PUBLIC_URL
- REDIS_RDB_POLICY
- REDIS_URL

### Flowise AI (Agents + Datastore + Flows) (25 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL

### marketplace-item-scheduler (23 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### advid-server (57 variables)

**Variable Names:**
- APP_BASE_URL
- AUTH0_AUDIENCE
- AUTH0_CALLBACK_URL
- AUTH0_CLIENT_ID
- AUTH0_CLIENT_SECRET
- AUTH0_DOMAIN
- COOKIE_SECRET
- DATABASE_URL
- GOOGLE_CLOUD_AUTH_PROVIDER_X509_CERT_URL
- GOOGLE_CLOUD_AUTH_URI
- GOOGLE_CLOUD_CLIENT_EMAIL
- GOOGLE_CLOUD_CLIENT_ID
- GOOGLE_CLOUD_CLIENT_X509_CERT_URL
- GOOGLE_CLOUD_PRIVATE_KEY
- GOOGLE_CLOUD_PRIVATE_KEY_ID
- GOOGLE_CLOUD_PROJECT_ID
- GOOGLE_CLOUD_TOKEN_URI
- GOOGLE_CLOUD_TYPE
- GOOGLE_CLOUD_UNIVERSE_DOMAIN
- JWT_SECRET
- N8N_SECRET
- NODE_ENV
- PORT
- QUEUE_REDIS_URL
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL
- REDIS_PORT
- REDIS_URL
- RESEND_API_KEY
- RETAIL_MGMT_URL
- SESSION_SECRET
- STRIPE_PUBLISHABLE_KEY
- STRIPE_SECRET_KEY
- WALMART_MANAGER_API_URL

### Redis-M2_I (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### payment-gateway (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### Postgres (37 variables)

**Variable Names:**
- DATABASE_PRIVATE_URL
- DATABASE_URL
- PGDATA
- PGHOST
- PGHOST_PRIVATE
- PGPORT
- PGPORT_PRIVATE
- POSTGRES_DB
- POSTGRES_PASSWORD
- POSTGRES_USER
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_VOLUME_ID
- RAILWAY_VOLUME_MOUNT_PATH
- RAILWAY_VOLUME_NAME
- SSL_CERT_DAYS

### advid-wm-service (66 variables)

**Variable Names:**
- ADVID_SERVER_API_BASE_URL
- ADVID_SERVER_URL
- AUTH0_AUDIENCE
- AUTH0_DOMAIN
- CREATIVE_SYNC_ENABLED
- CREATIVE_SYNC_SCHEDULE
- DATABASE_URL
- GOOGLE_CLOUD_BUCKET_NAME
- JWT_SECRET
- NODE_ENV
- PRIVATE_KEY
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL
- WALMART_API_DISPLAY_ADFURY_ADVERTISER_ID
- WALMART_API_DISPLAY_AUTH_TOKEN
- WALMART_API_DISPLAY_BASE_URL
- WALMART_API_DISPLAY_CONSUMER_ID
- WALMART_API_DISPLAY_FISHER_ADVERTISER_ID
- WALMART_API_DISPLAY_SEC_KEY_VERSION
- WALMART_API_HEALTH_CHECK_ADFURY_CAMPAIGN_ID
- WALMART_API_HEALTH_CHECK_FISHER_CAMPAIGN_ID
- WALMART_API_MARKETPLACE_BASE_URL
- WALMART_API_MARKETPLACE_REDIRECT_URI
- WALMART_API_MARKETPLACE_SANDBOX_BASE_URL
- WALMART_API_MARKETPLACE_TOKEN_URL
- WALMART_API_SP1_BASE_URL
- WALMART_API_SUPPLIER_REDIRECT_URI
- WALMART_API_WPA_ADVERTISER_ID
- WALMART_API_WPA_BASE_URL
- WM_API_MARKETPLACE_APP_CLIENT_ID
- WM_API_MARKETPLACE_APP_CLIENT_SECRET
- WM_API_MARKETPLACE_CLIENT_ID
- WM_API_MARKETPLACE_CLIENT_SECRET
- WM_API_MARKETPLACE_PARTNER_ID
- WM_API_MARKETPLACE_REFRESH_TOKEN
- WM_API_SP1_APP_CLIENT_ID
- WM_API_SP1_APP_CLIENT_SECRET
- WM_API_SP1_CLIENT_ID
- WM_API_SP1_CLIENT_SECRET
- WM_API_SP1_CONSUMER_CHANNEL_TYPE
- WM_MARKETPLACE_BASE64_CREDENTIALS
- WM_SEC_KEY_VERSION
- WM_WPA_API_ADVERTISER_ID

### adfury-email-service-stg (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### Postgres-C5Ge (42 variables)

**Variable Names:**
- DATABASE_PRIVATE_URL
- DATABASE_URL
- PGDATA
- PGDATABASE
- PGHOST
- PGPASSWORD
- PGPORT
- PGPRIVATEHOST
- PGUSER
- POSTGRES_DB
- POSTGRES_PASSWORD
- POSTGRES_USER
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_TCP_APPLICATION_PORT
- RAILWAY_TCP_PROXY_DOMAIN
- RAILWAY_TCP_PROXY_PORT
- RAILWAY_VOLUME_ID
- RAILWAY_VOLUME_MOUNT_PATH
- RAILWAY_VOLUME_NAME
- SSL_CERT_DAYS

### analytics-view (36 variables)

**Variable Names:**
- GOOGLE_CLOUD_AUTH_PROVIDER_X509_CERT_URL
- GOOGLE_CLOUD_AUTH_URI
- GOOGLE_CLOUD_CLIENT_EMAIL
- GOOGLE_CLOUD_CLIENT_ID
- GOOGLE_CLOUD_CLIENT_X509_CERT_URL
- GOOGLE_CLOUD_PRIVATE_KEY
- GOOGLE_CLOUD_PRIVATE_KEY_ID
- GOOGLE_CLOUD_PROJECT_ID
- GOOGLE_CLOUD_TOKEN_URI
- GOOGLE_CLOUD_TYPE
- GOOGLE_CLOUD_UNIVERSE_DOMAIN
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL

### Redis-yTjt (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### advid-server-stg (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### pdf-scraper (26 variables)

**Variable Names:**
- OPENAI_API_KEY
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL

### Postgres-yzsh (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### adfury-payment-gateway (37 variables)

**Variable Names:**
- AUTH0_AUDIENCE
- AUTH0_CALLBACK_URL
- AUTH0_CLIENT_ID
- AUTH0_CLIENT_SECRET
- AUTH0_DB_CONNECTION_ID
- AUTH0_DOMAIN
- AUTH0_PASSWORD_RESET_REDIRECT_URL
- COOKIE_SECRET
- DATABASE_URL
- JWT_SECRET
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL
- STRIPE_SECRET_KEY
- STRIPE_WEBHOOK_SECRET

### adfury-app-demo (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### Worker (40 variables)

**Variable Names:**
- DB_POSTGRESDB_DATABASE
- DB_POSTGRESDB_HOST
- DB_POSTGRESDB_PASSWORD
- DB_POSTGRESDB_PORT
- DB_POSTGRESDB_USER
- DB_TYPE
- ENABLE_ALPINE_PRIVATE_NETWORKING
- EXECUTIONS_MODE
- N8N_ENCRYPTION_KEY
- N8N_LISTEN_ADDRESS
- NODE_OPTIONS
- PORT
- QUEUE_BULL_REDIS_HOST
- QUEUE_BULL_REDIS_PASSWORD
- QUEUE_BULL_REDIS_PORT
- QUEUE_BULL_REDIS_USERNAME
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- WEBHOOK_URL

### Worker-tjT0 (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### Primary (44 variables)

**Variable Names:**
- DB_POSTGRESDB_DATABASE
- DB_POSTGRESDB_HOST
- DB_POSTGRESDB_PASSWORD
- DB_POSTGRESDB_PORT
- DB_POSTGRESDB_USER
- DB_TYPE
- ENABLE_ALPINE_PRIVATE_NETWORKING
- EXECUTIONS_MODE
- N8N_EDITOR_BASE_URL
- N8N_ENCRYPTION_KEY
- N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS
- N8N_LISTEN_ADDRESS
- NODE_OPTIONS
- PORT
- QUEUE_BULL_REDIS_HOST
- QUEUE_BULL_REDIS_PASSWORD
- QUEUE_BULL_REDIS_PORT
- QUEUE_BULL_REDIS_USERNAME
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL
- WEBHOOK_URL

### Primary-TvKP (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### langflowai/langflow:latest (30 variables)

**Variable Names:**
- LANGFLOW_DATABASE_URL
- LANGFLOW_LOG_LEVEL
- LANGFLOW_PORT
- LANGFLOW_SECRET_KEY
- PORT
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL

### asset-generator-stg (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### Soketi (33 variables)

**Variable Names:**
- PORT
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL
- SOKETI_DEFAULT_APP_ID
- SOKETI_DEFAULT_APP_KEY
- SOKETI_DEFAULT_APP_SECRET
- SOKETI_INTERNAL_HOST
- SOKETI_INTERNAL_PORT
- SOKETI_PUBLIC_HOST
- SOKETI_PUBLIC_PORT

### Postgres-56l- (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### retail-mgmt (37 variables)

**Variable Names:**
- APIFY_ACTOR_ID
- APIFY_TOKEN
- AUTH0_AUDIENCE
- AUTH0_CALLBACK_URL
- AUTH0_CLIENT_ID
- AUTH0_CLIENT_SECRET
- AUTH0_DOMAIN
- COOKIE_SECRET
- DATABASE_URL
- JWT_SECRET
- NODE_ENV
- PORT
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL

### Redis-HVJ1 (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### advid-client-sandbox (26 variables)

**Variable Names:**
- BASE_ASSET_URL
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL

### Redis (38 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_RUN_UID
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_TCP_APPLICATION_PORT
- RAILWAY_TCP_PROXY_DOMAIN
- RAILWAY_TCP_PROXY_PORT
- RAILWAY_VOLUME_ID
- RAILWAY_VOLUME_MOUNT_PATH
- RAILWAY_VOLUME_NAME
- REDISHOST
- REDISHOST_PRIVATE
- REDISPORT
- REDISPORT_PRIVATE
- REDISUSER
- REDIS_PASSWORD
- REDIS_PRIVATE_URL
- REDIS_URL

### adfury-email-service (31 variables)

**Variable Names:**
- EMAIL_RATE_LIMIT
- LOG_LEVEL
- NODE_ENV
- QUEUE_CONCURRENCY
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL
- REDIS_URL
- RESEND_API_KEY

### Redis-mXH1 (40 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_RUN_AS_ROOT
- RAILWAY_RUN_UID
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_TCP_APPLICATION_PORT
- RAILWAY_TCP_PROXY_DOMAIN
- RAILWAY_TCP_PROXY_PORT
- RAILWAY_VOLUME_ID
- RAILWAY_VOLUME_MOUNT_PATH
- RAILWAY_VOLUME_NAME
- REDISHOST
- REDISPASSWORD
- REDISPORT
- REDISUSER
- REDIS_AOF_ENABLED
- REDIS_PASSWORD
- REDIS_PUBLIC_URL
- REDIS_RDB_POLICY
- REDIS_URL

### asset-generator (58 variables)

**Variable Names:**
- CALLBACK_URL
- CLAID_API_KEY
- CLOUDINARY_API_KEY
- CLOUDINARY_API_SECRET
- CLOUDINARY_CLOUD_NAME
- EDEN_AI_API_KEY
- GOOGLE_CLOUD_AUTH_PROVIDER_X509_CERT_URL
- GOOGLE_CLOUD_AUTH_URI
- GOOGLE_CLOUD_CLIENT_EMAIL
- GOOGLE_CLOUD_CLIENT_ID
- GOOGLE_CLOUD_CLIENT_X509_CERT_URL
- GOOGLE_CLOUD_PRIVATE_KEY
- GOOGLE_CLOUD_PRIVATE_KEY_ID
- GOOGLE_CLOUD_PROJECT_ID
- GOOGLE_CLOUD_TOKEN_URI
- GOOGLE_CLOUD_TYPE
- GOOGLE_CLOUD_UNIVERSE_DOMAIN
- LANGFLOW_API_KEY
- LANGFLOW_API_TOKEN
- LANGFLOW_BASE_URL
- OPENAI_API_KEY
- PUSHER_APP_ID
- PUSHER_CLUSTER
- PUSHER_HOST
- PUSHER_KEY
- PUSHER_PORT
- PUSHER_SECRET
- PUSHER_USE_TLS
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL
- REDIS_URL
- REMOVE_BG_API_KEY
- UPLOADTHING_APP_ID
- UPLOADTHING_SECRET
- UPLOAD_THING_API_TOKEN

### retail-mgmt-stg (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### ash-assistant (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### Primary-1k-6 (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### Redis-S0jv (22 variables)

**Variable Names:**
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL

### advid-marketing-page (32 variables)

**Variable Names:**
- CONTENTFUL_ACCESS_TOKEN
- CONTENTFUL_SPACE_ID
- GHOST_ADMIN_API_KEY
- GHOST_API_URL
- GHOST_CONTENT_API_KEY
- N8N_WEBHOOK_URL
- NEXT_PUBLIC_BASE_URL
- RAILWAY_ENVIRONMENT
- RAILWAY_ENVIRONMENT_ID
- RAILWAY_ENVIRONMENT_NAME
- RAILWAY_PRIVATE_DOMAIN
- RAILWAY_PROJECT_ID
- RAILWAY_PROJECT_NAME
- RAILWAY_PUBLIC_DOMAIN
- RAILWAY_SERVICE_ADFURY_APP_URL
- RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL
- RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL
- RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL
- RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL
- RAILWAY_SERVICE_ADVID_SERVER_URL
- RAILWAY_SERVICE_ADVID_WM_SERVICE_URL
- RAILWAY_SERVICE_ANALYTICS_VIEW_URL
- RAILWAY_SERVICE_ASSET_GENERATOR_URL
- RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL
- RAILWAY_SERVICE_ID
- RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL
- RAILWAY_SERVICE_NAME
- RAILWAY_SERVICE_PDF_SCRAPER_URL
- RAILWAY_SERVICE_PRIMARY_URL
- RAILWAY_SERVICE_RETAIL_MGMT_URL
- RAILWAY_SERVICE_SOKETI_URL
- RAILWAY_STATIC_URL

**Variables Summary:**
- **Total Variables Across All Services:** 1295
- **Services with Variables Retrieved:** 40
- **Services Requiring Manual Variable Retrieval:** 0

## Service Documentation Files

- [Worker-fS_a Documentation](./service-Worker-fS_a.md)
- [adfury-app Documentation](./service-adfury-app.md)
- [Redis-Auth Documentation](./service-Redis-Auth.md)
- [Redis-Sessions Documentation](./service-Redis-Sessions.md)
- [Flowise AI (Agents + Datastore + Flows) Documentation](./service-Flowise_AI_(Agents_+_Datastore_+_Flows).md)
- [marketplace-item-scheduler Documentation](./service-marketplace-item-scheduler.md)
- [advid-server Documentation](./service-advid-server.md)
- [Redis-M2_I Documentation](./service-Redis-M2_I.md)
- [payment-gateway Documentation](./service-payment-gateway.md)
- [Postgres Documentation](./service-Postgres.md)
- [advid-wm-service Documentation](./service-advid-wm-service.md)
- [adfury-email-service-stg Documentation](./service-adfury-email-service-stg.md)
- [Postgres-C5Ge Documentation](./service-Postgres-C5Ge.md)
- [analytics-view Documentation](./service-analytics-view.md)
- [Redis-yTjt Documentation](./service-Redis-yTjt.md)
- [advid-server-stg Documentation](./service-advid-server-stg.md)
- [pdf-scraper Documentation](./service-pdf-scraper.md)
- [Postgres-yzsh Documentation](./service-Postgres-yzsh.md)
- [adfury-payment-gateway Documentation](./service-adfury-payment-gateway.md)
- [adfury-app-demo Documentation](./service-adfury-app-demo.md)
- [Worker Documentation](./service-Worker.md)
- [Worker-tjT0 Documentation](./service-Worker-tjT0.md)
- [Primary Documentation](./service-Primary.md)
- [Primary-TvKP Documentation](./service-Primary-TvKP.md)
- [langflowai/langflow:latest Documentation](./service-langflowai-langflow-latest.md)
- [asset-generator-stg Documentation](./service-asset-generator-stg.md)
- [Soketi Documentation](./service-Soketi.md)
- [Postgres-56l- Documentation](./service-Postgres-56l-.md)
- [retail-mgmt Documentation](./service-retail-mgmt.md)
- [Redis-HVJ1 Documentation](./service-Redis-HVJ1.md)
- [advid-client-sandbox Documentation](./service-advid-client-sandbox.md)
- [Redis Documentation](./service-Redis.md)
- [adfury-email-service Documentation](./service-adfury-email-service.md)
- [Redis-mXH1 Documentation](./service-Redis-mXH1.md)
- [asset-generator Documentation](./service-asset-generator.md)
- [retail-mgmt-stg Documentation](./service-retail-mgmt-stg.md)
- [ash-assistant Documentation](./service-ash-assistant.md)
- [Primary-1k-6 Documentation](./service-Primary-1k-6.md)
- [Redis-S0jv Documentation](./service-Redis-S0jv.md)
- [advid-marketing-page Documentation](./service-advid-marketing-page.md)

## Project Management Commands

```bash
# View project status
railway status

# List all projects
railway list

# Switch environments
railway environment

# View all services
railway status

# Open project dashboard
railway open

# Get all variables programmatically
railway variables --json

# Get variables for specific service
railway variables --service <service-name> --json
```

## Authentication & API Access

**Current Authentication Status:** Logged in as Bryce (<EMAIL>) 👋
✅ Authenticated

For programmatic access (CI/CD, scripts), set environment variables:
- `RAILWAY_TOKEN` - Project token (service-specific access)
- `RAILWAY_API_TOKEN` - Account/Team token (full access)

**Get tokens from:** [Railway Dashboard > Settings > Tokens](https://railway.app/dashboard)

## Network Architecture

Services in this project communicate via Railway's private networking at:
- **Private Domain Pattern:** `{service-name}.railway.internal`

## Useful Links

- [Railway Dashboard](https://railway.app/dashboard)
- [Railway Documentation](https://docs.railway.app)
- [Railway CLI Reference](https://docs.railway.app/reference/cli-api)
- [Variables Guide](https://docs.railway.app/guides/variables)

---

## Export Summary

**Generated Files:**
railway-docs-********-124102/Flowise_AI_(Agents_+_Datastore_+_Flows)-service.json
railway-docs-********-124102/Flowise_AI_(Agents_+_Datastore_+_Flows)-variables.json
railway-docs-********-124102/Postgres-56l--service.json
railway-docs-********-124102/Postgres-56l--variables.json
railway-docs-********-124102/Postgres-C5Ge-service.json
railway-docs-********-124102/Postgres-C5Ge-variables.json
railway-docs-********-124102/Postgres-service.json
railway-docs-********-124102/Postgres-variables.json
railway-docs-********-124102/Postgres-yzsh-service.json
railway-docs-********-124102/Postgres-yzsh-variables.json
railway-docs-********-124102/Primary-1k-6-service.json
railway-docs-********-124102/Primary-1k-6-variables.json
railway-docs-********-124102/Primary-TvKP-service.json
railway-docs-********-124102/Primary-TvKP-variables.json
railway-docs-********-124102/Primary-service.json
railway-docs-********-124102/Primary-variables.json
railway-docs-********-124102/Redis-Auth-service.json
railway-docs-********-124102/Redis-Auth-variables.json
railway-docs-********-124102/Redis-HVJ1-service.json
railway-docs-********-124102/Redis-HVJ1-variables.json
railway-docs-********-124102/Redis-M2_I-service.json
railway-docs-********-124102/Redis-M2_I-variables.json
railway-docs-********-124102/Redis-S0jv-service.json
railway-docs-********-124102/Redis-S0jv-variables.json
railway-docs-********-124102/Redis-Sessions-service.json
railway-docs-********-124102/Redis-Sessions-variables.json
railway-docs-********-124102/Redis-mXH1-service.json
railway-docs-********-124102/Redis-mXH1-variables.json
railway-docs-********-124102/Redis-service.json
railway-docs-********-124102/Redis-variables.json
railway-docs-********-124102/Redis-yTjt-service.json
railway-docs-********-124102/Redis-yTjt-variables.json
railway-docs-********-124102/Soketi-service.json
railway-docs-********-124102/Soketi-variables.json
railway-docs-********-124102/Worker-fS_a-service.json
railway-docs-********-124102/Worker-fS_a-variables.json
railway-docs-********-124102/Worker-service.json
railway-docs-********-124102/Worker-tjT0-service.json
railway-docs-********-124102/Worker-tjT0-variables.json
railway-docs-********-124102/Worker-variables.json
railway-docs-********-124102/adfury-app-demo-service.json
railway-docs-********-124102/adfury-app-demo-variables.json
railway-docs-********-124102/adfury-app-service.json
railway-docs-********-124102/adfury-app-variables.json
railway-docs-********-124102/adfury-email-service-service.json
railway-docs-********-124102/adfury-email-service-stg-service.json
railway-docs-********-124102/adfury-email-service-stg-variables.json
railway-docs-********-124102/adfury-email-service-variables.json
railway-docs-********-124102/adfury-payment-gateway-service.json
railway-docs-********-124102/adfury-payment-gateway-variables.json
railway-docs-********-124102/advid-client-sandbox-service.json
railway-docs-********-124102/advid-client-sandbox-variables.json
railway-docs-********-124102/advid-marketing-page-service.json
railway-docs-********-124102/advid-marketing-page-variables.json
railway-docs-********-124102/advid-server-service.json
railway-docs-********-124102/advid-server-stg-service.json
railway-docs-********-124102/advid-server-stg-variables.json
railway-docs-********-124102/advid-server-variables.json
railway-docs-********-124102/advid-wm-service-service.json
railway-docs-********-124102/advid-wm-service-variables.json
railway-docs-********-124102/analytics-view-service.json
railway-docs-********-124102/analytics-view-variables.json
railway-docs-********-124102/ash-assistant-service.json
railway-docs-********-124102/ash-assistant-variables.json
railway-docs-********-124102/asset-generator-service.json
railway-docs-********-124102/asset-generator-stg-service.json
railway-docs-********-124102/asset-generator-stg-variables.json
railway-docs-********-124102/asset-generator-variables.json
railway-docs-********-124102/langflowai-langflow-latest-service.json
railway-docs-********-124102/langflowai-langflow-latest-variables.json
railway-docs-********-124102/marketplace-item-scheduler-service.json
railway-docs-********-124102/marketplace-item-scheduler-variables.json
railway-docs-********-124102/payment-gateway-service.json
railway-docs-********-124102/payment-gateway-variables.json
railway-docs-********-124102/pdf-scraper-service.json
railway-docs-********-124102/pdf-scraper-variables.json
railway-docs-********-124102/project-status.json
railway-docs-********-124102/railway-ecosystem.md
railway-docs-********-124102/retail-mgmt-service.json
railway-docs-********-124102/retail-mgmt-stg-service.json
railway-docs-********-124102/retail-mgmt-stg-variables.json
railway-docs-********-124102/retail-mgmt-variables.json
railway-docs-********-124102/service-Flowise_AI_(Agents_+_Datastore_+_Flows).md
railway-docs-********-124102/service-Postgres-56l-.md
railway-docs-********-124102/service-Postgres-C5Ge.md
railway-docs-********-124102/service-Postgres-yzsh.md
railway-docs-********-124102/service-Postgres.md
railway-docs-********-124102/service-Primary-1k-6.md
railway-docs-********-124102/service-Primary-TvKP.md
railway-docs-********-124102/service-Primary.md
railway-docs-********-124102/service-Redis-Auth.md
railway-docs-********-124102/service-Redis-HVJ1.md
railway-docs-********-124102/service-Redis-M2_I.md
railway-docs-********-124102/service-Redis-S0jv.md
railway-docs-********-124102/service-Redis-Sessions.md
railway-docs-********-124102/service-Redis-mXH1.md
railway-docs-********-124102/service-Redis-yTjt.md
railway-docs-********-124102/service-Redis.md
railway-docs-********-124102/service-Soketi.md
railway-docs-********-124102/service-Worker-fS_a.md
railway-docs-********-124102/service-Worker-tjT0.md
railway-docs-********-124102/service-Worker.md
railway-docs-********-124102/service-adfury-app-demo.md
railway-docs-********-124102/service-adfury-app.md
railway-docs-********-124102/service-adfury-email-service-stg.md
railway-docs-********-124102/service-adfury-email-service.md
railway-docs-********-124102/service-adfury-payment-gateway.md
railway-docs-********-124102/service-advid-client-sandbox.md
railway-docs-********-124102/service-advid-marketing-page.md
railway-docs-********-124102/service-advid-server-stg.md
railway-docs-********-124102/service-advid-server.md
railway-docs-********-124102/service-advid-wm-service.md
railway-docs-********-124102/service-analytics-view.md
railway-docs-********-124102/service-ash-assistant.md
railway-docs-********-124102/service-asset-generator-stg.md
railway-docs-********-124102/service-asset-generator.md
railway-docs-********-124102/service-langflowai-langflow-latest.md
railway-docs-********-124102/service-marketplace-item-scheduler.md
railway-docs-********-124102/service-payment-gateway.md
railway-docs-********-124102/service-pdf-scraper.md
railway-docs-********-124102/service-retail-mgmt-stg.md
railway-docs-********-124102/service-retail-mgmt.md
railway-docs-********-124102/services-list.txt

**Total Documentation Files:**       41
**Variables Retrieved:** 1295 variables across 40 services

*This documentation was automatically generated from your Railway project configuration.*
