# Service: retail-mgmt-stg

**Generated:** Tue Jul 22 12:41:48 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** retail-mgmt-stg
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "retail-mgmt-stg",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | retail-mgmt-stg |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |


## Service Instances

**Total Instances:** 2

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 8908257e-e032-4ae7-bd34-84651bb01208 | Unknown | 89aed380-ce64-4a52-b022-20e17d3a5562 |
| c2329538-5a47-4dc7-be27-1541264cc9bd | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "retail-mgmt-stg",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "8908257e-e032-4ae7-bd34-84651bb01208",
          "serviceId": "************************************",
          "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "79a4565b-2154-4961-b4ae-20f4cd44a089",
            "meta": {
              "branch": "staging",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
              "commitMessage": "remove ignored files from git tracking",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "builder": "NIXPACKS"
                },
                "deploy": {
                  "healthcheckPath": "/health",
                  "healthcheckTimeout": 100,
                  "restartPolicyType": "ON_FAILURE",
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.buildCommand": "$.build.buildCommand",
                "build.builder": "$.build.builder",
                "deploy.envs": "$.deploy.envs",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "migrate",
              "repo": "AdVid-ai/retail-mgmt",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckTimeout": 100,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/retail-mgmt",
            "image": null
          }
        }
      },
      {
        "node": {
          "id": "c2329538-5a47-4dc7-be27-1541264cc9bd",
          "serviceId": "************************************",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "f7fc88a2-05e1-49fd-babb-b6ee8ddf3815",
            "meta": {
              "branch": "staging",
              "buildOnly": false,
              "commitAuthor": "",
              "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
              "commitMessage": "remove ignored files from git tracking",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "builder": "NIXPACKS"
                },
                "deploy": {
                  "healthcheckPath": "/health",
                  "healthcheckTimeout": 100,
                  "restartPolicyType": "ON_FAILURE",
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "isPublicRepoDeploy": false,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "patchId": "85f81d41-9d73-4265-9722-e040640b6d1a",
              "propertyFileMapping": {
                "build.buildCommand": "$.build.buildCommand",
                "build.builder": "$.build.builder",
                "deploy.envs": "$.deploy.envs",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "migrate",
              "repo": "AdVid-ai/retail-mgmt",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": 100,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/retail-mgmt",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "79a4565b-2154-4961-b4ae-20f4cd44a089",
  "meta": {
    "branch": "staging",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
    "commitMessage": "remove ignored files from git tracking",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "buildCommand": "npm install",
        "builder": "NIXPACKS"
      },
      "deploy": {
        "healthcheckPath": "/health",
        "healthcheckTimeout": 100,
        "restartPolicyType": "ON_FAILURE",
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.buildCommand": "$.build.buildCommand",
      "build.builder": "$.build.builder",
      "deploy.envs": "$.deploy.envs",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.restartPolicyType": "$.deploy.restartPolicyType",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "migrate",
    "repo": "AdVid-ai/retail-mgmt",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": "npm install",
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckTimeout": 100,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
{
  "canRedeploy": true,
  "id": "f7fc88a2-05e1-49fd-babb-b6ee8ddf3815",
  "meta": {
    "branch": "staging",
    "buildOnly": false,
    "commitAuthor": "",
    "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
    "commitMessage": "remove ignored files from git tracking",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "buildCommand": "npm install",
        "builder": "NIXPACKS"
      },
      "deploy": {
        "healthcheckPath": "/health",
        "healthcheckTimeout": 100,
        "restartPolicyType": "ON_FAILURE",
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "isPublicRepoDeploy": false,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "patchId": "85f81d41-9d73-4265-9722-e040640b6d1a",
    "propertyFileMapping": {
      "build.buildCommand": "$.build.buildCommand",
      "build.builder": "$.build.builder",
      "deploy.envs": "$.deploy.envs",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.restartPolicyType": "$.deploy.restartPolicyType",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "migrate",
    "repo": "AdVid-ai/retail-mgmt",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": "npm install",
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": 100,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service retail-mgmt-stg

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "retail-mgmt-stg" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
