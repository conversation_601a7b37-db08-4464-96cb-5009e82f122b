{"id": "5c52fa2a-848e-4f8f-84b9-f7ca251ef894", "name": "analytics-view", "serviceInstances": {"edges": [{"node": {"id": "e5d90686-6785-4fa7-929b-8d846780655c", "serviceId": "5c52fa2a-848e-4f8f-84b9-f7ca251ef894", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "8184b358-f403-49d1-a44b-bbe31d747bee", "meta": {"branch": "main", "buildOnly": false, "commitAuthor": "", "commitHash": "337a7554efc31759e5b23ae0ec2cc9f911d732a8", "commitMessage": "new repo", "fileServiceManifest": {}, "githubDeploymentId": **********, "ignoreWatchPatterns": true, "isPublicRepoDeploy": false, "logsV2": true, "nixpacksProviders": ["node"], "patchId": "179d63b8-f957-4595-88ab-4fd2fda43a5a", "propertyFileMapping": {}, "reason": "deploy", "repo": "AdVid-ai/analytics-view", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/analytics-view", "image": null}}}]}}