# Service: Worker

**Generated:** Tue Jul 22 12:41:28 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Worker
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "DB_POSTGRESDB_DATABASE": "railway",
  "DB_POSTGRESDB_HOST": "postgres.railway.internal",
  "DB_POSTGRESDB_PASSWORD": "IeZrAr-MN*2e67a0~pc!Lb3ADW5sS4cA",
  "DB_POSTGRESDB_PORT": "5432",
  "DB_POSTGRESDB_USER": "railway",
  "DB_TYPE": "postgresdb",
  "ENABLE_ALPINE_PRIVATE_NETWORKING": "true",
  "EXECUTIONS_MODE": "queue",
  "N8N_ENCRYPTION_KEY": "6ijSb5x*FOdhRa7_eg9SuLiKtLLkdKF7",
  "N8N_LISTEN_ADDRESS": "::",
  "NODE_OPTIONS": "--max_old_space_size=8192",
  "PORT": "5678",
  "QUEUE_BULL_REDIS_HOST": "interchange.proxy.rlwy.net",
  "QUEUE_BULL_REDIS_PASSWORD": "Sy0fDf94wRW0tap17sAWJaLYLh2evk*S",
  "QUEUE_BULL_REDIS_PORT": "41842",
  "QUEUE_BULL_REDIS_USERNAME": "default",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "worker.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "Worker",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "WEBHOOK_URL": "https://primary-production-35c9.up.railway.app"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| DB_POSTGRESDB_DATABASE | railway |
| DB_POSTGRESDB_HOST | postgres.railway.internal |
| DB_POSTGRESDB_PASSWORD | IeZrAr-MN*2e67a0~pc!Lb3ADW5sS4cA |
| DB_POSTGRESDB_PORT | 5432 |
| DB_POSTGRESDB_USER | railway |
| DB_TYPE | postgresdb |
| ENABLE_ALPINE_PRIVATE_NETWORKING | true |
| EXECUTIONS_MODE | queue |
| N8N_ENCRYPTION_KEY | 6ijSb5x*FOdhRa7_eg9SuLiKtLLkdKF7 |
| N8N_LISTEN_ADDRESS | :: |
| NODE_OPTIONS | --max_old_space_size=8192 |
| PORT | 5678 |
| QUEUE_BULL_REDIS_HOST | interchange.proxy.rlwy.net |
| QUEUE_BULL_REDIS_PASSWORD | Sy0fDf94wRW0tap17sAWJaLYLh2evk*S |
| QUEUE_BULL_REDIS_PORT | 41842 |
| QUEUE_BULL_REDIS_USERNAME | default |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | worker.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | Worker |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| WEBHOOK_URL | https://primary-production-35c9.up.railway.app |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 4ac792ca-97fe-4e5a-af46-780ce24ef6ff | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "Worker",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "4ac792ca-97fe-4e5a-af46-780ce24ef6ff",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "a6bacea5-9e4b-468a-a7fc-b48989ad0862",
            "meta": {
              "ignoreWatchPatterns": true,
              "image": "n8nio/n8n",
              "logsV2": true,
              "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
              "queuedReason": "Waiting for dependencies to deploy",
              "reason": "redeploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "n8n worker"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": null,
            "image": "n8nio/n8n"
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "a6bacea5-9e4b-468a-a7fc-b48989ad0862",
  "meta": {
    "ignoreWatchPatterns": true,
    "image": "n8nio/n8n",
    "logsV2": true,
    "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
    "queuedReason": "Waiting for dependencies to deploy",
    "reason": "redeploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "n8n worker"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Worker

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "Worker" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
