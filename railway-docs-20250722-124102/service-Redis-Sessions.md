# Service: Redis-Sessions

**Generated:** Tue Jul 22 12:41:09 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Redis-Sessions
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "redis-53br.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_RUN_AS_ROOT": "true",
  "RAILWAY_RUN_UID": "0",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "Redis-Sessions",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_TCP_APPLICATION_PORT": "6379",
  "RAILWAY_TCP_PROXY_DOMAIN": "yamanote.proxy.rlwy.net",
  "RAILWAY_TCP_PROXY_PORT": "38358",
  "RAILWAY_VOLUME_ID": "************************************",
  "RAILWAY_VOLUME_MOUNT_PATH": "/bitnami",
  "RAILWAY_VOLUME_NAME": "jelly-volume",
  "REDISHOST": "redis-53br.railway.internal",
  "REDISPASSWORD": "wForcselBYHrTXttDuccwwGhTIlkGwHr",
  "REDISPORT": "6379",
  "REDISUSER": "default",
  "REDIS_AOF_ENABLED": "no",
  "REDIS_PASSWORD": "wForcselBYHrTXttDuccwwGhTIlkGwHr",
  "REDIS_PUBLIC_URL": "redis://default:<EMAIL>:38358",
  "REDIS_RDB_POLICY": "3600#1 300#100 60#10000",
  "REDIS_URL": "redis://default:<EMAIL>:6379"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | redis-53br.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_RUN_AS_ROOT | true |
| RAILWAY_RUN_UID | 0 |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | Redis-Sessions |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_TCP_APPLICATION_PORT | 6379 |
| RAILWAY_TCP_PROXY_DOMAIN | yamanote.proxy.rlwy.net |
| RAILWAY_TCP_PROXY_PORT | 38358 |
| RAILWAY_VOLUME_ID | ************************************ |
| RAILWAY_VOLUME_MOUNT_PATH | /bitnami |
| RAILWAY_VOLUME_NAME | jelly-volume |
| REDISHOST | redis-53br.railway.internal |
| REDISPASSWORD | wForcselBYHrTXttDuccwwGhTIlkGwHr |
| REDISPORT | 6379 |
| REDISUSER | default |
| REDIS_AOF_ENABLED | no |
| REDIS_PASSWORD | wForcselBYHrTXttDuccwwGhTIlkGwHr |
| REDIS_PUBLIC_URL | redis://default:<EMAIL>:38358 |
| REDIS_RDB_POLICY | 3600#1 300#100 60#10000 |
| REDIS_URL | redis://default:<EMAIL>:6379 |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| d22925e2-c0ed-4853-9dbb-f70518a401bd | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "Redis-Sessions",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "d22925e2-c0ed-4853-9dbb-f70518a401bd",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "54e6ae8f-a113-4127-b04c-2f7ea23eabfc",
            "meta": {
              "duration": 1773042,
              "image": "bitnami/redis:7.2.5",
              "logsV2": true,
              "patchId": "f7da1d75-24ca-4ed0-818f-960d187a28df",
              "reason": "rollback",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": "/bitnami",
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/bitnami"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "bitnami/redis:7.2.5"
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "54e6ae8f-a113-4127-b04c-2f7ea23eabfc",
  "meta": {
    "duration": 1773042,
    "image": "bitnami/redis:7.2.5",
    "logsV2": true,
    "patchId": "f7da1d75-24ca-4ed0-818f-960d187a28df",
    "reason": "rollback",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": "/bitnami",
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/bitnami"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Redis-Sessions

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "Redis-Sessions" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
