{"id": "c3ee66f5-ee6e-4808-8a1f-bdf353bc6a22", "name": "adfury-email-service", "serviceInstances": {"edges": [{"node": {"id": "f1bdbead-7d00-40b8-b51e-6f49dcd80a82", "serviceId": "c3ee66f5-ee6e-4808-8a1f-bdf353bc6a22", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "ba67300a-99d2-4158-a893-5d5ecc00fb67", "meta": {"branch": "main", "buildOnly": false, "commitAuthor": "bryce27", "commitHash": "da1661e45f95bc46d7f9c33c56d3245bfdb0044c", "commitMessage": "Merge pull request #4 from AdVid-ai/staging\n\nfix: resolve Gmail spam warnings and improve email deliverability", "configFile": "railway.json", "fileServiceManifest": {"build": {"buildCommand": "npm run build", "builder": "NIXPACKS"}, "deploy": {"healthcheckPath": "/ready", "healthcheckTimeout": 60, "numReplicas": 1, "restartPolicyMaxRetries": 3, "restartPolicyType": "ON_FAILURE", "startCommand": "npm start"}}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {"build.buildCommand": "$.build.buildCommand", "build.builder": "$.build.builder", "deploy.healthcheckPath": "$.deploy.healthcheckPath", "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout", "deploy.numReplicas": "$.deploy.numReplicas", "deploy.restartPolicyMaxRetries": "$.deploy.restartPolicyMaxRetries", "deploy.restartPolicyType": "$.deploy.restartPolicyType", "deploy.startCommand": "$.deploy.startCommand"}, "reason": "deploy", "repo": "AdVid-ai/adfury-email-service", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": "npm run build", "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": "/ready", "healthcheckTimeout": 60, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 3, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "npm start"}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/adfury-email-service", "image": null}}}]}}