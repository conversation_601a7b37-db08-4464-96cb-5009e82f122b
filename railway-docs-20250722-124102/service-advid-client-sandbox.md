# Service: advid-client-sandbox

**Generated:** Tue Jul 22 12:41:42 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** advid-client-sandbox
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "BASE_ASSET_URL": "https://asset-generator-production.up.railway.app",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "aware-analysis.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "advid-client-sandbox",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "sandbox.advid.ai"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| BASE_ASSET_URL | https://asset-generator-production.up.railway.app |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | aware-analysis.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | sandbox.advid.ai |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | advid-client-sandbox |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | sandbox.advid.ai |


## Service Instances

**Total Instances:** 3

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 209f910f-3a37-46b8-9bd5-698cde8669ff | Unknown | ************************************ |
| cb0a8259-8dab-4d3b-b89a-cef256a3fa62 | Unknown | 89aed380-ce64-4a52-b022-20e17d3a5562 |
| dc848756-198b-4fb0-8219-99778f46d541 | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "advid-client-sandbox",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "209f910f-3a37-46b8-9bd5-698cde8669ff",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "fbe77ad6-1f08-4fcb-a29f-08454460c03e",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "alpendergrast",
              "commitHash": "6bc777c60845c84956321e9e2036dbbe292a91c3",
              "commitMessage": "adjust layout for url sub and detail pages",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {},
              "reason": "migrate",
              "repo": "AdVid-ai/advid-client-sandbox",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-client-sandbox",
            "image": null
          }
        }
      },
      {
        "node": {
          "id": "cb0a8259-8dab-4d3b-b89a-cef256a3fa62",
          "serviceId": "************************************",
          "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "latestDeployment": null,
          "source": null
        }
      },
      {
        "node": {
          "id": "dc848756-198b-4fb0-8219-99778f46d541",
          "serviceId": "************************************",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": null,
          "source": null
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "fbe77ad6-1f08-4fcb-a29f-08454460c03e",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "alpendergrast",
    "commitHash": "6bc777c60845c84956321e9e2036dbbe292a91c3",
    "commitMessage": "adjust layout for url sub and detail pages",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {},
    "reason": "migrate",
    "repo": "AdVid-ai/advid-client-sandbox",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
null
null
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service advid-client-sandbox

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "advid-client-sandbox" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
