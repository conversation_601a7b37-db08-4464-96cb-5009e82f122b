# Service: advid-server

**Generated:** Tue Jul 22 12:41:13 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** advid-server
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "APP_BASE_URL": "https://app.adfury.ai",
  "AUTH0_AUDIENCE": "http://localhost:5000",
  "AUTH0_CALLBACK_URL": "http://localhost:5000/callback",
  "AUTH0_CLIENT_ID": "O98PyUH72xNTjJo3Fswn7FgEwhNJuLle",
  "AUTH0_CLIENT_SECRET": "****************************************************************",
  "AUTH0_DOMAIN": "dev-zjqwcrjrkyr2dfsm.us.auth0.com",
  "COOKIE_SECRET": "****************************************************************",
  "DATABASE_URL": "postgresql://adfury_owner:<EMAIL>/adfury?sslmode=require&channel_binding=require",
  "GOOGLE_CLOUD_AUTH_PROVIDER_X509_CERT_URL": "https://www.googleapis.com/oauth2/v1/certs",
  "GOOGLE_CLOUD_AUTH_URI": "https://accounts.google.com/o/oauth2/auth",
  "GOOGLE_CLOUD_CLIENT_EMAIL": "<EMAIL>",
  "GOOGLE_CLOUD_CLIENT_ID": "115713899329077171498",
  "GOOGLE_CLOUD_CLIENT_X509_CERT_URL": "https://www.googleapis.com/robot/v1/metadata/x509/storage-service-account%40advid-gradient-bgs.iam.gserviceaccount.com",
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  "GOOGLE_CLOUD_PRIVATE_KEY_ID": "754231d842f8becfe3db8b1ae7b12408d22b5175",
  "GOOGLE_CLOUD_PROJECT_ID": "advid-gradient-bgs",
  "GOOGLE_CLOUD_TOKEN_URI": "https://oauth2.googleapis.com/token",
  "GOOGLE_CLOUD_TYPE": "service_account",
  "GOOGLE_CLOUD_UNIVERSE_DOMAIN": "googleapis.com",
  "JWT_SECRET": "c4de2da0eab60145f815f81f446d4908f40d54a6a8e712859c3184ee06ef0248",
  "N8N_SECRET": "9110c46a228cb11fcd1704c0681b71988fc3a8584896fa5f53359862430f14f4",
  "NODE_ENV": "development",
  "PORT": "5000",
  "QUEUE_REDIS_URL": "redis://default:<EMAIL>:6379 ",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "advid-server.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "advid-server",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "prod-server.adfury.ai",
  "REDIS_PORT": "12201",
  "REDIS_URL": "redis://default:<EMAIL>:12201",
  "RESEND_API_KEY": "re_XuBL4bEf_LqwVJTzVHh4k8cLRLeaXW34o",
  "RETAIL_MGMT_URL": "https://retail-mgmt-production.up.railway.app",
  "SESSION_SECRET": "'UuxhnCLfuM2KV7WNQqDlxI6m69QTvVM9KfpFVVENo38='",
  "STRIPE_PUBLISHABLE_KEY": "pk_test_51QjtU1AYoRIs6MbFE5t7t7AoLtnMuOFIBHTm5gBCzpe5srGCf4XEaBSAaVt3E8Ab9XKnosrVfnEnPc53sgCj7Bqx008qxGSyn0",
  "STRIPE_SECRET_KEY": "sk_test_51QjtU1AYoRIs6MbFVHdVRxfdDN6bmbJiKineZRlq3u1SFN3j8VJXjk2eXEFUllJBRE3tl8dK091ZusxCgw8ZPxb500zaSJqN9y",
  "WALMART_MANAGER_API_URL": "https://advid-wm-service-production.up.railway.app"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| APP_BASE_URL | https://app.adfury.ai |
| AUTH0_AUDIENCE | http://localhost:5000 |
| AUTH0_CALLBACK_URL | http://localhost:5000/callback |
| AUTH0_CLIENT_ID | O98PyUH72xNTjJo3Fswn7FgEwhNJuLle |
| AUTH0_CLIENT_SECRET | **************************************************************** |
| AUTH0_DOMAIN | dev-zjqwcrjrkyr2dfsm.us.auth0.com |
| COOKIE_SECRET | **************************************************************** |
| DATABASE_URL | postgresql://adfury_owner:<EMAIL>/adfury?sslmode=require&channel_binding=require |
| GOOGLE_CLOUD_AUTH_PROVIDER_X509_CERT_URL | https://www.googleapis.com/oauth2/v1/certs |
| GOOGLE_CLOUD_AUTH_URI | https://accounts.google.com/o/oauth2/auth |
| GOOGLE_CLOUD_CLIENT_EMAIL | <EMAIL> |
| GOOGLE_CLOUD_CLIENT_ID | 115713899329077171498 |
| GOOGLE_CLOUD_CLIENT_X509_CERT_URL | https://www.googleapis.com/robot/v1/metadata/x509/storage-service-account%40advid-gradient-bgs.iam.gserviceaccount.com |
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
 |
| GOOGLE_CLOUD_PRIVATE_KEY_ID | 754231d842f8becfe3db8b1ae7b12408d22b5175 |
| GOOGLE_CLOUD_PROJECT_ID | advid-gradient-bgs |
| GOOGLE_CLOUD_TOKEN_URI | https://oauth2.googleapis.com/token |
| GOOGLE_CLOUD_TYPE | service_account |
| GOOGLE_CLOUD_UNIVERSE_DOMAIN | googleapis.com |
| JWT_SECRET | c4de2da0eab60145f815f81f446d4908f40d54a6a8e712859c3184ee06ef0248 |
| N8N_SECRET | 9110c46a228cb11fcd1704c0681b71988fc3a8584896fa5f53359862430f14f4 |
| NODE_ENV | development |
| PORT | 5000 |
| QUEUE_REDIS_URL | redis://default:<EMAIL>:6379  |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | advid-server.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | advid-server |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | prod-server.adfury.ai |
| REDIS_PORT | 12201 |
| REDIS_URL | redis://default:<EMAIL>:12201 |
| RESEND_API_KEY | re_XuBL4bEf_LqwVJTzVHh4k8cLRLeaXW34o |
| RETAIL_MGMT_URL | https://retail-mgmt-production.up.railway.app |
| SESSION_SECRET | 'UuxhnCLfuM2KV7WNQqDlxI6m69QTvVM9KfpFVVENo38=' |
| STRIPE_PUBLISHABLE_KEY | pk_test_51QjtU1AYoRIs6MbFE5t7t7AoLtnMuOFIBHTm5gBCzpe5srGCf4XEaBSAaVt3E8Ab9XKnosrVfnEnPc53sgCj7Bqx008qxGSyn0 |
| STRIPE_SECRET_KEY | sk_test_51QjtU1AYoRIs6MbFVHdVRxfdDN6bmbJiKineZRlq3u1SFN3j8VJXjk2eXEFUllJBRE3tl8dK091ZusxCgw8ZPxb500zaSJqN9y |
| WALMART_MANAGER_API_URL | https://advid-wm-service-production.up.railway.app |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| c78091c5-da1f-4d7d-89e8-ffcec769701b | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "advid-server",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "c78091c5-da1f-4d7d-89e8-ffcec769701b",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "b347928d-c403-48a0-90ef-cca9f3164a2d",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "alpendergrast",
              "commitHash": "cee42720a546ff98e29bf7f0bcd5f2e497b50ab3",
              "commitMessage": "Merge pull request #116 from AdVid-ai/ADV-354\n\nADV-354 - Fix credential update",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {},
              "reason": "deploy",
              "repo": "AdVid-ai/advid-server",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 3
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-server",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "b347928d-c403-48a0-90ef-cca9f3164a2d",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "alpendergrast",
    "commitHash": "cee42720a546ff98e29bf7f0bcd5f2e497b50ab3",
    "commitMessage": "Merge pull request #116 from AdVid-ai/ADV-354\n\nADV-354 - Fix credential update",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {},
    "reason": "deploy",
    "repo": "AdVid-ai/advid-server",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 3
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service advid-server

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "advid-server" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
