{"id": "5afd7862-c46c-4904-8a0d-129007ede9b6", "name": "Postgres-C5Ge", "serviceInstances": {"edges": [{"node": {"id": "10aa8a7d-8b75-4759-9ecd-a45b2aef81ff", "serviceId": "5afd7862-c46c-4904-8a0d-129007ede9b6", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "25c3aca4-2967-4740-acd8-b62e512c9364", "meta": {"ignoreWatchPatterns": true, "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest", "logsV2": true, "patchId": "e4769826-016f-4d40-84d5-a63f51acd20d", "reason": "migrate", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-west2": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": ["/var/lib/postgresql/data"]}}, "source": {"repo": null, "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest"}}}]}}