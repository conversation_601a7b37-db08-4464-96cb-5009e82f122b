# Service: advid-marketing-page

**Generated:** Tue Jul 22 12:41:54 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** advid-marketing-page
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "CONTENTFUL_ACCESS_TOKEN": "97CQi4E1HNAKZLEqfK8GLWe7gz_aMkbzY_qnZSebQe4",
  "CONTENTFUL_SPACE_ID": "nqgirwgik5yp",
  "GHOST_ADMIN_API_KEY": "67bf2a3a44a490000135f794:88718e16d1c73a381cef9bad7f192a9bde9803f98cc41d9bfc1c1bc23d15bbda",
  "GHOST_API_URL": "https://adfury-ai.ghost.io",
  "GHOST_CONTENT_API_KEY": "bddd76665462e6dfc4c224cb3c",
  "N8N_WEBHOOK_URL": "https://primary-production-35c9.up.railway.app/webhook/************************************",
  "NEXT_PUBLIC_BASE_URL": "https://adfury.ai",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "advid-marketing-page.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "advid-marketing-page",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "advid-marketing-page-production.up.railway.app"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| CONTENTFUL_ACCESS_TOKEN | 97CQi4E1HNAKZLEqfK8GLWe7gz_aMkbzY_qnZSebQe4 |
| CONTENTFUL_SPACE_ID | nqgirwgik5yp |
| GHOST_ADMIN_API_KEY | 67bf2a3a44a490000135f794:88718e16d1c73a381cef9bad7f192a9bde9803f98cc41d9bfc1c1bc23d15bbda |
| GHOST_API_URL | https://adfury-ai.ghost.io |
| GHOST_CONTENT_API_KEY | bddd76665462e6dfc4c224cb3c |
| N8N_WEBHOOK_URL | https://primary-production-35c9.up.railway.app/webhook/************************************ |
| NEXT_PUBLIC_BASE_URL | https://adfury.ai |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | advid-marketing-page.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | advid-marketing-page |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | advid-marketing-page-production.up.railway.app |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| c0571ac9-c675-4142-bce6-f5949f0147e8 | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "advid-marketing-page",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "c0571ac9-c675-4142-bce6-f5949f0147e8",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "e9495bbd-814f-45a8-8201-17df1d1b514c",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "",
              "commitHash": "6ecd25d5e197741ce0f7994f2e9cbed9eefa9a6a",
              "commitMessage": "replace favicon",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "isPublicRepoDeploy": false,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "patchId": "d4c85156-5769-48e1-aec2-920834ab5ebd",
              "propertyFileMapping": {},
              "reason": "migrate",
              "repo": "AdVid-ai/advid-marketing-page",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-marketing-page",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "e9495bbd-814f-45a8-8201-17df1d1b514c",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "",
    "commitHash": "6ecd25d5e197741ce0f7994f2e9cbed9eefa9a6a",
    "commitMessage": "replace favicon",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "isPublicRepoDeploy": false,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "patchId": "d4c85156-5769-48e1-aec2-920834ab5ebd",
    "propertyFileMapping": {},
    "reason": "migrate",
    "repo": "AdVid-ai/advid-marketing-page",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service advid-marketing-page

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "advid-marketing-page" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
