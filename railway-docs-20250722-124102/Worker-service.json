{"id": "83517162-ebde-4f95-8a09-3ec95ca2f0f4", "name": "Worker", "serviceInstances": {"edges": [{"node": {"id": "4ac792ca-97fe-4e5a-af46-780ce24ef6ff", "serviceId": "83517162-ebde-4f95-8a09-3ec95ca2f0f4", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "a6bacea5-9e4b-468a-a7fc-b48989ad0862", "meta": {"ignoreWatchPatterns": true, "image": "n8nio/n8n", "logsV2": true, "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3", "queuedReason": "Waiting for dependencies to deploy", "reason": "redeploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-west2": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "n8n worker"}}, "volumeMounts": []}}, "source": {"repo": null, "image": "n8nio/n8n"}}}]}}