# Service: advid-wm-service

**Generated:** Tue Jul 22 12:41:18 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** advid-wm-service
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "ADVID_SERVER_API_BASE_URL": "https://advid-server-production.up.railway.app/api",
  "ADVID_SERVER_URL": "https://advid-server-production.up.railway.app",
  "AUTH0_AUDIENCE": "http://localhost:5000",
  "AUTH0_DOMAIN": "dev-zjqwcrjrkyr2dfsm.us.auth0.com",
  "CREATIVE_SYNC_ENABLED": "true",
  "CREATIVE_SYNC_SCHEDULE": "0 2 * * *",
  "DATABASE_URL": "postgresql://adfury_owner:<EMAIL>/adfury?sslmode=require&channel_binding=require",
  "GOOGLE_CLOUD_BUCKET_NAME": "adfury-wm-creatives",
  "JWT_SECRET": "c4de2da0eab60145f815f81f446d4908f40d54a6a8e712859c3184ee06ef0248",
  "NODE_ENV": "production",
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "advid-wm-service.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "advid-wm-service",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "advid-wm-service-production.up.railway.app",
  "WALMART_API_DISPLAY_ADFURY_ADVERTISER_ID": "16406952",
  "WALMART_API_DISPLAY_AUTH_TOKEN": "8b2b58f4-2023-4027-b4a1-b6f6f0bd1bae",
  "WALMART_API_DISPLAY_BASE_URL": "https://developer.api.us.walmart.com/api-proxy/service/display/api/v1/api/v1",
  "WALMART_API_DISPLAY_CONSUMER_ID": "1ec06f55-64fe-4198-bc6a-590d3e2537a3",
  "WALMART_API_DISPLAY_FISHER_ADVERTISER_ID": "274692",
  "WALMART_API_DISPLAY_SEC_KEY_VERSION": "1",
  "WALMART_API_HEALTH_CHECK_ADFURY_CAMPAIGN_ID": "915853",
  "WALMART_API_HEALTH_CHECK_FISHER_CAMPAIGN_ID": "96215",
  "WALMART_API_MARKETPLACE_BASE_URL": "https://marketplace.walmartapis.com/v3",
  "WALMART_API_MARKETPLACE_REDIRECT_URI": "https://app.adfury.ai/callback",
  "WALMART_API_MARKETPLACE_SANDBOX_BASE_URL": "https://sandbox.walmartapis.com/v3",
  "WALMART_API_MARKETPLACE_TOKEN_URL": "https://marketplace.walmartapis.com/v3/token",
  "WALMART_API_SP1_BASE_URL": "https://api-gateway.walmart.com",
  "WALMART_API_SUPPLIER_REDIRECT_URI": "https://app.adfury.ai/callback",
  "WALMART_API_WPA_ADVERTISER_ID": "16403588",
  "WALMART_API_WPA_BASE_URL": "https://developer.api.stg.walmart.com/api-proxy/service/WPA/Api/v1/api/v1",
  "WM_API_MARKETPLACE_APP_CLIENT_ID": "81b179dd-8a85-4373-9bc6-cad4c607a17b",
  "WM_API_MARKETPLACE_APP_CLIENT_SECRET": "AOoadBRpMJmVR85MAcf9Esobbby2HSrR-v2tLt0Gp5jvAfn3MEaimAPnlIs1cy2F8hAMgBf49Eh6ifnT3E4QxVU",
  "WM_API_MARKETPLACE_CLIENT_ID": "8c5b3e86-01b3-4525-844f-3d8b18385a4a",
  "WM_API_MARKETPLACE_CLIENT_SECRET": "AIuHJ_3yxyW6SHHX17MFYNgbjPnEg-p1aa7HCa-xSvRJ7YyiLhm-gwb6aslPIXianYtuYfSH8Oe2I98m_DENV0I",
  "WM_API_MARKETPLACE_PARTNER_ID": "10002732434",
  "WM_API_MARKETPLACE_REFRESH_TOKEN": "AMMLf7jdEECFKWpEAZCQs0TFiSUaOCWvOMAMa1f2qpYxiPsaS7-Mh6O_biSVFE4P4e2AxypAW-UDJDA9Mq_rjsk",
  "WM_API_SP1_APP_CLIENT_ID": "0f01081e-8017-4f75-aea0-33fa3e093654",
  "WM_API_SP1_APP_CLIENT_SECRET": "ALhPYJqDuI4n5JgYc8gTfGPMOF7dasmdpMqy-rcSQ-L2XLNaVBSJAVxLocPxe7ly-ljlv_9EzmwuyizqB-u8HY4",
  "WM_API_SP1_CLIENT_ID": "eced510c-816f-41f3-83bd-e24a2061b697",
  "WM_API_SP1_CLIENT_SECRET": "bTtitjJmXHpnjzcT3tpe_vz-o-QLEP1U2NaoeBuD_JOlouQzzx_YgBuNr_slwPg_Os1ULiZ6wE7ePq4rPOulGA",
  "WM_API_SP1_CONSUMER_CHANNEL_TYPE": "d62e611e-606e-41b9-96cf-38ee37331c47",
  "WM_MARKETPLACE_BASE64_CREDENTIALS": "OGM1YjNlODYtMDFiMy00NTI1LTg0NGYtM2Q4YjE4Mzg1YTRhOkFJdUhKXzN5eHlXNlNISFgxN01GWU5nYmpQbkVnLXAxYWE3SENhLXhTdlJKN1l5aUxobS1nd2I2YXNsUElYaWFuWXR1WWZTSDhPZTJJOThtX0RFTlYwSQ==",
  "WM_SEC_KEY_VERSION": "1",
  "WM_WPA_API_ADVERTISER_ID": "16403588"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| ADVID_SERVER_API_BASE_URL | https://advid-server-production.up.railway.app/api |
| ADVID_SERVER_URL | https://advid-server-production.up.railway.app |
| AUTH0_AUDIENCE | http://localhost:5000 |
| AUTH0_DOMAIN | dev-zjqwcrjrkyr2dfsm.us.auth0.com |
| CREATIVE_SYNC_ENABLED | true |
| CREATIVE_SYNC_SCHEDULE | 0 2 * * * |
| DATABASE_URL | postgresql://adfury_owner:<EMAIL>/adfury?sslmode=require&channel_binding=require |
| GOOGLE_CLOUD_BUCKET_NAME | adfury-wm-creatives |
| JWT_SECRET | c4de2da0eab60145f815f81f446d4908f40d54a6a8e712859c3184ee06ef0248 |
| NODE_ENV | production |
| PRIVATE_KEY | `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************` |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | advid-wm-service.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | advid-wm-service |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | advid-wm-service-production.up.railway.app |
| WALMART_API_DISPLAY_ADFURY_ADVERTISER_ID | 16406952 |
| WALMART_API_DISPLAY_AUTH_TOKEN | 8b2b58f4-2023-4027-b4a1-b6f6f0bd1bae |
| WALMART_API_DISPLAY_BASE_URL | https://developer.api.us.walmart.com/api-proxy/service/display/api/v1/api/v1 |
| WALMART_API_DISPLAY_CONSUMER_ID | 1ec06f55-64fe-4198-bc6a-590d3e2537a3 |
| WALMART_API_DISPLAY_FISHER_ADVERTISER_ID | 274692 |
| WALMART_API_DISPLAY_SEC_KEY_VERSION | 1 |
| WALMART_API_HEALTH_CHECK_ADFURY_CAMPAIGN_ID | 915853 |
| WALMART_API_HEALTH_CHECK_FISHER_CAMPAIGN_ID | 96215 |
| WALMART_API_MARKETPLACE_BASE_URL | https://marketplace.walmartapis.com/v3 |
| WALMART_API_MARKETPLACE_REDIRECT_URI | https://app.adfury.ai/callback |
| WALMART_API_MARKETPLACE_SANDBOX_BASE_URL | https://sandbox.walmartapis.com/v3 |
| WALMART_API_MARKETPLACE_TOKEN_URL | https://marketplace.walmartapis.com/v3/token |
| WALMART_API_SP1_BASE_URL | https://api-gateway.walmart.com |
| WALMART_API_SUPPLIER_REDIRECT_URI | https://app.adfury.ai/callback |
| WALMART_API_WPA_ADVERTISER_ID | 16403588 |
| WALMART_API_WPA_BASE_URL | https://developer.api.stg.walmart.com/api-proxy/service/WPA/Api/v1/api/v1 |
| WM_API_MARKETPLACE_APP_CLIENT_ID | 81b179dd-8a85-4373-9bc6-cad4c607a17b |
| WM_API_MARKETPLACE_APP_CLIENT_SECRET | AOoadBRpMJmVR85MAcf9Esobbby2HSrR-v2tLt0Gp5jvAfn3MEaimAPnlIs1cy2F8hAMgBf49Eh6ifnT3E4QxVU |
| WM_API_MARKETPLACE_CLIENT_ID | 8c5b3e86-01b3-4525-844f-3d8b18385a4a |
| WM_API_MARKETPLACE_CLIENT_SECRET | AIuHJ_3yxyW6SHHX17MFYNgbjPnEg-p1aa7HCa-xSvRJ7YyiLhm-gwb6aslPIXianYtuYfSH8Oe2I98m_DENV0I |
| WM_API_MARKETPLACE_PARTNER_ID | 10002732434 |
| WM_API_MARKETPLACE_REFRESH_TOKEN | AMMLf7jdEECFKWpEAZCQs0TFiSUaOCWvOMAMa1f2qpYxiPsaS7-Mh6O_biSVFE4P4e2AxypAW-UDJDA9Mq_rjsk |
| WM_API_SP1_APP_CLIENT_ID | 0f01081e-8017-4f75-aea0-33fa3e093654 |
| WM_API_SP1_APP_CLIENT_SECRET | ALhPYJqDuI4n5JgYc8gTfGPMOF7dasmdpMqy-rcSQ-L2XLNaVBSJAVxLocPxe7ly-ljlv_9EzmwuyizqB-u8HY4 |
| WM_API_SP1_CLIENT_ID | eced510c-816f-41f3-83bd-e24a2061b697 |
| WM_API_SP1_CLIENT_SECRET | bTtitjJmXHpnjzcT3tpe_vz-o-QLEP1U2NaoeBuD_JOlouQzzx_YgBuNr_slwPg_Os1ULiZ6wE7ePq4rPOulGA |
| WM_API_SP1_CONSUMER_CHANNEL_TYPE | d62e611e-606e-41b9-96cf-38ee37331c47 |
| WM_MARKETPLACE_BASE64_CREDENTIALS | OGM1YjNlODYtMDFiMy00NTI1LTg0NGYtM2Q4YjE4Mzg1YTRhOkFJdUhKXzN5eHlXNlNISFgxN01GWU5nYmpQbkVnLXAxYWE3SENhLXhTdlJKN1l5aUxobS1nd2I2YXNsUElYaWFuWXR1WWZTSDhPZTJJOThtX0RFTlYwSQ== |
| WM_SEC_KEY_VERSION | 1 |
| WM_WPA_API_ADVERTISER_ID | 16403588 |


## Service Instances

**Total Instances:** 3

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 3f3640f5-53c9-4ee1-b263-87fcb50fe5da | Unknown | 89aed380-ce64-4a52-b022-20e17d3a5562 |
| 61fbd61d-76d3-4cbc-8303-73247f1ef5bb | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |
| e179d54c-7201-4b8b-93e9-1e77eea85dd9 | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "advid-wm-service",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "3f3640f5-53c9-4ee1-b263-87fcb50fe5da",
          "serviceId": "************************************",
          "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "bc89cae0-5b23-4129-9083-4a5a7c0054c9",
            "meta": {
              "branch": "staging",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
              "commitMessage": "stuff",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE"
                }
              },
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.builder": "$.build.builder"
              },
              "reason": "deploy",
              "repo": "AdVid-ai/advid-wm-service",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-wm-service",
            "image": null
          }
        }
      },
      {
        "node": {
          "id": "61fbd61d-76d3-4cbc-8303-73247f1ef5bb",
          "serviceId": "************************************",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "1fcf9d3a-85aa-44c4-87c0-146a4b6efdb5",
            "meta": {
              "branch": "demo",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "b40b6b6367398a253007d6d9c6d9dfa506b0d46f",
              "commitMessage": "Merge pull request #6 from AdVid-ai/staging\n\nremove limit on creative id filtering",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE"
                }
              },
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "isPublicRepoDeploy": false,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "patchId": "bd7ae71f-4a92-455a-9de8-3c3d25d2c4c5",
              "propertyFileMapping": {
                "build.builder": "$.build.builder"
              },
              "reason": "migrate",
              "repo": "AdVid-ai/advid-wm-service",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-wm-service",
            "image": null
          }
        }
      },
      {
        "node": {
          "id": "e179d54c-7201-4b8b-93e9-1e77eea85dd9",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "40b50223-7a1f-42cd-a962-c8ce35027512",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
              "commitMessage": "stuff",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE"
                }
              },
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.builder": "$.build.builder"
              },
              "reason": "deploy",
              "repo": "AdVid-ai/advid-wm-service",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 3
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-wm-service",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "bc89cae0-5b23-4129-9083-4a5a7c0054c9",
  "meta": {
    "branch": "staging",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
    "commitMessage": "stuff",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE"
      }
    },
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.builder": "$.build.builder"
    },
    "reason": "deploy",
    "repo": "AdVid-ai/advid-wm-service",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
{
  "canRedeploy": true,
  "id": "1fcf9d3a-85aa-44c4-87c0-146a4b6efdb5",
  "meta": {
    "branch": "demo",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "b40b6b6367398a253007d6d9c6d9dfa506b0d46f",
    "commitMessage": "Merge pull request #6 from AdVid-ai/staging\n\nremove limit on creative id filtering",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE"
      }
    },
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "isPublicRepoDeploy": false,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "patchId": "bd7ae71f-4a92-455a-9de8-3c3d25d2c4c5",
    "propertyFileMapping": {
      "build.builder": "$.build.builder"
    },
    "reason": "migrate",
    "repo": "AdVid-ai/advid-wm-service",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
{
  "canRedeploy": true,
  "id": "40b50223-7a1f-42cd-a962-c8ce35027512",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
    "commitMessage": "stuff",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE"
      }
    },
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.builder": "$.build.builder"
    },
    "reason": "deploy",
    "repo": "AdVid-ai/advid-wm-service",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 3
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service advid-wm-service

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "advid-wm-service" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
