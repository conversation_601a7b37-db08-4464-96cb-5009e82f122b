{"id": "02dc9b92-589f-4736-acd1-78aa5fc2978a", "name": "Worker-fS_a", "serviceInstances": {"edges": [{"node": {"id": "18cc439d-c515-4a3b-b928-fa3910df5672", "serviceId": "02dc9b92-589f-4736-acd1-78aa5fc2978a", "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c", "latestDeployment": {"canRedeploy": true, "id": "3450a75f-4be8-4360-a7d5-a85083eca05a", "meta": {"image": "n8nio/n8n", "logsV2": true, "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d", "queuedReason": "Waiting for dependencies to deploy", "reason": "deploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "n8n worker"}}, "volumeMounts": []}}, "source": {"repo": null, "image": "n8nio/n8n"}}}]}}