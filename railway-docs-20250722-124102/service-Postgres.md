# Service: Postgres

**Generated:** Tue Jul 22 12:41:17 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Postgres
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "DATABASE_PRIVATE_URL": "postgres://railway:IeZrAr-MN*2e67a0~pc!<EMAIL>:5432/railway",
  "DATABASE_URL": "postgres://railway:IeZrAr-MN*2e67a0~pc!Lb3ADW5sS4cA@:/railway",
  "PGDATA": "/var/lib/postgresql/data/pgdata",
  "PGHOST": "",
  "PGHOST_PRIVATE": "postgres.railway.internal",
  "PGPORT": "",
  "PGPORT_PRIVATE": "5432",
  "POSTGRES_DB": "railway",
  "POSTGRES_PASSWORD": "IeZrAr-MN*2e67a0~pc!Lb3ADW5sS4cA",
  "POSTGRES_USER": "railway",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "postgres.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "Postgres",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_VOLUME_ID": "************************************",
  "RAILWAY_VOLUME_MOUNT_PATH": "/var/lib/postgresql/data",
  "RAILWAY_VOLUME_NAME": "work-volume",
  "SSL_CERT_DAYS": "820"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| DATABASE_PRIVATE_URL | postgres://railway:IeZrAr-MN*2e67a0~pc!<EMAIL>:5432/railway |
| DATABASE_URL | postgres://railway:IeZrAr-MN*2e67a0~pc!Lb3ADW5sS4cA@:/railway |
| PGDATA | /var/lib/postgresql/data/pgdata |
| PGHOST |  |
| PGHOST_PRIVATE | postgres.railway.internal |
| PGPORT |  |
| PGPORT_PRIVATE | 5432 |
| POSTGRES_DB | railway |
| POSTGRES_PASSWORD | IeZrAr-MN*2e67a0~pc!Lb3ADW5sS4cA |
| POSTGRES_USER | railway |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | postgres.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | Postgres |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_VOLUME_ID | ************************************ |
| RAILWAY_VOLUME_MOUNT_PATH | /var/lib/postgresql/data |
| RAILWAY_VOLUME_NAME | work-volume |
| SSL_CERT_DAYS | 820 |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 1bb37637-389e-473b-9f19-68b9cd81fd1c | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "Postgres",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "1bb37637-389e-473b-9f19-68b9cd81fd1c",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "d8ecc9d1-2b78-4313-8842-48a8c2f18dd3",
            "meta": {
              "deprecatedRegions": [
                {
                  "region": "us-west1",
                  "replacementRegion": "us-west2"
                }
              ],
              "ignoreWatchPatterns": true,
              "image": "ghcr.io/railwayapp-templates/postgres-ssl",
              "logsV2": true,
              "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
              "reason": "redeploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west1": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""
                }
              },
              "volumeMounts": [
                "/var/lib/postgresql/data"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "ghcr.io/railwayapp-templates/postgres-ssl"
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "d8ecc9d1-2b78-4313-8842-48a8c2f18dd3",
  "meta": {
    "deprecatedRegions": [
      {
        "region": "us-west1",
        "replacementRegion": "us-west2"
      }
    ],
    "ignoreWatchPatterns": true,
    "image": "ghcr.io/railwayapp-templates/postgres-ssl",
    "logsV2": true,
    "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
    "reason": "redeploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west1": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""
      }
    },
    "volumeMounts": [
      "/var/lib/postgresql/data"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Postgres

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "Postgres" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
