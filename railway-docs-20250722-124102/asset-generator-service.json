{"id": "c574bc61-5697-460d-b21a-7d3321d34895", "name": "asset-generator", "serviceInstances": {"edges": [{"node": {"id": "30d141c1-e085-40c5-87a4-df9c2dd005e8", "serviceId": "c574bc61-5697-460d-b21a-7d3321d34895", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "cc010d3f-24bf-4daf-8993-8809a1abc1bb", "meta": {"branch": "main", "buildOnly": false, "commitAuthor": "<PERSON><PERSON><PERSON><PERSON>", "commitHash": "8f1f18536fea90c1f7ac71cc524f272f63bc17fb", "commitMessage": "Added more error handling for pusher events", "configFile": "railway.toml", "fileServiceManifest": {"build": {"builder": "DOCKERFILE", "dockerfilePath": "Dockerfile"}, "deploy": {"healthcheckPath": "/api/health", "healthcheckTimeout": 100, "startCommand": "npm start"}}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {"build.builder": "$.build.builder", "build.dockerfilePath": "$.build.dockerfilePath", "deploy.healthcheckPath": "$.deploy.healthcheckPath", "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout", "deploy.startCommand": "$.deploy.startCommand"}, "reason": "deploy", "repo": "AdVid-ai/asset-generator", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "nixpacksConfigPath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": "/api/health", "healthcheckTimeout": 100, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 3}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "npm start"}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/asset-generator", "image": null}}}]}}