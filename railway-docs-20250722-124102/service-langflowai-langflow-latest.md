# Service: langflowai/langflow:latest

**Generated:** Tue Jul 22 12:41:33 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** langflowai/langflow:latest
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "LANGFLOW_DATABASE_URL": "postgresql://postgres:<EMAIL>:23600/railway",
  "LANGFLOW_LOG_LEVEL": "debug",
  "LANGFLOW_PORT": "7860",
  "LANGFLOW_SECRET_KEY": "tDSBO1LGV9xgaYS6u5Qlm82h6v5ovIND",
  "PORT": "7860",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "langflowailangflowlatest.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "langflowai/langflow:latest",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "langflowailangflowlatest-production-01c9.up.railway.app"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| LANGFLOW_DATABASE_URL | postgresql://postgres:<EMAIL>:23600/railway |
| LANGFLOW_LOG_LEVEL | debug |
| LANGFLOW_PORT | 7860 |
| LANGFLOW_SECRET_KEY | tDSBO1LGV9xgaYS6u5Qlm82h6v5ovIND |
| PORT | 7860 |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | langflowailangflowlatest.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | langflowai/langflow:latest |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | langflowailangflowlatest-production-01c9.up.railway.app |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| cd326eeb-1fa5-4f53-b7e1-7eb96f59ea29 | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "langflowai/langflow:latest",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "cd326eeb-1fa5-4f53-b7e1-7eb96f59ea29",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "6462d13d-5a55-4b5e-8ae4-c7c4307a4bff",
            "meta": {
              "ignoreWatchPatterns": true,
              "image": "langflowai/langflow:latest",
              "logsV2": true,
              "patchId": "bc70da66-ac5a-42ce-b10a-a928e53d528b",
              "reason": "migrate",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "python -m langflow run --host 0.0.0.0"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": null,
            "image": "langflowai/langflow:latest"
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "6462d13d-5a55-4b5e-8ae4-c7c4307a4bff",
  "meta": {
    "ignoreWatchPatterns": true,
    "image": "langflowai/langflow:latest",
    "logsV2": true,
    "patchId": "bc70da66-ac5a-42ce-b10a-a928e53d528b",
    "reason": "migrate",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "python -m langflow run --host 0.0.0.0"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service langflowai/langflow:latest

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "langflowai/langflow:latest" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
