# Service: adfury-email-service

**Generated:** Tue Jul 22 12:41:44 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** adfury-email-service
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "EMAIL_RATE_LIMIT": "100",
  "LOG_LEVEL": "debug",
  "NODE_ENV": "production",
  "QUEUE_CONCURRENCY": "5",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "adfury-email-service.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_PUBLIC_DOMAIN": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "adfury-email-service",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_STATIC_URL": "adfury-email-service-production.up.railway.app",
  "REDIS_URL": "redis://default:<EMAIL>:6379",
  "RESEND_API_KEY": "re_XuBL4bEf_LqwVJTzVHh4k8cLRLeaXW34o"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| EMAIL_RATE_LIMIT | 100 |
| LOG_LEVEL | debug |
| NODE_ENV | production |
| QUEUE_CONCURRENCY | 5 |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | adfury-email-service.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_PUBLIC_DOMAIN | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | adfury-email-service |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_STATIC_URL | adfury-email-service-production.up.railway.app |
| REDIS_URL | redis://default:<EMAIL>:6379 |
| RESEND_API_KEY | re_XuBL4bEf_LqwVJTzVHh4k8cLRLeaXW34o |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| f1bdbead-7d00-40b8-b51e-6f49dcd80a82 | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "adfury-email-service",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "f1bdbead-7d00-40b8-b51e-6f49dcd80a82",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "ba67300a-99d2-4158-a893-5d5ecc00fb67",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "da1661e45f95bc46d7f9c33c56d3245bfdb0044c",
              "commitMessage": "Merge pull request #4 from AdVid-ai/staging\n\nfix: resolve Gmail spam warnings and improve email deliverability",
              "configFile": "railway.json",
              "fileServiceManifest": {
                "build": {
                  "buildCommand": "npm run build",
                  "builder": "NIXPACKS"
                },
                "deploy": {
                  "healthcheckPath": "/ready",
                  "healthcheckTimeout": 60,
                  "numReplicas": 1,
                  "restartPolicyMaxRetries": 3,
                  "restartPolicyType": "ON_FAILURE",
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.buildCommand": "$.build.buildCommand",
                "build.builder": "$.build.builder",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.numReplicas": "$.deploy.numReplicas",
                "deploy.restartPolicyMaxRetries": "$.deploy.restartPolicyMaxRetries",
                "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "deploy",
              "repo": "AdVid-ai/adfury-email-service",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": "npm run build",
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": "/ready",
                  "healthcheckTimeout": 60,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 3,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/adfury-email-service",
            "image": null
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "ba67300a-99d2-4158-a893-5d5ecc00fb67",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "da1661e45f95bc46d7f9c33c56d3245bfdb0044c",
    "commitMessage": "Merge pull request #4 from AdVid-ai/staging\n\nfix: resolve Gmail spam warnings and improve email deliverability",
    "configFile": "railway.json",
    "fileServiceManifest": {
      "build": {
        "buildCommand": "npm run build",
        "builder": "NIXPACKS"
      },
      "deploy": {
        "healthcheckPath": "/ready",
        "healthcheckTimeout": 60,
        "numReplicas": 1,
        "restartPolicyMaxRetries": 3,
        "restartPolicyType": "ON_FAILURE",
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.buildCommand": "$.build.buildCommand",
      "build.builder": "$.build.builder",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.numReplicas": "$.deploy.numReplicas",
      "deploy.restartPolicyMaxRetries": "$.deploy.restartPolicyMaxRetries",
      "deploy.restartPolicyType": "$.deploy.restartPolicyType",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "deploy",
    "repo": "AdVid-ai/adfury-email-service",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": "npm run build",
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": "/ready",
        "healthcheckTimeout": 60,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 3,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service adfury-email-service

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "adfury-email-service" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
