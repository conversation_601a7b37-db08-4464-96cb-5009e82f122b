# Service: Redis-M2_I

**Generated:** Tue Jul 22 12:41:15 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Redis-M2_I
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "Redis-M2_I",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | Redis-M2_I |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 58ec488f-618f-4668-af33-1d93cb50c4ce | Unknown | 89aed380-ce64-4a52-b022-20e17d3a5562 |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "Redis-M2_I",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "58ec488f-618f-4668-af33-1d93cb50c4ce",
          "serviceId": "************************************",
          "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "fe49e186-3b84-44e7-a9f3-38bbfeb9a6fe",
            "meta": {
              "image": "bitnami/redis:7.2.5",
              "logsV2": true,
              "patchId": "1bcd540a-7927-46f8-9b30-49e1db3e8f67",
              "reason": "deploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": "/bitnami",
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/bitnami"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "bitnami/redis:7.2.5"
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "fe49e186-3b84-44e7-a9f3-38bbfeb9a6fe",
  "meta": {
    "image": "bitnami/redis:7.2.5",
    "logsV2": true,
    "patchId": "1bcd540a-7927-46f8-9b30-49e1db3e8f67",
    "reason": "deploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": "/bitnami",
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/bitnami"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Redis-M2_I

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "Redis-M2_I" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
