# Service: Postgres-C5Ge

**Generated:** Tue Jul 22 12:41:20 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Postgres-C5Ge
**Status:** Unknown
**Service ID:** ************************************
**Created:** Unknown
**Updated:** Unknown

## Environment Variables

**Retrieved programmatically:**

```json
{
  "DATABASE_PRIVATE_URL": "postgresql://postgres:<EMAIL>:5432/railway",
  "DATABASE_URL": "postgresql://postgres:<EMAIL>:23600/railway",
  "PGDATA": "/var/lib/postgresql/data/pgdata",
  "PGDATABASE": "railway",
  "PGHOST": "monorail.proxy.rlwy.net",
  "PGPASSWORD": "CtQdJxkoatXJINZQNOywAUwWckWjtbED",
  "PGPORT": "23600",
  "PGPRIVATEHOST": "postgres-c5ge.railway.internal",
  "PGUSER": "postgres",
  "POSTGRES_DB": "railway",
  "POSTGRES_PASSWORD": "CtQdJxkoatXJINZQNOywAUwWckWjtbED",
  "POSTGRES_USER": "postgres",
  "RAILWAY_ENVIRONMENT": "production",
  "RAILWAY_ENVIRONMENT_ID": "************************************",
  "RAILWAY_ENVIRONMENT_NAME": "production",
  "RAILWAY_PRIVATE_DOMAIN": "postgres-c5ge.railway.internal",
  "RAILWAY_PROJECT_ID": "************************************",
  "RAILWAY_PROJECT_NAME": "advid",
  "RAILWAY_SERVICE_ADFURY_APP_URL": "app.adfury.ai",
  "RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL": "adfury-email-service-production.up.railway.app",
  "RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL": "adfury-payment-gateway-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL": "sandbox.advid.ai",
  "RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL": "advid-marketing-page-production.up.railway.app",
  "RAILWAY_SERVICE_ADVID_SERVER_URL": "prod-server.adfury.ai",
  "RAILWAY_SERVICE_ADVID_WM_SERVICE_URL": "advid-wm-service-production.up.railway.app",
  "RAILWAY_SERVICE_ANALYTICS_VIEW_URL": "metrics.adfury.ai",
  "RAILWAY_SERVICE_ASSET_GENERATOR_URL": "asset-generator-production.up.railway.app",
  "RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL": "flowiseai-railway-production-1f29.up.railway.app",
  "RAILWAY_SERVICE_ID": "************************************",
  "RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL": "langflowailangflowlatest-production-01c9.up.railway.app",
  "RAILWAY_SERVICE_NAME": "Postgres-C5Ge",
  "RAILWAY_SERVICE_PDF_SCRAPER_URL": "pdf-scraper-production.up.railway.app",
  "RAILWAY_SERVICE_PRIMARY_URL": "primary-production-35c9.up.railway.app",
  "RAILWAY_SERVICE_RETAIL_MGMT_URL": "retail-mgmt-production.up.railway.app",
  "RAILWAY_SERVICE_SOKETI_URL": "soketi-production-7b57.up.railway.app",
  "RAILWAY_TCP_APPLICATION_PORT": "5432",
  "RAILWAY_TCP_PROXY_DOMAIN": "monorail.proxy.rlwy.net",
  "RAILWAY_TCP_PROXY_PORT": "23600",
  "RAILWAY_VOLUME_ID": "************************************",
  "RAILWAY_VOLUME_MOUNT_PATH": "/var/lib/postgresql/data",
  "RAILWAY_VOLUME_NAME": "tomatoes-volume",
  "SSL_CERT_DAYS": "820"
}
```

**Variables Table:**

| Variable | Value |
|----------|-------|
| DATABASE_PRIVATE_URL | postgresql://postgres:<EMAIL>:5432/railway |
| DATABASE_URL | postgresql://postgres:<EMAIL>:23600/railway |
| PGDATA | /var/lib/postgresql/data/pgdata |
| PGDATABASE | railway |
| PGHOST | monorail.proxy.rlwy.net |
| PGPASSWORD | CtQdJxkoatXJINZQNOywAUwWckWjtbED |
| PGPORT | 23600 |
| PGPRIVATEHOST | postgres-c5ge.railway.internal |
| PGUSER | postgres |
| POSTGRES_DB | railway |
| POSTGRES_PASSWORD | CtQdJxkoatXJINZQNOywAUwWckWjtbED |
| POSTGRES_USER | postgres |
| RAILWAY_ENVIRONMENT | production |
| RAILWAY_ENVIRONMENT_ID | ************************************ |
| RAILWAY_ENVIRONMENT_NAME | production |
| RAILWAY_PRIVATE_DOMAIN | postgres-c5ge.railway.internal |
| RAILWAY_PROJECT_ID | ************************************ |
| RAILWAY_PROJECT_NAME | advid |
| RAILWAY_SERVICE_ADFURY_APP_URL | app.adfury.ai |
| RAILWAY_SERVICE_ADFURY_EMAIL_SERVICE_URL | adfury-email-service-production.up.railway.app |
| RAILWAY_SERVICE_ADFURY_PAYMENT_GATEWAY_URL | adfury-payment-gateway-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_CLIENT_SANDBOX_URL | sandbox.advid.ai |
| RAILWAY_SERVICE_ADVID_MARKETING_PAGE_URL | advid-marketing-page-production.up.railway.app |
| RAILWAY_SERVICE_ADVID_SERVER_URL | prod-server.adfury.ai |
| RAILWAY_SERVICE_ADVID_WM_SERVICE_URL | advid-wm-service-production.up.railway.app |
| RAILWAY_SERVICE_ANALYTICS_VIEW_URL | metrics.adfury.ai |
| RAILWAY_SERVICE_ASSET_GENERATOR_URL | asset-generator-production.up.railway.app |
| RAILWAY_SERVICE_FLOWISE_AI_AGENTS_+_DATASTORE_+_FLOWS__URL | flowiseai-railway-production-1f29.up.railway.app |
| RAILWAY_SERVICE_ID | ************************************ |
| RAILWAY_SERVICE_LANGFLOWAI_LANGFLOW_LATEST_URL | langflowailangflowlatest-production-01c9.up.railway.app |
| RAILWAY_SERVICE_NAME | Postgres-C5Ge |
| RAILWAY_SERVICE_PDF_SCRAPER_URL | pdf-scraper-production.up.railway.app |
| RAILWAY_SERVICE_PRIMARY_URL | primary-production-35c9.up.railway.app |
| RAILWAY_SERVICE_RETAIL_MGMT_URL | retail-mgmt-production.up.railway.app |
| RAILWAY_SERVICE_SOKETI_URL | soketi-production-7b57.up.railway.app |
| RAILWAY_TCP_APPLICATION_PORT | 5432 |
| RAILWAY_TCP_PROXY_DOMAIN | monorail.proxy.rlwy.net |
| RAILWAY_TCP_PROXY_PORT | 23600 |
| RAILWAY_VOLUME_ID | ************************************ |
| RAILWAY_VOLUME_MOUNT_PATH | /var/lib/postgresql/data |
| RAILWAY_VOLUME_NAME | tomatoes-volume |
| SSL_CERT_DAYS | 820 |


## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 10aa8a7d-8b75-4759-9ecd-a45b2aef81ff | Unknown | ************************************ |


## Service Configuration

```json
{
  "id": "************************************",
  "name": "Postgres-C5Ge",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "10aa8a7d-8b75-4759-9ecd-a45b2aef81ff",
          "serviceId": "************************************",
          "environmentId": "************************************",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "25c3aca4-2967-4740-acd8-b62e512c9364",
            "meta": {
              "ignoreWatchPatterns": true,
              "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest",
              "logsV2": true,
              "patchId": "e4769826-016f-4d40-84d5-a63f51acd20d",
              "reason": "migrate",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/var/lib/postgresql/data"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest"
          }
        }
      }
    ]
  }
}
```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "25c3aca4-2967-4740-acd8-b62e512c9364",
  "meta": {
    "ignoreWatchPatterns": true,
    "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest",
    "logsV2": true,
    "patchId": "e4769826-016f-4d40-84d5-a63f51acd20d",
    "reason": "migrate",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/var/lib/postgresql/data"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Postgres-C5Ge

# After linking, view variables
railway variables

# Get variables programmatically
railway variables --service "Postgres-C5Ge" --json

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `************************************`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
