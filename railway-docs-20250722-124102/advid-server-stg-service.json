{"id": "7311f8ab-2bea-4c27-ad70-d2c4b7e16548", "name": "advid-server-stg", "serviceInstances": {"edges": [{"node": {"id": "11e73551-f8f2-4bc8-bd55-78cb40302fbd", "serviceId": "7311f8ab-2bea-4c27-ad70-d2c4b7e16548", "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42", "latestDeployment": {"canRedeploy": true, "id": "167dbaed-3826-4571-b6eb-ad5e2266a105", "meta": {"branch": "staging", "buildOnly": false, "commitAuthor": "alpendergrast", "commitHash": "7f6597cff162671d5661e35738e5c5cd04288465", "commitMessage": "Merge pull request #115 from AdVid-ai/development\n\nDevelopment->Staging", "fileServiceManifest": {}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {}, "reason": "deploy", "repo": "AdVid-ai/advid-server", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "nixpacksConfigPath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/advid-server", "image": null}}}, {"node": {"id": "11f15535-06da-431d-ad9d-e3bf186013e6", "serviceId": "7311f8ab-2bea-4c27-ad70-d2c4b7e16548", "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562", "latestDeployment": {"canRedeploy": true, "id": "fa1cbf0d-5a2d-4386-ae53-88ad305e5acb", "meta": {"branch": "staging", "buildOnly": false, "commitAuthor": "alpendergrast", "commitHash": "7f6597cff162671d5661e35738e5c5cd04288465", "commitMessage": "Merge pull request #115 from AdVid-ai/development\n\nDevelopment->Staging", "fileServiceManifest": {}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {}, "reason": "deploy", "repo": "AdVid-ai/advid-server", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "nixpacksConfigPath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/advid-server", "image": null}}}]}}