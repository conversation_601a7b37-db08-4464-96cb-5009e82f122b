{"id": "ad7cc5f3-c9e4-430f-8523-519cfaf6bd60", "name": "Postgres-56l-", "serviceInstances": {"edges": [{"node": {"id": "8c9a6a48-a005-470b-bf0c-c5d060ea5e29", "serviceId": "ad7cc5f3-c9e4-430f-8523-519cfaf6bd60", "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42", "latestDeployment": {"canRedeploy": true, "id": "7485ee67-7c70-4d9f-920b-d9d26c43c486", "meta": {"image": "ghcr.io/railwayapp-templates/postgres-ssl", "logsV2": true, "patchId": "f883665b-8007-49cc-b888-df564b6c20aa", "reason": "deploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": "/var/lib/postgresql/data", "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""}}, "volumeMounts": ["/var/lib/postgresql/data"]}}, "source": {"repo": null, "image": "ghcr.io/railwayapp-templates/postgres-ssl"}}}]}}