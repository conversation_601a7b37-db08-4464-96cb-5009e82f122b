<!DOCTYPE html>
<html lang="en" style="overflow-y: scroll;">
<head>
  <meta charset="UTF-8">
  <title>Newman Summary Report</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.13.0/css/all.min.css">
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.2/styles/default.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/v/bs4/dt-1.10.18/datatables.min.css"/>

<style>
code.renderMarkdown table, code.renderMarkdown th, code.renderMarkdown td {
    border: 1px solid black;
    width: max-content;
    padding: .75rem;
}

.theme-dark {
    --background-color: #222;
    --bg-card-deck: #444444;
    --text-color: #ccd2d8;
    --tab-border: solid 1px #444;
    --success: #3c9372;
    --failure: #c24a3f;
    --warning: #d28c23;
    --info: #4083b6;
    --badge-outline: #3c9372;
    --badge-bg: #cdd3db;
    --badge-text: #ccd2d9;
    --failure-row: #c24a3f;
    --warning-row: #d28c23;
    --card-bg: #444;
    --card-footer: #4f5758;
    --form-input: #ececb5;
    --hov-text: #d2dae5;
    --h4-text: #ccd1d9;
}

.theme-light {
    --tab-border: solid 1px #fff;
    --text-color: #000000;
    --success: #42a745;
    --failure: #dc3544;
    --warning: #f4c10b;
    --info: #49a1b8;
    --badge-outline: #040411;
    --badge-bg: #f8f9fa;
    --badge-text: #fff;
    --failure-row: #f5c6cb;
    --warning-row: #ffeeba;
    --card-bg: #f7f7f7;
    --hov-text: #fff;
    --h4-text: #ffffff;
}

body {
  padding-top:30px;
  background-color: var(--background-color)!important;
  color: var(--text-color);
}

#execution-data {
  padding: 10px;
  border: var(--tab-border);
  border-top: none;
}

.nav-tabs {
  padding-top: 10px;
  height: 105px;
  overflow-y: auto;
}

body.theme-dark .card-header {
    background-color: #444;
}

body.theme-light .card-header {
    background-color: #f7f7f7;
}

.card-footer {
    padding: .75rem 1.25rem;
    background-color: var(--card-footer);
}

.card-deck {
    background-color: var(--bg-card-deck)!important;
}
.form-control {
    background: var(--form-input);
}

.custom-tab {
  padding: 10px 15px;
  margin-right: 0px;
  height: 47px;
  width: 69px;
  text-align: center;
  border: var(--tab-border);
  border-bottom: none;
  cursor:pointer;
}

body.theme-dark .text-white {
    color: #ccd2d9!important;
}
h4 {
    color: var(--h4-text);
}

body.theme-dark h1 {
    color: #ccd2da;
}

body.theme-dark .bg-light>td {
    background: #4f5858!important;
}

body.theme-dark .hljs {
    background: #0a0a0ab0!important;
    color: #8d8787!important;
}

.bg-info {
    background-color: var(--info)!important;
}
.bg-success {
    background-color: var(--success)!important;
}

.alert-success {
    background-color: var(--success)!important;
}

.alert-warning {
    background-color: var(--warning)!important;
}

.alert-info {
    background-color: var(--info)!important;
}

.bg-warning {
    background-color: var(--warning)!important;
}

.badge-warning {
    color: var(--badge-text)!important;
    background-color: var(--warning)!important;
}

.table-warning>td {
    background-color: var(--warning-row);
}

.alert-danger {
    background-color: var(--failure)!important;
}

body.theme-dark .alert-dark {
    background-color: #636467!important;
}

body.theme-dark .text-dark {
    color: #d1dae4!important;
}

body.theme-dark .badge-light {
    color: #212529;
    background-color: #cdd3db;
}

body.theme-light .badge-light {
    color: #212529;
    background-color: #f8f9fa;
}
body.theme-light .bg-danger {
    background-color: var(--failure)!important;
}

body.theme-dark .bg-danger {
    background-color: var(--failure)!important;
}

.table-danger>td {
    background-color: var(--failure-row);
}

body.theme-dark .table .thead-light th {
    background-color: #4f5858!important;
    border-color: #dee2e6!important;
    color: #ccd2d8!important;
}

.itPassed {
  background: var(--success);
  color: white;
}
.itFailed {
  background: var(--failure);
  color: white;
}

.resultsInfoPass {
  color: var(--success);
  padding-top: 4px;
}

.resultsInfoFail {
  color: var(--failure);
  padding-top: 4px;
}

.badge-outline-success {
  color: var(--success);
  border: 1px solid var(--success);
  background-color: transparent;
}

.badge-outline {
  color: var(--badge-outline);
  border: 1px solid var(--badge-outline);
  background-color: transparent;
}

.btn-outline-success {
    color: var(--success)!important;
    border-color: var(--success)!important;
}

.backToTop:hover {
  background-color: var(--success);
  border-color: var(--success);
  color: var(--hov-text)!important;
}

.btn-outline-success:hover {
  background-color: var(--success);
  border-color: var(--success);
  color: var(--hov-text)!important;
}

.btn-outline-secondary {
  background-color: var(--success)!important;
  color: var(--hov-text)!important;
}

body.theme-dark .env-heading {
    color: #ccd2d9!important;
}

body, html {
  height:100%;
}

.card {
  overflow:hidden;
}

body.theme-dark .card-body {
    background-color: #444;
}

body.theme-light .card-body {
    background-color: #f7f7f7;
}

body.theme-dark .card-body .bg-danger {
    background-color: var(--failure)!important;
}

body.theme-light .card-body .bg-danger {
    background-color: var(--failure)!important;
}

.card-body .rotate {
  z-index: 8;
  float: right;
  height: 100%;
}

.card-body .rotate i {
  color: #14141426;
  position: absolute;
  left: 0;
  left: auto;
  right: -10px;
  bottom: 0;
  display: block;
  -webkit-transform: rotate(-44deg);
  -moz-transform: rotate(-44deg);
  -o-transform: rotate(-44deg);
  -ms-transform: rotate(-44deg);
  transform: rotate(-44deg);
}

.dyn-height {
  max-height:350px;
  overflow-y:auto;
}

.nav-pills .nav-link.active {
  background-color: transparent!important;
}

.backToTop {
  display: none;
  position: fixed;
  bottom: 10px;
  right: 20px;
  z-index: 99;
  font-size: 15px;
  outline: none;
  cursor: pointer;
  padding: 15px;
  border-radius: 4px;
}

.card-header .fa {
  transition: .3s transform ease-in-out;
}
.card-header .collapsed .fa {
  transform: rotate(90deg);
}

.single-line-tabs {
  padding-top: 10px;
  height: 60px;
}

::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* The switch - the box around the slider */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 20px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 0px;
  bottom: 4px;
  top: 0;
  bottom: 0;
  margin: auto 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  box-shadow: 0 0px 15px #2020203d;
  background: white;
  background-repeat: no-repeat;
  background-position: center;
}

input:checked + .slider {
  background-color: #4083b6;
}

input:focus + .slider {
  box-shadow: 0 0 1px #4083b6;
}

input:checked + .slider:before {
  -webkit-transform: translateX(24px);
  -ms-transform: translateX(24px);
  transform: translateX(24px);
  background: white;
  background-repeat: no-repeat;
  background-position: center;
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

table.dataTable td, table.dataTable tr {
    vertical-align: middle;
}

body.theme-dark code {
    color: #ccd2d8!important;
}

body.theme-light code {
    color: #000000!important;
}

.text-wrap {
    word-wrap: break-word; 
    min-width: 600px; 
    max-width: 600px; 
    white-space: normal;
}

</style>
</head>
<body class="theme-dark">
<script>
    function setTheme(themeName) {
        localStorage.setItem('theme', themeName);
        document.body.className = themeName;
    }

    function toggleTheme() {
        if (localStorage.getItem('theme') === 'theme-light') {
            setTheme('theme-dark');
        } else {
            setTheme('theme-light');
        }
    }
</script>
  <div class="container">
        <div class="container">
            <label>Light</label>
            <label id="switch" class="switch">
                <input type="checkbox" onchange="toggleTheme()" id="slider">
                <span class="slider round"></span>
            </label>
            <label>Dark</label>
        </div>
        <div class="card">
        <div class="card-header">
            <ul class="nav nav-pills mb-3 nav-justified" id="pills-tab" role="tablist">
            <li class="nav-item bg-info active" data-toggle="tooltip" title="Click to view the Summary">
                <a class="nav-link text-white" id="pills-summary-tab" data-toggle="pill" href="#pills-summary" role="tab" aria-controls="pills-summary" aria-selected="true">Summary</a>
            </li>
            <li class="nav-item bg-success" data-toggle="tooltip" title="Click to view the Requests">
                <a class="nav-link text-white" id="pills-requests-tab" data-toggle="pill" href="#pills-requests" role="tab" aria-controls="pills-requests" aria-selected="false">Total Requests <span class="badge badge-light">4</span></a>
            </li>
            <li class="nav-item bg-danger" data-toggle="tooltip" title="Click to view the Failed Tests">
                <a class="nav-link text-white" id="pills-failed-tab" data-toggle="pill" href="#pills-failed" role="tab" aria-controls="pills-failed" aria-selected="false">Failed Tests <span class="badge badge-light">0</span></a>
            </li>
            <li class="nav-item bg-warning" data-toggle="tooltip" title="Click to view the Skipped Tests">
                <a class="nav-link text-white" id="pills-skipped-tab" data-toggle="pill" href="#pills-skipped" role="tab" aria-controls="pills-skipped" aria-selected="false">Skipped Tests <span class="badge badge-light">0</span></a>
            </li>
            </ul>
        <div class="tab-content" id="pills-tabContent">
            <div class="tab-pane fade show active" id="pills-summary" role="tabcard" aria-labelledby="pills-summary-tab">
<div class="row">
  <div class="col-md-9 col-lg-12 main">
   <h1 class="display-2 text-center">Newman Run Dashboard</h1>
   <h5 class="text-center">Monday, 23 June 2025 09:19:01</h5>
   <div class="row">
    <div class="col-lg-3 col-md-6">
     <div class="card text-white card-success">
      <div class="card-body bg-success">
       <div class="rotate">
        <i class="fa fa-retweet fa-5x"></i>
       </div>
       <h6 class="text-uppercase">Total Iterations</h6>
       <h1 class="display-1">1</h1>
      </div>
     </div>
    </div>
    <div class="col-lg-3 col-md-6">
     <div class="card text-white card-danger">
      <div class="card-body bg-success">
       <div class="rotate">
        <i class="fa fa-list fa-4x"></i>
       </div>
       <h6 class="text-uppercase">Total Assertions</h6>
       <h1 class="display-1">5</h1>
      </div>
     </div>
    </div>
    <div class="col-lg-3 col-md-6">
     <div class="card text-white card-info">
      <div class="card-body bg-success">
       <div class="rotate">
        <i class="fa fa-plus-circle fa-5x"></i>
       </div>
       <h6 class="text-uppercase">Total Failed Tests</h6>
       <h1 class="display-1">0</h1>
      </div>
     </div>
    </div>
    <div class="col-lg-3 col-md-6">
     <div class="card text-white card-warning">
      <div class="card-body bg-success">
       <div class="rotate">
        <i class="fa fa-share fa-5x"></i>
       </div>
       <h6 class="text-uppercase">Total Skipped Tests</h6>
       <h1 class="display-1">0</h1>
      </div>
     </div>
    </div>
   </div>
   <hr>
    <div class="row">
    <div class="col">
        <div class="row">
        <div class="col-sm-12 mb-3">
            <div class="card border-info">
                <div class="card-body">
                <h5 class="card-title text-uppercase text-white text-center bg-info">File Information</h5>
                <span><i class="fas fa-file-code"></i></span><strong> Collection:</strong> AdFury Smoke API Testing<br>
                
                <span><i class="fas fa-file-code"></i></span><strong> Environment:</strong> local<br>
                </div>
            </div>
        </div>
        </div>
        <div class="row">
        <div class="col-sm-12 mb-3">
            <div class="card border-info">
                <div class="card-body">
                <h5 class="card-title text-uppercase text-white text-center bg-info">Timings and Data</h5>
                <span><i class="fas fa-stopwatch"></i></span><strong> Total run duration:</strong> 2.8s<br>
                <span><i class="fas fa-database"></i></span><strong> Total data received:</strong> 3.31KB<br>
                <span><i class="fas fa-stopwatch"></i></span><strong> Average response time:</strong> 680ms<br>
                </div>
            </div>
        </div>
        </div>
        <div class="row">
        <div class="col-sm-12 mb-3">
            <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="thead-inverse">
                    <tr class="text-center">
                        <th class="text-uppercase">Summary Item</th>
                        <th class="text-uppercase">Total</th>
                        <th class="text-uppercase">Failed</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Requests</td>
                        <td class="text-center">4</td>
                        <td class="text-center">0</td>
                    </tr>
                    <tr>
                        <td>Prerequest Scripts</td>
                        <td class="text-center">0</td>
                        <td class="text-center">0</td>
                    </tr>
                    <tr>
                        <td>Test Scripts</td>
                        <td class="text-center">3</td>
                        <td class="text-center">0</td>
                    </tr>
                    <tr class="">
                        <td>Assertions</td>
                        <td class="text-center">5</td>
                        <td class="text-center">0</td>
                    </tr>
                    <tr class="">
                        <td>Skipped Tests</td>
                        <td class="text-center">0</td>
                        <td class="text-center">-</td>
                    </tr>
                </tbody>
            </table>
            </div>
        </div>
        </div>
    <hr>
   </div>
   </div>
  </div>
 </div>
        </div>
            <div class="tab-pane fade" id="pills-failed" role="tabcard" aria-labelledby="pills-failed-tab">
                <button id="topOfFailuresScreen" class="btn btn-outline-success btn-sm backToTop" onclick="topFunction()">Go To Top</button>

                <div class="alert alert-success text-uppercase text-center">
                     <br><br><h1 class="text-center">There are no failed tests <span><i class="far fa-thumbs-up"></i></span></h1><br><br>
                </div>
            </div>

            <div class="tab-pane fade" id="pills-skipped" role="tabcard" aria-labelledby="pills-skipped-tab">
                <button id="topOfSkippedScreen" class="btn btn-outline-success btn-sm backToTop" onclick="topFunction()">Go To Top</button>

                <div class="alert alert-success text-uppercase text-center">
                    <br><br><h1 class="text-center">There are no skipped tests <span><i class="far fa-thumbs-up"></i></span></h1><br><br>
                </div>
            </div>
            <div class="tab-pane fade" id="pills-requests" role="tabcard" aria-labelledby="pills-requests-tab">

            <button id="topOfRequestsScreen" class="btn btn-outline-success btn-sm backToTop" onclick="topFunction()">Go To Top</button>

            <div class="btn-group float-right" role="group" aria-label="Button Group">
                <button id="openAll" class="btn btn-outline-success btn-sm float-right" style="text-align: center; width: 140px;">Expand Folders</button>
                <button id="openAllRequests" class="btn btn-outline-success btn-sm float-right" style="text-align: center; width: 140px;">Expand Requests</button>
            </div>

    <div class="text-uppercase" id="execution-menu">
        <h5>1 Iteration available to view</h5>
        
        <nav class="table-responsive">
        <ul class="nav single-line-tabs" id="iterationList">
        </ul>
        </nav>
    </div>
    <h6 class="text-uppercase text-muted" id="iterationSelected" style="padding-top: 10px;"></h6>
	<div class="tab-content" id="execution-data">
        <div class="alert alert-dark text-uppercase text-center iteration-0">
        <a data-toggle="collapse" href="#" data-target="#folder-collapse-89fc4928-a512-440d-9c32-cccbb8ec4495-iteration-0" aria-expanded="false" aria-controls="collapse" id="folder-89fc4928-a512-440d-9c32-cccbb8ec4495-iteration-0" class="collapsed text-dark z-block">
        <i class="fas fa-info-circle float-left resultsInfoPass" ></i>
            Smoke - 4 Requests in the folder<i class="float-lg-right fa fa-chevron-down" style="padding-top: 5px;"></i>
        </a>
        </div>
        <div id="folder-collapse-89fc4928-a512-440d-9c32-cccbb8ec4495-iteration-0" class="collapse" aria-labelledby="folder-89fc4928-a512-440d-9c32-cccbb8ec4495">
            <div id="folder-82306157-650f-4ebd-afa0-bd51f534c6ff" class="card-deck iteration-0">
            <div class="row iteration-0">
                <div class="col-sm-12 mb-3 iteration-0">
                <div class="card iteration-0">
                    <div class="card-header  bg-success iteration-0">
                        <a data-toggle="collapse" href="#" data-target="#collapse-82306157-650f-4ebd-afa0-bd51f534c6ff" aria-expanded="false" aria-controls="collapse" id="requests-82306157-650f-4ebd-afa0-bd51f534c6ff" class="collapsed text-white z-block">
                    Iteration: 1 - SignUp <i class="float-lg-right fa fa-chevron-down" style="padding-top: 5px;"></i>
                </a>
                    </div>
                    <div id="collapse-82306157-650f-4ebd-afa0-bd51f534c6ff" class="collapse" aria-labelledby="requests-82306157-650f-4ebd-afa0-bd51f534c6ff">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-12 mb-3">
                                <div class="card-deck">
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Request Information</h5>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Request Method:</strong> <span class="badge-outline-success badge badge-success"> POST</span><br>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Request URL:</strong> <a href="http://localhost:5005/auth/signup" target="_blank">http://localhost:5005/auth/signup</a><br>
                                    </div>
                                </div>
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Information</h5>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Response Code:</strong> <span class="float-right badge-outline badge badge-success"> 400 - Bad Request</span><br>
                                    <span><i class="fas fa-stopwatch"></i></span><strong> Mean time per request:</strong> <span class="float-right badge-outline badge badge-success"> 564ms</span><br>
                                    <span><i class="fas fa-database"></i></span><strong> Mean size per request:</strong> <span class="float-right badge-outline badge badge-success"> 62B</span><br>
                                    <hr>
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Test Pass Percentage</h5>
                                    <div>
                                        <div class="progress" style="height: 40px;">
                                            <div class="progress-bar bg-success" style="width: 100%" role="progressbar">
                                            <h5 class="text-uppercase text-white text-center" style="padding-top:5px;"><strong>No Tests for this request</strong></h5>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 mb-3">
                                <div class="card-deck">
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                        <h5 class="card-title text-uppercase text-white text-center bg-info">Request Headers</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                            <thead class="thead-light text-center"><tr><th>Header Name</th><th>Header Value</th></tr></thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="text-nowrap">Content-Type</td>
                                                        <td class="text-wrap">application/json</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">User-Agent</td>
                                                        <td class="text-wrap">PostmanRuntime/7.39.1</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Accept</td>
                                                        <td class="text-wrap">*/*</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Cache-Control</td>
                                                        <td class="text-wrap">no-cache</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Postman-Token</td>
                                                        <td class="text-wrap">8d7d829d-11fd-4263-8b22-fb186cbf0aff</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Host</td>
                                                        <td class="text-wrap">localhost:5005</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Accept-Encoding</td>
                                                        <td class="text-wrap">gzip, deflate, br</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Connection</td>
                                                        <td class="text-wrap">keep-alive</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Content-Length</td>
                                                        <td class="text-wrap">105</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Request Body</h5>
                                        <div class="dyn-height">
                                            <pre><code id="copyReqText-0" class="prettyPrint">{
            &quot;name&quot;: &quot;Aayushi&quot;,
            &quot;email&quot;: &quot;<EMAIL>&quot;,
            &quot;password&quot;: &quot;aayushiahlawat98@&quot;
        }</code></pre>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-outline-secondary btn-sm copyButton" type="button" data-clipboard-action="copy" data-clipboard-target="#copyReqText-0">Copy to Clipboard</button>
                                        </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Headers</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                        <thead class="thead-light text-center"><tr><th>Header Name</th><th>Header Value</th></tr></thead>
                                            <tbody>
                                                <tr>
                                                    <td class="text-nowrap">content-type</td>
                                                    <td class="text-wrap">application/json; charset&#x3D;utf-8</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">content-length</td>
                                                    <td class="text-wrap">62</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Date</td>
                                                    <td class="text-wrap">Mon, 23 Jun 2025 14:18:59 GMT</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Connection</td>
                                                    <td class="text-wrap">keep-alive</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Keep-Alive</td>
                                                    <td class="text-wrap">timeout&#x3D;72</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Body</h5>
                                        <div class="dyn-height">
                                                <pre><code id="copyText-254db245-e329-48c7-b002-76f0000eca4c" class="prettyPrint">{&quot;error&quot;:&quot;The user already exists.&quot;,&quot;details&quot;:&quot;Unknown error&quot;}</code></pre>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-outline-secondary btn-sm copyButton" type="button" data-clipboard-action="copy" data-clipboard-target="#copyText-254db245-e329-48c7-b002-76f0000eca4c">Copy to Clipboard</button>
                                        </div>
                                </div>
                            </div>
                            </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="card border-info">
                                <div class="card-body">
                                <h5 class="card-title text-uppercase text-white text-center bg-info">Test Information</h5>
                                    <h5 class="alert alert-success text-uppercase text-center">No Tests for this request</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
                </div>
            </div>
            </div>
            <div id="folder-d256b0ee-5967-4409-95e6-19e5130648ec" class="card-deck iteration-0">
            <div class="row iteration-0">
                <div class="col-sm-12 mb-3 iteration-0">
                <div class="card iteration-0">
                    <div class="card-header  bg-success iteration-0">
                        <a data-toggle="collapse" href="#" data-target="#collapse-d256b0ee-5967-4409-95e6-19e5130648ec" aria-expanded="false" aria-controls="collapse" id="requests-d256b0ee-5967-4409-95e6-19e5130648ec" class="collapsed text-white z-block">
                    Iteration: 1 - Login <i class="float-lg-right fa fa-chevron-down" style="padding-top: 5px;"></i>
                </a>
                    </div>
                    <div id="collapse-d256b0ee-5967-4409-95e6-19e5130648ec" class="collapse" aria-labelledby="requests-d256b0ee-5967-4409-95e6-19e5130648ec">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-12 mb-3">
                                <div class="card-deck">
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Request Information</h5>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Request Method:</strong> <span class="badge-outline-success badge badge-success"> POST</span><br>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Request URL:</strong> <a href="http://localhost:5005/auth/login" target="_blank">http://localhost:5005/auth/login</a><br>
                                    </div>
                                </div>
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Information</h5>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Response Code:</strong> <span class="float-right badge-outline badge badge-success"> 200 - OK</span><br>
                                    <span><i class="fas fa-stopwatch"></i></span><strong> Mean time per request:</strong> <span class="float-right badge-outline badge badge-success"> 1515ms</span><br>
                                    <span><i class="fas fa-database"></i></span><strong> Mean size per request:</strong> <span class="float-right badge-outline badge badge-success"> 1.18KB</span><br>
                                    <hr>
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Test Pass Percentage</h5>
                                    <div>
                                        <div class="progress" style="height: 40px;">
                                            <div class="progress-bar  bg-success" style="width: 100%" role="progressbar">
                                            <h5 style="padding-top:5px;"><strong>100 %</strong></h5>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 mb-3">
                                <div class="card-deck">
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                        <h5 class="card-title text-uppercase text-white text-center bg-info">Request Headers</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                            <thead class="thead-light text-center"><tr><th>Header Name</th><th>Header Value</th></tr></thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="text-nowrap"></td>
                                                        <td class="text-wrap"></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Content-Type</td>
                                                        <td class="text-wrap">application/json</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">User-Agent</td>
                                                        <td class="text-wrap">PostmanRuntime/7.39.1</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Accept</td>
                                                        <td class="text-wrap">*/*</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Cache-Control</td>
                                                        <td class="text-wrap">no-cache</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Postman-Token</td>
                                                        <td class="text-wrap">5279aaad-028f-4feb-baeb-d556f019a638</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Host</td>
                                                        <td class="text-wrap">localhost:5005</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Accept-Encoding</td>
                                                        <td class="text-wrap">gzip, deflate, br</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Connection</td>
                                                        <td class="text-wrap">keep-alive</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Content-Length</td>
                                                        <td class="text-wrap">82</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Request Body</h5>
                                        <div class="dyn-height">
                                            <pre><code id="copyReqText-1" class="prettyPrint">{
            &quot;email&quot;: &quot;<EMAIL>&quot;,
            &quot;password&quot;: &quot;aayushiahlawat98@&quot;
        }</code></pre>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-outline-secondary btn-sm copyButton" type="button" data-clipboard-action="copy" data-clipboard-target="#copyReqText-1">Copy to Clipboard</button>
                                        </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Headers</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                        <thead class="thead-light text-center"><tr><th>Header Name</th><th>Header Value</th></tr></thead>
                                            <tbody>
                                                <tr>
                                                    <td class="text-nowrap">content-type</td>
                                                    <td class="text-wrap">application/json; charset&#x3D;utf-8</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">set-cookie</td>
                                                    <td class="text-wrap">sessionId&#x3D;_FRebIrg7rZOIWCFF2khaVHLsltypj4T.TVMPK%2Be7dxxub%2BKU48iMcUoK89%2BILrSOdgIcM%2F9%2FfbI; Path&#x3D;/; Expires&#x3D;Tue, 24 Jun 2025 14:19:00 GMT; HttpOnly</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">content-length</td>
                                                    <td class="text-wrap">1213</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Date</td>
                                                    <td class="text-wrap">Mon, 23 Jun 2025 14:19:00 GMT</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Connection</td>
                                                    <td class="text-wrap">keep-alive</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Keep-Alive</td>
                                                    <td class="text-wrap">timeout&#x3D;72</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Body</h5>
                                        <div class="dyn-height">
                                                <pre><code id="copyText-21bacba5-836d-4914-9e32-89c1571f20f9" class="prettyPrint">{&quot;message&quot;:&quot;Login successful&quot;,&quot;user&quot;:{&quot;id&quot;:&quot;204f6251-2403-45b4-b36e-6d53301fc92e&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;name&quot;:&quot;Aashi&quot;,&quot;auth0Id&quot;:&quot;auth0|6852bf62c423161d1bc6fc96&quot;,&quot;accountId&quot;:null,&quot;picture&quot;:&quot;https://s.gravatar.com/avatar/c18c090b03a112b6247b3c2220daac70?s&#x3D;480&amp;r&#x3D;pg&amp;d&#x3D;https%3A%2F%2Fcdn.auth0.com%2Favatars%2Faa.png&quot;},&quot;access_token&quot;:&quot;eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjZNTS16Z0RnMmJlU3A0UGhLVHpQNCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hnodVEkb7AAjtw54SdNHo7kLzQlgXRkyvpVF3TWrJJc2o-8smgZagAUIiTxQ3hr3vLe2YZ4daZnxaoXiJKvyZ0pkxbpzsG803o1_UrPaxVtOsT3SrE-GHJUZAbVWQ6_tq8Twr2zPIJvf1PebhMIRgDigdu3m-YX93gXetCQWVA4vhq_L2Z82Xwp_oUQvwIeMtnrYgAJ9_x3hZQGnqlJBtlUEl8jItZW5MPXaIk-BoZzijPPyvtiv14ckehinWFRMK6ZAuKga79xeOy6r4SK_-5KlSIQMJWmWULWRtF6klyo-nbkiPr5Kiwh1o-ieWcEuMLKvPxDfGYcKspK7J0A5Rw&quot;,&quot;_debug_sessionid&quot;:&quot;unknown&quot;}</code></pre>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-outline-secondary btn-sm copyButton" type="button" data-clipboard-action="copy" data-clipboard-target="#copyText-21bacba5-836d-4914-9e32-89c1571f20f9">Copy to Clipboard</button>
                                        </div>
                                </div>
                            </div>
                            </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="card border-info">
                                <div class="card-body">
                                <h5 class="card-title text-uppercase text-white text-center bg-info">Test Information</h5>
                                    <div class="table-responsive text-nowrap">
                                        <table class="datatable table table-hover">
                                        <thead><tr class="text-center"><th>Name</th><th>Passed</th><th>Failed</th><th>Skipped</th></tr></thead>
                                            <tbody>
                                                <tr >
                                                    <td >Status is 200</td>
                                                    <td class="text-center bg-success">1</td>
                                                    <td class="text-center ">0</td>
                                                    <td class="text-center ">0</td>
                                                </tr>
                                                <tr >
                                                    <td >access_token present</td>
                                                    <td class="text-center bg-success">1</td>
                                                    <td class="text-center ">0</td>
                                                    <td class="text-center ">0</td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr class="bg-light">
                                                    <td><strong>Total</strong></td>
                                                    <td class="text-center">2</td>
                                                    <td class="text-center">0</td>
                                                    <td class="text-center">0</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    <div class="row d-none">
                                    <div class="col-sm-12 mb-3">
                                        <div class="card-deck">
                                        <div class="card border-danger" style="width: 50rem;">
                                            <div class="card-body">
                                                <h5 class="card-title text-uppercase text-white text-center bg-danger">Test Failure</h5>
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                    <thead><tr class="text-nowrap"><th>Test Name</th><th>Assertion Error</th></tr></thead>
                                                        <tbody>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
                </div>
            </div>
            </div>
            <div id="folder-f84fb2ff-afa3-47ff-aba3-e7c653db59e9" class="card-deck iteration-0">
            <div class="row iteration-0">
                <div class="col-sm-12 mb-3 iteration-0">
                <div class="card iteration-0">
                    <div class="card-header  bg-success iteration-0">
                        <a data-toggle="collapse" href="#" data-target="#collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9" aria-expanded="false" aria-controls="collapse" id="requests-f84fb2ff-afa3-47ff-aba3-e7c653db59e9" class="collapsed text-white z-block">
                    Iteration: 1 - Get All Accounts <i class="float-lg-right fa fa-chevron-down" style="padding-top: 5px;"></i>
                </a>
                    </div>
                    <div id="collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9" class="collapse" aria-labelledby="requests-f84fb2ff-afa3-47ff-aba3-e7c653db59e9">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-12 mb-3">
                                <div class="card-deck">
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Request Information</h5>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Request Method:</strong> <span class="badge-outline-success badge badge-success"> GET</span><br>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Request URL:</strong> <a href="http://localhost:5005/api/accounts" target="_blank">http://localhost:5005/api/accounts</a><br>
                                    </div>
                                </div>
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Information</h5>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Response Code:</strong> <span class="float-right badge-outline badge badge-success"> 200 - OK</span><br>
                                    <span><i class="fas fa-stopwatch"></i></span><strong> Mean time per request:</strong> <span class="float-right badge-outline badge badge-success"> 256ms</span><br>
                                    <span><i class="fas fa-database"></i></span><strong> Mean size per request:</strong> <span class="float-right badge-outline badge badge-success"> 1.98KB</span><br>
                                    <hr>
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Test Pass Percentage</h5>
                                    <div>
                                        <div class="progress" style="height: 40px;">
                                            <div class="progress-bar  bg-success" style="width: 100%" role="progressbar">
                                            <h5 style="padding-top:5px;"><strong>100 %</strong></h5>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 mb-3">
                                <div class="card-deck">
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                        <h5 class="card-title text-uppercase text-white text-center bg-info">Request Headers</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                            <thead class="thead-light text-center"><tr><th>Header Name</th><th>Header Value</th></tr></thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="text-nowrap">Authorization</td>
                                                        <td class="text-wrap">Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjZNTS16Z0RnMmJlU3A0UGhLVHpQNCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hnodVEkb7AAjtw54SdNHo7kLzQlgXRkyvpVF3TWrJJc2o-8smgZagAUIiTxQ3hr3vLe2YZ4daZnxaoXiJKvyZ0pkxbpzsG803o1_UrPaxVtOsT3SrE-GHJUZAbVWQ6_tq8Twr2zPIJvf1PebhMIRgDigdu3m-YX93gXetCQWVA4vhq_L2Z82Xwp_oUQvwIeMtnrYgAJ9_x3hZQGnqlJBtlUEl8jItZW5MPXaIk-BoZzijPPyvtiv14ckehinWFRMK6ZAuKga79xeOy6r4SK_-5KlSIQMJWmWULWRtF6klyo-nbkiPr5Kiwh1o-ieWcEuMLKvPxDfGYcKspK7J0A5Rw</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">User-Agent</td>
                                                        <td class="text-wrap">PostmanRuntime/7.39.1</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Accept</td>
                                                        <td class="text-wrap">*/*</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Cache-Control</td>
                                                        <td class="text-wrap">no-cache</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Postman-Token</td>
                                                        <td class="text-wrap">5fca8abf-5f09-4ed2-9e22-152de91a1699</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Host</td>
                                                        <td class="text-wrap">localhost:5005</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Accept-Encoding</td>
                                                        <td class="text-wrap">gzip, deflate, br</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Connection</td>
                                                        <td class="text-wrap">keep-alive</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Cookie</td>
                                                        <td class="text-wrap">sessionId&#x3D;_FRebIrg7rZOIWCFF2khaVHLsltypj4T.TVMPK%2Be7dxxub%2BKU48iMcUoK89%2BILrSOdgIcM%2F9%2FfbI</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Headers</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                        <thead class="thead-light text-center"><tr><th>Header Name</th><th>Header Value</th></tr></thead>
                                            <tbody>
                                                <tr>
                                                    <td class="text-nowrap">content-type</td>
                                                    <td class="text-wrap">application/json; charset&#x3D;utf-8</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">set-cookie</td>
                                                    <td class="text-wrap">sessionId&#x3D;_FRebIrg7rZOIWCFF2khaVHLsltypj4T.TVMPK%2Be7dxxub%2BKU48iMcUoK89%2BILrSOdgIcM%2F9%2FfbI; Path&#x3D;/; Expires&#x3D;Tue, 24 Jun 2025 14:19:01 GMT; HttpOnly</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">content-length</td>
                                                    <td class="text-wrap">2031</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Date</td>
                                                    <td class="text-wrap">Mon, 23 Jun 2025 14:19:01 GMT</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Connection</td>
                                                    <td class="text-wrap">keep-alive</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Keep-Alive</td>
                                                    <td class="text-wrap">timeout&#x3D;72</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Body</h5>
                                        <div class="dyn-height">
                                                <pre><code id="copyText-977804be-8e94-41e5-be8b-5ada69d3822f" class="prettyPrint">[{&quot;id&quot;:&quot;44f5b50a-0cb7-4ce8-b991-f21388eb4525&quot;,&quot;name&quot;:&quot;jan 24 new Account&quot;,&quot;type&quot;:&quot;3p&quot;,&quot;isTrial&quot;:false,&quot;seats&quot;:1,&quot;ownerId&quot;:&quot;4fbd1aa7-aefc-4198-954b-728594a6847d&quot;,&quot;status&quot;:&quot;active&quot;,&quot;stripeCustomerId&quot;:null,&quot;validSubscription&quot;:false,&quot;hasPaymentMethod&quot;:false,&quot;createdAt&quot;:&quot;2025-06-10T19:47:13.219Z&quot;,&quot;updatedAt&quot;:&quot;2025-06-10T19:47:13.219Z&quot;},{&quot;id&quot;:&quot;30fa9b84-4418-4449-a94b-d53a4762a084&quot;,&quot;name&quot;:&quot;june 10 New Account&quot;,&quot;type&quot;:&quot;3p&quot;,&quot;isTrial&quot;:false,&quot;seats&quot;:1,&quot;ownerId&quot;:&quot;c3fe62f2-1805-45bc-9717-33f5ec3094a4&quot;,&quot;status&quot;:&quot;active&quot;,&quot;stripeCustomerId&quot;:null,&quot;validSubscription&quot;:false,&quot;hasPaymentMethod&quot;:false,&quot;createdAt&quot;:&quot;2025-06-10T19:52:03.905Z&quot;,&quot;updatedAt&quot;:&quot;2025-06-10T19:52:03.905Z&quot;},{&quot;id&quot;:&quot;b599690f-d1fb-4ceb-8a29-144ee967a3bb&quot;,&quot;name&quot;:&quot;Digital Marketing Agency&quot;,&quot;type&quot;:&quot;agency&quot;,&quot;isTrial&quot;:false,&quot;seats&quot;:10,&quot;ownerId&quot;:&quot;4e2a6a52-be0f-4956-879b-ee4139a09ba1&quot;,&quot;status&quot;:&quot;active&quot;,&quot;stripeCustomerId&quot;:null,&quot;validSubscription&quot;:true,&quot;hasPaymentMethod&quot;:true,&quot;createdAt&quot;:&quot;2025-06-16T15:56:55.435Z&quot;,&quot;updatedAt&quot;:&quot;2025-06-16T15:56:55.435Z&quot;},{&quot;id&quot;:&quot;9de5f3a2-ab66-4ad1-9e84-4d33f438fe0d&quot;,&quot;name&quot;:&quot;E-commerce Brand House&quot;,&quot;type&quot;:&quot;1p&quot;,&quot;isTrial&quot;:true,&quot;seats&quot;:5,&quot;ownerId&quot;:&quot;40365f19-a3d2-44c6-8334-1f92fff18150&quot;,&quot;status&quot;:&quot;active&quot;,&quot;stripeCustomerId&quot;:null,&quot;validSubscription&quot;:false,&quot;hasPaymentMethod&quot;:false,&quot;createdAt&quot;:&quot;2025-06-16T15:56:55.435Z&quot;,&quot;updatedAt&quot;:&quot;2025-06-16T15:56:55.435Z&quot;},{&quot;id&quot;:&quot;173fbb4e-1f18-4907-bb63-a3ebc90a15bf&quot;,&quot;name&quot;:&quot;RetailCorp Walmart Division&quot;,&quot;type&quot;:&quot;1p&quot;,&quot;isTrial&quot;:false,&quot;seats&quot;:25,&quot;ownerId&quot;:&quot;d917ecfe-3c2b-432f-89f9-06c61965b939&quot;,&quot;status&quot;:&quot;active&quot;,&quot;stripeCustomerId&quot;:null,&quot;validSubscription&quot;:true,&quot;hasPaymentMethod&quot;:true,&quot;createdAt&quot;:&quot;2025-06-18T19:35:15.390Z&quot;,&quot;updatedAt&quot;:&quot;2025-06-18T19:35:15.390Z&quot;},{&quot;id&quot;:&quot;404e5da7-f383-4fba-915f-ef4a9656571c&quot;,&quot;name&quot;:&quot;MultiChannel Marketing Solutions&quot;,&quot;type&quot;:&quot;agency&quot;,&quot;isTrial&quot;:false,&quot;seats&quot;:15,&quot;ownerId&quot;:&quot;81e8b194-0ef0-4f65-8deb-acfe59ba1058&quot;,&quot;status&quot;:&quot;active&quot;,&quot;stripeCustomerId&quot;:null,&quot;validSubscription&quot;:true,&quot;hasPaymentMethod&quot;:true,&quot;createdAt&quot;:&quot;2025-06-18T19:35:15.390Z&quot;,&quot;updatedAt&quot;:&quot;2025-06-18T19:35:15.390Z&quot;}]</code></pre>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-outline-secondary btn-sm copyButton" type="button" data-clipboard-action="copy" data-clipboard-target="#copyText-977804be-8e94-41e5-be8b-5ada69d3822f">Copy to Clipboard</button>
                                        </div>
                                </div>
                            </div>
                            </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="card border-info">
                                <div class="card-body">
                                <h5 class="card-title text-uppercase text-white text-center bg-info">Test Information</h5>
                                    <div class="table-responsive text-nowrap">
                                        <table class="datatable table table-hover">
                                        <thead><tr class="text-center"><th>Name</th><th>Passed</th><th>Failed</th><th>Skipped</th></tr></thead>
                                            <tbody>
                                                <tr >
                                                    <td >200 OK</td>
                                                    <td class="text-center bg-success">1</td>
                                                    <td class="text-center ">0</td>
                                                    <td class="text-center ">0</td>
                                                </tr>
                                                <tr >
                                                    <td >Returns an array</td>
                                                    <td class="text-center bg-success">1</td>
                                                    <td class="text-center ">0</td>
                                                    <td class="text-center ">0</td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr class="bg-light">
                                                    <td><strong>Total</strong></td>
                                                    <td class="text-center">2</td>
                                                    <td class="text-center">0</td>
                                                    <td class="text-center">0</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    <div class="row d-none">
                                    <div class="col-sm-12 mb-3">
                                        <div class="card-deck">
                                        <div class="card border-danger" style="width: 50rem;">
                                            <div class="card-body">
                                                <h5 class="card-title text-uppercase text-white text-center bg-danger">Test Failure</h5>
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                    <thead><tr class="text-nowrap"><th>Test Name</th><th>Assertion Error</th></tr></thead>
                                                        <tbody>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
                </div>
            </div>
            </div>
            <div id="folder-f426689b-4cc7-410c-b4b5-5fad714b7c07" class="card-deck iteration-0">
            <div class="row iteration-0">
                <div class="col-sm-12 mb-3 iteration-0">
                <div class="card iteration-0">
                    <div class="card-header  bg-success iteration-0">
                        <a data-toggle="collapse" href="#" data-target="#collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07" aria-expanded="false" aria-controls="collapse" id="requests-f426689b-4cc7-410c-b4b5-5fad714b7c07" class="collapsed text-white z-block">
                    Iteration: 1 - Get All Users for the Account <i class="float-lg-right fa fa-chevron-down" style="padding-top: 5px;"></i>
                </a>
                    </div>
                    <div id="collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07" class="collapse" aria-labelledby="requests-f426689b-4cc7-410c-b4b5-5fad714b7c07">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-12 mb-3">
                                <div class="card-deck">
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Request Information</h5>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Request Method:</strong> <span class="badge-outline-success badge badge-success"> GET</span><br>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Request URL:</strong> <a href="http://localhost:5005/api/accounts/44f5b50a-0cb7-4ce8-b991-f21388eb4525/members" target="_blank">http://localhost:5005/api/accounts/44f5b50a-0cb7-4ce8-b991-f21388eb4525/members</a><br>
                                    </div>
                                </div>
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Information</h5>
                                    <span><i class="fas fa-info-circle"></i></span><strong> Response Code:</strong> <span class="float-right badge-outline badge badge-success"> 403 - Forbidden</span><br>
                                    <span><i class="fas fa-stopwatch"></i></span><strong> Mean time per request:</strong> <span class="float-right badge-outline badge badge-success"> 385ms</span><br>
                                    <span><i class="fas fa-database"></i></span><strong> Mean size per request:</strong> <span class="float-right badge-outline badge badge-success"> 82B</span><br>
                                    <hr>
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Test Pass Percentage</h5>
                                    <div>
                                        <div class="progress" style="height: 40px;">
                                            <div class="progress-bar  bg-success" style="width: 100%" role="progressbar">
                                            <h5 style="padding-top:5px;"><strong>100 %</strong></h5>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 mb-3">
                                <div class="card-deck">
                                <div class="card border-info" style="width: 50rem;">
                                    <div class="card-body">
                                        <h5 class="card-title text-uppercase text-white text-center bg-info">Request Headers</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                            <thead class="thead-light text-center"><tr><th>Header Name</th><th>Header Value</th></tr></thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="text-nowrap">Authorization</td>
                                                        <td class="text-wrap">Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjZNTS16Z0RnMmJlU3A0UGhLVHpQNCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hnodVEkb7AAjtw54SdNHo7kLzQlgXRkyvpVF3TWrJJc2o-8smgZagAUIiTxQ3hr3vLe2YZ4daZnxaoXiJKvyZ0pkxbpzsG803o1_UrPaxVtOsT3SrE-GHJUZAbVWQ6_tq8Twr2zPIJvf1PebhMIRgDigdu3m-YX93gXetCQWVA4vhq_L2Z82Xwp_oUQvwIeMtnrYgAJ9_x3hZQGnqlJBtlUEl8jItZW5MPXaIk-BoZzijPPyvtiv14ckehinWFRMK6ZAuKga79xeOy6r4SK_-5KlSIQMJWmWULWRtF6klyo-nbkiPr5Kiwh1o-ieWcEuMLKvPxDfGYcKspK7J0A5Rw</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">User-Agent</td>
                                                        <td class="text-wrap">PostmanRuntime/7.39.1</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Accept</td>
                                                        <td class="text-wrap">*/*</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Cache-Control</td>
                                                        <td class="text-wrap">no-cache</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Postman-Token</td>
                                                        <td class="text-wrap">6abc4166-6487-4980-a6a9-b3689bf84ed0</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Host</td>
                                                        <td class="text-wrap">localhost:5005</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Accept-Encoding</td>
                                                        <td class="text-wrap">gzip, deflate, br</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Connection</td>
                                                        <td class="text-wrap">keep-alive</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-nowrap">Cookie</td>
                                                        <td class="text-wrap">sessionId&#x3D;_FRebIrg7rZOIWCFF2khaVHLsltypj4T.TVMPK%2Be7dxxub%2BKU48iMcUoK89%2BILrSOdgIcM%2F9%2FfbI</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Headers</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                        <thead class="thead-light text-center"><tr><th>Header Name</th><th>Header Value</th></tr></thead>
                                            <tbody>
                                                <tr>
                                                    <td class="text-nowrap">content-type</td>
                                                    <td class="text-wrap">application/json; charset&#x3D;utf-8</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">set-cookie</td>
                                                    <td class="text-wrap">sessionId&#x3D;_FRebIrg7rZOIWCFF2khaVHLsltypj4T.TVMPK%2Be7dxxub%2BKU48iMcUoK89%2BILrSOdgIcM%2F9%2FfbI; Path&#x3D;/; Expires&#x3D;Tue, 24 Jun 2025 14:19:01 GMT; HttpOnly</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">content-length</td>
                                                    <td class="text-wrap">82</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Date</td>
                                                    <td class="text-wrap">Mon, 23 Jun 2025 14:19:01 GMT</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Connection</td>
                                                    <td class="text-wrap">keep-alive</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Keep-Alive</td>
                                                    <td class="text-wrap">timeout&#x3D;72</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        </div>
                        <div class="row">
                        <div class="col-sm-12 mb-3">
                            <div class="card-deck">
                            <div class="card border-info" style="width: 50rem;">
                                <div class="card-body">
                                    <h5 class="card-title text-uppercase text-white text-center bg-info">Response Body</h5>
                                        <div class="dyn-height">
                                                <pre><code id="copyText-c8236a31-5f1f-4226-b17c-e96aed5b9215" class="prettyPrint">{&quot;error&quot;:&quot;Forbidden - You do not have permission to view members of this account&quot;}</code></pre>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-outline-secondary btn-sm copyButton" type="button" data-clipboard-action="copy" data-clipboard-target="#copyText-c8236a31-5f1f-4226-b17c-e96aed5b9215">Copy to Clipboard</button>
                                        </div>
                                </div>
                            </div>
                            </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="card border-info">
                                <div class="card-body">
                                <h5 class="card-title text-uppercase text-white text-center bg-info">Test Information</h5>
                                    <div class="table-responsive text-nowrap">
                                        <table class="datatable table table-hover">
                                        <thead><tr class="text-center"><th>Name</th><th>Passed</th><th>Failed</th><th>Skipped</th></tr></thead>
                                            <tbody>
                                                <tr >
                                                    <td >200 OK</td>
                                                    <td class="text-center bg-success">1</td>
                                                    <td class="text-center ">0</td>
                                                    <td class="text-center ">0</td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr class="bg-light">
                                                    <td><strong>Total</strong></td>
                                                    <td class="text-center">1</td>
                                                    <td class="text-center">0</td>
                                                    <td class="text-center">0</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    <div class="row d-none">
                                    <div class="col-sm-12 mb-3">
                                        <div class="card-deck">
                                        <div class="card border-danger" style="width: 50rem;">
                                            <div class="card-body">
                                                <h5 class="card-title text-uppercase text-white text-center bg-danger">Test Failure</h5>
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                    <thead><tr class="text-nowrap"><th>Test Name</th><th>Assertion Error</th></tr></thead>
                                                        <tbody>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
                </div>
            </div>
            </div>
        </div>
        </div>
    </div>
    </div>
    </div>
    </div>
    </div>


<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.2.1/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/v/bs4/dt-1.10.18/datatables.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.0/clipboard.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/remarkable/1.7.1/remarkable.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.2/highlight.min.js"></script>
<script>hljs.initHighlightingOnLoad();</script>

<!-- Set slider initial position depending on the localstorage state -->

<script>
(function () {
  var sliderChecked = true;
  if (localStorage.getItem('theme') === 'theme-light') {
    setTheme('theme-light');
    sliderChecked = false;
  } else {
    setTheme('theme-dark');
    sliderChecked = true;
  }
  $(document).ready( function () {
    document.getElementById('slider').checked = sliderChecked;
  });
})();
</script>

<!-- Data Table Configuration -->

<script>
$(document).ready( function () {
    $('.datatable').DataTable({
        "retrieve": true,
        "paging": false,
        "info": false,
        "fixedColumns":   {
            "heightMatch": 'none'
        }
    });
});
</script>

<!-- Tooltip Configuration -->

<script>
$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip({
        trigger : 'hover'
    })
})
</script>

<!-- Show/Hide The Folders -->

<script>
$('#openAll').on('click', function(e) {
let clickCount = $(this).data("clickCount") || 1
switch (clickCount){
    case 1:
            $('#folder-89fc4928-a512-440d-9c32-cccbb8ec4495-iteration-0').removeClass('collapsed')
            $('#folder-collapse-89fc4928-a512-440d-9c32-cccbb8ec4495-iteration-0').addClass('show')
        $('#openAll').html("Collapse Folders");
        break;
    case 2:
            $('#folder-89fc4928-a512-440d-9c32-cccbb8ec4495-iteration-0').addClass('collapsed')
            $('#folder-collapse-89fc4928-a512-440d-9c32-cccbb8ec4495-iteration-0').removeClass('show')
        $('#openAll').html("Expand Folders");
        break;
}
clickCount = clickCount > 1 ? 1 : ++clickCount;
$(this).data("clickCount", clickCount)
})
</script>

<!-- Show/Hide The Requests -->

<script>
$('#openAllRequests').on('click', function(e) {
let clickCount = $(this).data("clickCount") || 1
switch (clickCount){
    case 1:
            $('#requests-82306157-650f-4ebd-afa0-bd51f534c6ff').removeClass('collapsed')
            $('#collapse-82306157-650f-4ebd-afa0-bd51f534c6ff').removeClass('collapsed')
            $('#requests-82306157-650f-4ebd-afa0-bd51f534c6ff').addClass('show')
            $('#collapse-82306157-650f-4ebd-afa0-bd51f534c6ff').addClass('show')
            $('#requests-d256b0ee-5967-4409-95e6-19e5130648ec').removeClass('collapsed')
            $('#collapse-d256b0ee-5967-4409-95e6-19e5130648ec').removeClass('collapsed')
            $('#requests-d256b0ee-5967-4409-95e6-19e5130648ec').addClass('show')
            $('#collapse-d256b0ee-5967-4409-95e6-19e5130648ec').addClass('show')
            $('#requests-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').removeClass('collapsed')
            $('#collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').removeClass('collapsed')
            $('#requests-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').addClass('show')
            $('#collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').addClass('show')
            $('#requests-f426689b-4cc7-410c-b4b5-5fad714b7c07').removeClass('collapsed')
            $('#collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07').removeClass('collapsed')
            $('#requests-f426689b-4cc7-410c-b4b5-5fad714b7c07').addClass('show')
            $('#collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07').addClass('show')
        $('#openAllRequests').html("Collapse Requests");
        break;
    case 2:
            $('#requests-82306157-650f-4ebd-afa0-bd51f534c6ff').addClass('collapsed')
            $('#collapse-82306157-650f-4ebd-afa0-bd51f534c6ff').addClass('collapsed')
            $('#requests-82306157-650f-4ebd-afa0-bd51f534c6ff').removeClass('show')
            $('#collapse-82306157-650f-4ebd-afa0-bd51f534c6ff').removeClass('show')
            $('#requests-d256b0ee-5967-4409-95e6-19e5130648ec').addClass('collapsed')
            $('#collapse-d256b0ee-5967-4409-95e6-19e5130648ec').addClass('collapsed')
            $('#requests-d256b0ee-5967-4409-95e6-19e5130648ec').removeClass('show')
            $('#collapse-d256b0ee-5967-4409-95e6-19e5130648ec').removeClass('show')
            $('#requests-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').addClass('collapsed')
            $('#collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').addClass('collapsed')
            $('#requests-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').removeClass('show')
            $('#collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').removeClass('show')
            $('#requests-f426689b-4cc7-410c-b4b5-5fad714b7c07').addClass('collapsed')
            $('#collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07').addClass('collapsed')
            $('#requests-f426689b-4cc7-410c-b4b5-5fad714b7c07').removeClass('show')
            $('#collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07').removeClass('show')
        $('#openAllRequests').html("Expand Requests");
        break;
}
clickCount = clickCount > 1 ? 1 : ++clickCount;
$(this).data("clickCount", clickCount)
})
</script>

<!-- Show/Hide The Skipped Tests -->

<script>
$('#openAllSkipped').on('click', function(e) {
let clickCount = $(this).data("clickCount") || 1
switch (clickCount){
    case 1:
            $('#skipped-82306157-650f-4ebd-afa0-bd51f534c6ff').removeClass('collapsed')
            $('#skipped-collapse-82306157-650f-4ebd-afa0-bd51f534c6ff').removeClass('collapsed')
            $('#skipped-82306157-650f-4ebd-afa0-bd51f534c6ff').addClass('show')
            $('#skipped-collapse-82306157-650f-4ebd-afa0-bd51f534c6ff').addClass('show')
            $('#skipped-d256b0ee-5967-4409-95e6-19e5130648ec').removeClass('collapsed')
            $('#skipped-collapse-d256b0ee-5967-4409-95e6-19e5130648ec').removeClass('collapsed')
            $('#skipped-d256b0ee-5967-4409-95e6-19e5130648ec').addClass('show')
            $('#skipped-collapse-d256b0ee-5967-4409-95e6-19e5130648ec').addClass('show')
            $('#skipped-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').removeClass('collapsed')
            $('#skipped-collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').removeClass('collapsed')
            $('#skipped-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').addClass('show')
            $('#skipped-collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').addClass('show')
            $('#skipped-f426689b-4cc7-410c-b4b5-5fad714b7c07').removeClass('collapsed')
            $('#skipped-collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07').removeClass('collapsed')
            $('#skipped-f426689b-4cc7-410c-b4b5-5fad714b7c07').addClass('show')
            $('#skipped-collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07').addClass('show')
        $('#openAllSkipped').html("Collapse All Skipped Tests");
        break;
    case 2:
            $('#skipped-82306157-650f-4ebd-afa0-bd51f534c6ff').addClass('collapsed')
            $('#skipped-collapse-82306157-650f-4ebd-afa0-bd51f534c6ff').addClass('collapsed')
            $('#skipped-82306157-650f-4ebd-afa0-bd51f534c6ff').removeClass('show')
            $('#skipped-collapse-82306157-650f-4ebd-afa0-bd51f534c6ff').removeClass('show')
            $('#skipped-d256b0ee-5967-4409-95e6-19e5130648ec').addClass('collapsed')
            $('#skipped-collapse-d256b0ee-5967-4409-95e6-19e5130648ec').addClass('collapsed')
            $('#skipped-d256b0ee-5967-4409-95e6-19e5130648ec').removeClass('show')
            $('#skipped-collapse-d256b0ee-5967-4409-95e6-19e5130648ec').removeClass('show')
            $('#skipped-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').addClass('collapsed')
            $('#skipped-collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').addClass('collapsed')
            $('#skipped-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').removeClass('show')
            $('#skipped-collapse-f84fb2ff-afa3-47ff-aba3-e7c653db59e9').removeClass('show')
            $('#skipped-f426689b-4cc7-410c-b4b5-5fad714b7c07').addClass('collapsed')
            $('#skipped-collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07').addClass('collapsed')
            $('#skipped-f426689b-4cc7-410c-b4b5-5fad714b7c07').removeClass('show')
            $('#skipped-collapse-f426689b-4cc7-410c-b4b5-5fad714b7c07').removeClass('show')
        $('#openAllSkipped').html("Expand All Skipped Tests");
        break;
}
clickCount = clickCount > 1 ? 1 : ++clickCount;
$(this).data("clickCount", clickCount)
})
</script>

<!-- Show/Hide The Failures -->

<script>
$('#openAllFailed').on('click', function(e) {
let clickCount = $(this).data("clickCount") || 1
let failedItemContent
let failedItemHeading
switch (clickCount){
    case 1:
        $('#openAllFailed').html("Collapse All Failed Tests");
        break;
    case 2:
        $('#openAllFailed').html("Expand All Failed Tests");
        break;
}
clickCount = clickCount > 1 ? 1 : ++clickCount;
$(this).data("clickCount", clickCount)
})
</script>

<!-- Pretty Print the Response Body-->

<script>
function isJSON(data)
{
    var ret = true;
    try {
            JSON.parse(data);
    }catch(e) {
            ret = false;
    }
    return ret;
}

function isXML(data)
{
    return (data.length > 0 && data[0] === '<');
}

// see https://gist.github.com/sente/1083506/d2834134cd070dbcc08bf42ee27dabb746a1c54d#gistcomment-2254622
function formatXML(data) {
    const PADDING = ' '.repeat(2); // set desired indent size here
    const reg = /(>)(<)(\/*)/g;
    let pad = 0;
    xml = data.replace(reg, '$1\r\n$2$3');

    return xml.split('\r\n').map((node, index) => {
        let indent = 0;
        if (node.match(/.+<\/\w[^>]*>$/)) {
            indent = 0;
        } else if (node.match(/^<\/\w/) && pad > 0) {
            pad -= 1;
        } else if (node.match(/^<\w[^>]*[^\/]>.*$/)) {
            indent = 1;
        } else {
            indent = 0;
        }

        pad += indent;
        return PADDING.repeat(pad - indent) + node;
    }).join('\r\n');
}

$(".prettyPrint").each(function () {
        var data = $(this).text();
        // Verify whether data is JSON
        if(isJSON(data))
        {
                obj = JSON.parse(data);
                data = JSON.stringify(obj, null, 2);
        }
        else if(isXML(data)) {
            data = formatXML(data);
        }
        $(this).text(data);
});
</script>


<!-- Copy Response Body To Clipboard -->

<script>
    var clipboard = new ClipboardJS('.copyButton');

    clipboard.on('success', function(e) {
        e.clearSelection();
        $(".copyButton").addClass("bg-success text-white")
        e.trigger.textContent = '✔ Copied!';
        window.setTimeout(function() {
            $(".copyButton").removeClass("bg-success text-white")
            e.trigger.textContent = 'Copy to Clipboard';
        }, 2000);
    });
    clipboard.on('error', function(e) {
        e.clearSelection();
        $(".copyButton").addClass("bg-danger text-white")
        e.trigger.textContent = '✗ Not Copied';
        window.setTimeout(function() {
            $(".copyButton").removeClass("bg-danger text-white")
            e.trigger.textContent = 'Copy to Clipboard';
        }, 2000);
    });

</script>

<!-- Render the Description Markdown and link in the test failures -->

<script>
    const remarkable = new Remarkable();

    const descriptions = document.querySelectorAll(".renderMarkdown");
    descriptions.forEach(description => {
        description.innerHTML = renderHtmlFromMarkdown(description.textContent);
    });
    function renderHtmlFromMarkdown(markdown) {
        return remarkable.render(trim(markdown));
    }
    function trim(string) {
        return string ? string.replace(/^ +| +$/gm, "") : string;
    }
</script>

<!-- Show/Hide The Toggles When Scrolling The Page -->

<script>
window.onscroll = function() {scrollFunction()};

function scrollFunction() {
  if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
    document.getElementById("topOfRequestsScreen").style.display = "block";
    document.getElementById("topOfFailuresScreen").style.display = "block";
    document.getElementById("topOfSkippedScreen").style.display = "block";
    document.getElementById("openAll").style.display = "none";
    document.getElementById("openAllRequests").style.display = "none";


  } else {
    document.getElementById("topOfRequestsScreen").style.display = "none";
    document.getElementById("topOfFailuresScreen").style.display = "none";
    document.getElementById("topOfSkippedScreen").style.display = "none";
    document.getElementById("openAll").style.display = "block";
    document.getElementById("openAllRequests").style.display = "block";

  }
}

function topFunction() {
  document.body.scrollTop = 0;
  document.documentElement.scrollTop = 0;
}
</script>

<!-- Creates The Iteration Tabs -->

<script type="text/javascript">
    "use strict";

window.onload = function () {

  // set display for all blocks of response
  var allItems = document.querySelectorAll('[class*=iteration-]');
  allItems.forEach(function(elem){
    elem.style.display = 'block';
  });

   
  let totalIterations = 1;
   

  let menu = document.querySelector('#execution-menu .nav');

  for(var i = 0; i < totalIterations; i++)
  {
    let li = document.createElement('li');
    li.appendChild(document.createTextNode((i + 1)));
    li.setAttribute('id', 'iteration-' + i);
    li.classList.add("custom-tab");
    li.classList.add("itPassed");

    li.addEventListener('click', function() {
      //set display to none for all except row
      let allItems = document.querySelectorAll('[class*=iteration-]:not(.row)');
      allItems.forEach(function(elem) {
        elem.style.display = 'none';
      })

      let allMenus = document.querySelectorAll('[id*=iteration-]');
      allMenus.forEach(function(elem){
        elem.style.borderBottom = 'none';
      })

      this.style.borderBottom = 'solid 3px #032a33';

      let items = document.querySelectorAll("." + this.id + ':not(.row)');
      items.forEach(function(elem) {
        elem.style.display = elem.style.display == 'block' ? 'none' : 'block';
      })
    });
    menu.appendChild(li);
  }

  //shows first tab data
  document.getElementById('iteration-0').click();
  document.getElementById('iterationSelected').innerHTML = `Iteration ${document.getElementById('iteration-0').innerHTML} selected`

}
</script>

<!-- Create the Selected Iteration Label -->

<script>
$(document).ready(function(){
    $(function() {
        $("#iterationList li").click(function() {
            document.getElementById('iterationSelected').innerHTML = "Iteration " + this.innerHTML + " selected"
        });
    });
});
</script>

<!-- Filter Action for the Iterations -->

<script>
$("#filterInput").on("input paste", function() {
    var value = $(this).val();
    $("#iterationList li").filter(function() {
	    $("#showOnlyFailures").data("clickCount") ? $("#showOnlyFailures").click():null;
        $(this).toggle($(this).text().indexOf(value) > -1)
    });
});
</script>

<!-- Showing the Failed Interations -->

<script>
$('#showOnlyFailures').on('click', function(e) {
    let clickCount = $(this).data("clickCount") || 1
	$("#filterInput").val()!="" && clickCount==1 ? $("#filterInput").val('').trigger('input'): null;
    let selectedIteration = $('#iterationList li').filter(function () {
        return $(this).css('border-bottom').indexOf("solid") > -1 && $(this).hasClass('itFailed');
    });
    selectedIteration.length || clickCount > 1 ? null : $("#iterationList li.itFailed")[0].click()
    $("#iterationList li.itPassed").toggle()
    $("div.bg-success [id*=requests]").parents('[class^="row iteration-"]').toggle();
    clickCount = clickCount > 1 ? 1 : ++clickCount;
    clickCount > 1 ? $("#showOnlyFailures").html("Show All Iterations") : $("#showOnlyFailures").html("Show Failed Iterations");
    $(this).data("clickCount", clickCount)
})
</script>
</body>
</html>
