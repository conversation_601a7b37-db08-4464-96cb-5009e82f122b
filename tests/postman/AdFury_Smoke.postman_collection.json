{"info": {"_postman_id": "3fcf17ef-af6f-4547-a68a-f31acf31f01b", "name": "AdFury Smoke API Testing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "45741685", "_collection_link": "https://engineering-team-8216.postman.co/workspace/advid-ai~8118d6c4-f7c2-4f4d-88b9-4230840f478f/collection/45741685-3fcf17ef-af6f-4547-a68a-f31acf31f01b?action=share&source=collection_link&creator=45741685"}, "item": [{"name": "Smoke", "item": [{"name": "SignUp", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"aayush<PERSON>lawat98@\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/signup", "host": ["{{baseUrl}}"], "path": ["auth", "signup"]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status is 200\", () => pm.expect(pm.response.code).to.equal(200));", "", "const json = pm.response.json();", "pm.test(\"access_token present\", () =>", "  pm.expect(json).to.have.property('access_token')", ");", "", "// Save JUST the raw JWT to env var authToken", "pm.environment.set('authToken', json.access_token);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"aayushiahlawat98@\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:5005/auth/login", "protocol": "http", "host": ["localhost"], "port": "5005", "path": ["auth", "login"]}}, "response": []}, {"name": "Get All Accounts", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"200 OK\", () => pm.response.code === 200);", "    pm.test(\"Returns an array\", () =>", "      Array.isArray(pm.response.json())", "    );", "", "    // Grab first account ID for downstream requests", "    const first = pm.response.json()[0];", "    if (first?.id) pm.environment.set(\"accountId\", first.id);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/accounts", "host": ["{{baseUrl}}"], "path": ["api", "accounts"]}}, "response": []}, {"name": "Get All Users for the Account", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"200 OK\", () => pm.response.code === 200);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}/members", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}", "members"]}}, "response": []}]}]}