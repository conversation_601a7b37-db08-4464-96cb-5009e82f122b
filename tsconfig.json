{"compilerOptions": {"allowImportingTsExtensions": true, "allowJs": true, "checkJs": false, "module": "ESNext", "moduleResolution": "Node", "target": "ES2020", "outDir": "dist", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true}, "include": ["**/*.ts", "src/services/notifications.js", "src/routes/product-mgmt/product-mgmt.routes.js", "src/routes/creatives/creatives.routes.js"], "exclude": ["node_modules"]}