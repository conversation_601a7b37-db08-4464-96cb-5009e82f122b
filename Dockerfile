# Use Node.js LTS image
FROM node:20-slim

# Install build dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Clean install dependencies
RUN npm ci && \
    npm install -g tsx

# Copy source code
COPY . .

# Expose port
EXPOSE 5005

# Start the server directly with tsx
CMD ["tsx", "app.ts"]
