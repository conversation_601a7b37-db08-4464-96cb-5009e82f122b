# Service: Primary

**Generated:** Tue Jul 22 12:29:30 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Primary
   **Status:** Unknown
 **Service ID:** 90b1e1f1-123c-4053-bb1e-fb7e3c9b6b88
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Primary
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| f91b943d-8bc6-48ee-bcca-e807588e1d62 | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "90b1e1f1-123c-4053-bb1e-fb7e3c9b6b88",
  "name": "Primary",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "f91b943d-8bc6-48ee-bcca-e807588e1d62",
          "serviceId": "90b1e1f1-123c-4053-bb1e-fb7e3c9b6b88",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "98d96ec3-414a-4369-b373-3922529c8061",
            "meta": {
              "image": "n8nio/n8n",
              "logsV2": true,
              "patchId": "d031b9af-dcd6-4d2c-8403-60ff11f0846f",
              "reason": "deploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": "/healthz",
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "n8n start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": null,
            "image": "n8nio/n8n"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "98d96ec3-414a-4369-b373-3922529c8061",
  "meta": {
    "image": "n8nio/n8n",
    "logsV2": true,
    "patchId": "d031b9af-dcd6-4d2c-8403-60ff11f0846f",
    "reason": "deploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": "/healthz",
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "n8n start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Primary

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `90b1e1f1-123c-4053-bb1e-fb7e3c9b6b88`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
