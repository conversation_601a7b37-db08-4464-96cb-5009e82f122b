# Service: advid-wm-service

**Generated:** Tue Jul 22 12:29:30 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** advid-wm-service
   **Status:** Unknown
 **Service ID:** 3f4d0bdf-202e-4a46-8f9f-10b248fdb152
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service advid-wm-service
railway variables
```

## Service Instances

**Total Instances:** 3

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 3f3640f5-53c9-4ee1-b263-87fcb50fe5da | Unknown | 89aed380-ce64-4a52-b022-20e17d3a5562 |
| 61fbd61d-76d3-4cbc-8303-73247f1ef5bb | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |
| e179d54c-7201-4b8b-93e9-1e77eea85dd9 | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "3f4d0bdf-202e-4a46-8f9f-10b248fdb152",
  "name": "advid-wm-service",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "3f3640f5-53c9-4ee1-b263-87fcb50fe5da",
          "serviceId": "3f4d0bdf-202e-4a46-8f9f-10b248fdb152",
          "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "bc89cae0-5b23-4129-9083-4a5a7c0054c9",
            "meta": {
              "branch": "staging",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
              "commitMessage": "stuff",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE"
                }
              },
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.builder": "$.build.builder"
              },
              "reason": "deploy",
              "repo": "AdVid-ai/advid-wm-service",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-wm-service",
            "image": null
          }
        }
      },
      {
        "node": {
          "id": "61fbd61d-76d3-4cbc-8303-73247f1ef5bb",
          "serviceId": "3f4d0bdf-202e-4a46-8f9f-10b248fdb152",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "1fcf9d3a-85aa-44c4-87c0-146a4b6efdb5",
            "meta": {
              "branch": "demo",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "b40b6b6367398a253007d6d9c6d9dfa506b0d46f",
              "commitMessage": "Merge pull request #6 from AdVid-ai/staging\n\nremove limit on creative id filtering",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE"
                }
              },
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "isPublicRepoDeploy": false,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "patchId": "bd7ae71f-4a92-455a-9de8-3c3d25d2c4c5",
              "propertyFileMapping": {
                "build.builder": "$.build.builder"
              },
              "reason": "migrate",
              "repo": "AdVid-ai/advid-wm-service",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-wm-service",
            "image": null
          }
        }
      },
      {
        "node": {
          "id": "e179d54c-7201-4b8b-93e9-1e77eea85dd9",
          "serviceId": "3f4d0bdf-202e-4a46-8f9f-10b248fdb152",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "40b50223-7a1f-42cd-a962-c8ce35027512",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
              "commitMessage": "stuff",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE"
                }
              },
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.builder": "$.build.builder"
              },
              "reason": "deploy",
              "repo": "AdVid-ai/advid-wm-service",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 3
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-wm-service",
            "image": null
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "bc89cae0-5b23-4129-9083-4a5a7c0054c9",
  "meta": {
    "branch": "staging",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
    "commitMessage": "stuff",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE"
      }
    },
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.builder": "$.build.builder"
    },
    "reason": "deploy",
    "repo": "AdVid-ai/advid-wm-service",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
{
  "canRedeploy": true,
  "id": "1fcf9d3a-85aa-44c4-87c0-146a4b6efdb5",
  "meta": {
    "branch": "demo",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "b40b6b6367398a253007d6d9c6d9dfa506b0d46f",
    "commitMessage": "Merge pull request #6 from AdVid-ai/staging\n\nremove limit on creative id filtering",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE"
      }
    },
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "isPublicRepoDeploy": false,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "patchId": "bd7ae71f-4a92-455a-9de8-3c3d25d2c4c5",
    "propertyFileMapping": {
      "build.builder": "$.build.builder"
    },
    "reason": "migrate",
    "repo": "AdVid-ai/advid-wm-service",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
{
  "canRedeploy": true,
  "id": "40b50223-7a1f-42cd-a962-c8ce35027512",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "d6b0defcde71adabf0b1745fd65d7f2b77df9156",
    "commitMessage": "stuff",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE"
      }
    },
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.builder": "$.build.builder"
    },
    "reason": "deploy",
    "repo": "AdVid-ai/advid-wm-service",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 3
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service advid-wm-service

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `3f4d0bdf-202e-4a46-8f9f-10b248fdb152`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
