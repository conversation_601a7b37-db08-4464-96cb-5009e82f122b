{"id": "dc33e660-d3df-46c5-b2db-f3d56d619df4", "name": "ash-assistant", "serviceInstances": {"edges": [{"node": {"id": "73c719cc-30bb-4bac-b959-a4fdceb709f8", "serviceId": "dc33e660-d3df-46c5-b2db-f3d56d619df4", "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42", "latestDeployment": {"canRedeploy": true, "id": "55949f22-08ea-41a1-9e0b-eb0af7208ee1", "meta": {"branch": "main", "buildOnly": false, "commitAuthor": "", "commitHash": "ed74283a56d8c1c1d1f96880c520606c395e3874", "commitMessage": "ai ideas folder (potential features/enhancements/considerations)", "fileServiceManifest": {}, "githubDeploymentId": **********, "ignoreWatchPatterns": true, "isPublicRepoDeploy": false, "logsV2": true, "nixpacksProviders": ["node"], "patchId": "d32b2948-77f8-48ad-94ba-57874285a05d", "propertyFileMapping": {}, "reason": "migrate", "repo": "AdVid-ai/ash-assistant", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/ash-assistant", "image": null}}}, {"node": {"id": "e973e8f3-d9a8-44e0-8517-8bcc788c6373", "serviceId": "dc33e660-d3df-46c5-b2db-f3d56d619df4", "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562", "latestDeployment": {"canRedeploy": true, "id": "d1f07e17-7abd-41a6-9349-5aa777d827ea", "meta": {"branch": "main", "buildOnly": false, "commitAuthor": "bryce27", "commitHash": "ed74283a56d8c1c1d1f96880c520606c395e3874", "commitMessage": "ai ideas folder (potential features/enhancements/considerations)", "fileServiceManifest": {}, "githubDeploymentId": **********, "ignoreWatchPatterns": true, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {}, "reason": "migrate", "repo": "AdVid-ai/ash-assistant", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/ash-assistant", "image": null}}}]}}