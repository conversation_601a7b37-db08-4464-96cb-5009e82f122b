# Service: Postgres-C5Ge

**Generated:** Tue Jul 22 12:29:30 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Postgres-C5Ge
   **Status:** Unknown
 **Service ID:** 5afd7862-c46c-4904-8a0d-129007ede9b6
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Postgres-C5Ge
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 10aa8a7d-8b75-4759-9ecd-a45b2aef81ff | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "5afd7862-c46c-4904-8a0d-129007ede9b6",
  "name": "Postgres-C5Ge",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "10aa8a7d-8b75-4759-9ecd-a45b2aef81ff",
          "serviceId": "5afd7862-c46c-4904-8a0d-129007ede9b6",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "25c3aca4-2967-4740-acd8-b62e512c9364",
            "meta": {
              "ignoreWatchPatterns": true,
              "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest",
              "logsV2": true,
              "patchId": "e4769826-016f-4d40-84d5-a63f51acd20d",
              "reason": "migrate",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/var/lib/postgresql/data"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "25c3aca4-2967-4740-acd8-b62e512c9364",
  "meta": {
    "ignoreWatchPatterns": true,
    "image": "ghcr.io/railwayapp-templates/postgres-ssl:latest",
    "logsV2": true,
    "patchId": "e4769826-016f-4d40-84d5-a63f51acd20d",
    "reason": "migrate",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/var/lib/postgresql/data"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Postgres-C5Ge

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `5afd7862-c46c-4904-8a0d-129007ede9b6`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
