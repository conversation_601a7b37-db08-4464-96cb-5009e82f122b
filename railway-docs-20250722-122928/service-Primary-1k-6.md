# Service: Primary-1k-6

**Generated:** Tue Jul 22 12:29:31 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Primary-1k-6
   **Status:** Unknown
 **Service ID:** ef43fc0e-01ac-47d4-be06-a63dd627e3d3
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Primary-1k-6
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| cb6ad666-ae04-4c73-a2f5-fccea3f1ac02 | Unknown | 304cee46-c13a-48a2-8feb-2a8be4ba237c |


## Service Configuration

 ```json
 {
  "id": "ef43fc0e-01ac-47d4-be06-a63dd627e3d3",
  "name": "Primary-1k-6",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "cb6ad666-ae04-4c73-a2f5-fccea3f1ac02",
          "serviceId": "ef43fc0e-01ac-47d4-be06-a63dd627e3d3",
          "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "8eccd9d2-c6b9-4e6e-93c1-f060f12ec537",
            "meta": {
              "image": "n8nio/n8n",
              "logsV2": true,
              "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d",
              "queuedReason": "Waiting for dependencies to deploy",
              "reason": "deploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckPath": "/healthz",
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "n8n start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": null,
            "image": "n8nio/n8n"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "8eccd9d2-c6b9-4e6e-93c1-f060f12ec537",
  "meta": {
    "image": "n8nio/n8n",
    "logsV2": true,
    "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d",
    "queuedReason": "Waiting for dependencies to deploy",
    "reason": "deploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckPath": "/healthz",
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "n8n start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Primary-1k-6

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `ef43fc0e-01ac-47d4-be06-a63dd627e3d3`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
