# Service: adfury-app

**Generated:** Tue Jul 22 12:29:29 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** adfury-app
   **Status:** Unknown
 **Service ID:** 0c861b92-6665-48f2-a392-8c1942ba89e3
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service adfury-app
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| ab39dd90-b997-4a37-be1b-6246232532ff | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "0c861b92-6665-48f2-a392-8c1942ba89e3",
  "name": "adfury-app",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "ab39dd90-b997-4a37-be1b-6246232532ff",
          "serviceId": "0c861b92-6665-48f2-a392-8c1942ba89e3",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "fbc0d7fe-a715-4b19-b951-3ed80ca55f98",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "alpendergrast",
              "commitHash": "b08ca1993fec98ea936e3c7db9cc1b2266bb6836",
              "commitMessage": "fix advertiser id detection from builder",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {},
              "reason": "deploy",
              "repo": "AdVid-ai/advid-app",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-app",
            "image": null
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "fbc0d7fe-a715-4b19-b951-3ed80ca55f98",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "alpendergrast",
    "commitHash": "b08ca1993fec98ea936e3c7db9cc1b2266bb6836",
    "commitMessage": "fix advertiser id detection from builder",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {},
    "reason": "deploy",
    "repo": "AdVid-ai/advid-app",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service adfury-app

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `0c861b92-6665-48f2-a392-8c1942ba89e3`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
