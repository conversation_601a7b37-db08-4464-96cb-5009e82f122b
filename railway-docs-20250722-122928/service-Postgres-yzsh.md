# Service: Postgres-yzsh

**Generated:** Tue Jul 22 12:29:30 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Postgres-yzsh
   **Status:** Unknown
 **Service ID:** 7ae6349f-4f26-46c0-8149-0b7b4ccbccd6
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Postgres-yzsh
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 25b58d17-4f07-4e2a-90c1-0b1e55667e5c | Unknown | 304cee46-c13a-48a2-8feb-2a8be4ba237c |


## Service Configuration

 ```json
 {
  "id": "7ae6349f-4f26-46c0-8149-0b7b4ccbccd6",
  "name": "Postgres-yzsh",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "25b58d17-4f07-4e2a-90c1-0b1e55667e5c",
          "serviceId": "7ae6349f-4f26-46c0-8149-0b7b4ccbccd6",
          "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "b2f26052-870e-487a-921b-aa3c8d1b97b7",
            "meta": {
              "image": "ghcr.io/railwayapp-templates/postgres-ssl",
              "logsV2": true,
              "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d",
              "reason": "deploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": "/var/lib/postgresql/data",
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""
                }
              },
              "volumeMounts": [
                "/var/lib/postgresql/data"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "ghcr.io/railwayapp-templates/postgres-ssl"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "b2f26052-870e-487a-921b-aa3c8d1b97b7",
  "meta": {
    "image": "ghcr.io/railwayapp-templates/postgres-ssl",
    "logsV2": true,
    "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d",
    "reason": "deploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": "/var/lib/postgresql/data",
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""
      }
    },
    "volumeMounts": [
      "/var/lib/postgresql/data"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Postgres-yzsh

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `7ae6349f-4f26-46c0-8149-0b7b4ccbccd6`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
