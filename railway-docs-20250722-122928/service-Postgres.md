# Service: Postgres

**Generated:** Tue Jul 22 12:29:29 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Postgres
   **Status:** Unknown
 **Service ID:** 3d163159-581d-46f8-856c-b1a3e4a6d704
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Postgres
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 1bb37637-389e-473b-9f19-68b9cd81fd1c | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "3d163159-581d-46f8-856c-b1a3e4a6d704",
  "name": "Postgres",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "1bb37637-389e-473b-9f19-68b9cd81fd1c",
          "serviceId": "3d163159-581d-46f8-856c-b1a3e4a6d704",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "d8ecc9d1-2b78-4313-8842-48a8c2f18dd3",
            "meta": {
              "deprecatedRegions": [
                {
                  "region": "us-west1",
                  "replacementRegion": "us-west2"
                }
              ],
              "ignoreWatchPatterns": true,
              "image": "ghcr.io/railwayapp-templates/postgres-ssl",
              "logsV2": true,
              "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
              "reason": "redeploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west1": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""
                }
              },
              "volumeMounts": [
                "/var/lib/postgresql/data"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "ghcr.io/railwayapp-templates/postgres-ssl"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "d8ecc9d1-2b78-4313-8842-48a8c2f18dd3",
  "meta": {
    "deprecatedRegions": [
      {
        "region": "us-west1",
        "replacementRegion": "us-west2"
      }
    ],
    "ignoreWatchPatterns": true,
    "image": "ghcr.io/railwayapp-templates/postgres-ssl",
    "logsV2": true,
    "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
    "reason": "redeploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west1": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""
      }
    },
    "volumeMounts": [
      "/var/lib/postgresql/data"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Postgres

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `3d163159-581d-46f8-856c-b1a3e4a6d704`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
