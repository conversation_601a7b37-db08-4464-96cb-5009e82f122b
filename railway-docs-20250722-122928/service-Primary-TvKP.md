# Service: Primary-TvKP

**Generated:** Tue Jul 22 12:29:30 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Primary-TvKP
   **Status:** Unknown
 **Service ID:** 93758c48-deac-4f56-917b-04f42091437c
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Primary-TvKP
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 644fbe0d-0c35-46d8-ba4a-a70b05332bb7 | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |


## Service Configuration

 ```json
 {
  "id": "93758c48-deac-4f56-917b-04f42091437c",
  "name": "Primary-TvKP",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "644fbe0d-0c35-46d8-ba4a-a70b05332bb7",
          "serviceId": "93758c48-deac-4f56-917b-04f42091437c",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "0bdf7cca-84dc-4629-9694-3ae55c4919c8",
            "meta": {
              "image": "n8nio/n8n",
              "logsV2": true,
              "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
              "queuedReason": "Waiting for dependencies to deploy",
              "reason": "deploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckPath": "/healthz",
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "n8n start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": null,
            "image": "n8nio/n8n"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "0bdf7cca-84dc-4629-9694-3ae55c4919c8",
  "meta": {
    "image": "n8nio/n8n",
    "logsV2": true,
    "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
    "queuedReason": "Waiting for dependencies to deploy",
    "reason": "deploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckPath": "/healthz",
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "n8n start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Primary-TvKP

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `93758c48-deac-4f56-917b-04f42091437c`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
