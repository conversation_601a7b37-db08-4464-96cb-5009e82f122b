{"id": "a5c5a05e-a068-40d3-99ad-f1e74c08ebfb", "name": "asset-generator-stg", "serviceInstances": {"edges": [{"node": {"id": "bbde1137-0507-449e-951a-73ac991f1172", "serviceId": "a5c5a05e-a068-40d3-99ad-f1e74c08ebfb", "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42", "latestDeployment": {"canRedeploy": true, "id": "9907055f-8340-4814-9ae6-0915ada8e42e", "meta": {"branch": "staging", "buildOnly": false, "commitAuthor": "<PERSON><PERSON><PERSON><PERSON>", "commitHash": "3eba527548d4d24d2b291f6dfad6ef2555f7c8e0", "commitMessage": "Merge pull request #14 from AdVid-ai/main\n\nMerge main into staging", "configFile": "railway.toml", "deprecatedRegions": [{"region": "us-west1", "replacementRegion": "us-west2"}], "fileServiceManifest": {"build": {"builder": "DOCKERFILE", "dockerfilePath": "Dockerfile"}, "deploy": {"healthcheckPath": "/api/health", "healthcheckTimeout": 100, "startCommand": "npm start"}}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {"build.builder": "$.build.builder", "build.dockerfilePath": "$.build.dockerfilePath", "deploy.healthcheckPath": "$.deploy.healthcheckPath", "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout", "deploy.startCommand": "$.deploy.startCommand"}, "reason": "deploy", "repo": "AdVid-ai/asset-generator", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "nixpacksConfigPath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": "/api/health", "healthcheckTimeout": 100, "limitOverride": null, "multiRegionConfig": {"us-west1": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "npm start"}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/asset-generator", "image": null}}}, {"node": {"id": "c712444f-cc5d-4ff2-b7db-4272dd61cdc5", "serviceId": "a5c5a05e-a068-40d3-99ad-f1e74c08ebfb", "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562", "latestDeployment": {"canRedeploy": true, "id": "75f8f61f-4ac4-4539-a0dd-9582b57f46db", "meta": {"branch": "staging", "buildOnly": false, "commitAuthor": "<PERSON><PERSON><PERSON><PERSON>", "commitHash": "3eba527548d4d24d2b291f6dfad6ef2555f7c8e0", "commitMessage": "Merge pull request #14 from AdVid-ai/main\n\nMerge main into staging", "configFile": "railway.toml", "deprecatedRegions": [{"region": "us-west1", "replacementRegion": "us-west2"}], "fileServiceManifest": {"build": {"builder": "DOCKERFILE", "dockerfilePath": "Dockerfile"}, "deploy": {"healthcheckPath": "/api/health", "healthcheckTimeout": 100, "startCommand": "npm start"}}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {"build.builder": "$.build.builder", "build.dockerfilePath": "$.build.dockerfilePath", "deploy.healthcheckPath": "$.deploy.healthcheckPath", "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout", "deploy.startCommand": "$.deploy.startCommand"}, "reason": "deploy", "repo": "AdVid-ai/asset-generator", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "nixpacksConfigPath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": "/api/health", "healthcheckTimeout": 100, "limitOverride": null, "multiRegionConfig": {"us-west1": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "npm start"}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/asset-generator", "image": null}}}]}}