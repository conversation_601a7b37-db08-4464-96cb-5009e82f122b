# Service: advid-marketing-page

**Generated:** Tue Jul 22 12:29:31 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** advid-marketing-page
   **Status:** Unknown
 **Service ID:** f0be02d6-581d-404c-a2bd-9a3627f30343
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service advid-marketing-page
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| c0571ac9-c675-4142-bce6-f5949f0147e8 | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "f0be02d6-581d-404c-a2bd-9a3627f30343",
  "name": "advid-marketing-page",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "c0571ac9-c675-4142-bce6-f5949f0147e8",
          "serviceId": "f0be02d6-581d-404c-a2bd-9a3627f30343",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "e9495bbd-814f-45a8-8201-17df1d1b514c",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "",
              "commitHash": "6ecd25d5e197741ce0f7994f2e9cbed9eefa9a6a",
              "commitMessage": "replace favicon",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "isPublicRepoDeploy": false,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "patchId": "d4c85156-5769-48e1-aec2-920834ab5ebd",
              "propertyFileMapping": {},
              "reason": "migrate",
              "repo": "AdVid-ai/advid-marketing-page",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-marketing-page",
            "image": null
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "e9495bbd-814f-45a8-8201-17df1d1b514c",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "",
    "commitHash": "6ecd25d5e197741ce0f7994f2e9cbed9eefa9a6a",
    "commitMessage": "replace favicon",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "isPublicRepoDeploy": false,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "patchId": "d4c85156-5769-48e1-aec2-920834ab5ebd",
    "propertyFileMapping": {},
    "reason": "migrate",
    "repo": "AdVid-ai/advid-marketing-page",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service advid-marketing-page

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `f0be02d6-581d-404c-a2bd-9a3627f30343`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
