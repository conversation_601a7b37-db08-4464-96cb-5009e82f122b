{"id": "3d163159-581d-46f8-856c-b1a3e4a6d704", "name": "Postgres", "serviceInstances": {"edges": [{"node": {"id": "1bb37637-389e-473b-9f19-68b9cd81fd1c", "serviceId": "3d163159-581d-46f8-856c-b1a3e4a6d704", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "d8ecc9d1-2b78-4313-8842-48a8c2f18dd3", "meta": {"deprecatedRegions": [{"region": "us-west1", "replacementRegion": "us-west2"}], "ignoreWatchPatterns": true, "image": "ghcr.io/railwayapp-templates/postgres-ssl", "logsV2": true, "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3", "reason": "redeploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-west1": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "/bin/sh -c \"unset PGPORT; docker-entrypoint.sh postgres --port=5432\""}}, "volumeMounts": ["/var/lib/postgresql/data"]}}, "source": {"repo": null, "image": "ghcr.io/railwayapp-templates/postgres-ssl"}}}]}}