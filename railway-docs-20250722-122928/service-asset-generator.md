# Service: asset-generator

**Generated:** Tue Jul 22 12:29:31 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** asset-generator
   **Status:** Unknown
 **Service ID:** c574bc61-5697-460d-b21a-7d3321d34895
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service asset-generator
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 30d141c1-e085-40c5-87a4-df9c2dd005e8 | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "c574bc61-5697-460d-b21a-7d3321d34895",
  "name": "asset-generator",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "30d141c1-e085-40c5-87a4-df9c2dd005e8",
          "serviceId": "c574bc61-5697-460d-b21a-7d3321d34895",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "cc010d3f-24bf-4daf-8993-8809a1abc1bb",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "JRippelmeyer",
              "commitHash": "8f1f18536fea90c1f7ac71cc524f272f63bc17fb",
              "commitMessage": "Added more error handling for pusher events",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile"
                },
                "deploy": {
                  "healthcheckPath": "/api/health",
                  "healthcheckTimeout": 100,
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.builder": "$.build.builder",
                "build.dockerfilePath": "$.build.dockerfilePath",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "deploy",
              "repo": "AdVid-ai/asset-generator",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": "/api/health",
                  "healthcheckTimeout": 100,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 3
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/asset-generator",
            "image": null
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "cc010d3f-24bf-4daf-8993-8809a1abc1bb",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "JRippelmeyer",
    "commitHash": "8f1f18536fea90c1f7ac71cc524f272f63bc17fb",
    "commitMessage": "Added more error handling for pusher events",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile"
      },
      "deploy": {
        "healthcheckPath": "/api/health",
        "healthcheckTimeout": 100,
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.builder": "$.build.builder",
      "build.dockerfilePath": "$.build.dockerfilePath",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "deploy",
    "repo": "AdVid-ai/asset-generator",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": "/api/health",
        "healthcheckTimeout": 100,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 3
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service asset-generator

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `c574bc61-5697-460d-b21a-7d3321d34895`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
