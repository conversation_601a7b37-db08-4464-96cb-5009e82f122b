# Service: retail-mgmt-stg

**Generated:** Tue Jul 22 12:29:31 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** retail-mgmt-stg
   **Status:** Unknown
 **Service ID:** c9ba0a6c-0acf-4cda-8b50-5e07e59032b2
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service retail-mgmt-stg
railway variables
```

## Service Instances

**Total Instances:** 2

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 8908257e-e032-4ae7-bd34-84651bb01208 | Unknown | 89aed380-ce64-4a52-b022-20e17d3a5562 |
| c2329538-5a47-4dc7-be27-1541264cc9bd | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |


## Service Configuration

 ```json
 {
  "id": "c9ba0a6c-0acf-4cda-8b50-5e07e59032b2",
  "name": "retail-mgmt-stg",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "8908257e-e032-4ae7-bd34-84651bb01208",
          "serviceId": "c9ba0a6c-0acf-4cda-8b50-5e07e59032b2",
          "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "79a4565b-2154-4961-b4ae-20f4cd44a089",
            "meta": {
              "branch": "staging",
              "buildOnly": false,
              "commitAuthor": "bryce27",
              "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
              "commitMessage": "remove ignored files from git tracking",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "builder": "NIXPACKS"
                },
                "deploy": {
                  "healthcheckPath": "/health",
                  "healthcheckTimeout": 100,
                  "restartPolicyType": "ON_FAILURE",
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {
                "build.buildCommand": "$.build.buildCommand",
                "build.builder": "$.build.builder",
                "deploy.envs": "$.deploy.envs",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "migrate",
              "repo": "AdVid-ai/retail-mgmt",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckTimeout": 100,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/retail-mgmt",
            "image": null
          }
        }
      },
      {
        "node": {
          "id": "c2329538-5a47-4dc7-be27-1541264cc9bd",
          "serviceId": "c9ba0a6c-0acf-4cda-8b50-5e07e59032b2",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "f7fc88a2-05e1-49fd-babb-b6ee8ddf3815",
            "meta": {
              "branch": "staging",
              "buildOnly": false,
              "commitAuthor": "",
              "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
              "commitMessage": "remove ignored files from git tracking",
              "configFile": "railway.toml",
              "fileServiceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "builder": "NIXPACKS"
                },
                "deploy": {
                  "healthcheckPath": "/health",
                  "healthcheckTimeout": 100,
                  "restartPolicyType": "ON_FAILURE",
                  "startCommand": "npm start"
                }
              },
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "isPublicRepoDeploy": false,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "patchId": "85f81d41-9d73-4265-9722-e040640b6d1a",
              "propertyFileMapping": {
                "build.buildCommand": "$.build.buildCommand",
                "build.builder": "$.build.builder",
                "deploy.envs": "$.deploy.envs",
                "deploy.healthcheckPath": "$.deploy.healthcheckPath",
                "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
                "deploy.restartPolicyType": "$.deploy.restartPolicyType",
                "deploy.startCommand": "$.deploy.startCommand"
              },
              "reason": "migrate",
              "repo": "AdVid-ai/retail-mgmt",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": "npm install",
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": 100,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "npm start"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/retail-mgmt",
            "image": null
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "79a4565b-2154-4961-b4ae-20f4cd44a089",
  "meta": {
    "branch": "staging",
    "buildOnly": false,
    "commitAuthor": "bryce27",
    "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
    "commitMessage": "remove ignored files from git tracking",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "buildCommand": "npm install",
        "builder": "NIXPACKS"
      },
      "deploy": {
        "healthcheckPath": "/health",
        "healthcheckTimeout": 100,
        "restartPolicyType": "ON_FAILURE",
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {
      "build.buildCommand": "$.build.buildCommand",
      "build.builder": "$.build.builder",
      "deploy.envs": "$.deploy.envs",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.restartPolicyType": "$.deploy.restartPolicyType",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "migrate",
    "repo": "AdVid-ai/retail-mgmt",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": "npm install",
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckTimeout": 100,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
{
  "canRedeploy": true,
  "id": "f7fc88a2-05e1-49fd-babb-b6ee8ddf3815",
  "meta": {
    "branch": "staging",
    "buildOnly": false,
    "commitAuthor": "",
    "commitHash": "bc09fc2eabe80697da3768585fad3e89389230b6",
    "commitMessage": "remove ignored files from git tracking",
    "configFile": "railway.toml",
    "fileServiceManifest": {
      "build": {
        "buildCommand": "npm install",
        "builder": "NIXPACKS"
      },
      "deploy": {
        "healthcheckPath": "/health",
        "healthcheckTimeout": 100,
        "restartPolicyType": "ON_FAILURE",
        "startCommand": "npm start"
      }
    },
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "isPublicRepoDeploy": false,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "patchId": "85f81d41-9d73-4265-9722-e040640b6d1a",
    "propertyFileMapping": {
      "build.buildCommand": "$.build.buildCommand",
      "build.builder": "$.build.builder",
      "deploy.envs": "$.deploy.envs",
      "deploy.healthcheckPath": "$.deploy.healthcheckPath",
      "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout",
      "deploy.restartPolicyType": "$.deploy.restartPolicyType",
      "deploy.startCommand": "$.deploy.startCommand"
    },
    "reason": "migrate",
    "repo": "AdVid-ai/retail-mgmt",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": "npm install",
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": 100,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "npm start"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service retail-mgmt-stg

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `c9ba0a6c-0acf-4cda-8b50-5e07e59032b2`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
