{"id": "0fa43be0-b6bc-47bd-bae3-e489eb8a49fa", "name": "Redis-Sessions", "serviceInstances": {"edges": [{"node": {"id": "d22925e2-c0ed-4853-9dbb-f70518a401bd", "serviceId": "0fa43be0-b6bc-47bd-bae3-e489eb8a49fa", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "54e6ae8f-a113-4127-b04c-2f7ea23eabfc", "meta": {"duration": 1773042, "image": "bitnami/redis:7.2.5", "logsV2": true, "patchId": "f7da1d75-24ca-4ed0-818f-960d187a28df", "reason": "rollback", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": "/bitnami", "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": ["/bitnami"]}}, "source": {"repo": null, "image": "bitnami/redis:7.2.5"}}}]}}