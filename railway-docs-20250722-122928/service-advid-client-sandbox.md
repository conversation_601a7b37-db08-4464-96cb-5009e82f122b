# Service: advid-client-sandbox

**Generated:** Tue Jul 22 12:29:31 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** advid-client-sandbox
   **Status:** Unknown
 **Service ID:** bc5cf161-1545-49bc-b66f-eec5348ab37e
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service advid-client-sandbox
railway variables
```

## Service Instances

**Total Instances:** 3

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 209f910f-3a37-46b8-9bd5-698cde8669ff | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |
| cb0a8259-8dab-4d3b-b89a-cef256a3fa62 | Unknown | 89aed380-ce64-4a52-b022-20e17d3a5562 |
| dc848756-198b-4fb0-8219-99778f46d541 | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |


## Service Configuration

 ```json
 {
  "id": "bc5cf161-1545-49bc-b66f-eec5348ab37e",
  "name": "advid-client-sandbox",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "209f910f-3a37-46b8-9bd5-698cde8669ff",
          "serviceId": "bc5cf161-1545-49bc-b66f-eec5348ab37e",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "fbe77ad6-1f08-4fcb-a29f-08454460c03e",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "alpendergrast",
              "commitHash": "6bc777c60845c84956321e9e2036dbbe292a91c3",
              "commitMessage": "adjust layout for url sub and detail pages",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {},
              "reason": "migrate",
              "repo": "AdVid-ai/advid-client-sandbox",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/advid-client-sandbox",
            "image": null
          }
        }
      },
      {
        "node": {
          "id": "cb0a8259-8dab-4d3b-b89a-cef256a3fa62",
          "serviceId": "bc5cf161-1545-49bc-b66f-eec5348ab37e",
          "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "latestDeployment": null,
          "source": null
        }
      },
      {
        "node": {
          "id": "dc848756-198b-4fb0-8219-99778f46d541",
          "serviceId": "bc5cf161-1545-49bc-b66f-eec5348ab37e",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": null,
          "source": null
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "fbe77ad6-1f08-4fcb-a29f-08454460c03e",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "alpendergrast",
    "commitHash": "6bc777c60845c84956321e9e2036dbbe292a91c3",
    "commitMessage": "adjust layout for url sub and detail pages",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {},
    "reason": "migrate",
    "repo": "AdVid-ai/advid-client-sandbox",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
null
null
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service advid-client-sandbox

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `bc5cf161-1545-49bc-b66f-eec5348ab37e`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
