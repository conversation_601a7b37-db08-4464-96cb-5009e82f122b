{"id": "0e93f435-2291-4820-b176-715fe50fa6e4", "name": "Red<PERSON><PERSON><PERSON><PERSON>", "serviceInstances": {"edges": [{"node": {"id": "a0657a45-0977-4a75-a88d-0382e24a99e2", "serviceId": "0e93f435-2291-4820-b176-715fe50fa6e4", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "1197ab63-74c1-480b-8077-7fdff57e4037", "meta": {"ignoreWatchPatterns": true, "image": "bitnami/redis:7.2.5", "logsV2": true, "patchId": "17bf9a42-14c3-4701-a049-b88eee9dbab2", "reason": "migrate", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": ["/bitnami"]}}, "source": {"repo": null, "image": "bitnami/redis:7.2.5"}}}]}}