{"id": "948dfff7-bba7-4ba2-84a2-a2cf4c7b5353", "name": "langflowai/langflow:latest", "serviceInstances": {"edges": [{"node": {"id": "cd326eeb-1fa5-4f53-b7e1-7eb96f59ea29", "serviceId": "948dfff7-bba7-4ba2-84a2-a2cf4c7b5353", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "6462d13d-5a55-4b5e-8ae4-c7c4307a4bff", "meta": {"ignoreWatchPatterns": true, "image": "langflowai/langflow:latest", "logsV2": true, "patchId": "bc70da66-ac5a-42ce-b10a-a928e53d528b", "reason": "migrate", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "python -m langflow run --host 0.0.0.0"}}, "volumeMounts": []}}, "source": {"repo": null, "image": "langflowai/langflow:latest"}}}]}}