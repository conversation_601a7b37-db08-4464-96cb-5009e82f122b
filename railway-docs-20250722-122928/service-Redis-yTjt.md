# Service: Redis-yTjt

**Generated:** Tue Jul 22 12:29:30 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Redis-yTjt
   **Status:** Unknown
 **Service ID:** 5cb303d9-3b6c-4945-b3a8-7e24a1980d85
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Redis-yTjt
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 1421b278-225b-4aa0-8d43-d4b519bf19f3 | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |


## Service Configuration

 ```json
 {
  "id": "5cb303d9-3b6c-4945-b3a8-7e24a1980d85",
  "name": "Redis-yTjt",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "1421b278-225b-4aa0-8d43-d4b519bf19f3",
          "serviceId": "5cb303d9-3b6c-4945-b3a8-7e24a1980d85",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "cbdbe06d-17ba-43a9-8575-5a998d4b1af3",
            "meta": {
              "image": "bitnami/redis",
              "logsV2": true,
              "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
              "reason": "deploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": "/bitnami",
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/bitnami"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "bitnami/redis"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "cbdbe06d-17ba-43a9-8575-5a998d4b1af3",
  "meta": {
    "image": "bitnami/redis",
    "logsV2": true,
    "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
    "reason": "deploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": "/bitnami",
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/bitnami"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Redis-yTjt

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `5cb303d9-3b6c-4945-b3a8-7e24a1980d85`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
