{"id": "b8730e7f-b1f6-4ac0-9a5f-bc76aa3ecf88", "name": "Redis-HVJ1", "serviceInstances": {"edges": [{"node": {"id": "105fdb75-c19f-4be7-81d8-6da52e9a6164", "serviceId": "b8730e7f-b1f6-4ac0-9a5f-bc76aa3ecf88", "environmentId": "304cee46-c13a-48a2-8feb-2a8be4ba237c", "latestDeployment": {"canRedeploy": true, "id": "31acf8eb-dd50-4fd1-bbad-0e58c9c0f9b4", "meta": {"image": "bitnami/redis", "logsV2": true, "patchId": "e1e6ca44-f20c-4344-bc9f-cf5e64f11c0d", "reason": "deploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": "/bitnami", "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": ["/bitnami"]}}, "source": {"repo": null, "image": "bitnami/redis"}}}]}}