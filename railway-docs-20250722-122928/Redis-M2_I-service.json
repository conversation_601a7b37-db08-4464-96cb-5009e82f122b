{"id": "31dd5c7b-c1ae-43a6-bf72-c06c2a9d8c14", "name": "Redis-M2_I", "serviceInstances": {"edges": [{"node": {"id": "58ec488f-618f-4668-af33-1d93cb50c4ce", "serviceId": "31dd5c7b-c1ae-43a6-bf72-c06c2a9d8c14", "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562", "latestDeployment": {"canRedeploy": true, "id": "fe49e186-3b84-44e7-a9f3-38bbfeb9a6fe", "meta": {"image": "bitnami/redis:7.2.5", "logsV2": true, "patchId": "1bcd540a-7927-46f8-9b30-49e1db3e8f67", "reason": "deploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": "/bitnami", "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": ["/bitnami"]}}, "source": {"repo": null, "image": "bitnami/redis:7.2.5"}}}]}}