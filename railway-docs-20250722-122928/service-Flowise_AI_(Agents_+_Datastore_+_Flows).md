# Service: Flowise AI (Agents + Datastore + Flows)

**Generated:** Tue Jul 22 12:29:29 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Flowise AI (Agents + Datastore + Flows)
   **Status:** Unknown
 **Service ID:** 11267c52-9831-4e48-a751-654fcc1b32f2
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Flowise AI (Agents + Datastore + Flows)
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| ebb4a4e0-6770-45c1-840f-60209ace4e70 | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "11267c52-9831-4e48-a751-654fcc1b32f2",
  "name": "Flowise AI (Agents + Datastore + Flows)",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "ebb4a4e0-6770-45c1-840f-60209ace4e70",
          "serviceId": "11267c52-9831-4e48-a751-654fcc1b32f2",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "4450fd59-c073-48f0-8968-3d213caef549",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "chungyau97",
              "commitHash": "6baec938601da61cb709e4ec61af2fcf058ba71f",
              "commitMessage": "Optimize export import (#4795)\n\n* feat: add saveBatch and optimize duplication id handling\n\n* feat: improve lookup performance by using Set",
              "fileServiceManifest": {},
              "isPublicRepoDeploy": true,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "propertyFileMapping": {},
              "reason": "deploy",
              "repo": "FlowiseAI/Flowise",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "DOCKERFILE",
                  "dockerfilePath": "Dockerfile",
                  "nixpacksConfigPath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "FlowiseAI/Flowise",
            "image": null
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "4450fd59-c073-48f0-8968-3d213caef549",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "chungyau97",
    "commitHash": "6baec938601da61cb709e4ec61af2fcf058ba71f",
    "commitMessage": "Optimize export import (#4795)\n\n* feat: add saveBatch and optimize duplication id handling\n\n* feat: improve lookup performance by using Set",
    "fileServiceManifest": {},
    "isPublicRepoDeploy": true,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "propertyFileMapping": {},
    "reason": "deploy",
    "repo": "FlowiseAI/Flowise",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "DOCKERFILE",
        "dockerfilePath": "Dockerfile",
        "nixpacksConfigPath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Flowise AI (Agents + Datastore + Flows)

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `11267c52-9831-4e48-a751-654fcc1b32f2`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
