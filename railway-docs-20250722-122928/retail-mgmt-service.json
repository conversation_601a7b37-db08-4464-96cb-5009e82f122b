{"id": "ae24a0e5-3bc5-4c98-a3d4-1177262e1217", "name": "retail-mgmt", "serviceInstances": {"edges": [{"node": {"id": "ce4c1c6d-2081-4729-afaf-f7589e081581", "serviceId": "ae24a0e5-3bc5-4c98-a3d4-1177262e1217", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "8a91d03c-ba43-43f9-a4f7-240650c8b273", "meta": {"branch": "main", "buildOnly": false, "commitAuthor": "bryce27", "commitHash": "9e369b30593d03a2d625d9d6345990d4a91e1570", "commitMessage": "strip duplicates from final images array", "configFile": "railway.toml", "fileServiceManifest": {"build": {"buildCommand": "npm install", "builder": "NIXPACKS"}, "deploy": {"healthcheckPath": "/health", "healthcheckTimeout": 100, "restartPolicyType": "ON_FAILURE", "startCommand": "npm start"}}, "githubDeploymentId": **********, "ignoreWatchPatterns": true, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {"build.buildCommand": "$.build.buildCommand", "build.builder": "$.build.builder", "deploy.envs": "$.deploy.envs", "deploy.healthcheckPath": "$.deploy.healthcheckPath", "deploy.healthcheckTimeout": "$.deploy.healthcheckTimeout", "deploy.restartPolicyType": "$.deploy.restartPolicyType", "deploy.startCommand": "$.deploy.startCommand"}, "reason": "migrate", "repo": "AdVid-ai/retail-mgmt", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": "npm install", "buildEnvironment": null, "builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "nixpacksConfigPath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckTimeout": 100, "limitOverride": null, "multiRegionConfig": {"us-west2": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "npm start"}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/retail-mgmt", "image": null}}}]}}