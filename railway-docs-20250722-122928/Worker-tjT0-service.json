{"id": "8d6fd41f-bfca-4b9e-9a78-d51987db8592", "name": "Worker-tjT0", "serviceInstances": {"edges": [{"node": {"id": "c1411af9-44b4-4241-969d-f3b3aa2e1e8f", "serviceId": "8d6fd41f-bfca-4b9e-9a78-d51987db8592", "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42", "latestDeployment": {"canRedeploy": true, "id": "e436027d-02d2-4995-a17c-aef0823562ae", "meta": {"image": "n8nio/n8n", "logsV2": true, "patchId": "f883665b-8007-49cc-b888-df564b6c20aa", "queuedReason": "Waiting for dependencies to deploy", "reason": "deploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "n8n worker"}}, "volumeMounts": []}}, "source": {"repo": null, "image": "n8nio/n8n"}}}]}}