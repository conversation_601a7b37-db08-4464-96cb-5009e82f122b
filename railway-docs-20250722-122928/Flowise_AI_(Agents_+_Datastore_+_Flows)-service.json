{"id": "11267c52-9831-4e48-a751-654fcc1b32f2", "name": "Flowise AI (Agents + Datastore + Flows)", "serviceInstances": {"edges": [{"node": {"id": "ebb4a4e0-6770-45c1-840f-60209ace4e70", "serviceId": "11267c52-9831-4e48-a751-654fcc1b32f2", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "4450fd59-c073-48f0-8968-3d213caef549", "meta": {"branch": "main", "buildOnly": false, "commitAuthor": "chungyau97", "commitHash": "6baec938601da61cb709e4ec61af2fcf058ba71f", "commitMessage": "Optimize export import (#4795)\n\n* feat: add saveBatch and optimize duplication id handling\n\n* feat: improve lookup performance by using Set", "fileServiceManifest": {}, "isPublicRepoDeploy": true, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {}, "reason": "deploy", "repo": "FlowiseAI/Flowise", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "nixpacksConfigPath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "FlowiseAI/Flowise", "image": null}}}]}}