{"id": "0c861b92-6665-48f2-a392-8c1942ba89e3", "name": "adfury-app", "serviceInstances": {"edges": [{"node": {"id": "ab39dd90-b997-4a37-be1b-6246232532ff", "serviceId": "0c861b92-6665-48f2-a392-8c1942ba89e3", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "fbc0d7fe-a715-4b19-b951-3ed80ca55f98", "meta": {"branch": "main", "buildOnly": false, "commitAuthor": "alpendergrast", "commitHash": "b08ca1993fec98ea936e3c7db9cc1b2266bb6836", "commitMessage": "fix advertiser id detection from builder", "fileServiceManifest": {}, "githubDeploymentId": **********, "logsV2": true, "nixpacksProviders": ["node"], "propertyFileMapping": {}, "reason": "deploy", "repo": "AdVid-ai/advid-app", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/advid-app", "image": null}}}]}}