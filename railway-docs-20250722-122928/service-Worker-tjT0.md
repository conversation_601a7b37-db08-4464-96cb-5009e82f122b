# Service: Worker-tjT0

**Generated:** Tue Jul 22 12:29:30 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Worker-tjT0
   **Status:** Unknown
 **Service ID:** 8d6fd41f-bfca-4b9e-9a78-d51987db8592
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Worker-tjT0
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| c1411af9-44b4-4241-969d-f3b3aa2e1e8f | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |


## Service Configuration

 ```json
 {
  "id": "8d6fd41f-bfca-4b9e-9a78-d51987db8592",
  "name": "Worker-tjT0",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "c1411af9-44b4-4241-969d-f3b3aa2e1e8f",
          "serviceId": "8d6fd41f-bfca-4b9e-9a78-d51987db8592",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "e436027d-02d2-4995-a17c-aef0823562ae",
            "meta": {
              "image": "n8nio/n8n",
              "logsV2": true,
              "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
              "queuedReason": "Waiting for dependencies to deploy",
              "reason": "deploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckPath": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": "n8n worker"
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": null,
            "image": "n8nio/n8n"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "e436027d-02d2-4995-a17c-aef0823562ae",
  "meta": {
    "image": "n8nio/n8n",
    "logsV2": true,
    "patchId": "f883665b-8007-49cc-b888-df564b6c20aa",
    "queuedReason": "Waiting for dependencies to deploy",
    "reason": "deploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckPath": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": "n8n worker"
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Worker-tjT0

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `8d6fd41f-bfca-4b9e-9a78-d51987db8592`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
