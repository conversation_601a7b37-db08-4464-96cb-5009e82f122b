# Service: Redis

**Generated:** Tue Jul 22 12:29:31 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Redis
   **Status:** Unknown
 **Service ID:** bfaaa506-b8ae-4abf-aa10-eee051b96dc0
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Redis
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| 775ebe1d-1914-4b46-9be2-4d4ce11c36da | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "bfaaa506-b8ae-4abf-aa10-eee051b96dc0",
  "name": "<PERSON><PERSON>",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "775ebe1d-1914-4b46-9be2-4d4ce11c36da",
          "serviceId": "bfaaa506-b8ae-4abf-aa10-eee051b96dc0",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "57b83c66-c020-4068-9831-9522dcd106b3",
            "meta": {
              "ignoreWatchPatterns": true,
              "image": "bitnami/redis",
              "logsV2": true,
              "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
              "reason": "redeploy",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/bitnami"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "bitnami/redis"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "57b83c66-c020-4068-9831-9522dcd106b3",
  "meta": {
    "ignoreWatchPatterns": true,
    "image": "bitnami/redis",
    "logsV2": true,
    "patchId": "c0ee73b8-82fd-4489-9d3c-8fbba21fe3c3",
    "reason": "redeploy",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/bitnami"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Redis

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `bfaaa506-b8ae-4abf-aa10-eee051b96dc0`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
