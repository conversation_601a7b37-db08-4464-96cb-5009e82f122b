{"id": "a7d244ed-0030-4e59-b62e-14e48505bca1", "name": "<PERSON><PERSON><PERSON>", "serviceInstances": {"edges": [{"node": {"id": "0235ace9-06be-477d-b6f6-937c0fce801d", "serviceId": "a7d244ed-0030-4e59-b62e-14e48505bca1", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "b11743ed-9d84-4cf6-b5a2-974df9471517", "meta": {"image": "quay.io/soketi/soketi:1.6.1-16-debian", "logsV2": true, "patchId": "bc5748a4-c7cc-4ee7-8248-0a399835d50a", "reason": "deploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckPath": "/ready", "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "/bin/sh -c \"export AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1; node /app/bin/server.js start\""}}, "volumeMounts": []}}, "source": {"repo": null, "image": "quay.io/soketi/soketi:1.6.1-16-debian"}}}]}}