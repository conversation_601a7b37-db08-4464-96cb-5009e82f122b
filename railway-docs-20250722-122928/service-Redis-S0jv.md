# Service: Redis-S0jv

**Generated:** Tue Jul 22 12:29:31 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** Redis-S0jv
   **Status:** Unknown
 **Service ID:** ef63a6d7-63ac-4802-a7ec-7b02a707bd9c
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service Redis-S0jv
railway variables
```

## Service Instances

**Total Instances:** 2

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| a4b8a2a3-b67b-4caf-86a2-df83999a0858 | Unknown | d3f99cee-ca48-4eac-8649-4cd70cf17a42 |
| c043b5f3-74c1-43eb-ae03-a7b65b97b129 | Unknown | 89aed380-ce64-4a52-b022-20e17d3a5562 |


## Service Configuration

 ```json
 {
  "id": "ef63a6d7-63ac-4802-a7ec-7b02a707bd9c",
  "name": "Redis-S0jv",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "a4b8a2a3-b67b-4caf-86a2-df83999a0858",
          "serviceId": "ef63a6d7-63ac-4802-a7ec-7b02a707bd9c",
          "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "b0d99880-45e2-459e-943e-1209898006e6",
            "meta": {
              "ignoreWatchPatterns": true,
              "image": "bitnami/redis:7.2.5",
              "logsV2": true,
              "patchId": "fad79739-7b39-4a53-ac5d-a81f09688f68",
              "reason": "migrate",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/bitnami"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "bitnami/redis:7.2.5"
          }
        }
      },
      {
        "node": {
          "id": "c043b5f3-74c1-43eb-ae03-a7b65b97b129",
          "serviceId": "ef63a6d7-63ac-4802-a7ec-7b02a707bd9c",
          "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "5a8b9289-572f-455a-ae40-68a858452a13",
            "meta": {
              "ignoreWatchPatterns": true,
              "image": "bitnami/redis:7.2.5",
              "logsV2": true,
              "patchId": "f2cd465e-5cb5-4286-a583-2afa84ae59c9",
              "reason": "migrate",
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "drainingSeconds": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-east4-eqdc4a": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "overlapSeconds": null,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "requiredMountPath": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": [
                "/bitnami"
              ]
            }
          },
          "source": {
            "repo": null,
            "image": "bitnami/redis:7.2.5"
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "b0d99880-45e2-459e-943e-1209898006e6",
  "meta": {
    "ignoreWatchPatterns": true,
    "image": "bitnami/redis:7.2.5",
    "logsV2": true,
    "patchId": "fad79739-7b39-4a53-ac5d-a81f09688f68",
    "reason": "migrate",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/bitnami"
    ]
  }
}
{
  "canRedeploy": true,
  "id": "5a8b9289-572f-455a-ae40-68a858452a13",
  "meta": {
    "ignoreWatchPatterns": true,
    "image": "bitnami/redis:7.2.5",
    "logsV2": true,
    "patchId": "f2cd465e-5cb5-4286-a583-2afa84ae59c9",
    "reason": "migrate",
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "drainingSeconds": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-east4-eqdc4a": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "overlapSeconds": null,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "requiredMountPath": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": [
      "/bitnami"
    ]
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service Redis-S0jv

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `ef63a6d7-63ac-4802-a7ec-7b02a707bd9c`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
