{"id": "90b1e1f1-123c-4053-bb1e-fb7e3c9b6b88", "name": "Primary", "serviceInstances": {"edges": [{"node": {"id": "f91b943d-8bc6-48ee-bcca-e807588e1d62", "serviceId": "90b1e1f1-123c-4053-bb1e-fb7e3c9b6b88", "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8", "latestDeployment": {"canRedeploy": true, "id": "98d96ec3-414a-4369-b373-3922529c8061", "meta": {"image": "n8nio/n8n", "logsV2": true, "patchId": "d031b9af-dcd6-4d2c-8403-60ff11f0846f", "reason": "deploy", "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "drainingSeconds": null, "healthcheckPath": "/healthz", "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-east4-eqdc4a": {"numReplicas": 1}}, "numReplicas": 1, "overlapSeconds": null, "preDeployCommand": null, "region": null, "registryCredentials": null, "requiredMountPath": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": "n8n start"}}, "volumeMounts": []}}, "source": {"repo": null, "image": "n8nio/n8n"}}}]}}