# Service: pdf-scraper

**Generated:** Tue Jul 22 12:29:30 CDT 2025
**Project:** advid
**Environment:** production

## Service Overview

**Name:** pdf-scraper
   **Status:** Unknown
 **Service ID:** 73790433-b9ca-4a8a-854e-9faf553990eb
 **Created:** Unknown
 **Updated:** Unknown

## Environment Variables

*Environment variables require interactive service linking. To view variables for this service:*

```bash
railway service pdf-scraper
railway variables
```

## Service Instances

**Total Instances:** 1

| Instance ID | Status | Environment |
|-------------|--------|-------------|
| c0850753-8c68-4690-80b8-170aaf9ce876 | Unknown | 52147228-dbf8-4586-9d28-59509668b4a8 |


## Service Configuration

 ```json
 {
  "id": "73790433-b9ca-4a8a-854e-9faf553990eb",
  "name": "pdf-scraper",
  "serviceInstances": {
    "edges": [
      {
        "node": {
          "id": "c0850753-8c68-4690-80b8-170aaf9ce876",
          "serviceId": "73790433-b9ca-4a8a-854e-9faf553990eb",
          "environmentId": "52147228-dbf8-4586-9d28-59509668b4a8",
          "latestDeployment": {
            "canRedeploy": true,
            "id": "2dace867-c6c8-42b0-92fb-572db35e6159",
            "meta": {
              "branch": "main",
              "buildOnly": false,
              "commitAuthor": "JRippelmeyer",
              "commitHash": "56c54fc5229b76b6aa560fe763ca338e940c3b0d",
              "commitMessage": "Change any type",
              "fileServiceManifest": {},
              "githubDeploymentId": **********,
              "ignoreWatchPatterns": true,
              "isPublicRepoDeploy": false,
              "logsV2": true,
              "nixpacksProviders": [
                "node"
              ],
              "patchId": "aece8ff4-e158-40d2-a2ed-a8b0ca760322",
              "propertyFileMapping": {},
              "reason": "migrate",
              "repo": "AdVid-ai/pdf-scraper",
              "rootDirectory": null,
              "runtime": "V2",
              "serviceManifest": {
                "build": {
                  "buildCommand": null,
                  "buildEnvironment": null,
                  "builder": "NIXPACKS",
                  "dockerfilePath": null,
                  "nixpacksPlan": null,
                  "watchPatterns": []
                },
                "deploy": {
                  "cronSchedule": null,
                  "healthcheckTimeout": null,
                  "limitOverride": null,
                  "multiRegionConfig": {
                    "us-west2": {
                      "numReplicas": 1
                    }
                  },
                  "numReplicas": 1,
                  "preDeployCommand": null,
                  "region": null,
                  "registryCredentials": null,
                  "restartPolicyMaxRetries": 10,
                  "restartPolicyType": "ON_FAILURE",
                  "runtime": "V2",
                  "sleepApplication": false,
                  "startCommand": null
                }
              },
              "volumeMounts": []
            }
          },
          "source": {
            "repo": "AdVid-ai/pdf-scraper",
            "image": null
          }
        }
      }
    ]
  }
}
 ```

## Deployment Information

**Latest Deployment:**
```json
{
  "canRedeploy": true,
  "id": "2dace867-c6c8-42b0-92fb-572db35e6159",
  "meta": {
    "branch": "main",
    "buildOnly": false,
    "commitAuthor": "JRippelmeyer",
    "commitHash": "56c54fc5229b76b6aa560fe763ca338e940c3b0d",
    "commitMessage": "Change any type",
    "fileServiceManifest": {},
    "githubDeploymentId": **********,
    "ignoreWatchPatterns": true,
    "isPublicRepoDeploy": false,
    "logsV2": true,
    "nixpacksProviders": [
      "node"
    ],
    "patchId": "aece8ff4-e158-40d2-a2ed-a8b0ca760322",
    "propertyFileMapping": {},
    "reason": "migrate",
    "repo": "AdVid-ai/pdf-scraper",
    "rootDirectory": null,
    "runtime": "V2",
    "serviceManifest": {
      "build": {
        "buildCommand": null,
        "buildEnvironment": null,
        "builder": "NIXPACKS",
        "dockerfilePath": null,
        "nixpacksPlan": null,
        "watchPatterns": []
      },
      "deploy": {
        "cronSchedule": null,
        "healthcheckTimeout": null,
        "limitOverride": null,
        "multiRegionConfig": {
          "us-west2": {
            "numReplicas": 1
          }
        },
        "numReplicas": 1,
        "preDeployCommand": null,
        "region": null,
        "registryCredentials": null,
        "restartPolicyMaxRetries": 10,
        "restartPolicyType": "ON_FAILURE",
        "runtime": "V2",
        "sleepApplication": false,
        "startCommand": null
      }
    },
    "volumeMounts": []
  }
}
```


## Commands for This Service

```bash
# Link to this service and view its details
railway service pdf-scraper

# After linking, view variables
railway variables

# View logs for this service
railway logs

# Deploy this service (from its source directory)
railway up

# SSH into this service
railway ssh

# View service status
railway status

# View environment-specific variables
railway variables --environment production
```

## Additional Service Information

**Service ID:** `73790433-b9ca-4a8a-854e-9faf553990eb`
**Direct Railway Dashboard:** [View in Dashboard](https://railway.app/dashboard)

---
*Generated by Railway Documentation Script*
