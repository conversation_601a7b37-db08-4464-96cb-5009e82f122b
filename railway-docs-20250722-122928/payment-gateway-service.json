{"id": "3360d2c6-78de-4fb3-bf80-bfee41f0685a", "name": "payment-gateway", "serviceInstances": {"edges": [{"node": {"id": "62747196-fd40-4e72-bbfc-a19e356ee0a1", "serviceId": "3360d2c6-78de-4fb3-bf80-bfee41f0685a", "environmentId": "d3f99cee-ca48-4eac-8649-4cd70cf17a42", "latestDeployment": {"canRedeploy": true, "id": "3814227e-e6d6-46d8-9f3d-3092b532bae9", "meta": {"branch": "staging", "buildOnly": false, "commitAuthor": "", "commitHash": "6d34742f84f1c8f220adcf58e134c57d6cacf6e5", "commitMessage": "add stripe routes", "fileServiceManifest": {}, "githubDeploymentId": **********, "ignoreWatchPatterns": true, "isPublicRepoDeploy": false, "logsV2": true, "nixpacksProviders": ["node"], "patchId": "fad79739-7b39-4a53-ac5d-a81f09688f68", "propertyFileMapping": {}, "reason": "migrate", "repo": "AdVid-ai/payment-gateway", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-west2": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/payment-gateway", "image": null}}}, {"node": {"id": "9aa2967b-94eb-4b5c-94f5-670fa2b7b82a", "serviceId": "3360d2c6-78de-4fb3-bf80-bfee41f0685a", "environmentId": "89aed380-ce64-4a52-b022-20e17d3a5562", "latestDeployment": {"canRedeploy": true, "id": "2d7ed75a-6a59-44e5-a467-1d00ba35ad46", "meta": {"branch": "staging", "buildOnly": false, "commitAuthor": "", "commitHash": "6d34742f84f1c8f220adcf58e134c57d6cacf6e5", "commitMessage": "add stripe routes", "fileServiceManifest": {}, "githubDeploymentId": **********, "ignoreWatchPatterns": true, "isPublicRepoDeploy": false, "logsV2": true, "nixpacksProviders": ["node"], "patchId": "23fbae35-4f89-47d2-84e0-204c977e18c3", "propertyFileMapping": {}, "reason": "migrate", "repo": "AdVid-ai/payment-gateway", "rootDirectory": null, "runtime": "V2", "serviceManifest": {"build": {"buildCommand": null, "buildEnvironment": null, "builder": "NIXPACKS", "dockerfilePath": null, "nixpacksPlan": null, "watchPatterns": []}, "deploy": {"cronSchedule": null, "healthcheckTimeout": null, "limitOverride": null, "multiRegionConfig": {"us-west2": {"numReplicas": 1}}, "numReplicas": 1, "preDeployCommand": null, "region": null, "registryCredentials": null, "restartPolicyMaxRetries": 10, "restartPolicyType": "ON_FAILURE", "runtime": "V2", "sleepApplication": false, "startCommand": null}}, "volumeMounts": []}}, "source": {"repo": "AdVid-ai/payment-gateway", "image": null}}}]}}