import {
  getAllNotifications,
  getNotificationById,
  getNotificationsByUserId,
  createNotification,
  updateNotificationStatus,
  deleteNotification,
  getUserNotifications,
  markNotificationsAsRead
} from './notifications.controllers.js'
import { notificationSchema } from './notifications.schemas.js'

export default async function notificationRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Mark notifications as read
  fastify.post('/mark-as-read', {
    onRequest: [fastify.verifyAuth],
    handler: markNotificationsAsRead
  })

  // Get all notifications
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllNotifications
  })

  // Get notification by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: getNotificationById
  })

  // Create notification
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    schema: notificationSchema.create,
    handler: createNotification
  })

  // Update notification status
  fastify.put('/:id/status', {
    onRequest: [fastify.verifyAuth],
    schema: notificationSchema.updateStatus,
    handler: updateNotificationStatus
  })

  // Delete notification
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: deleteNotification
  })

  // Get user notifications
  fastify.get('/user/:userId', {
    onRequest: [fastify.verifyAuth],
    handler: getUserNotifications
  })
} 