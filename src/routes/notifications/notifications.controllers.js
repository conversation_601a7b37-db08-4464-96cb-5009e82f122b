import { eq, inArray, or, desc } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { notifications } from '../../db/schema/notifications.ts'
import { users } from '../../db/schema/users.ts'
import { userAccounts } from '../../db/schema/userAccounts.ts'

export async function getAllNotifications(request, reply) {
  try {
    const allNotifications = await db.select().from(notifications)
    return reply.send(allNotifications)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch notifications' })
  }
}

export async function getNotificationById(request, reply) {
  try {
    const { id } = request.params

    const [notification] = await db
      .select()
      .from(notifications)
      .where(eq(notifications.id, id))
      .limit(1)

    if (!notification) {
      return reply.code(404).send({ error: 'Notification not found' })
    }

    return reply.send(notification)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch notification' })
  }
}

export async function getNotificationsByUserId(request, reply) {
  try {
    const { userId } = request.params

    const userNotifications = await db
      .select()
      .from(notifications)
      .where(eq(notifications.userId, userId))

    return reply.send(userNotifications)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch user notifications' })
  }
}

export async function createNotification(request, reply) {
  try {
    const { message, userId, accountId } = request.body

    const [notification] = await db
      .insert(notifications)
      .values({
        message,
        userId,
        accountId,
        status: 'unread',
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    return reply.code(201).send(notification)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create notification' })
  }
}

export async function updateNotificationStatus(request, reply) {
  try {
    const { id } = request.params
    const { status } = request.body

    const [notification] = await db
      .update(notifications)
      .set({
        status,
        updatedAt: new Date()
      })
      .where(eq(notifications.id, id))
      .returning()

    if (!notification) {
      return reply.code(404).send({ error: 'Notification not found' })
    }

    return reply.send(notification)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update notification' })
  }
}

export async function deleteNotification(request, reply) {
  try {
    const { id } = request.params

    const [deletedNotification] = await db
      .delete(notifications)
      .where(eq(notifications.id, id))
      .returning()

    if (!deletedNotification) {
      return reply.code(404).send({ error: 'Notification not found' })
    }

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete notification' })
  }
}

export async function getUserNotifications(request, reply) {
    try {
      const { userId } = request.params

      // Get user details
      const [user] = await db
        .select({
          id: users.id
        })
        .from(users)
        .where(eq(users.id, userId))
        .limit(1)

      if (!user) {
        return reply.code(404).send({ error: 'User not found' })
      }

      // Get all accounts associated with the user
      const userAccountRelations = await db
        .select({
          accountId: userAccounts.accountId
        })
        .from(userAccounts)
        .where(eq(userAccounts.userId, userId))

      const accountIds = userAccountRelations.map(rel => rel.accountId)

      // Get user's personal notifications and notifications for accounts they're associated with
      let whereConditions = or(
        eq(notifications.userId, userId),
        accountIds.length > 0 ? inArray(notifications.accountId, accountIds) : undefined
      );

      // Get notifications
      const userNotifications = await db
        .select()
        .from(notifications)
        .where(whereConditions)
        .orderBy(desc(notifications.createdAt))

      return reply.send(userNotifications)
    } catch (error) {
      request.log.error(error)
      return reply.code(500).send({ error: 'Failed to fetch notifications' })
    }
}

export async function markNotificationsAsRead(request, reply) {
  try {
    const { notificationIds } = request.body

    if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
      return reply.code(400).send({ error: 'Invalid notification IDs provided' })
    }

    // Filter out any invalid/undefined IDs
    const validNotificationIds = notificationIds.filter(Boolean)

    if (validNotificationIds.length === 0) {
      return reply.code(400).send({ error: 'No valid notification IDs provided' })
    }

    // Log the IDs we're trying to update
    console.log('Attempting to mark notifications as read:', validNotificationIds)

    // First verify these notifications exist
    const existingNotifications = await db
      .select({ id: notifications.id })
      .from(notifications)
      .where(inArray(notifications.id, validNotificationIds))

    console.log('Found notifications:', existingNotifications.length)

    if (existingNotifications.length === 0) {
      return reply.code(404).send({ error: 'No notifications found with the provided IDs' })
    }

    const updatedNotifications = await db
      .update(notifications)
      .set({
        status: 'read',
        updatedAt: new Date()
      })
      .where(inArray(notifications.id, validNotificationIds))
      .returning()

    return reply.send(updatedNotifications)
  } catch (error) {
    console.error('Error:', error)
    return reply.code(500).send({ error: 'Failed to mark notifications as read' })
  }
}