export const notificationSchema = {
  create: {
    body: {
      type: 'object',
      required: ['message'],
      properties: {
        message: {
          type: 'object',
          additionalProperties: true
        },
        user_id: {
          type: 'string',
          format: 'uuid'
        },
        account_id: {
          type: 'string',
          format: 'uuid'
        }
      }
    }
  },
  updateStatus: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      required: ['status'],
      properties: {
        status: {
          type: 'string',
          enum: ['read', 'unread']
        }
      }
    }
  }
}