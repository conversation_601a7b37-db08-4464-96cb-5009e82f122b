import {
  getAll<PERSON>eywordGenerations,
  getK<PERSON>wordGenerationById,
  create<PERSON>eywordGeneration,
  updateKeywordGeneration,
  deleteKeywordGeneration
} from './keywordGenerations.controllers.js'

export default async function keywordGenerationRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all keyword generations
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllKeywordGenerations
  })

  // Get keyword generation by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: getKeywordGenerationById
  })

  // Create keyword generation
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    handler: createKeywordGeneration
  })

  // Update keyword generation
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: update<PERSON><PERSON>wordGeneration
  })

  // Delete keyword generation
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: deleteKeywordGeneration
  })
} 