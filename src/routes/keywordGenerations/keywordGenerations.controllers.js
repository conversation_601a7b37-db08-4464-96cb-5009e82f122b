import { eq } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { keywordGenerations } from '../../db/schema/keywordGenerations.ts'

export async function getAllKeywordGenerations(request, reply) {
  try {
    const allKeywordGenerations = await db.select().from(keywordGenerations)
    return reply.send(allKeywordGenerations)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch keyword generations' })
  }
}

export async function getKeywordGenerationById(request, reply) {
  try {
    const { id } = request.params
    
    const [keywordGeneration] = await db
      .select()
      .from(keywordGenerations)
      .where(eq(keywordGenerations.id, id))
      .limit(1)

    if (!keywordGeneration) {
      return reply.code(404).send({ error: 'Keyword generation not found' })
    }

    return reply.send(keywordGeneration)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch keyword generation' })
  }
}

export async function createKeywordGeneration(request, reply) {
  try {
    const { productId, prompt, model, settings } = request.body

    const [keywordGeneration] = await db
      .insert(keywordGenerations)
      .values({
        productId,
        prompt,
        model,
        settings,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    return reply.code(201).send(keywordGeneration)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create keyword generation' })
  }
}

export async function updateKeywordGeneration(request, reply) {
  try {
    const { id } = request.params
    const updateData = {
      ...request.body
    }

    const [keywordGeneration] = await db
      .update(keywordGenerations)
      .set(updateData)
      .where(eq(keywordGenerations.id, id))
      .returning()

    if (!keywordGeneration) {
      return reply.code(404).send({ error: 'Keyword generation not found' })
    }

    return reply.send(keywordGeneration)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update keyword generation' })
  }
}

export async function deleteKeywordGeneration(request, reply) {
  try {
    const { id } = request.params

    const [deletedKeywordGeneration] = await db
      .delete(keywordGenerations)
      .where(eq(keywordGenerations.id, id))
      .returning()

    if (!deletedKeywordGeneration) {
      return reply.code(404).send({ error: 'Keyword generation not found' })
    }

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete keyword generation' })
  }
} 