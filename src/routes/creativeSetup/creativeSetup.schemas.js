export const creativeSetupSchema = {
  getUserAccessibleProjects: {
    querystring: {
      type: 'object',
      properties: {
        page: { type: 'integer', minimum: 1, default: 1 },
        limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                brandId: { type: ['string', 'null'], format: 'uuid' },
                name: { type: 'string' },
                wmCampaigns: { type: 'object' },
                retailerId: { type: 'string', format: 'uuid' },
                currentStep: { type: 'integer' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
                creator: {
                  type: ['object', 'null'],
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    name: { type: 'string' },
                    email: { type: 'string', format: 'email' }
                  }
                },
                brand: {
                  type: ['object', 'null'],
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    name: { type: 'string' }
                  }
                },
                retailer: {
                  type: ['object', 'null'],
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    name: { type: 'string' },
                    slug: { type: 'string' },
                    logoUrl: { type: ['string', 'null'] },
                    url: { type: ['string', 'null'] }
                  }
                },
                brandGuidelines: {
                  type: ['object', 'null'],
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    label: { type: ['string', 'null'] },
                    fileName: { type: 'string' },
                    assetUrl: { type: 'string' },
                    status: { type: 'string' },
                    uploadedBy: { type: ['string', 'null'], format: 'uuid' },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' }
                  }
                }
              }
            }
          },
          pagination: {
            type: 'object',
            properties: {
              total: { type: 'integer' },
              page: { type: 'integer' },
              limit: { type: 'integer' },
              totalPages: { type: 'integer' }
            }
          }
        }
      },
      401: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      },
      500: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      }
    }
  },

  getUserBrandsByRetailer: {
    params: {
      type: 'object',
      required: ['retailerId'],
      properties: {
        retailerId: { type: 'string', format: 'uuid' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                name: { type: 'string' },
                description: { type: ['string', 'null'] },
                colors: { 
                  type: ['array', 'null'],
                  items: { type: 'string' }
                },
                logoAssetUrl: { type: ['string', 'null'] },
                productCount: { type: ['integer', 'null'] },
                userRole: { type: 'string' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
                retailer: {
                  type: 'object',
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    name: { type: 'string' },
                    slug: { type: 'string' },
                    logoUrl: { type: ['string', 'null'] },
                    url: { type: ['string', 'null'] }
                  }
                },
                brandGuidelines: {
                  type: ['object', 'null'],
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    label: { type: ['string', 'null'] },
                    fileName: { type: 'string' },
                    assetUrl: { type: 'string' },
                    status: { type: 'string' },
                    uploadedBy: { type: ['string', 'null'], format: 'uuid' },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' }
                  }
                }
              }
            }
          },
          total: { type: 'integer' },
          retailerId: { type: 'string', format: 'uuid' },
          userId: { type: 'string', format: 'uuid' }
        }
      },
      400: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      },
      401: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      },
      500: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      }
    }
  },

  getUserProductsByBrand: {
    params: {
      type: 'object',
      properties: {
        brandId: { type: 'string', format: 'uuid' }
      }
    },
    querystring: {
      type: 'object',
      properties: {
        page: { type: 'integer', minimum: 1, default: 1 },
        limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                productId: { type: 'string' },
                productTitle: { type: 'string' },
                productType: { type: ['string', 'null'] },
                category: { type: ['string', 'null'] },
                thumbnailUrl: { type: ['string', 'null'] },
                shortDescription: { type: ['string', 'null'] },
                longDescription: { type: ['string', 'null'] },
                genAiDescription: { type: ['string', 'null'] },
                specifications: { 
                  type: 'array',
                  items: { type: 'object' }
                },
                productHighlights: { 
                  type: 'array',
                  items: { type: 'object' }
                },
                classType: { type: ['string', 'null'] },
                upc: { type: ['string', 'null'] },
                images: { 
                  type: 'array',
                  items: { type: 'object' }
                },
                customImages: { 
                  type: 'array',
                  items: { type: 'object' }
                },
                pdpSummary: { type: ['string', 'null'] },
                brand: {
                  type: ['object', 'null'],
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    name: { type: 'string' },
                    description: { type: ['string', 'null'] },
                    logoAssetUrl: { type: ['string', 'null'] }
                  }
                },
                account: {
                  type: ['object', 'null'],
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    name: { type: 'string' }
                  }
                }
              }
            }
          },
          pagination: {
            type: 'object',
            properties: {
              total: { type: 'integer' },
              page: { type: 'integer' },
              limit: { type: 'integer' },
              totalPages: { type: 'integer' }
            }
          },
          brandId: { type: ['string', 'null'], format: 'uuid' },
          userBrandIds: { 
            type: 'array',
            items: { type: 'string', format: 'uuid' }
          },
          userId: { type: 'string', format: 'uuid' }
        }
      },
      401: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      },
      403: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      },
      500: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      }
    }
  }
} 