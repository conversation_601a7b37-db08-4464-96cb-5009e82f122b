import { db, sql } from '../../db/index.js'
import { projects } from '../../db/schema/projects.ts'
import { brands } from '../../db/schema/brands.ts'
import { users } from '../../db/schema/users.ts'
import { brandUsers } from '../../db/schema/brandUsers.ts'
import { retailers, brandRetailers } from '../../db/schema/retailers.ts'
import { brandGuidelines } from '../../db/schema/brandGuidelines.ts'
import { eq, and, desc, count, inArray, or, isNull } from 'drizzle-orm'
import { products } from '../../db/schema/products.ts'
import { accounts } from '../../db/schema/accounts.ts'
import { authService } from '../../services/authorization.service.js'

/**
 * Provides a debug summary of a user's brand assignments and accessible projects.
 *
 * Returns the user's brand assignments, total project count, count of projects without a brand, and count and details of projects accessible to the user based on their brand assignments. Responds with 401 if the user is not authenticated.
 */
export async function debugUserAccess(request, reply) {
  try {
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Get user's brand assignments
    const userBrands = await db
      .select({
        brandId: brandUsers.brandId,
        role: brandUsers.role,
        brandName: brands.name
      })
      .from(brandUsers)
      .leftJoin(brands, eq(brandUsers.brandId, brands.id))
      .where(eq(brandUsers.userId, sessionUser.id))

    // Get all projects
    const allProjects = await db
      .select({
        id: projects.id,
        name: projects.name,
        brandId: projects.brandId,
        brandName: brands.name
      })
      .from(projects)
      .leftJoin(brands, eq(projects.brandId, brands.id))

    // Get projects with null brandId
    const nullBrandProjects = allProjects.filter(p => p.brandId === null)

    // Get projects with brandId that user has access to
    const userBrandIds = userBrands.map(ub => ub.brandId)
    const accessibleBrandProjects = allProjects.filter(p => p.brandId && userBrandIds.includes(p.brandId))

    return reply.send({
      userId: sessionUser.id,
      userEmail: sessionUser.email,
      userBrands,
      allProjects: allProjects.length,
      nullBrandProjects: nullBrandProjects.length,
      accessibleBrandProjects: accessibleBrandProjects.length,
      breakdown: {
        userBrands,
        nullBrandProjects,
        accessibleBrandProjects
      }
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Debug failed' })
  }
}

/**
 * Returns a paginated list of projects that the authenticated user can access, including projects with no brand or those assigned to the user's brands.
 *
 * The response contains project details with related creator, brand, and retailer information, as well as pagination metadata. Responds with a 401 error if the user is not authenticated, or a 500 error if retrieval fails.
 */
export async function getUserAccessibleProjects(request, reply) {
  try {
    const { page = 1, limit = 10 } = request.query

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Convert page and limit to numbers and calculate offset
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Get brands accessible to this user using authorization service
    const accessibleBrands = await authService.getUserAccessibleBrands(sessionUser.id)
    const accessibleBrandIds = accessibleBrands.map(brand => brand.id)

    // Debug logging
    request.log.info(`User ${sessionUser.id} has access to ${accessibleBrandIds.length} brands:`, accessibleBrandIds)

    // Build the where condition: projects with accessible brands OR projects with null brandId created by this user
    let whereCondition
    if (accessibleBrandIds.length > 0) {
      whereCondition = or(
        // Projects with brands the user has access to
        inArray(projects.brandId, accessibleBrandIds),
        // Projects with null brandId that were created by this user
        and(
          isNull(projects.brandId),
          eq(projects.createdBy, sessionUser.id)
        )
      )
    } else {
      // User has no brand assignments, only show projects with null brandId that they created
      whereCondition = and(
        isNull(projects.brandId),
        eq(projects.createdBy, sessionUser.id)
      )
    }

    // Debug: Check all projects in database
    const allProjects = await db
      .select({ id: projects.id, brandId: projects.brandId, name: projects.name })
      .from(projects)
      .limit(10)

    request.log.info(`Total projects in database (first 10):`, allProjects)

    // Get total count for pagination
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(projects)
      .where(whereCondition)

    request.log.info(`Total accessible projects for user: ${totalCount}`)

    // Get paginated projects with related data
    const projectsData = await db
      .select({
        id: projects.id,
        brandId: projects.brandId,
        name: projects.name,
        wmCampaigns: projects.wmCampaigns,
        retailerId: projects.retailerId,
        currentStep: projects.currentStep,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        creator: {
          id: users.id,
          name: users.name,
          email: users.email
        },
        brand: {
          id: brands.id,
          name: brands.name
        },
        retailer: {
          id: retailers.id,
          name: retailers.name,
          slug: retailers.slug,
          logoUrl: retailers.logoUrl,
          url: retailers.url
        }
      })
      .from(projects)
      .leftJoin(users, eq(projects.createdBy, users.id))
      .leftJoin(brands, eq(projects.brandId, brands.id))
      .leftJoin(retailers, eq(projects.retailerId, retailers.id))
      .where(whereCondition)
      .orderBy(desc(projects.createdAt))
      .limit(limitNum)
      .offset(offset)

    return reply.send({
      data: projectsData,
      pagination: {
        total: totalCount,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(totalCount / limitNum)
      }
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch accessible projects' })
  }
}

/**
 * Provides a detailed debug report of a user's brands associated with a specific retailer, including brand assignments, retailer relationships, and brand guidelines.
 *
 * Returns comprehensive data and intermediate query results for troubleshooting brand access and relationship issues, along with execution timing and debug metadata.
 */
export async function debugGetUserBrandsByRetailer(request, reply) {
  try {
    const { retailerId } = request.params
    const startTime = Date.now()

    if (!retailerId) {
      return reply.code(400).send({ error: 'retailerId is required' })
    }

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    request.log.info(`[DEBUG] Starting getUserBrandsByRetailer for user ${sessionUser.id} and retailer ${retailerId}`)

    // Step 1: Check user's brand assignments
    const userBrandAssignments = await db
      .select({
        brandId: brandUsers.brandId,
        role: brandUsers.role,
        brandName: brands.name
      })
      .from(brandUsers)
      .leftJoin(brands, eq(brandUsers.brandId, brands.id))
      .where(eq(brandUsers.userId, sessionUser.id))

    request.log.info(`[DEBUG] User brand assignments:`, {
      count: userBrandAssignments.length,
      assignments: userBrandAssignments
    })

    // Step 2: Check all brand-retailer relationships for the specified retailer
    const brandRetailerRelationships = await db
      .select({
        brandId: brandRetailers.brandId,
        retailerId: brandRetailers.retailerId,
        brandName: brands.name,
        retailerName: retailers.name
      })
      .from(brandRetailers)
      .leftJoin(brands, eq(brandRetailers.brandId, brands.id))
      .leftJoin(retailers, eq(brandRetailers.retailerId, retailers.id))
      .where(eq(brandRetailers.retailerId, retailerId))

    request.log.info(`[DEBUG] Brand-retailer relationships for retailer ${retailerId}:`, {
      count: brandRetailerRelationships.length,
      relationships: brandRetailerRelationships
    })

    // Step 3: Get the main query result
    const userBrandsWithRetailer = await db
      .select({
        id: brands.id,
        name: brands.name,
        description: brands.description,
        colors: brands.colors,
        logoAssetUrl: brands.logoAssetUrl,
        productCount: brands.productCount,
        userRole: brandUsers.role,
        createdAt: brands.createdAt,
        updatedAt: brands.updatedAt,
        retailer: {
          id: retailers.id,
          name: retailers.name,
          slug: retailers.slug,
          logoUrl: retailers.logoUrl,
          url: retailers.url
        }
      })
      .from(brands)
      .innerJoin(brandUsers, eq(brands.id, brandUsers.brandId))
      .innerJoin(brandRetailers, eq(brands.id, brandRetailers.brandId))
      .innerJoin(retailers, eq(brandRetailers.retailerId, retailers.id))
      .where(
        and(
          eq(brandUsers.userId, sessionUser.id),
          eq(brandRetailers.retailerId, retailerId)
        )
      )
      .orderBy(brands.name)

    request.log.info(`[DEBUG] Main query result - user brands with retailer:`, {
      count: userBrandsWithRetailer.length,
      data: userBrandsWithRetailer
    })

    // Step 4: Check brand guidelines
    const brandIds = userBrandsWithRetailer.map(brand => brand.id)
    let brandGuidelinesMap = {}
    let allGuidelines = []
    let activeGuidelines = []

    if (brandIds.length > 0) {
      // Get all guidelines for debugging
      allGuidelines = await db
        .select({
          brandId: brandGuidelines.brandId,
          id: brandGuidelines.id,
          status: brandGuidelines.status,
          label: brandGuidelines.label,
          fileName: brandGuidelines.fileName,
          createdAt: brandGuidelines.createdAt
        })
        .from(brandGuidelines)
        .where(inArray(brandGuidelines.brandId, brandIds))
        .orderBy(brandGuidelines.brandId, brandGuidelines.createdAt)

      request.log.info(`[DEBUG] All brand guidelines found:`, {
        count: allGuidelines.length,
        data: allGuidelines
      })

      // Get active guidelines
      activeGuidelines = await db
        .select({
          brandId: brandGuidelines.brandId,
          id: brandGuidelines.id,
          label: brandGuidelines.label,
          fileName: brandGuidelines.fileName,
          assetUrl: brandGuidelines.assetUrl,
          status: brandGuidelines.status,
          uploadedBy: brandGuidelines.uploadedBy,
          createdAt: brandGuidelines.createdAt,
          updatedAt: brandGuidelines.updatedAt
        })
        .from(brandGuidelines)
        .where(
          and(
            inArray(brandGuidelines.brandId, brandIds),
            eq(brandGuidelines.status, 'active')
          )
        )
        .orderBy(brandGuidelines.brandId, brandGuidelines.createdAt)

      request.log.info(`[DEBUG] Active brand guidelines found:`, {
        count: activeGuidelines.length,
        data: activeGuidelines
      })

      // Create a map of brandId to first active guidelines
      activeGuidelines.forEach(guideline => {
        if (!brandGuidelinesMap[guideline.brandId]) {
          brandGuidelinesMap[guideline.brandId] = {
            id: guideline.id,
            label: guideline.label,
            fileName: guideline.fileName,
            assetUrl: guideline.assetUrl,
            status: guideline.status,
            uploadedBy: guideline.uploadedBy,
            createdAt: guideline.createdAt,
            updatedAt: guideline.updatedAt
          }
        }
      })
    }

    // Add brand guidelines to each brand
    const brandsWithGuidelines = userBrandsWithRetailer.map(brand => ({
      ...brand,
      brandGuidelines: brandGuidelinesMap[brand.id] || null
    }))

    const executionTime = Date.now() - startTime

    request.log.info(`[DEBUG] Final result summary:`, {
      executionTime: `${executionTime}ms`,
      userBrandAssignmentsCount: userBrandAssignments.length,
      brandRetailerRelationshipsCount: brandRetailerRelationships.length,
      finalResultCount: brandsWithGuidelines.length,
      allGuidelinesCount: allGuidelines.length,
      activeGuidelinesCount: activeGuidelines.length
    })

    return reply.send({
      data: brandsWithGuidelines,
      total: brandsWithGuidelines.length,
      retailerId,
      userId: sessionUser.id,
      debug: {
        executionTime: `${executionTime}ms`,
        userBrandAssignments,
        brandRetailerRelationships,
        allBrandGuidelines: allGuidelines,
        activeBrandGuidelines: activeGuidelines,
        guidelinesMap: brandGuidelinesMap,
        querySteps: [
          'Checked user brand assignments',
          'Checked brand-retailer relationships',
          'Executed main query with joins',
          'Retrieved brand guidelines',
          'Mapped guidelines to brands'
        ]
      }
    })
  } catch (error) {
    request.log.error(`[DEBUG] Error in debugGetUserBrandsByRetailer:`, error)
    return reply.code(500).send({
      error: 'Failed to fetch user brands by retailer',
      debug: {
        errorMessage: error.message,
        errorStack: error.stack
      }
    })
  }
}

/**
 * Returns all brands assigned to the authenticated user that are associated with a specified retailer, including each brand's details and its first active brand guideline if available.
 *
 * Responds with a 400 error if `retailerId` is missing, or 401 if the user is not authenticated. The response includes the list of brands with retailer and guideline information, the total count, retailer ID, and user ID.
 */
export async function getUserBrandsByRetailer(request, reply) {
  try {
    const { retailerId } = request.params

    if (!retailerId) {
      return reply.code(400).send({ error: 'retailerId is required' })
    }

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Get brands that:
    // 1. The user is assigned to (through brandUsers)
    // 2. Are associated with the specified retailer (through brandRetailers)
    const userBrandsWithRetailer = await db
      .select({
        id: brands.id,
        name: brands.name,
        description: brands.description,
        colors: brands.colors,
        logoAssetUrl: brands.logoAssetUrl,
        productCount: brands.productCount,
        userRole: brandUsers.role,
        createdAt: brands.createdAt,
        updatedAt: brands.updatedAt,
        retailer: {
          id: retailers.id,
          name: retailers.name,
          slug: retailers.slug,
          logoUrl: retailers.logoUrl,
          url: retailers.url
        }
      })
      .from(brands)
      .innerJoin(brandUsers, eq(brands.id, brandUsers.brandId))
      .innerJoin(brandRetailers, eq(brands.id, brandRetailers.brandId))
      .innerJoin(retailers, eq(brandRetailers.retailerId, retailers.id))
      .where(
        and(
          eq(brandUsers.userId, sessionUser.id),
          eq(brandRetailers.retailerId, retailerId)
        )
      )
      .orderBy(
        sql`CASE WHEN logo_asset_url IS NOT NULL THEN 0 ELSE 1 END`,
        brands.name
      )

    // Get the first active brand guidelines for each brand
    const brandIds = userBrandsWithRetailer.map(brand => brand.id)

    let brandGuidelinesMap = {}
    if (brandIds.length > 0) {
      const brandGuidelinesRecords = await db
        .select({
          brandId: brandGuidelines.brandId,
          id: brandGuidelines.id,
          label: brandGuidelines.label,
          fileName: brandGuidelines.fileName,
          assetUrl: brandGuidelines.assetUrl,
          status: brandGuidelines.status,
          uploadedBy: brandGuidelines.uploadedBy,
          createdAt: brandGuidelines.createdAt,
          updatedAt: brandGuidelines.updatedAt
        })
        .from(brandGuidelines)
        .where(
          and(
            inArray(brandGuidelines.brandId, brandIds),
            eq(brandGuidelines.status, 'active')
          )
        )
        .orderBy(brandGuidelines.brandId, brandGuidelines.createdAt)

      // Create a map of brandId to first active guidelines
      brandGuidelinesRecords.forEach(guideline => {
        if (!brandGuidelinesMap[guideline.brandId]) {
          brandGuidelinesMap[guideline.brandId] = {
            id: guideline.id,
            label: guideline.label,
            fileName: guideline.fileName,
            assetUrl: guideline.assetUrl,
            status: guideline.status,
            uploadedBy: guideline.uploadedBy,
            createdAt: guideline.createdAt,
            updatedAt: guideline.updatedAt
          }
        }
      })
    }

    // Add brand guidelines to each brand
    const brandsWithGuidelines = userBrandsWithRetailer.map(brand => ({
      ...brand,
      brandGuidelines: brandGuidelinesMap[brand.id] || null
    }))

    return reply.send({
      data: brandsWithGuidelines,
      total: brandsWithGuidelines.length,
      retailerId,
      userId: sessionUser.id
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch user brands by retailer' })
  }
}

/**
 * Returns a paginated list of products for a specific brand or all brands assigned to the authenticated user.
 *
 * If a brand ID is provided, only products for that brand are returned after verifying the user's access. Otherwise, products from all brands the user can access are included. The response contains product details, associated brand and account information, pagination metadata, the queried brand ID (if any), the user's accessible brand IDs, and the user ID.
 */
export async function getUserProductsByBrand(request, reply) {
  try {
    const { brandId } = request.params
    const { page = 1, limit = 10 } = request.query

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Convert page and limit to numbers and calculate offset
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Get all brand IDs that the user has access to
    const userBrands = await db
      .select({ brandId: brandUsers.brandId })
      .from(brandUsers)
      .where(eq(brandUsers.userId, sessionUser.id))

    const userBrandIds = userBrands.map(ub => ub.brandId)

    if (userBrandIds.length === 0) {
      return reply.send({
        data: [],
        pagination: {
          total: 0,
          page: pageNum,
          limit: limitNum,
          totalPages: 0
        },
        message: 'User has no access to any brands'
      })
    }

    // Build the where condition based on whether brandId is provided
    let whereCondition
    if (brandId) {
      // Verify user has access to the specific brand
      if (!userBrandIds.includes(brandId)) {
        return reply.code(403).send({
          error: 'Access denied - User is not assigned to this brand'
        })
      }
      whereCondition = eq(products.brandId, brandId)
    } else {
      // Get products from all user's accessible brands
      whereCondition = inArray(products.brandId, userBrandIds)
    }

    // Get total count for pagination
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(products)
      .where(whereCondition)

    // Get paginated products with related data
    const productsData = await db
      .select({
        id: products.id,
        productId: products.productId,
        productTitle: products.productTitle,
        productType: products.productType,
        category: products.category,
        thumbnailUrl: products.thumbnailUrl,
        shortDescription: products.shortDescription,
        longDescription: products.longDescription,
        genAiDescription: products.genAiDescription,
        specifications: products.specifications,
        productHighlights: products.productHighlights,
        classType: products.classType,
        upc: products.upc,
        images: products.images,
        customImages: products.customImages,
        pdpSummary: products.pdpSummary,
        brand: {
          id: brands.id,
          name: brands.name,
          description: brands.description,
          logoAssetUrl: brands.logoAssetUrl
        },
        account: {
          id: accounts.id,
          name: accounts.name
        }
      })
      .from(products)
      .leftJoin(brands, eq(products.brandId, brands.id))
      .leftJoin(accounts, eq(products.accountId, accounts.id))
      .where(whereCondition)
      .orderBy(products.productTitle)
      .limit(limitNum)
      .offset(offset)

    return reply.send({
      data: productsData,
      pagination: {
        total: totalCount,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(totalCount / limitNum)
      },
      brandId: brandId || null,
      userBrandIds,
      userId: sessionUser.id
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch products by brand' })
  }
}
