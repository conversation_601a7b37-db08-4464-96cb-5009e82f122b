import { getUserAccessibleProjects, debugUserAccess, getUserBrandsByRetailer, debugGetUserBrandsByRetailer, getUserProductsByBrand } from './creativeSetup.controllers.js'
import { creativeSetupSchema } from './creativeSetup.schemas.js'

/**
 * Registers creative setup-related HTTP GET routes on a Fastify instance, enabling retrieval of user-accessible projects, brands by retailer, products by brand, and debug endpoints. All routes enforce authentication.
 * @throws {Error} If the Fastify instance does not have the required authentication decorator.
 */
export default async function creativeSetupRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all projects accessible to the current user based on brand assignments
  fastify.get('/projects', {
    onRequest: [fastify.verifyAuth],
    schema: creativeSetupSchema.getUserAccessibleProjects,
    handler: getUserAccessibleProjects
  })

  // Get brands that user is assigned to and that have a specific retailerId
  fastify.get('/brands/retailer/:retailerId', {
    onRequest: [fastify.verifyAuth],
    // schema: creativeSetupSchema.getUserBrandsByRetailer,
    handler: getUserBrandsByRetailer
  })

  // DEBUG: Get brands that user is assigned to and that have a specific retailerId with detailed logging
  fastify.get('/debug/brands/retailer/:retailerId', {
    onRequest: [fastify.verifyAuth],
    handler: debugGetUserBrandsByRetailer
  })

  // Get products for a specific brand or all products from all user brands
  fastify.get('/products/:brandId?', {
    onRequest: [fastify.verifyAuth],
    schema: creativeSetupSchema.getUserProductsByBrand,
    handler: getUserProductsByBrand
  })

  // Debug endpoint to check user access
  fastify.get('/debug', {
    onRequest: [fastify.verifyAuth],
    handler: debugUserAccess
  })
}
