import { FastifyInstance } from "fastify";
import { eq, and, inArray } from "drizzle-orm";
import { users } from "../db/schema/users.ts";
import { randomUUID } from "crypto";
import { db } from "../db/index.js";
import * as schema from "../db/schema/index.js";
import axios from "axios";
import { getOrUploadFile } from "../services/google.service";

// Helper function to get Auth0 Management API token
async function getManagementApiToken() {
  try {
    const response = await axios.post(
      `https://${process.env.AUTH0_DOMAIN}/oauth/token`,
      {
        grant_type: "client_credentials",
        client_id: process.env.AUTH0_CLIENT_ID,
        client_secret: process.env.AUTH0_CLIENT_SECRET,
        audience: `https://${process.env.AUTH0_DOMAIN}/api/v2/`,
      }
    );
    return response.data.access_token;
  } catch (error) {
    console.error("Error getting management token:", error);
    throw error;
  }
}

export default async function userRoutes(fastify: FastifyInstance) {
  // Get all users
  fastify.get("/", async (request, reply) => {
    const allUsers = await db.select().from(users);
    return allUsers;
  });

  // Get user by id
  fastify.get("/:id", async (request, reply) => {
    const { id } = request.params as { id: string };

    const results = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!results || results.length === 0) {
      return reply.code(404).send({ error: "User not found" });
    }

    return results[0];
  });

  // Create user
  fastify.post("/", async (request, reply) => {
    const userData = request.body as Omit<typeof users.$inferInsert, "id">;
    const id = randomUUID();

    await db.insert(users).values({ ...userData, id });

    // Fetch the created user
    const newUser = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!newUser || newUser.length === 0) {
      return reply.code(500).send({ error: "Failed to create user" });
    }

    return reply.code(201).send(newUser[0]);
  });

  // Update user's Auth0 profile picture
  fastify.put("/:id/profile-picture", async (request, reply) => {
    try {
      const { id } = request.params as { id: string };

      if (!request.isMultipart()) {
        return reply.code(400).send({ error: 'Request must be multipart/form-data' })
      }

      interface MultipartBody {
        file: {
          _buf: Buffer;
          filename: string;
        }
      }

      const body = await request.body as MultipartBody

      if (!body || !body.file) {
        return reply.code(400).send({
          error: 'No file uploaded',
          body: body,
          keys: body ? Object.keys(body) : []
        })
      }

      const fileData = body.file

      // Convert file buffer and generate unique filename using UUID
      const buffer = fileData._buf

      // Generate unique filename
      const fileName = `${id}-${Date.now()}.${fileData.filename.split('.').pop()}`;

      // Upload to Google Cloud Storage
      const picture_url = await getOrUploadFile(
        'adfury-profile-picutures',
        buffer,
        fileName
      );

      // First, get the user from our database to get their Auth0 ID
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user || !user.auth0Id) {
        return reply.code(404).send({ error: "User not found" });
      }

      // Get Management API token
      const token = await getManagementApiToken();

      // Update Auth0 user profile
      await axios.patch(
        `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
        {
          picture: picture_url,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      return reply.code(200).send({
        message: "Profile picture updated successfully",
        picture_url
      });
    } catch (error: any) {
      console.error("Error updating profile picture:", error);
      return reply.code(500).send({
        error: "Failed to update profile picture",
        details: error.response?.data || error.message,
      });
    }
  });

  // Update user
  fastify.put("/:id", async (request, reply) => {
    const { id } = request.params as { id: string };
    const userData = request.body as Partial<typeof users.$inferInsert>;

    await db
      .update(users)
      .set({ ...userData, updatedAt: new Date() })
      .where(eq(users.id, id));

    // Fetch the updated user
    const updatedUser = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!updatedUser || updatedUser.length === 0) {
      return reply.code(404).send({ error: "User not found" });
    }

    return updatedUser[0];
  });

  // Delete user
  fastify.delete("/:id", async (request, reply) => {
    const { id } = request.params as { id: string };

    await db.delete(users).where(eq(users.id, id));

    return reply.code(204).send();
  });

  // Update user's Auth0 profile (name/email)
  fastify.put("/:id/profile", async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const { name, email } = request.body as { name?: string; email?: string };

      // First, get the user from our database to get their Auth0 ID
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user || !user.auth0Id) {
        return reply.code(404).send({ error: "User not found" });
      }

      // Get Management API token
      const token = await getManagementApiToken();

      // Prepare update payload
      const updateData: { name?: string; email?: string } = {};
      if (name) updateData.name = name;
      if (email) updateData.email = email;

      // Update Auth0 user profile
      await axios.patch(
        `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
        updateData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      return reply.code(200).send({ message: "Profile updated successfully" });
    } catch (error: any) {
      console.error("Error updating profile:", error);
      return reply.code(500).send({
        error: "Failed to update profile",
        details: error.response?.data || error.message,
      });
    }
  });

  // Reset user's Auth0 password
  fastify.post("/:id/reset-password", async (request, reply) => {
    try {
      const { id } = request.params as { id: string };

      // First, get the user from our database to get their Auth0 ID
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user || !user.auth0Id) {
        return reply.code(404).send({ error: "User not found" });
      }

      // Get Management API token
      const token = await getManagementApiToken();

      // Log the user details we're working with
      console.log('Attempting password reset for:', {
        userId: id,
        auth0Id: user.auth0Id,
        email: user.email
      });

      // Trigger password reset email with connection specified
      const response = await axios.post(
        `https://${process.env.AUTH0_DOMAIN}/api/v2/tickets/password-change`,
        {
          email: user.email,
          result_url: process.env.AUTH0_PASSWORD_RESET_REDIRECT_URL,
          ttl_sec: 86400,
          mark_email_as_verified: true,
          connection_id: process.env.AUTH0_DB_CONNECTION_ID
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      // Log the response from Auth0
      console.log('Auth0 password reset response:', response.data);

      return reply.code(200).send({
        message: "Password reset email sent successfully",
        ticket: response.data
      });
    } catch (error: any) {
      console.error("Error initiating password reset:", error);
      return reply.code(500).send({
        error: "Failed to initiate password reset",
        details: error.response?.data || error.message,
      });
    }
  });

  // GET /api/users/:id/profile - Get complete user profile with all relationships
  fastify.get("/:id/profile", async (request, reply) => {
    const { id } = request.params as { id: string };

    try {
      // Get user with basic info
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user) {
        return reply.code(404).send({ error: "User not found" });
      }

      // Get user's account memberships with roles
      const accountMemberships = await db
        .select({
          accountId: schema.userAccounts.accountId,
          accountName: schema.accounts.name,
          role: schema.userAccounts.role,
          status: schema.userAccounts.status
        })
        .from(schema.userAccounts)
        .innerJoin(schema.accounts, eq(schema.userAccounts.accountId, schema.accounts.id))
        .where(eq(schema.userAccounts.userId, id));

      // Get user's brand assignments with roles
      const brandAssignments = await db
        .select({
          brandId: schema.brandUsers.brandId,
          brandName: schema.brands.name,
          role: schema.brandUsers.role
        })
        .from(schema.brandUsers)
        .innerJoin(schema.brands, eq(schema.brandUsers.brandId, schema.brands.id))
        .where(eq(schema.brandUsers.userId, id));

      // Get retailers for each brand
      const brandIds = brandAssignments.map((ba: any) => ba.brandId);
      const brandRetailers = brandIds.length > 0 ? await db
        .select({
          brandId: schema.brandRetailers.brandId,
          retailerId: schema.brandRetailers.retailerId,
          retailerName: schema.retailers.name,
          retailerSlug: schema.retailers.slug,
          retailerLogoUrl: schema.retailers.logoUrl,
          retailerUrl: schema.retailers.url
        })
        .from(schema.brandRetailers)
        .innerJoin(schema.retailers, eq(schema.brandRetailers.retailerId, schema.retailers.id))
        .where(inArray(schema.brandRetailers.brandId, brandIds)) : [];

      // Group retailers by brand
      const retailersByBrand = brandRetailers.reduce((acc: Record<string, any[]>, br: any) => {
        if (!acc[br.brandId]) {
          acc[br.brandId] = [];
        }
        acc[br.brandId].push({
          id: br.retailerId,
          name: br.retailerName,
          slug: br.retailerSlug,
          logoUrl: br.retailerLogoUrl,
          url: br.retailerUrl
        });
        return acc;
      }, {} as Record<string, any[]>);

      // Add retailers to brand assignments
      const brandAssignmentsWithRetailers = brandAssignments.map((ba: any) => ({
        ...ba,
        retailers: retailersByBrand[ba.brandId] || []
      }));

      // Get user's credential set associations
      const credentialSets = await db
        .select({
          credentialSetId: schema.userCredentialSets.credentialSetId,
          name: schema.credentialSets.name,
          type: schema.credentialSets.type,
          isActive: schema.credentialSets.isActive,
          credentials: schema.credentialSets.credentials
        })
        .from(schema.userCredentialSets)
        .innerJoin(schema.credentialSets, eq(schema.userCredentialSets.credentialSetId, schema.credentialSets.id))
        .where(eq(schema.userCredentialSets.userId, id));

      // Separate plugins from other credential sets
      const plugins = credentialSets
        .filter((cs: any) => cs.type === 'plugin')
        .map((cs: any) => ({
          name: cs.name,
          isActive: cs.isActive,
          credentials: cs.credentials,
          credentialSetId: cs.credentialSetId
        }));

      const nonPluginCredentialSets = credentialSets.filter((cs: any) => cs.type !== 'plugin');

      // Get user settings
      const [userSettingsData] = await db
        .select()
        .from(schema.userSettings)
        .where(eq(schema.userSettings.userId, id))
        .limit(1);

      // Try to get Auth0 user info for additional details (lastLogin, loginCount, etc.)
      let auth0User = null;
      if (user.auth0Id) {
        try {
          const token = await getManagementApiToken();
          const auth0Response = await axios.get(
            `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            }
          );
          auth0User = auth0Response.data;
        } catch (auth0Error: any) {
          console.error("Auth0 user not found, using database data only:", auth0Error.response?.status);
          // Continue without Auth0 data
        }
      }

      return {
        user: {
          ...user,
          lastLogin: auth0User?.last_login || null,
          loginCount: auth0User?.logins_count || 0,
          emailVerified: auth0User?.email_verified || false,
          picture: auth0User?.picture || null
        },
        accountMemberships,
        brandAssignments: brandAssignmentsWithRetailers,
        credentialSets: nonPluginCredentialSets,
        settings: userSettingsData?.settings || {},
        plugins
      };
    } catch (error: any) {
      console.error("Error fetching user profile:", error);
      return reply.code(500).send({
        error: "Failed to fetch user profile",
        details: error.response?.data || error.message,
      });
    }
  });

  // PUT /api/users/:id/profile-info - Update basic profile info
  fastify.put("/:id/profile-info", async (request, reply) => {
    const { id } = request.params as { id: string };
    const { name, phone, language, timeZone, jobTitle } = request.body as {
      name?: string;
      phone?: string;
      language?: string;
      timeZone?: string;
      jobTitle?: string;
    };

    try {
      // Update database user info
      const updateData: Partial<typeof users.$inferInsert> = {};
      if (name) updateData.name = name;
      if (phone) updateData.phone = phone;
      if (language) updateData.language = language;
      if (timeZone) updateData.timeZone = timeZone;
      if (jobTitle) updateData.jobTitle = jobTitle;

      if (Object.keys(updateData).length > 0) {
        await db
          .update(users)
          .set({ ...updateData, updatedAt: new Date() })
          .where(eq(users.id, id));
      }

      // Update Auth0 profile if name is changed
      if (name) {
        const [user] = await db
          .select()
          .from(users)
          .where(eq(users.id, id))
          .limit(1);

        if (user?.auth0Id) {
          const token = await getManagementApiToken();
          await axios.patch(
            `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
            { name },
            {
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            }
          );
        }
      }

      return reply.send({ message: "Profile updated successfully" });
    } catch (error: any) {
      console.error("Error updating profile info:", error);
      return reply.code(500).send({
        error: "Failed to update profile info",
        details: error.response?.data || error.message,
      });
    }
  });

  // PUT /api/users/:id/brands - Update user's brand assignments
  fastify.put("/:id/brands", async (request, reply) => {
    const { id } = request.params as { id: string };
    const { brandAssignments } = request.body as {
      brandAssignments: Array<{ brandId: string; role: string; action: 'add' | 'remove' | 'update' }>;
    };

    try {
      for (const assignment of brandAssignments) {
        const { brandId, role, action } = assignment;

        switch (action) {
          case 'add':
            await db
              .insert(schema.brandUsers)
              .values({
                userId: id,
                brandId,
                role,
                createdAt: new Date(),
                updatedAt: new Date()
              })
              .onConflictDoUpdate({
                target: [schema.brandUsers.userId, schema.brandUsers.brandId],
                set: { role, updatedAt: new Date() }
              });
            break;

          case 'remove':
            await db
              .delete(schema.brandUsers)
              .where(
                and(
                  eq(schema.brandUsers.userId, id),
                  eq(schema.brandUsers.brandId, brandId)
                )
              );
            break;

          case 'update':
            await db
              .update(schema.brandUsers)
              .set({ role, updatedAt: new Date() })
              .where(
                and(
                  eq(schema.brandUsers.userId, id),
                  eq(schema.brandUsers.brandId, brandId)
                )
              );
            break;
        }
      }

      return reply.send({ message: "Brand assignments updated successfully" });
    } catch (error: any) {
      console.error("Error updating brand assignments:", error);
      return reply.code(500).send({
        error: "Failed to update brand assignments",
        details: error.message,
      });
    }
  });

  // GET /api/users/:id/brands - Get all brands assigned to user
  fastify.get("/:id/brands", async (request, reply) => {
    const { id } = request.params as { id: string };

    try {
      const brandAssignments = await db
        .select({
          brandId: schema.brandUsers.brandId,
          brandName: schema.brands.name,
          role: schema.brandUsers.role,
          accountId: schema.brands.accountId,
          accountName: schema.accounts.name
        })
        .from(schema.brandUsers)
        .innerJoin(schema.brands, eq(schema.brandUsers.brandId, schema.brands.id))
        .innerJoin(schema.accounts, eq(schema.brands.accountId, schema.accounts.id))
        .where(eq(schema.brandUsers.userId, id));

      return reply.send(brandAssignments);
    } catch (error: any) {
      console.error("Error fetching user brands:", error);
      return reply.code(500).send({
        error: "Failed to fetch user brands",
        details: error.message,
      });
    }
  });

  // PUT /api/users/:id/role - Update user's account role
  fastify.put("/:id/role", async (request, reply) => {
    const { id } = request.params as { id: string };
    const { accountId, role } = request.body as { accountId: string; role: string };

    try {
      const validRoles = ['owner', 'admin', 'member'];
      if (!validRoles.includes(role)) {
        return reply.code(400).send({ error: "Invalid role" });
      }

      await db
        .update(schema.userAccounts)
        .set({ role, updatedAt: new Date() })
        .where(
          and(
            eq(schema.userAccounts.userId, id),
            eq(schema.userAccounts.accountId, accountId)
          )
        );

      return reply.send({ message: "User role updated successfully" });
    } catch (error: any) {
      console.error("Error updating user role:", error);
      return reply.code(500).send({
        error: "Failed to update user role",
        details: error.message,
      });
    }
  });

  // POST /api/users/:id/resend-invite - Resend invitation email
  fastify.post("/:id/resend-invite", async (request, reply) => {
    const { id } = request.params as { id: string };

    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user?.auth0Id) {
        return reply.code(404).send({ error: "User not found" });
      }

      // Trigger password reset email (same as invitation)
      const token = await getManagementApiToken();
      const response = await axios.post(
        `https://${process.env.AUTH0_DOMAIN}/api/v2/tickets/password-change`,
        {
          email: user.email,
          result_url: process.env.AUTH0_PASSWORD_RESET_REDIRECT_URL,
          ttl_sec: 86400,
          mark_email_as_verified: true,
          connection_id: process.env.AUTH0_DB_CONNECTION_ID
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      return reply.send({
        message: "Invitation resent successfully",
        ticket: response.data
      });
    } catch (error: any) {
      console.error("Error resending invitation:", error);
      return reply.code(500).send({
        error: "Failed to resend invitation",
        details: error.response?.data || error.message,
      });
    }
  });

  // GET /api/users/:id/plugins - Get user's connected plugins
  fastify.get("/:id/plugins", async (request, reply) => {
    const { id } = request.params as { id: string };

    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user) {
        return reply.code(404).send({ error: "User not found" });
      }

      // Get user's plugin credential sets
      const plugins = await db
        .select({
          credentialSetId: schema.userCredentialSets.credentialSetId,
          name: schema.credentialSets.name,
          type: schema.credentialSets.type,
          isActive: schema.credentialSets.isActive,
          credentials: schema.credentialSets.credentials,
          createdAt: schema.credentialSets.createdAt
        })
        .from(schema.userCredentialSets)
        .innerJoin(schema.credentialSets, eq(schema.userCredentialSets.credentialSetId, schema.credentialSets.id))
        .where(
          and(
            eq(schema.userCredentialSets.userId, id),
            eq(schema.credentialSets.type, 'plugin')
          )
        );

      return reply.send({ plugins });
    } catch (error: any) {
      console.error("Error fetching user plugins:", error);
      return reply.code(500).send({
        error: "Failed to fetch user plugins",
        details: error.message,
      });
    }
  });

  // POST /api/users/:id/plugins - Connect a plugin to user
  fastify.post("/:id/plugins", async (request, reply) => {
    const { id } = request.params as { id: string };
    const { pluginName, pluginData } = request.body as {
      pluginName: string;
      pluginData: any;
    };

    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user) {
        return reply.code(404).send({ error: "User not found" });
      }

      // Get user's account (assuming the user has at least one account membership)
      const [userAccount] = await db
        .select({ accountId: schema.userAccounts.accountId })
        .from(schema.userAccounts)
        .where(eq(schema.userAccounts.userId, id))
        .limit(1);

      if (!userAccount) {
        return reply.code(400).send({ error: "User must be associated with an account to add plugins" });
      }

      // Check if plugin already exists for this user
      const existingPlugin = await db
        .select()
        .from(schema.userCredentialSets)
        .innerJoin(schema.credentialSets, eq(schema.userCredentialSets.credentialSetId, schema.credentialSets.id))
        .where(
          and(
            eq(schema.userCredentialSets.userId, id),
            eq(schema.credentialSets.type, 'plugin'),
            eq(schema.credentialSets.name, pluginName)
          )
        )
        .limit(1);

      if (existingPlugin.length > 0) {
        // Update existing plugin
        await db
          .update(schema.credentialSets)
          .set({
            credentials: pluginData,
            isActive: true,
            updatedAt: new Date()
          })
          .where(eq(schema.credentialSets.id, existingPlugin[0].credential_sets.id));
      } else {
        // Create new credential set for the plugin
        const [newCredentialSet] = await db
          .insert(schema.credentialSets)
          .values({
            name: pluginName,
            type: 'plugin',
            credentials: pluginData,
            accountId: userAccount.accountId,
            adminId: id,
            isActive: true,
            isShared: false
          })
          .returning({ id: schema.credentialSets.id });

        // Associate the credential set with the user
        await db
          .insert(schema.userCredentialSets)
          .values({
            userId: id,
            credentialSetId: newCredentialSet.id
          });
      }

      return reply.send({ message: "Plugin connected successfully" });
    } catch (error: any) {
      console.error("Error connecting plugin:", error);
      return reply.code(500).send({
        error: "Failed to connect plugin",
        details: error.message,
      });
    }
  });

  // DELETE /api/users/:id/plugins/:pluginName - Disconnect plugin
  fastify.delete("/:id/plugins/:pluginName", async (request, reply) => {
    const { id, pluginName } = request.params as { id: string; pluginName: string };

    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user) {
        return reply.code(404).send({ error: "User not found" });
      }

      // Find the plugin credential set for this user
      const pluginCredentialSet = await db
        .select({ credentialSetId: schema.credentialSets.id })
        .from(schema.userCredentialSets)
        .innerJoin(schema.credentialSets, eq(schema.userCredentialSets.credentialSetId, schema.credentialSets.id))
        .where(
          and(
            eq(schema.userCredentialSets.userId, id),
            eq(schema.credentialSets.type, 'plugin'),
            eq(schema.credentialSets.name, pluginName)
          )
        )
        .limit(1);

      if (pluginCredentialSet.length === 0) {
        return reply.code(404).send({ error: "Plugin not found for this user" });
      }

      // Set the credential set as inactive instead of deleting
      await db
        .update(schema.credentialSets)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(schema.credentialSets.id, pluginCredentialSet[0].credentialSetId));

      return reply.send({ message: "Plugin disconnected successfully" });
    } catch (error: any) {
      console.error("Error disconnecting plugin:", error);
      return reply.code(500).send({
        error: "Failed to disconnect plugin",
        details: error.message,
      });
    }
  });

  // GET /api/users/:id/available-brands - Get brands available for assignment to user
  fastify.get("/:id/available-brands", async (request, reply) => {
    const { id } = request.params as { id: string };

    try {
      // Get user's current account memberships
      const userAccountMemberships = await db
        .select({ accountId: schema.userAccounts.accountId })
        .from(schema.userAccounts)
        .where(eq(schema.userAccounts.userId, id));

      if (userAccountMemberships.length === 0) {
        return reply.send([]);
      }

      const accountIds = userAccountMemberships.map((ua: any) => ua.accountId);

      // Get all brands in accounts the user belongs to
      const allAccountBrands = await db
        .select({
          brandId: schema.brands.id,
          brandName: schema.brands.name,
          accountId: schema.brands.accountId,
          accountName: schema.accounts.name
        })
        .from(schema.brands)
        .innerJoin(schema.accounts, eq(schema.brands.accountId, schema.accounts.id))
        .where(inArray(schema.brands.accountId, accountIds));

      // Get brands the user is already assigned to
      const userBrandAssignments = await db
        .select({ brandId: schema.brandUsers.brandId })
        .from(schema.brandUsers)
        .where(eq(schema.brandUsers.userId, id));

      const assignedBrandIds = new Set(userBrandAssignments.map((ub: any) => ub.brandId));

      // Filter out brands already assigned to the user
      const availableBrands = allAccountBrands.filter(
        (brand: any) => !assignedBrandIds.has(brand.brandId)
      );

      return reply.send(availableBrands);
    } catch (error: any) {
      console.error("Error fetching available brands:", error);
      return reply.code(500).send({
        error: "Failed to fetch available brands",
        details: error.message,
      });
    }
  });

  // PUT /api/users/:id/update-profile - Comprehensive user profile update
  fastify.put("/:id/update-profile", async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const {
        name,
        jobTitle,
        email,
        phone,
        brandRetailerAssignments,
        role,
        accountId,
        status,
        retailerCredentials
      } = request.body as {
        name?: string;
        jobTitle?: string;
        email?: string;
        phone?: string;
        brandRetailerAssignments?: Array<{ brandId: string; retailerId: string; action: 'add' | 'remove' }>;
        role?: string; // admin-only field
        accountId?: string; // required if role is provided
        status?: string; // admin-only field (active/inactive)
        retailerCredentials?: {
          retailerName?: string;
          label?: string;
          advertisingId?: string;
          status?: string;
        };
      };

      // Verify user exists
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user) {
        return reply.code(404).send({ error: "User not found" });
      }

      // 1. Update basic user fields in local database
      const userUpdateData: Partial<typeof users.$inferInsert> = {};
      if (name) userUpdateData.name = name;
      if (jobTitle) userUpdateData.jobTitle = jobTitle;
      if (email) userUpdateData.email = email;
      if (phone) userUpdateData.phone = phone;
      if (status) userUpdateData.status = status; // admin should control this

      if (Object.keys(userUpdateData).length > 0) {
        await db
          .update(users)
          .set({ ...userUpdateData, updatedAt: new Date() })
          .where(eq(users.id, id));
      }

      // 2. Update Auth0 profile if name or email changed
      if ((name || email) && user.auth0Id) {
        try {
          const token = await getManagementApiToken();
          const auth0Updates: { name?: string; email?: string } = {};
          if (name) auth0Updates.name = name;
          if (email) auth0Updates.email = email;

          await axios.patch(
            `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
            auth0Updates,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            }
          );
        } catch (auth0Error: any) {
          console.error("Auth0 update failed:", auth0Error);
          // Continue with other updates even if Auth0 fails
        }
      }

      // 3. Update user role (admin-only operation)
      if (role && accountId) {
        await db
          .update(schema.userAccounts)
          .set({ role, updatedAt: new Date() })
          .where(
            and(
              eq(schema.userAccounts.userId, id),
              eq(schema.userAccounts.accountId, accountId)
            )
          );
      }

      // 4. Handle brand retailer assignments
      if (brandRetailerAssignments && brandRetailerAssignments.length > 0) {
        for (const assignment of brandRetailerAssignments) {
          const { brandId, retailerId, action } = assignment;
          
          if (action === 'add') {
            await db
              .insert(schema.brandRetailers)
              .values({
                brandId,
                retailerId,
                createdAt: new Date(),
                updatedAt: new Date()
              })
              .onConflictDoNothing();
          } else if (action === 'remove') {
            await db
              .delete(schema.brandRetailers)
              .where(
                and(
                  eq(schema.brandRetailers.brandId, brandId),
                  eq(schema.brandRetailers.retailerId, retailerId)
                )
              );
          }
        }
      }

      // 5. Update retailer credentials (any retailer)
      if (retailerCredentials) {
        const { retailerName, label, advertisingId, status: credStatus } = retailerCredentials;
        const credentialSetName = retailerName ? retailerName.toLowerCase().replace(/\s+/g, '_') : 'retailer_connect';
        
        // Look for existing credential set for this retailer and user
        const existingCreds = await db
          .select({ id: schema.credentialSets.id })
          .from(schema.credentialSets)
          .innerJoin(schema.userCredentialSets, eq(schema.credentialSets.id, schema.userCredentialSets.credentialSetId))
          .where(
            and(
              eq(schema.userCredentialSets.userId, id),
              eq(schema.credentialSets.name, credentialSetName)
            )
          )
          .limit(1);

        const credentialsPayload = {
          retailerName: retailerName || null,
          label: label || null,
          advertisingId: advertisingId || null,
          status: credStatus || null
        };

        if (existingCreds.length > 0) {
          // Update existing credentials
          await db
            .update(schema.credentialSets)
            .set({
              credentials: credentialsPayload,
              isActive: true,
              updatedAt: new Date()
            })
            .where(eq(schema.credentialSets.id, existingCreds[0].id));
        } else {
          // Create new credential set
          const [userAccount] = await db
            .select({ accountId: schema.userAccounts.accountId })
            .from(schema.userAccounts)
            .where(eq(schema.userAccounts.userId, id))
            .limit(1);

          if (userAccount) {
            const [newCredSet] = await db
              .insert(schema.credentialSets)
              .values({
                name: credentialSetName,
                type: 'retailer',
                credentials: credentialsPayload,
                accountId: userAccount.accountId,
                adminId: id,
                isActive: true,
                isShared: false
              })
              .returning({ id: schema.credentialSets.id });

            // Link to user
            await db
              .insert(schema.userCredentialSets)
              .values({
                userId: id,
                credentialSetId: newCredSet.id
              });
          }
        }
      }

      return reply.send({ 
        message: "User profile updated successfully",
        updatedFields: Object.keys(request.body as object)
      });

    } catch (error: any) {
      console.error("Error updating user profile:", error);
      return reply.code(500).send({
        error: "Failed to update user profile",
        details: error.message,
      });
    }
  });
}
