import { eq, and, desc, isNull } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { proofComments } from '../../db/schema/proofComments.ts'
import { proofs } from '../../db/schema/proofs.ts'
import { reviewers } from '../../db/schema/reviewers.ts'
import { users } from '../../db/schema/users.ts'

/**
 * Create a new proof comment
 */
export async function createProofComment(request, reply) {
  try {
    const { proofId, reviewerId, comment, adUnitId, replyToId, x, y } = request.body

    // Validate required fields
    if (!proofId || !reviewerId || !comment) {
      return reply.code(400).send({ 
        error: 'Missing required fields: proofId, reviewerId, and comment are required' 
      })
    }

    // Verify the proof exists
    const [existingProof] = await db
      .select()
      .from(proofs)
      .where(eq(proofs.id, proofId))
      .limit(1)

    if (!existingProof) {
      return reply.code(404).send({ error: 'Proof not found' })
    }

    // Verify the reviewer exists
    const [existingReviewer] = await db
      .select()
      .from(reviewers)
      .where(eq(reviewers.id, reviewerId))
      .limit(1)

    if (!existingReviewer) {
      return reply.code(404).send({ error: 'Reviewer not found' })
    }

    // If this is a reply, verify the parent comment exists
    if (replyToId) {
      const [parentComment] = await db
        .select()
        .from(proofComments)
        .where(eq(proofComments.id, replyToId))
        .limit(1)

      if (!parentComment) {
        return reply.code(404).send({ error: 'Parent comment not found' })
      }

      // Ensure the parent comment belongs to the same proof
      if (parentComment.proofId !== proofId) {
        return reply.code(400).send({ error: 'Parent comment must belong to the same proof' })
      }
    }

    // Create the proof comment
    const [proofComment] = await db
      .insert(proofComments)
      .values({
        proofId,
        reviewerId,
        comment,
        adUnitId: adUnitId || null,
        replyToId: replyToId || null,
        x: x || null,
        y: y || null,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    return reply.code(201).send(proofComment)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create proof comment' })
  }
}

/**
 * Get all comments for a specific proof
 */
export async function getProofComments(request, reply) {
  try {
    const { proofId } = request.params

    // Verify the proof exists
    const [existingProof] = await db
      .select()
      .from(proofs)
      .where(eq(proofs.id, proofId))
      .limit(1)

    if (!existingProof) {
      return reply.code(404).send({ error: 'Proof not found' })
    }

    // Get all comments for this proof with reviewer information
    const comments = await db
      .select({
        id: proofComments.id,
        proofId: proofComments.proofId,
        reviewerId: proofComments.reviewerId,
        adUnitId: proofComments.adUnitId,
        comment: proofComments.comment,
        replyToId: proofComments.replyToId,
        createdAt: proofComments.createdAt,
        updatedAt: proofComments.updatedAt,
        x: proofComments.x,
        y: proofComments.y,
        reviewer: {
          id: reviewers.id,
          name: reviewers.name,
          email: reviewers.email,
          isExternal: reviewers.isExternal
        }
      })
      .from(proofComments)
      .leftJoin(reviewers, eq(proofComments.reviewerId, reviewers.id))
      .where(eq(proofComments.proofId, proofId))
      .orderBy(desc(proofComments.createdAt))

    return reply.send(comments)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch proof comments' })
  }
}

/**
 * Get a specific proof comment by ID
 */
export async function getProofCommentById(request, reply) {
  try {
    const { id } = request.params

    const [comment] = await db
      .select({
        id: proofComments.id,
        proofId: proofComments.proofId,
        reviewerId: proofComments.reviewerId,
        adUnitId: proofComments.adUnitId,
        comment: proofComments.comment,
        replyToId: proofComments.replyToId,
        createdAt: proofComments.createdAt,
        updatedAt: proofComments.updatedAt,
        reviewer: {
          id: reviewers.id,
          name: reviewers.name,
          email: reviewers.email,
          isExternal: reviewers.isExternal
        }
      })
      .from(proofComments)
      .leftJoin(reviewers, eq(proofComments.reviewerId, reviewers.id))
      .where(eq(proofComments.id, id))
      .limit(1)

    if (!comment) {
      return reply.code(404).send({ error: 'Comment not found' })
    }

    return reply.send(comment)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch comment' })
  }
}

/**
 * Update a proof comment
 */
export async function updateProofComment(request, reply) {
  try {
    const { id } = request.params
    const { comment, adUnitId } = request.body

    // Verify the comment exists
    const [existingComment] = await db
      .select()
      .from(proofComments)
      .where(eq(proofComments.id, id))
      .limit(1)

    if (!existingComment) {
      return reply.code(404).send({ error: 'Comment not found' })
    }

    // Update the comment
    const [updatedComment] = await db
      .update(proofComments)
      .set({
        comment: comment || existingComment.comment,
        adUnitId: adUnitId !== undefined ? adUnitId : existingComment.adUnitId,
        updatedAt: new Date()
      })
      .where(eq(proofComments.id, id))
      .returning()

    return reply.send(updatedComment)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update comment' })
  }
}

/**
 * Delete a proof comment
 */
export async function deleteProofComment(request, reply) {
  try {
    const { id } = request.params

    // Verify the comment exists
    const [existingComment] = await db
      .select()
      .from(proofComments)
      .where(eq(proofComments.id, id))
      .limit(1)

    if (!existingComment) {
      return reply.code(404).send({ error: 'Comment not found' })
    }

    // Delete the comment
    await db
      .delete(proofComments)
      .where(eq(proofComments.id, id))

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete comment' })
  }
}

/**
 * Get replies for a specific comment
 */
export async function getCommentReplies(request, reply) {
  try {
    const { commentId } = request.params

    // Verify the parent comment exists
    const [parentComment] = await db
      .select()
      .from(proofComments)
      .where(eq(proofComments.id, commentId))
      .limit(1)

    if (!parentComment) {
      return reply.code(404).send({ error: 'Parent comment not found' })
    }

    // Get all replies to this comment
    const replies = await db
      .select({
        id: proofComments.id,
        proofId: proofComments.proofId,
        reviewerId: proofComments.reviewerId,
        adUnitId: proofComments.adUnitId,
        comment: proofComments.comment,
        replyToId: proofComments.replyToId,
        createdAt: proofComments.createdAt,
        updatedAt: proofComments.updatedAt,
        x: proofComments.x,
        y: proofComments.y,
        reviewer: {
          id: reviewers.id,
          name: reviewers.name,
          email: reviewers.email,
          isExternal: reviewers.isExternal
        }
      })
      .from(proofComments)
      .leftJoin(reviewers, eq(proofComments.reviewerId, reviewers.id))
      .where(eq(proofComments.replyToId, commentId))
      .orderBy(proofComments.createdAt)

    return reply.send(replies)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch comment replies' })
  }
}

/**
 * Get all top-level comments for a proof (comments that are not replies)
 */
export async function getTopLevelComments(request, reply) {
  try {
    const { proofId } = request.params

    // Verify the proof exists
    const [existingProof] = await db
      .select()
      .from(proofs)
      .where(eq(proofs.id, proofId))
      .limit(1)

    if (!existingProof) {
      return reply.code(404).send({ error: 'Proof not found' })
    }

    // Get all top-level comments (not replies) for this proof
    const comments = await db
      .select({
        id: proofComments.id,
        proofId: proofComments.proofId,
        reviewerId: proofComments.reviewerId,
        adUnitId: proofComments.adUnitId,
        comment: proofComments.comment,
        replyToId: proofComments.replyToId,
        createdAt: proofComments.createdAt,
        updatedAt: proofComments.updatedAt,
        x: proofComments.x,
        y: proofComments.y,
        reviewer: {
          id: reviewers.id,
          name: reviewers.name,
          email: reviewers.email,
          isExternal: reviewers.isExternal
        }
      })
      .from(proofComments)
      .leftJoin(reviewers, eq(proofComments.reviewerId, reviewers.id))
      .where(
        and(
          eq(proofComments.proofId, proofId),
          isNull(proofComments.replyToId)
        )
      )
      .orderBy(desc(proofComments.createdAt))

    return reply.send(comments)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch top-level comments' })
  }
}

/**
 * Get all comments for a proof (both top-level and replies) in chronological order
 */
export async function getAllCommentsForProof(request, reply) {
  try {
    const { proofId } = request.params

    // Verify the proof exists
    const [existingProof] = await db
      .select()
      .from(proofs)
      .where(eq(proofs.id, proofId))
      .limit(1)

    if (!existingProof) {
      return reply.code(404).send({ error: 'Proof not found' })
    }

    // Get all comments for this proof with reviewer information, ordered chronologically
    const comments = await db
      .select({
        id: proofComments.id,
        proofId: proofComments.proofId,
        reviewerId: proofComments.reviewerId,
        adUnitId: proofComments.adUnitId,
        comment: proofComments.comment,
        replyToId: proofComments.replyToId,
        createdAt: proofComments.createdAt,
        updatedAt: proofComments.updatedAt,
        x: proofComments.x,
        y: proofComments.y,
        reviewer: {
          id: reviewers.id,
          name: reviewers.name,
          email: reviewers.email,
          isExternal: reviewers.isExternal
        }
      })
      .from(proofComments)
      .leftJoin(reviewers, eq(proofComments.reviewerId, reviewers.id))
      .where(eq(proofComments.proofId, proofId))
      .orderBy(proofComments.createdAt) // Chronological order (oldest first)

    return reply.send(comments)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch all comments for proof' })
  }
}
