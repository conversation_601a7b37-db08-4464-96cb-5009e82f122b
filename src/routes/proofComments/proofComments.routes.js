import {
  createProofComment,
  getProofComments,
  getProofCommentById,
  updateProofComment,
  deleteProofComment,
  getCommentReplies,
  getTopLevelComments,
  getAllCommentsForProof
} from './proofComments.controllers.js'

export default async function proofCommentsRoutes(fastify, options) {
  // Ensure auth decorator is available
//   if (!fastify.hasDecorator('verifyAuth')) {
//     throw new Error('Auth plugin must be registered before routes')
//   }

  // Create a new proof comment
  fastify.post('/', {
    // onRequest: [fastify.verifyAuth],
    handler: createProofComment
  })

  // Get a specific proof comment by ID
  fastify.get('/:id', {
    // onRequest: [fastify.verifyAuth],
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid' }
        }
      }
    },
    handler: getProofCommentById
  })

  // Update a proof comment
  fastify.put('/:id', {
    // onRequest: [fastify.verifyAuth],
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid' }
        }
      },
      body: {
        type: 'object',
        properties: {
          comment: { type: 'string' },
          adUnitId: { type: 'string' }
        }
      }
    },
    handler: updateProofComment
  })

  // Delete a proof comment
  fastify.delete('/:id', {
    // onRequest: [fastify.verifyAuth],
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid' }
        }
      }
    },
    handler: deleteProofComment
  })

  // Get all comments for a specific proof (newest first)
  fastify.get('/proof/:proofId', {
    // onRequest: [fastify.verifyAuth],
    schema: {
      params: {
        type: 'object',
        required: ['proofId'],
        properties: {
          proofId: { type: 'string', format: 'uuid' }
        }
      }
    },
    handler: getProofComments
  })

  // Get all comments for a proof in chronological order (oldest first)
  fastify.get('/proof/:proofId/all', {
    // onRequest: [fastify.verifyAuth],
    schema: {
      params: {
        type: 'object',
        required: ['proofId'],
        properties: {
          proofId: { type: 'string', format: 'uuid' }
        }
      }
    },
    handler: getAllCommentsForProof
  })

  // Get only top-level comments for a proof (no replies)
  fastify.get('/proof/:proofId/top-level', {
    // onRequest: [fastify.verifyAuth],
    schema: {
      params: {
        type: 'object',
        required: ['proofId'],
        properties: {
          proofId: { type: 'string', format: 'uuid' }
        }
      }
    },
    handler: getTopLevelComments
  })

  // Get replies for a specific comment
  fastify.get('/:commentId/replies', {
    // onRequest: [fastify.verifyAuth],
    schema: {
      params: {
        type: 'object',
        required: ['commentId'],
        properties: {
          commentId: { type: 'string', format: 'uuid' }
        }
      }
    },
    handler: getCommentReplies
  })
}
