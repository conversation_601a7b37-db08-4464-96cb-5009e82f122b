import {
  getAllAssets,
  getAssetById,
  createAsset,
  updateAsset,
  deleteAsset
} from './assets.controllers.js'
import { assetSchema } from './assets.schemas.js'

export default async function assetRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all assets
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllAssets
  })

  // Get asset by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: getAssetById
  })

  // Create asset
  fastify.post('/:bucket', {
    onRequest: [fastify.verifyAuth],
    handler: createAsset,
    config: {
      // Remove schema validation temporarily for debugging
    }
  })

  // Update asset
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: assetSchema.update,
    handler: updateAsset
  })

  // Delete asset
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: deleteAsset
  })
} 