import { eq } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { assets } from '../../db/schema/assets.ts'
import { getOrUploadFile } from '../../services/google.service.js'
import { v4 as uuidv4 } from 'uuid'

export async function getAllAssets(request, reply) {
  try {
    const allAssets = await db.select().from(assets)
    return reply.send(allAssets)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch assets' })
  }
}

export async function getAssetById(request, reply) {
  try {
    const { id } = request.params
    
    const [asset] = await db
      .select({
        url: assets.url
      })
      .from(assets)
      .where(eq(assets.id, id))
      .limit(1)

    if (!asset) {
      return reply.code(404).send({ error: 'Asset not found' })
    }

    return reply.send(asset)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch asset' })
  }
}

/**
 * Handles file upload via multipart/form-data, stores the file in Google Cloud Storage, and returns the uploaded file's URL.
 *
 * Responds with HTTP 201 and the file URL on success. Returns HTTP 400 if the request is not multipart or if no file is provided. On error, responds with HTTP 500 and error details.
 */
export async function createAsset(request, reply) {
  try {
    const { bucket } = request.params
    
    if (!request.isMultipart()) {
      return reply.code(400).send({ error: 'Request must be multipart/form-data' })
    }

    // Get the raw multipart data
    const body = await request.body

    if (!body || !body.file) {
      return reply.code(400).send({ 
        error: 'No file uploaded',
        body: body,
        keys: body ? Object.keys(body) : []
      })
    }

    const fileData = body.file

    // Convert file buffer and generate unique filename using UUID
    const buffer = fileData._buf
    const fileExtension = fileData.filename.split('.').pop()
    const fileName = `${uuidv4()}.${fileExtension}`

    // Upload to Google Cloud Storage
    const url = await getOrUploadFile(
      bucket,
      buffer,
      fileName
    )

    return reply.code(201).send(url)
  } catch (error) {
    console.error('Error in createAsset:', error)
    request.log.error(error)
    return reply.code(500).send({ 
      error: 'Failed to create asset',
      details: error.message 
    })
  }
}

export async function updateAsset(request, reply) {
  try {
    const { id } = request.params
    const { url } = request.body

    const [asset] = await db
      .update(assets)
      .set({
        url,
        updatedAt: new Date()
      })
      .where(eq(assets.id, id))
      .returning()

    if (!asset) {
      return reply.code(404).send({ error: 'Asset not found' })
    }

    return reply.send(asset)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update asset' })
  }
}

export async function deleteAsset(request, reply) {
  try {
    const { id } = request.params

    const [deletedAsset] = await db
      .delete(assets)
      .where(eq(assets.id, id))
      .returning()

    if (!deletedAsset) {
      return reply.code(404).send({ error: 'Asset not found' })
    }

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete asset' })
  }
} 