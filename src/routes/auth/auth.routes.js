import { signup, login, requestPasswordReset, handleEmailVerification, resendEmailVerification, verifyEmail } from "./auth.controllers.js";
import { signupSchema, loginSchema, resendVerificationSchema } from "./auth.schemas.js";
import { eq } from 'drizzle-orm'
import { db } from '../../db/index.js'
import * as schema from '../../db/schema/index.js'
import { users } from '../../db/schema/users.js'
import { AuthService } from '../../services/auth.service.js'

/**
 * Registers authentication-related routes on the Fastify instance, including login, signup, password reset, user profile retrieval, and logout.
 *
 * Sets up public endpoints for user authentication and password reset, as well as protected endpoints for retrieving the authenticated user's profile and logging out. Ensures the authentication plugin is registered before route setup. Handles session management, user and account data retrieval, and proper error responses.
 */
export default async function authRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator("verifyAuth")) {
    throw new Error("Auth plugin must be registered before routes");
  }

  // Public routes with stricter rate limiting
  fastify.post("/login", {
    schema: loginSchema,
    config: {
      rateLimit: {
        max: 10, // 5 login attempts
        timeWindow: '15 minutes' // per 15 minutes
      }
    }
  }, login);

  fastify.post("/signup", {
    schema: signupSchema,
    config: {
      rateLimit: {
        max: 10, // 3 signup attempts
        timeWindow: '15 minutes' // per hour
      }
    }
  }, signup);

  // Password Reset Route with strict limiting
  fastify.post("/password-reset", {
    handler: requestPasswordReset,
    config: {
      rateLimit: {
        max: 3, // 2 password reset attempts
        timeWindow: '1 hour' // per hour
      }
    }
  });

  // Email Verification Routes
  fastify.get("/email-verified", {
    handler: handleEmailVerification,
    config: {
      rateLimit: {
        max: 10, // 10 verification attempts
        timeWindow: '1 hour' // per hour
      }
    }
  });

  fastify.post("/resend-verification", {
    schema: resendVerificationSchema,
    handler: resendEmailVerification,
    config: {
      rateLimit: {
        max: 3, // 3 resend attempts
        timeWindow: '1 hour' // per hour
      }
    }
  });

  fastify.post("/verify-email", {
    handler: verifyEmail,
    config: {
      rateLimit: {
        max: 10, 
        timeWindow: '1 hour'
      }
    }
  });

  // Protected route example
  fastify.get("/me", {
    onRequest: fastify.verifyAuth,
    handler: async (request, reply) => {
      const sessionUser = request.user;

      const lastLogin = sessionUser.last_login;

      console.log("ME Route - User Data from Session:", {
        user: sessionUser,
        hasUser: !!sessionUser,
        cookies: request.cookies,
      });

      let [dbUser] = await db
        .select()
        .from(users)
        .where(eq(users.auth0Id, sessionUser.auth0Id))
        .limit(1);

      if (!dbUser) {
        request.log.error({ auth0Id: sessionUser.auth0Id }, "Session user not found in database.");
        return reply.code(404).send({ error: "User profile not found in database." });
      }

      console.log("DB User:", dbUser);

      // Look up user's accounts through the user_accounts junction table
      let accounts = await db
        .select({
          id: schema.accounts.id,
          name: schema.accounts.name,
          stripeCustomerId: schema.accounts.stripeCustomerId,
          validSubscription: schema.accounts.validSubscription,
          hasPaymentMethod: schema.accounts.hasPaymentMethod,
          ownerId: schema.accounts.ownerId,
          role: schema.userAccounts.role,
          onboardingStep: schema.accounts.onboardingStep,
        })
        .from(schema.userAccounts)
        .leftJoin(schema.accounts, eq(schema.userAccounts.accountId, schema.accounts.id))
        .where(eq(schema.userAccounts.userId, dbUser.id));

      // For backwards compatibility, use the first account if any exist
      const primaryAccount = accounts[0];

      const userWithPermissions = {
        ...sessionUser,
        last_login: lastLogin,
        accounts: accounts,
        stripeCustomerId: primaryAccount?.stripeCustomerId,
        validSubscription: primaryAccount?.validSubscription,
        hasPaymentMethod: primaryAccount?.hasPaymentMethod
      };

      reply.send({ user: userWithPermissions });
    },
  });

  // Logout route
  fastify.post("/logout", async (request, reply) => {
    try {
      if (request.session) {
        // Capture the session ID before destroying the session
        const sessionId = request.session.id;

        // Regular session destruction
        await request.session.destroy();
        request.log.info('Session destroyed successfully.');

        // Also explicitly remove from Redis if available
        if (request.server.redis && sessionId) {
          const sessionKey = `sess:${sessionId}`;
          try {
            await request.server.redis.del(sessionKey);
            request.log.info(`Redis session ${sessionKey} explicitly deleted`);
          } catch (redisErr) {
            request.log.error(redisErr, 'Error deleting session from Redis');
          }
        }
      } else {
        request.log.warn('Logout attempt without active session.');
      }
      // Clear the session cookie (optional, but good practice)
      // The cookie name matches the default ('sessionId') or your custom config
      reply.clearCookie('sessionId', { path: '/' });
      reply.send({ message: "Logged out successfully" });
    } catch (err) {
      request.log.error(err, 'Error during session destruction');
      reply.code(500).send({ error: 'Logout failed' });
    }
  });
}
