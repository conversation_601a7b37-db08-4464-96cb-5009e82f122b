import { ManagementClient } from 'auth0'
import { eq, and } from 'drizzle-orm'
import axios from 'axios'
import * as schema from '../../db/schema/index.js'
import { users } from '../../db/schema/users.js'
import { accounts } from '../../db/schema/accounts.js'
import { invitations } from '../../db/schema/invitations.js'
import { brandUsers } from '../../db/schema/brandUsers.js'
import { BetaService } from '../../services/beta.service.js'
import { LeadsService } from '../../services/leads.service.js'
import { emailQueue } from '../../services/emailQueue.service.js'
import { db } from '../../db/index.js'
import { logger } from '../../utils/logger.js'

const auth0Client = new ManagementClient({
  domain: process.env.AUTH0_DOMAIN,
  clientId: process.env.AUTH0_CLIENT_ID,
  clientSecret: process.env.AUTH0_CLIENT_SECRET,
})

async function processInvitation(db, email, userId, invitationId) {
  logger.info('Processing invitation', {
    invitationId,
    email,
    userId
  });

  // Find the specific invitation by ID and normalized email
  const [invitation] = await db
    .select()
    .from(invitations)
    .where(
      and(
        eq(invitations.id, invitationId),
        eq(invitations.email, email)
      )
    )
    .limit(1);

  if (!invitation) {
    throw new Error('Invalid invitation')
  }

  if (invitation.status !== 'pending') {
    throw new Error('Invitation has already been processed')
  }

  // Create user-account relationship
  await db
    .insert(schema.userAccounts)
    .values({
      userId,
      accountId: invitation.accountId
    })

  // Assign user to brands if brandIds are specified
  if (invitation.brandIds && invitation.brandIds.length > 0) {
    const brandUserValues = invitation.brandIds.map(brandId => ({
      brandId,
      userId,
      role: invitation.role || 'member'
    }));

    await db
      .insert(schema.brandUsers)
      .values(brandUserValues);
  }

  // Update invitation status to accepted
  await db
    .update(invitations)
    .set({ 
      status: 'accepted',
      updatedAt: new Date()
    })
    .where(eq(invitations.id, invitation.id));

  // Delete the invitation after successful processing
  await db
    .delete(invitations)
    .where(eq(invitations.id, invitation.id))

  logger.info('Invitation processed successfully', {
    invitationId,
    accountId: invitation.accountId,
    brandIds: invitation.brandIds,
    role: invitation.role
  });

  return invitation
}

export async function signup(request, reply) {
  const { email, password, name } = request.body
  const { invitationId, betaCode } = request.query  // Get invitationId and betaCode from query params
  
  try {
    const uniqueNumber = Math.floor(1000 + Math.random() * 9000)  // Moved to top

    // Create user in Auth0 without automatic email verification
    const auth0Response = await auth0Client.users.create({
      connection: 'Username-Password-Authentication',
      email,
      password,
      name,
      verify_email: false // We'll handle verification manually with tickets
    })

    const auth0User = auth0Response.data

    if (!auth0User?.user_id) {
      throw new Error('Failed to get Auth0 user ID')
    }

    // Create email verification ticket
    try {
      const baseUrl = process.env.APP_BASE_URL || 'https://app.adfury.ai'

      const verificationUrl = `${baseUrl}/login?authid=${auth0User.user_id}&email=${email}`
      
      const ticket = await auth0Client.tickets.verifyEmail({
        user_id: auth0User.user_id,
        result_url: verificationUrl,
        includeEmailInRedirect: true,
        ttl_sec: 86400 // 24 hours
      })

      logger.info('Email verification ticket created', {
        userId: auth0User.user_id,
        email,
        ticketUrl: ticket.data.ticket
      })

      // Send custom verification email using our email service
      try {
        await emailQueue.addCustomEmail({
          to: email,
          subject: 'Verify your email address',
          heading: 'Welcome to AdFury!',
          content: `
            <p>Hello ${name},</p>
            <p>Thank you for signing up! Please verify your email address to complete your registration and start using AdFury.</p>
            <p>Click the button below to verify your email:</p>
          `,
          buttonText: 'Verify Email Address',
          buttonLink: ticket.data.ticket,
          footerText: 'If you didn\'t create an account, you can safely ignore this email.',
          fromName: 'AdFury Team'
        })
        logger.info('Custom verification email queued', { email })
      } catch (emailError) {
        logger.error('Failed to queue custom verification email', emailError, { email })
        // Continue with signup even if custom email fails - Auth0 will send default verification
      }
    } catch (ticketError) {
      logger.error('Failed to create email verification ticket', ticketError, { email })
      // Continue with signup - user can request verification later
    }

    // Get access token immediately after creation
    const tokenResponse = await axios.post(`https://${process.env.AUTH0_DOMAIN}/oauth/token`, {
      grant_type: 'http://auth0.com/oauth/grant-type/password-realm',
      realm: 'Username-Password-Authentication',
      username: email,
      password,
      client_id: process.env.AUTH0_CLIENT_ID,
      client_secret: process.env.AUTH0_CLIENT_SECRET,
      audience: process.env.AUTH0_AUDIENCE,
      scope: 'openid profile email'
    })

    const { access_token, expires_in } = tokenResponse.data

    if (!access_token) {
      throw new Error('Failed to get access token')
    }

    // Get user profile from Auth0
    const userInfoResponse = await axios.get(`https://${process.env.AUTH0_DOMAIN}/userinfo`, {
      headers: {
        Authorization: `Bearer ${access_token}`
      }
    })

    const userInfo = userInfoResponse.data

    if (!userInfo || !userInfo.sub) {
      throw new Error('Failed to get user information')
    }

    let newAccount, newUser

    if (invitationId) {
      // If invitationId is provided, create user and process invitation
      
      // First, find the invitation
      const [invitation] = await db
        .select()
        .from(invitations)
        .where(
          and(
            eq(invitations.id, invitationId),
            eq(invitations.email, email)
          )
        )
        .limit(1);

      if (!invitation) {
        throw new Error('Invalid invitation or email mismatch')
      }

      if (invitation.status !== 'pending') {
        throw new Error('Invitation has already been processed')
      }

      // Create the user
      [newUser] = await db
        .insert(users)
        .values({
          auth0Id: auth0User.user_id,
          email,
          name,
          isVerified: true
        })
        .returning()

      // Create user-account relationship
      await db
        .insert(schema.userAccounts)
        .values({
          userId: newUser.id,
          accountId: invitation.accountId
        })

      // Assign user to brands if brandIds are specified
      if (invitation.brandIds && invitation.brandIds.length > 0) {
        const brandUserValues = invitation.brandIds.map(brandId => ({
          brandId,
          userId: newUser.id,
          role: invitation.role || 'member'
        }));

        await db
          .insert(schema.brandUsers)
          .values(brandUserValues);
      }

      // Update invitation status to accepted
      await db
        .update(invitations)
        .set({ 
          status: 'accepted',
          updatedAt: new Date()
        })
        .where(eq(invitations.id, invitation.id));

      // Delete the invitation after successful processing
      await db
        .delete(invitations)
        .where(eq(invitations.id, invitation.id));

      // Get the account information for response
      const [account] = await db
        .select()
        .from(accounts)
        .where(eq(accounts.id, invitation.accountId))
        .limit(1);

      newAccount = account;

      logger.info('Invitation processed successfully during signup', {
        userId: newUser.id,
        invitationId,
        accountId: invitation.accountId,
        brandIds: invitation.brandIds,
        role: invitation.role
      });

    } else {
      // Standard signup flow - just create user (no account needed)
      [newUser] = await db
        .insert(users)
        .values({
          auth0Id: auth0User.user_id,
          email,
          name
        })
        .returning()

      // Users can create accounts later when needed
      newAccount = null
    }

    // Store user data in session instead of sending token in response
    logger.debug('Starting session save in signup', { sessionExists: !!request.session });

    // Check for existing session by email in Redis
    if (request.server.redis) {
      try {
        // Get all session keys from Redis
        const sessionKeys = await request.server.redis.keys('sess:*');

        // Iterate through each session
        for (const sessionKey of sessionKeys) {
          const sessionData = await request.server.redis.get(sessionKey);

          if (sessionData) {
            try {
              const parsedSession = JSON.parse(sessionData);

              // If this session belongs to the same user, delete it
              if (parsedSession.user && parsedSession.user.email === email) {
                console.log(`Found existing session for ${email}, deleting: ${sessionKey}`);
                await request.server.redis.del(sessionKey);
              }
            } catch (parseErr) {
              console.error('Error parsing session data:', parseErr);
            }
          }
        }
      } catch (redisErr) {
        console.error('Redis operation failed when checking for existing sessions:', redisErr);
      }
    }

    // Regenerate the session ID to prevent session fixation
    await new Promise((resolve, reject) => {
      request.session.regenerate((err) => {
        if (err) {
          console.error('Session regeneration error in signup:', err);
          reject(err);
        } else {
          console.log('Session ID regenerated successfully in signup');
          resolve();
        }
      });
    });

    // Get the user's primary account (first account they belong to)
    let primaryAccountId = null;
    if (invitationId) {
      // For invited users, use the account they were invited to
      const userAccountRelations = await db
        .select({ accountId: schema.userAccounts.accountId })
        .from(schema.userAccounts)
        .where(eq(schema.userAccounts.userId, newUser.id))
        .limit(1);
      primaryAccountId = userAccountRelations[0]?.accountId || null;
    }
    // For non-invited users, primaryAccountId stays null since they don't auto-create accounts

    request.session.user = {
      id: newUser.id,
      email: newUser.email,
      name: newUser.name,
      auth0Id: auth0User.user_id,
      accountId: primaryAccountId,
      picture: userInfo.picture // Add profile picture
    };
    console.log('Session user data set in signup:', request.session.user);

    request.session.auth0Token = access_token; // Store token if needed for API calls
    console.log('Auth0 token stored in session in signup');

    // Set Redis session TTL to match Auth0 token expiration if available
    if (expires_in && typeof request.session.cookie.maxAge === 'number') {
      console.log(`Setting Redis session expiration to match Auth0 token: ${expires_in} seconds`);
      request.session.cookie.maxAge = expires_in * 1000; // Convert seconds to milliseconds

      // Also set TTL directly on Redis if fastify redis client is available
      if (request.server.redis && typeof request.session.id === 'string') {
        try {
          const sessionKey = `sess:${request.session.id}`;
          await request.server.redis.expire(sessionKey, expires_in);
          console.log(`Set Redis TTL for key ${sessionKey} to ${expires_in} seconds`);
        } catch (err) {
          console.error('Failed to set Redis TTL directly:', err);
        }
      }
    }

    // Force save the session explicitly
    if (typeof request.session.save === 'function') {
      console.log('Explicitly calling session.save() in signup');
      await new Promise((resolve, reject) => {
        request.session.save(err => {
          if (err) {
            console.error('Session save error in signup:', err);
            reject(err);
          } else {
            console.log('Session explicitly saved successfully in signup');
            resolve();
          }
        });
      });
    } else {
      console.log('No session.save method available in signup');
    }

    reply.code(201).send({
      message: 'User created successfully. Please check your email to verify your account.',
      user: request.session.user, // Send back the user data from session
      account: newAccount,
      access_token,
      emailVerificationRequired: true,
      // Removed access_token from response
      _debug_sessionid: request.session.id || 'unknown' // Add session ID for debugging
    });
  } catch (error) {
    logger.error('Signup failed', error, {
      email,
      auth0Error: error.response?.data
    })
    reply.code(400).send({
      error: error.message,
      details: error.response?.data || error.detail || 'Unknown error'
    })
  }
}

export async function login(request, reply) {
  try {
    const { email: username, password } = request.body
    const { invitationId } = request.query  // Get invitationId from query params
    const { authid } = request.query  // Get authid from query params

    logger.debug('Login attempt', { username, invitationId });

    if (!username || !password) {
      return reply.code(400).send({ error: 'Email and password are required' })
    }

    // Get the token using Resource Owner Password flow
    const tokenResponse = await axios.post(`https://${process.env.AUTH0_DOMAIN}/oauth/token`, {
      grant_type: 'http://auth0.com/oauth/grant-type/password-realm',
      realm: 'Username-Password-Authentication',
      username,
      password,
      client_id: process.env.AUTH0_CLIENT_ID,
      client_secret: process.env.AUTH0_CLIENT_SECRET,
      audience: process.env.AUTH0_AUDIENCE,
      scope: 'openid profile email'
    })

    const { access_token, expires_in } = tokenResponse.data

    if (!access_token) {
      reply.code(401).send({ error: 'The email or password you entered doesn\'t match our records. Please try again.' })
      return
    }

    // Get user profile from Auth0
    const userInfoResponse = await axios.get(`https://${process.env.AUTH0_DOMAIN}/userinfo`, {
      headers: {
        Authorization: `Bearer ${access_token}`
      }
    })

    const userInfo = userInfoResponse.data

    if (!userInfo || !userInfo.sub) {
      reply.code(401).send({ error: 'Failed to get user information' })
      return
    }

    // Find user in our database
    let [user] = await db
      .select()
      .from(users)
      .where(eq(users.auth0Id, userInfo.sub))
      .limit(1)

    if (!user) {
      // If user doesn't exist in our DB but exists in Auth0, create them
      [user] = await db
        .insert(users)
        .values({
          auth0Id: userInfo.sub,
          email: userInfo.email,
          name: userInfo.name || userInfo.email
        })
        .returning()
    }

    // If authid query parameter matches the user's Auth0 ID, automatically verify the user
    if (authid && authid === user.auth0Id && !user.isVerified) {
      [user] = await db
        .update(users)
        .set({ 
          isVerified: true,
          updatedAt: new Date()
        })
        .where(eq(users.id, user.id))
        .returning()

      logger.info('User automatically verified via authid', { 
        userId: user.id, 
        email: user.email,
        authid 
      })
    }

    if (!user.isVerified) {
      return reply.code(401).send({ error: 'Your account is not verified. Please check your email for a verification link.' })
    }

    // Process invitation if invitationId is provided
    if (invitationId) {
      await processInvitation(db, userInfo.email, user.id, invitationId);
      // Refresh user data after invitation processing
      [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, user.id))
        .limit(1)
    }

    // Store user data in session
    console.log('Starting session save. Session object exists:', !!request.session);

    // Check for existing session by email in Redis
    if (request.server.redis) {
      try {
        // Get all session keys from Redis
        const sessionKeys = await request.server.redis.keys('sess:*');

        // Iterate through each session
        for (const sessionKey of sessionKeys) {
          const sessionData = await request.server.redis.get(sessionKey);

          if (sessionData) {
            try {
              const parsedSession = JSON.parse(sessionData);

              // If this session belongs to the same user, delete it
              if (parsedSession.user && parsedSession.user.email === username) {
                console.log(`Found existing session for ${username}, deleting: ${sessionKey}`);
                await request.server.redis.del(sessionKey);
              }
            } catch (parseErr) {
              console.error('Error parsing session data:', parseErr);
            }
          }
        }
      } catch (redisErr) {
        console.error('Redis operation failed when checking for existing sessions:', redisErr);
      }
    }

    // Regenerate the session ID to prevent session fixation
    await new Promise((resolve, reject) => {
      request.session.regenerate((err) => {
        if (err) {
          console.error('Session regeneration error in login:', err);
          reject(err);
        } else {
          console.log('Session ID regenerated successfully in login');
          resolve();
        }
      });
    });

    // Get the user's primary account (first account they belong to)
    const userAccountRelations = await db
      .select({ 
        onboardingStep: accounts.onboardingStep,
        accountId: accounts.id
      })
      .from(schema.userAccounts)
      .leftJoin(accounts, eq(schema.userAccounts.accountId, accounts.id))
      .where(eq(schema.userAccounts.userId, user.id))
      .limit(1);

    const primaryAccountId = userAccountRelations[0]?.accountId || null;

    request.session.user = {
      id: user.id,
      email: user.email,
      name: user.name,
      auth0Id: user.auth0Id,
      accountId: primaryAccountId,
      picture: userInfo.picture, // Add profile picture
      isVerified: user.isVerified || false,
      onboardingStep: userAccountRelations[0]?.onboardingStep || 0
    };
    console.log('Session user data set:', request.session.user);

    request.session.auth0Token = access_token; // Store token if needed for API calls
    console.log('Auth0 token stored in session');

    // Set Redis session TTL to match Auth0 token expiration if available
    if (expires_in && typeof request.session.cookie.maxAge === 'number') {
      console.log(`Setting Redis session expiration to match Auth0 token: ${expires_in} seconds`);
      request.session.cookie.maxAge = expires_in * 1000; // Convert seconds to milliseconds

      // Also set TTL directly on Redis if fastify redis client is available
      if (request.server.redis && typeof request.session.id === 'string') {
        try {
          const sessionKey = `sess:${request.session.id}`;
          await request.server.redis.expire(sessionKey, expires_in);
          console.log(`Set Redis TTL for key ${sessionKey} to ${expires_in} seconds`);
        } catch (err) {
          console.error('Failed to set Redis TTL directly:', err);
        }
      }
    }

    // Force save the session explicitly
    if (typeof request.session.save === 'function') {
      console.log('Explicitly calling session.save()');
      await new Promise((resolve, reject) => {
        request.session.save(err => {
          if (err) {
            console.error('Session save error:', err);
            reject(err);
          } else {
            console.log('Session explicitly saved successfully');
            resolve();
          }
        });
      });
    } else {
      console.log('No session.save method available');
    }

    // Update lastLoginAt timestamp
    await db
      .update(users)
      .set({ lastLoginAt: new Date() })
      .where(eq(users.id, user.id));

    reply.send({
      message: 'Login successful',
      user: request.session.user, // Send back the user data from session
      access_token,
      // Removed access_token from response
      _debug_sessionid: request.session.id || 'unknown' // Add session ID for debugging
    });
  } catch (error) {
    console.error('Login Error:', error)
    if (error.response?.data) {
      console.error('Auth0 Error Details:', error.response.data)
    }
    reply.code(401).send({
      error: 'The email or password you entered doesn\'t match our records. Please try again.',
      details: error.response?.data || error.message
    })
  }
}

/**
 * Handles password reset requests by generating an Auth0 password reset ticket and sending a custom reset email.
 *
 * Always responds with a generic success message to prevent disclosure of user existence, regardless of whether the email is registered.
 * If the Auth0 ticket is generated, attempts to send a custom reset email; if this fails, Auth0's default email is still sent.
 * Responds with a 500 error and details only if an unexpected error occurs.
 */
export async function requestPasswordReset(request, reply) {
  const { email } = request.body

  try {
    // Get Management API token
    const tokenResponse = await axios.post(
      `https://${process.env.AUTH0_DOMAIN}/oauth/token`,
      {
        grant_type: "client_credentials",
        client_id: process.env.AUTH0_CLIENT_ID,
        client_secret: process.env.AUTH0_CLIENT_SECRET,
        audience: `https://${process.env.AUTH0_DOMAIN}/api/v2/`,
      }
    );

    if (!tokenResponse.data || !tokenResponse.data.access_token) {
      throw new Error('Failed to obtain Auth0 management token');
    }

    const token = tokenResponse.data.access_token;

    // Trigger password reset email
    const resetResponse = await axios.post(
      `https://${process.env.AUTH0_DOMAIN}/api/v2/tickets/password-change`,
      {
        email,
        connection_id: process.env.AUTH0_DB_CONNECTION_ID,
        ttl_sec: 86400 // 24 hours
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log('Password reset ticket received:', resetResponse.data);

    // If we got a ticket successfully, send our custom email
    if (resetResponse.data && resetResponse.data.ticket) {
      try {
        await emailQueue.addPasswordResetEmail({
          to: email,
          resetUrl: resetResponse.data.ticket
        });
        console.log('Custom password reset email sent to:', email);
      } catch (emailError) {
        console.error('Failed to send custom password reset email:', emailError);
        // We'll continue even if our custom email fails - Auth0 will still send its default email
      }
    }

    // For security reasons, always return a generic success message
    // regardless of whether the email exists in Auth0
    reply.send({
      message: 'If an account with that email exists, a password reset link has been sent.'
    });

  } catch (error) {
    console.error('Password Reset Request Error:', error);
    if (error.response?.status === 404) {
      // If user not found, still send a generic success message for security
      reply.send({
        message: 'If an account with that email exists, a password reset link has been sent.'
      });
    } else {
      // Handle other potential errors
      reply.code(500).send({
        error: 'Failed to process password reset request.',
        details: error.response?.data || error.message
      });
    }
  }
}

/**
 * Handles email verification callback from Auth0
 * This endpoint is called when users click the verification link in their email
 */
export async function handleEmailVerification(request, reply) {
  try {
    const { state, email } = request.query
    
    if (!state) {
      return reply.code(400).send({ error: 'Missing verification state' })
    }

    let verificationState
    try {
      verificationState = JSON.parse(decodeURIComponent(state))
    } catch (error) {
      logger.error('Invalid verification state', error, { state })
      return reply.code(400).send({ error: 'Invalid verification state' })
    }

    const { email: stateEmail, invitationId, timestamp } = verificationState

    // Verify the email matches
    if (email && email !== stateEmail) {
      logger.warn('Email verification mismatch', { queryEmail: email, stateEmail })
      return reply.code(400).send({ error: 'Email verification mismatch' })
    }

    // Check if verification is not too old (24 hours)
    const verificationAge = Date.now() - timestamp
    if (verificationAge > 86400000) { // 24 hours in milliseconds
      logger.warn('Email verification expired', { email: stateEmail, age: verificationAge })
      return reply.code(400).send({ error: 'Email verification link has expired' })
    }

    // Find the user in our database
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.email, stateEmail))
      .limit(1)

    if (!user) {
      logger.error('User not found for email verification', { email: stateEmail })
      return reply.code(404).send({ error: 'User not found' })
    }

    // Update user verification status in our database
    await db
      .update(users)
      .set({ 
        isVerified: true,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id))

    logger.info('User email verified successfully', { 
      userId: user.id, 
      email: stateEmail 
    })

    // If there was an invitation, process it now that the user is verified
    if (invitationId) {
      try {
        await processInvitation(db, stateEmail, user.id, invitationId)
        logger.info('Invitation processed after email verification', { 
          userId: user.id, 
          invitationId 
        })
      } catch (invitationError) {
        logger.error('Failed to process invitation after verification', invitationError, {
          userId: user.id,
          invitationId
        })
        // Continue with verification success even if invitation processing fails
      }
    }

    // Redirect to success page
    const baseUrl = process.env.APP_BASE_URL || 'https://app.adfury.ai'
    const redirectUrl = `${baseUrl}/email-verified-success${invitationId ? `?invitationProcessed=true` : ''}`
    
    reply.redirect(redirectUrl)
  } catch (error) {
    logger.error('Email verification failed', error)
    reply.code(500).send({
      error: 'Email verification failed',
      details: error.message
    })
  }
}

/**
 * Resends email verification for a user
 */
export async function resendEmailVerification(request, reply) {
  try {
    const { email } = request.body

    if (!email) {
      return reply.code(400).send({ error: 'Email is required' })
    }

    // Find the user in our database
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1)

    if (!user) {
      // For security, don't reveal if user exists
      return reply.send({ 
        message: 'If an account with that email exists and is not verified, a verification email has been sent.' 
      })
    }

    if (user.isVerified) {
      return reply.code(400).send({ error: 'Email is already verified' })
    }

    // Create new email verification ticket
    const baseUrl = process.env.APP_BASE_URL || 'http://localhost:3000'

    const ticket = await auth0Client.tickets.verifyEmail({
      user_id: user.auth0Id,
      result_url: `${baseUrl}/login?authid=${user.auth0Id}&email=${email}`,
      includeEmailInRedirect: true,
      ttl_sec: 86400 // 24 hours
    })

    logger.info('Email verification ticket resent', {
      userId: user.id,
      email,
      ticketUrl: ticket.data.ticket
    })

    // Send custom verification email
    try {
      await emailQueue.addCustomEmail({
        to: email,
        subject: 'Verify your email address',
        heading: 'Email Verification',
        content: `
          <p>Hello ${user.name},</p>
          <p>You requested to resend your email verification. Please click the button below to verify your email address:</p>
        `,
        buttonText: 'Verify Email Address',
        buttonLink: ticket.data.ticket,
        footerText: 'If you didn\'t request this, you can safely ignore this email.',
        fromName: 'AdFury Team'
      })
      logger.info('Custom verification email resent', { email })
    } catch (emailError) {
      logger.error('Failed to queue verification email resend', emailError, { email })
      // Continue even if custom email fails
    }

    reply.send({
      message: 'If an account with that email exists and is not verified, a verification email has been sent.'
    })
  } catch (error) {
    logger.error('Resend email verification failed', error)
    reply.code(500).send({
      error: 'Failed to resend verification email',
      details: error.message
    })
  }
}

/**
 * Verifies a user's email using their Auth0 ID
 */
export async function verifyEmail(request, reply) {
  try {
    const { authId, email } = request.body

    if (!authId || !email) {
      return reply.code(400).send({ error: 'More fields are required' })
    }

    // Find the user in our database by Auth0 ID
    const [user] = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.auth0Id, authId),
          eq(users.email, email)
        )
      )
      .limit(1)

    if (!user) {
      logger.error('User not found for email verification')
      return reply.code(404).send({ error: 'User not found' })
    }

    // Check if user is already verified
    if (user.isVerified) {
      logger.info('User already verified', { userId: user.id, email: user.email })
      
      return reply.send({ 
        message: 'Email already verified',
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          isVerified: true
        }
      })
    }

    // Update user verification status in our database
    const [updatedUser] = await db
      .update(users)
      .set({ 
        isVerified: true,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id))
      .returning()

    logger.info('User email verified successfully', { 
      userId: user.id, 
      email: user.email,
      authId 
    })

    // Otherwise return JSON response
    reply.send({
      message: 'Email verified successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        isVerified: updatedUser.isVerified
      }
    })
  } catch (error) {
    logger.error('Email verification failed', error, { authid: request.query.authid })
    reply.code(500).send({
      error: 'Email verification failed',
      details: error.message
    })
  }
}

