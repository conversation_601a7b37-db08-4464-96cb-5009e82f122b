import {
  createProof,
  getProofById,
  getAllProofs,
  updateProof,
  deleteProof,
  getProofsByGenerationVersion,
  getProofForReviewer,
  generateReviewerHash
} from './proofs.controllers.js'
import { proofSchema } from './proofs.schemas.js'

export default async function proofRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all proofs
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllProofs
  })

  // Get proof by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: proofSchema.getById,
    handler: getProofById
  })

  // Create proof
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    schema: proofSchema.create,
    handler: createProof
  })

  // Update proof
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: proofSchema.update,
    handler: updateProof
  })

  // Delete proof
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: proofSchema.delete,
    handler: deleteProof
  })

  // Get proofs by generation version
  fastify.get('/generation-version/:generationVersionId', {
    onRequest: [fastify.verifyAuth],
    schema: proofSchema.getByGenerationVersion,
    handler: getProofsByGenerationVersion
  })

  // Get proof for reviewer (with reviewer hash authentication)
  fastify.get('/reviewer/:generationVersionId', {
    // Note: This route doesn't require auth since it uses reviewer hash for authentication
    schema: proofSchema.getForReviewer,
    handler: getProofForReviewer
  })

  // Generate reviewer hash
  fastify.get('/generate-reviewer-hash/:reviewerId/:reviewerName', {
    onRequest: [fastify.verifyAuth],
    handler: generateReviewerHash
  })
}
