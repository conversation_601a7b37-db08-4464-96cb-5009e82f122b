export const proofSchema = {
  create: {
    body: {
      type: 'object',
      required: ['generationVersionId', 'title'],
      properties: {
        generationVersionId: { type: 'string', format: 'uuid' },
        title: { type: 'string' },
        emailDescription: { type: 'string' },
        adUnitIds: {
          type: 'array',
          items: { type: 'string' }
        },
        reviewers: {
          type: 'object',
          properties: {
            internal: {
              type: 'array',
              items: { type: 'string', format: 'uuid' }
            },
            external: {
              type: 'array',
              items: { type: 'string', format: 'uuid' }
            }
          },
          additionalProperties: false
        }
      }
    }
  },
  update: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        emailDescription: { type: 'string' },
        adUnitIds: {
          type: 'array',
          items: { type: 'string' }
        },
        reviewers: {
          type: 'object',
          properties: {
            internal: {
              type: 'array',
              items: { type: 'string', format: 'uuid' }
            },
            external: {
              type: 'array',
              items: { type: 'string', format: 'uuid' }
            }
          },
          additionalProperties: false
        }
      }
    }
  },
  getById: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    }
  },
  delete: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    }
  },
  getByGenerationVersion: {
    params: {
      type: 'object',
      required: ['generationVersionId'],
      properties: {
        generationVersionId: { type: 'string', format: 'uuid' }
      }
    }
  },
  getForReviewer: {
    params: {
      type: 'object',
      required: ['generationVersionId'],
      properties: {
        generationVersionId: { type: 'string', format: 'uuid' }
      }
    },
    querystring: {
      type: 'object',
      required: ['reviewer'],
      properties: {
        reviewer: { type: 'string' }
      }
    }
  },
  generateReviewerHash: {
    params: {
      type: 'object',
      required: ['reviewerId', 'reviewerName'],
      properties: {
        reviewerId: { type: 'string', format: 'uuid' },
        reviewerName: { type: 'string' }
      }
    }
  }
} 