import { eq, and, inArray } from 'drizzle-orm'
import crypto from 'crypto'
import chalk from 'chalk'
import { db } from '../../db/index.js'
import { proofs } from '../../db/schema/proofs.ts'
import { generationVersions } from '../../db/schema/generationVersions.ts'
import { generations } from '../../db/schema/generations.ts'
import { externalReviewers } from '../../db/schema/externalReviewers.ts'
import { reviewers as reviewersTable } from '../../db/schema/reviewers.ts'
import { users } from '../../db/schema/users.ts'
import { userAccounts } from '../../db/schema/userAccounts.ts'
import { brands } from '../../db/schema/brands.ts'
import { emailQueue } from '../../services/emailQueue.service.js'

// Helper function to generate secure hash for reviewer authentication
async function generateReviewerHashHelper(reviewerId, reviewerName) {
  const data = `${reviewerId}:${reviewerName}:adfury-reviewer-salt`
  return crypto.createHash('sha256').update(data).digest('hex')
}

// Controller function to generate reviewer hash for API endpoint
export async function generateReviewerHash(request, reply) {
  try {
    const { reviewerId, reviewerName } = request.params

    if (!reviewerId || !reviewerName) {
      return reply.code(400).send({ 
        error: 'Both reviewerId and reviewerName are required' 
      })
    }

    // Generate the hash using the reviewerId from the reviewers table
    const hash = await generateReviewerHashHelper(reviewerId, reviewerName)
    
    return reply.send({ 
      reviewerId,
      reviewerName,
      hash,
      proofUrl: `https://app.adfury.ai/creative-proof?reviewer=${hash}`
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to generate reviewer hash' })
  }
}

// Helper function to verify reviewer hash (basic length check)
function verifyReviewerHash(hash) {
  return hash && hash.length === 64 // SHA256 hex length
}

// Helper function to extract reviewer info from hash by checking against proof reviewers
// Uses reviewerId (from reviewers table) for hash verification
async function getReviewerFromHash(hash, proofId) {
  // Get all reviewers for this proof from the reviewers table
  const allReviewers = await db
    .select({
      id: reviewersTable.id,
      name: reviewersTable.name,
      email: reviewersTable.email,
      userId: reviewersTable.userId,
      isExternal: reviewersTable.isExternal
    })
    .from(reviewersTable)
    .where(eq(reviewersTable.proofId, proofId))

  console.log(`Found ${allReviewers.length} reviewers for proof ${proofId}`)

  // Check each reviewer to see if their hash matches
  for (const reviewer of allReviewers) {
    // First try the new way (using reviewerId)
    const expectedHashNew = await generateReviewerHashHelper(reviewer.id, reviewer.name)
    console.log(`Checking reviewer ${reviewer.name}:`)
    console.log(`  New hash (reviewerId): ${expectedHashNew}`)
    console.log(`  Provided hash: ${hash}`)
    
    if (hash === expectedHashNew) {
      console.log(`  ✅ Match found using reviewerId`)
      return { 
        name: reviewer.name, 
        email: reviewer.email,
        type: reviewer.isExternal ? 'external' : 'internal',
        reviewerId: reviewer.id,
        userId: reviewer.userId
      }
    }
    
    // Backward compatibility: Try the old way (using userId) if userId exists
    if (reviewer.userId) {
      const expectedHashOld = await generateReviewerHashHelper(reviewer.userId, reviewer.name)
      console.log(`  Old hash (userId): ${expectedHashOld}`)
      
      if (hash === expectedHashOld) {
        console.log(`  ✅ Match found using userId (backward compatibility)`)
        return { 
          name: reviewer.name, 
          email: reviewer.email,
          type: reviewer.isExternal ? 'external' : 'internal',
          reviewerId: reviewer.id,
          userId: reviewer.userId
        }
      }
    }
    
    console.log(`  ❌ No match for ${reviewer.name}`)
  }

  return null
}



export async function createProof(request, reply) {
  try {
    const {
      generationVersionId,
      title,
      emailDescription,
      adUnitIds,
      reviewers
    } = request.body

    console.log('request.body', request.body)

    // Get the current user creating the proof
    const currentUser = request.user
    if (!currentUser) {
      return reply.code(401).send({ error: 'Unauthorized - No user session found' })
    }

    // Verify that the generation version exists
    const [existingGenerationVersion] = await db
      .select()
      .from(generationVersions)
      .where(eq(generationVersions.id, generationVersionId))
      .limit(1)

    console.log('existingGenerationVersion', existingGenerationVersion)

    if (!existingGenerationVersion) {
      console.log('Generation version not found')
      return reply.code(404).send({ error: 'Generation version not found' })
    }

    // Create the proof record
    const [proof] = await db
      .insert(proofs)
      .values({
        generationVersionId,
        title,
        emailDescription: emailDescription || null,
        adUnitIds: adUnitIds || [],
        reviewers: reviewers || { internal: [], external: [] },
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    console.log('Created proof:', proof)

    if (!proof || !proof.id) {
      console.error('Failed to create proof or proof ID is missing')
      return reply.code(500).send({ error: 'Failed to create proof' })
    }

    // Process reviewers and send invitation emails
    const reviewerData = reviewers || { internal: [], external: [] }
    
    // Add the proof creator as a reviewer
    console.log('Current user:', currentUser)
    console.log('Proof object:', proof)
    
    const reviewerValues = {
      proofId: proof.id,
      name: currentUser.name || 'Unknown User',
      email: currentUser.email || '<EMAIL>',
      userId: currentUser.id,
      isExternal: false,
      status: 'pending',
      requestedBy: currentUser.id,
      lastRequestedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    console.log('About to insert creator as reviewer:', reviewerValues)

    try {
      console.log('Attempting to insert reviewer with values:', reviewerValues)
      console.log('Reviewers table schema:', reviewersTable)
      
      const result = await db
        .insert(reviewersTable)
        .values([reviewerValues])
        .returning()
      
      console.log('Successfully inserted reviewer:', result)
    } catch (insertError) {
      console.error('Error inserting reviewer:', insertError)
      console.error('Reviewer values:', reviewerValues)
      console.error('Error stack:', insertError.stack)
      throw insertError
    }
    
    // Fetch internal reviewers (users)
    if (reviewerData.internal && reviewerData.internal.length > 0) {
      const internalReviewersList = await db
        .select({
          id: users.id,
          name: users.name,
          email: users.email
        })
        .from(users)
        .where(inArray(users.id, reviewerData.internal))

      // Save internal reviewers to reviewers table and send emails
      for (const reviewer of internalReviewersList) {
        // Save to reviewers table
        console.log('About to insert internal reviewer:', {
          proofId: proof.id,
          name: reviewer.name,
          email: reviewer.email,
          userId: reviewer.id,
          isExternal: false,
          status: 'pending',
          requestedBy: currentUser.id
        })

        const [insertedReviewer] = await db
          .insert(reviewersTable)
          .values([{
            proofId: proof.id,
            name: reviewer.name,
            email: reviewer.email,
            userId: reviewer.id,
            isExternal: false,
            status: 'pending',
            requestedBy: currentUser.id,
            lastRequestedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          }])
          .returning()

        const reviewerHash = await generateReviewerHashHelper(insertedReviewer.id, reviewer.name)
        const proofUrl = `https://app.adfury.ai/creative-proof?session=${generationVersionId}&reviewer=${reviewerHash}`
        
        try {
          await emailQueue.addCustomEmail({
            to: reviewer.email,
            subject: `Proof Review Request: ${title}`,
            heading: `You've been invited to review a creative proof`,
            content: `
              <p>Hello ${reviewer.name},</p>
              <p>You've been invited to review a creative proof titled "<strong>${title}</strong>".</p>
              ${emailDescription ? `<p><strong>Description:</strong> ${emailDescription}</p>` : ''}
              <p>Please click the button below to review the proof and provide your feedback.</p>
            `,
            buttonText: 'Review Proof',
            buttonLink: proofUrl,
            footerText: 'This is an automated message from AdFury. Please do not reply to this email.',
            fromName: 'AdFury Team'
          })
          
          console.log(chalk.green('✅ Invitation email sent to internal reviewer:'), chalk.cyan(reviewer.email))
        } catch (emailError) {
          console.error(chalk.red('❌ Failed to send email to internal reviewer'), chalk.cyan(reviewer.email) + chalk.red(':'), emailError)
        }
      }
    }

    // Process external reviewers
    if (reviewerData.external && reviewerData.external.length > 0) {
      // Handle both ID-based and email-based external reviewers
      const externalReviewersList = []
      
      for (const externalReviewerData of reviewerData.external) {
        let externalReviewer = null
        
        if (typeof externalReviewerData === 'string') {
          // If it's a string, it could be an ID or email
          // First try to find by ID
          const [existingById] = await db
            .select()
            .from(externalReviewers)
            .where(eq(externalReviewers.id, externalReviewerData))
            .limit(1)
          
          if (existingById) {
            externalReviewer = existingById
          } else {
            // Try to find by email
            const [existingByEmail] = await db
              .select()
              .from(externalReviewers)
              .where(eq(externalReviewers.email, externalReviewerData))
              .limit(1)
            
            if (existingByEmail) {
              externalReviewer = existingByEmail
            } else {
              // If it looks like an email, create a new external reviewer
              if (externalReviewerData.includes('@')) {
                                 // Get user's account if not in session
                 let userAccountId = currentUser.accountId
                 if (!userAccountId) {
                   const [userAccount] = await db
                     .select({ accountId: userAccounts.accountId })
                     .from(userAccounts)
                     .where(eq(userAccounts.userId, currentUser.id))
                     .limit(1)
                   userAccountId = userAccount?.accountId
                 }

                 const [newExternalReviewer] = await db
                   .insert(externalReviewers)
                   .values({
                     accountId: userAccountId,
                     jobTitle: 'External Reviewer',
                     name: externalReviewerData.split('@')[0], // Use email prefix as name
                     email: externalReviewerData,
                     requestedByUserId: currentUser.id,
                     createdAt: new Date(),
                     updatedAt: new Date()
                   })
                   .returning()
                
                externalReviewer = newExternalReviewer
              }
            }
          }
        } else if (typeof externalReviewerData === 'object') {
          // If it's an object with email and name
          const [existingByEmail] = await db
            .select()
            .from(externalReviewers)
            .where(eq(externalReviewers.email, externalReviewerData.email))
            .limit(1)
          
          if (existingByEmail) {
            externalReviewer = existingByEmail
          } else {
                         // Get user's account if not in session
             let userAccountId = currentUser.accountId
             if (!userAccountId) {
               const [userAccount] = await db
                 .select({ accountId: userAccounts.accountId })
                 .from(userAccounts)
                 .where(eq(userAccounts.userId, currentUser.id))
                 .limit(1)
               userAccountId = userAccount?.accountId
             }

             // Create new external reviewer
             const [newExternalReviewer] = await db
               .insert(externalReviewers)
               .values({
                 accountId: userAccountId,
                 jobTitle: externalReviewerData.jobTitle || 'External Reviewer',
                 name: externalReviewerData.name,
                 email: externalReviewerData.email,
                 requestedByUserId: currentUser.id,
                 createdAt: new Date(),
                 updatedAt: new Date()
               })
               .returning()
            
            externalReviewer = newExternalReviewer
          }
        }
        
        if (externalReviewer) {
          externalReviewersList.push(externalReviewer)
        }
      }

      // Save external reviewers to reviewers table and send emails
      for (const reviewer of externalReviewersList) {
        // Save to reviewers table
        console.log('About to insert external reviewer:', {
          proofId: proof.id,
          name: reviewer.name,
          email: reviewer.email,
          userId: reviewer.id, // Use external reviewer's ID
          isExternal: true,
          status: 'pending',
          requestedBy: currentUser.id
        })

        const [insertedReviewer] = await db
          .insert(reviewersTable)
          .values([{
            proofId: proof.id,
            name: reviewer.name,
            email: reviewer.email,
            userId: reviewer.id, // Use external reviewer's ID from externalReviewers table
            isExternal: true,
            status: 'pending',
            requestedBy: currentUser.id,
            lastRequestedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          }])
          .returning()

        const reviewerHash = await generateReviewerHashHelper(insertedReviewer.id, reviewer.name)
        const proofUrl = `https://app.adfury.ai/creative-proof?session=${generationVersionId}&reviewer=${reviewerHash}`
        
        try {
          console.log(chalk.blue('📧 Sending email to external reviewer:'), chalk.cyan(reviewer.email))
          console.log(chalk.gray('🔗 Proof URL:'), chalk.underline.cyan(proofUrl))
          await emailQueue.addCustomEmail({
            to: reviewer.email,
            subject: `Proof Review Request: ${title}`,
            heading: `You've been invited to review a creative proof`,
            content: `
              <p>Hello ${reviewer.name},</p>
              <p>You've been invited to review a creative proof titled "<strong>${title}</strong>".</p>
              ${emailDescription ? `<p><strong>Description:</strong> ${emailDescription}</p>` : ''}
              <p>Please click the button below to review the proof and provide your feedback.</p>
            `,
            buttonText: 'Review Proof',
            buttonLink: proofUrl,
            footerText: 'This is an automated message from AdFury. Please do not reply to this email.',
            fromName: 'AdFury Team'
          })
          
          console.log(chalk.green('✅ Invitation email sent to external reviewer:'), chalk.cyan(reviewer.email))
        } catch (emailError) {
          console.error(chalk.red('❌ Failed to send email to external reviewer'), chalk.cyan(reviewer.email) + chalk.red(':'), emailError)
        }
      }
    }

    return reply.code(201).send(proof)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create proof' })
  }
}

export async function getProofById(request, reply) {
  try {
    const { id } = request.params

    // Get the proof with its related generation version data
    const [result] = await db
      .select({
        proof: proofs,
        generationVersion: generationVersions,
        generation: generations
      })
      .from(proofs)
      .leftJoin(generationVersions, eq(proofs.generationVersionId, generationVersions.id))
      .leftJoin(generations, eq(generationVersions.generationId, generations.id))
      .where(eq(proofs.id, id))
      .limit(1)

    if (!result) {
      return reply.code(404).send({ error: 'Proof not found' })
    }

    // Flatten the response
    const response = {
      ...result.proof,
      generationVersion: result.generationVersion,
      generation: result.generation
    }

    return reply.send(response)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch proof' })
  }
}

export async function getAllProofs(request, reply) {
  try {
    const allProofs = await db
      .select({
        proof: proofs,
        generationVersion: generationVersions,
        generation: generations
      })
      .from(proofs)
      .leftJoin(generationVersions, eq(proofs.generationVersionId, generationVersions.id))
      .leftJoin(generations, eq(generationVersions.generationId, generations.id))
      .orderBy(proofs.createdAt)

    return reply.send(allProofs)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch proofs' })
  }
}

export async function updateProof(request, reply) {
  try {
    const { id } = request.params
    const {
      title,
      emailDescription,
      adUnitIds,
      reviewers
    } = request.body

    // Get the current user updating the proof
    const currentUser = request.user
    if (!currentUser) {
      return reply.code(401).send({ error: 'Unauthorized - No user session found' })
    }

    // Check if proof exists
    const [existingProof] = await db
      .select()
      .from(proofs)
      .where(eq(proofs.id, id))
      .limit(1)

    if (!existingProof) {
      return reply.code(404).send({ error: 'Proof not found' })
    }

    // Get existing reviewers for this proof
    const existingReviewers = await db
      .select({
        id: reviewersTable.id,
        name: reviewersTable.name,
        email: reviewersTable.email,
        userId: reviewersTable.userId,
        isExternal: reviewersTable.isExternal
      })
      .from(reviewersTable)
      .where(eq(reviewersTable.proofId, id))

    // Update the proof basic fields
    const [updatedProof] = await db
      .update(proofs)
      .set({
        title: title || existingProof.title,
        emailDescription: emailDescription !== undefined ? emailDescription : existingProof.emailDescription,
        adUnitIds: adUnitIds || existingProof.adUnitIds,
        reviewers: reviewers || existingProof.reviewers,
        updatedAt: new Date()
      })
      .where(eq(proofs.id, id))
      .returning()

    // Process new reviewers if provided
    if (reviewers) {
      const reviewerData = reviewers || { internal: [], external: [] }
      
      // Get existing reviewer user IDs for comparison
      const existingInternalIds = existingReviewers
        .filter(r => !r.isExternal && r.userId)
        .map(r => r.userId)
      
      const existingExternalEmails = existingReviewers
        .filter(r => r.isExternal)
        .map(r => r.email)

      // Process new internal reviewers
      if (reviewerData.internal && reviewerData.internal.length > 0) {
        const newInternalIds = reviewerData.internal.filter(id => !existingInternalIds.includes(id))
        
        if (newInternalIds.length > 0) {
          const newInternalReviewers = await db
            .select({
              id: users.id,
              name: users.name,
              email: users.email
            })
            .from(users)
            .where(inArray(users.id, newInternalIds))

          // Save new internal reviewers to reviewers table and send emails
          for (const reviewer of newInternalReviewers) {
            console.log('Adding new internal reviewer:', {
              proofId: id,
              name: reviewer.name,
              email: reviewer.email,
              userId: reviewer.id,
              isExternal: false
            })

            const [insertedReviewer] = await db
              .insert(reviewersTable)
              .values([{
                proofId: id,
                name: reviewer.name,
                email: reviewer.email,
                userId: reviewer.id,
                isExternal: false,
                status: 'pending',
                requestedBy: currentUser.id,
                lastRequestedAt: new Date(),
                createdAt: new Date(),
                updatedAt: new Date()
              }])
              .returning()

            const reviewerHash = await generateReviewerHashHelper(insertedReviewer.id, reviewer.name)
            const proofUrl = `https://app.adfury.ai/creative-proof?session=${updatedProof.generationVersionId}&reviewer=${reviewerHash}`
            
            try {
              await emailQueue.addCustomEmail({
                to: reviewer.email,
                subject: `Proof Review Request: ${updatedProof.title}`,
                heading: `You've been invited to review a creative proof`,
                content: `
                  <p>Hello ${reviewer.name},</p>
                  <p>You've been invited to review a creative proof titled "<strong>${updatedProof.title}</strong>".</p>
                  ${updatedProof.emailDescription ? `<p><strong>Description:</strong> ${updatedProof.emailDescription}</p>` : ''}
                  <p>Please click the button below to review the proof and provide your feedback.</p>
                `,
                buttonText: 'Review Proof',
                buttonLink: proofUrl,
                footerText: 'This is an automated message from AdFury. Please do not reply to this email.',
                fromName: 'AdFury Team'
              })
              
              console.log(chalk.green('✅ Invitation email sent to new internal reviewer:'), chalk.cyan(reviewer.email))
            } catch (emailError) {
              console.error(chalk.red('❌ Failed to send email to new internal reviewer'), chalk.cyan(reviewer.email) + chalk.red(':'), emailError)
            }
          }
        }
      }

      // Process new external reviewers
      if (reviewerData.external && reviewerData.external.length > 0) {
        const newExternalReviewers = []
        
        for (const externalReviewerData of reviewerData.external) {
          let reviewerEmail = null
          
          if (typeof externalReviewerData === 'string') {
            reviewerEmail = externalReviewerData.includes('@') ? externalReviewerData : null
          } else if (typeof externalReviewerData === 'object' && externalReviewerData.email) {
            reviewerEmail = externalReviewerData.email
          }
          
          // Only process if it's a new external reviewer
          if (reviewerEmail && !existingExternalEmails.includes(reviewerEmail)) {
            let externalReviewer = null
            
            if (typeof externalReviewerData === 'string') {
              // Try to find existing external reviewer by ID first
              const [existingById] = await db
                .select()
                .from(externalReviewers)
                .where(eq(externalReviewers.id, externalReviewerData))
                .limit(1)
              
              if (existingById) {
                externalReviewer = existingById
              } else {
                // Try to find by email
                const [existingByEmail] = await db
                  .select()
                  .from(externalReviewers)
                  .where(eq(externalReviewers.email, reviewerEmail))
                  .limit(1)
                
                if (existingByEmail) {
                  externalReviewer = existingByEmail
                } else {
                  // Create new external reviewer
                  let userAccountId = currentUser.accountId
                  if (!userAccountId) {
                    const [userAccount] = await db
                      .select({ accountId: userAccounts.accountId })
                      .from(userAccounts)
                      .where(eq(userAccounts.userId, currentUser.id))
                      .limit(1)
                    userAccountId = userAccount?.accountId
                  }

                  const [newExternalReviewer] = await db
                    .insert(externalReviewers)
                    .values({
                      accountId: userAccountId,
                      jobTitle: 'External Reviewer',
                      name: reviewerEmail.split('@')[0],
                      email: reviewerEmail,
                      requestedByUserId: currentUser.id,
                      createdAt: new Date(),
                      updatedAt: new Date()
                    })
                    .returning()
                  
                  externalReviewer = newExternalReviewer
                }
              }
            } else if (typeof externalReviewerData === 'object') {
              // Try to find existing by email
              const [existingByEmail] = await db
                .select()
                .from(externalReviewers)
                .where(eq(externalReviewers.email, externalReviewerData.email))
                .limit(1)
              
              if (existingByEmail) {
                externalReviewer = existingByEmail
              } else {
                // Create new external reviewer
                let userAccountId = currentUser.accountId
                if (!userAccountId) {
                  const [userAccount] = await db
                    .select({ accountId: userAccounts.accountId })
                    .from(userAccounts)
                    .where(eq(userAccounts.userId, currentUser.id))
                    .limit(1)
                  userAccountId = userAccount?.accountId
                }

                const [newExternalReviewer] = await db
                  .insert(externalReviewers)
                  .values({
                    accountId: userAccountId,
                    jobTitle: externalReviewerData.jobTitle || 'External Reviewer',
                    name: externalReviewerData.name,
                    email: externalReviewerData.email,
                    requestedByUserId: currentUser.id,
                    createdAt: new Date(),
                    updatedAt: new Date()
                  })
                  .returning()
                
                externalReviewer = newExternalReviewer
              }
            }
            
            if (externalReviewer) {
              newExternalReviewers.push(externalReviewer)
            }
          }
        }

        // Save new external reviewers to reviewers table and send emails
        for (const reviewer of newExternalReviewers) {
          console.log('Adding new external reviewer:', {
            proofId: id,
            name: reviewer.name,
            email: reviewer.email,
            userId: reviewer.id,
            isExternal: true
          })

          const [insertedReviewer] = await db
            .insert(reviewersTable)
            .values([{
              proofId: id,
              name: reviewer.name,
              email: reviewer.email,
              userId: reviewer.id,
              isExternal: true,
              status: 'pending',
              requestedBy: currentUser.id,
              lastRequestedAt: new Date(),
              createdAt: new Date(),
              updatedAt: new Date()
            }])
            .returning()

          const reviewerHash = await generateReviewerHashHelper(insertedReviewer.id, reviewer.name)
          const proofUrl = `https://app.adfury.ai/creative-proof?session=${updatedProof.generationVersionId}&reviewer=${reviewerHash}`
          
          try {
            console.log(chalk.blue('📧 Sending email to new external reviewer:'), chalk.cyan(reviewer.email))
            console.log(chalk.gray('🔗 Proof URL:'), chalk.underline.cyan(proofUrl))
            await emailQueue.addCustomEmail({
              to: reviewer.email,
              subject: `Proof Review Request: ${updatedProof.title}`,
              heading: `You've been invited to review a creative proof`,
              content: `
                <p>Hello ${reviewer.name},</p>
                <p>You've been invited to review a creative proof titled "<strong>${updatedProof.title}</strong>".</p>
                ${updatedProof.emailDescription ? `<p><strong>Description:</strong> ${updatedProof.emailDescription}</p>` : ''}
                <p>Please click the button below to review the proof and provide your feedback.</p>
              `,
              buttonText: 'Review Proof',
              buttonLink: proofUrl,
              footerText: 'This is an automated message from AdFury. Please do not reply to this email.',
              fromName: 'AdFury Team'
            })
            
            console.log(chalk.green('✅ Invitation email sent to new external reviewer:'), chalk.cyan(reviewer.email))
          } catch (emailError) {
            console.error(chalk.red('❌ Failed to send email to new external reviewer'), chalk.cyan(reviewer.email) + chalk.red(':'), emailError)
          }
        }
      }
    }

    return reply.send(updatedProof)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update proof' })
  }
}

export async function deleteProof(request, reply) {
  try {
    const { id } = request.params

    // Check if proof exists
    const [existingProof] = await db
      .select()
      .from(proofs)
      .where(eq(proofs.id, id))
      .limit(1)

    if (!existingProof) {
      return reply.code(404).send({ error: 'Proof not found' })
    }

    // Delete the proof
    await db
      .delete(proofs)
      .where(eq(proofs.id, id))

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete proof' })
  }
}

export async function getProofsByGenerationVersion(request, reply) {
  try {
    const { generationVersionId } = request.params

    // Verify that the generation version exists
    const [existingGenerationVersion] = await db
      .select()
      .from(generationVersions)
      .where(eq(generationVersions.id, generationVersionId))
      .limit(1)

    if (!existingGenerationVersion) {
      return reply.code(404).send({ error: 'Generation version not found' })
    }

    // Get all proofs for this generation version
    const proofsForVersion = await db
      .select()
      .from(proofs)
      .where(eq(proofs.generationVersionId, generationVersionId))
      .orderBy(proofs.createdAt)

    return reply.send(proofsForVersion)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch proofs for generation version' })
  }
}

export async function getProofForReviewer(request, reply) {
  try {
    const { generationVersionId } = request.params
    const { reviewer } = request.query

    if (!reviewer) {
      return reply.code(400).send({ error: 'Reviewer hash is required' })
    }

    // Verify that the generation version exists
    const [existingGenerationVersion] = await db
      .select()
      .from(generationVersions)
      .where(eq(generationVersions.id, generationVersionId))
      .limit(1)

    if (!existingGenerationVersion) {
      return reply.code(404).send({ error: 'Generation version not found' })
    }

    // Get the proof for this generation version
    const [result] = await db
      .select({
        proof: proofs,
        generationVersion: generationVersions,
        generation: generations,
        brand: brands
      })
      .from(proofs)
      .leftJoin(generationVersions, eq(proofs.generationVersionId, generationVersions.id))
      .leftJoin(generations, eq(generationVersions.generationId, generations.id))
      .leftJoin(brands, eq(generations.brandId, brands.id))
      .where(eq(proofs.generationVersionId, generationVersionId))
      .limit(1)

    if (!result) {
      return reply.code(404).send({ error: 'Proof not found' })
    }

    // Basic hash verification
    if (!verifyReviewerHash(reviewer)) {
      console.log('Invalid reviewer authentication')
      return reply.code(401).send({ error: 'Invalid reviewer authentication' })
    }

    // Get reviewer info from hash
    const reviewerInfo = await getReviewerFromHash(reviewer, result.proof.id)
    
    if (!reviewerInfo) {
      console.log('reviewerInfo', result.proof, reviewer)
      console.log('Invalid reviewer authentication 2')
      return reply.code(401).send({ error: 'Invalid reviewer authentication' })
    }

    // Flatten the response
    const response = {
      ...result.proof,
      generationVersion: result.generationVersion,
      generation: result.generation,
      brand: result.brand,
      // Don't expose full reviewer list to individual reviewers
      reviewerAccess: true,
      reviewerName: reviewerInfo.name,
      reviewerInfo: reviewerInfo
    }

    return reply.send(response)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch proof for reviewer' })
  }
}
