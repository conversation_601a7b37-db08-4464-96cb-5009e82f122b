import { eq, ne, and, inArray } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { accounts } from '../../db/schema/accounts.ts'
import { brands } from '../../db/schema/brands.ts'
import { brandUsers } from '../../db/schema/brandUsers.ts'
import { users } from '../../db/schema/users.ts'
import { AuthService } from '../../services/auth.service.ts'
import { authService } from '../../services/authorization.service.js'
import { userAccounts } from '../../db/schema/userAccounts.ts'
import { credentialSets } from '../../db/schema/credentialSets.ts'
import { retailers, brandRetailers } from '../../db/schema/retailers.ts'
import { userCredentialSets } from '../../db/schema/userCredentialSets.ts'
import { invitations } from '../../db/schema/invitations.ts'

/**
 * Retrieves all accounts accessible to the authenticated user.
 * 
 * Returns a 401 error if no user session is found, or a 500 error if retrieval fails.
 */
export async function getAllAccounts(request, reply) {
  try {
    const sessionUser = request.user;
    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Get only accounts the user has access to
    const accessibleAccounts = await authService.getUserAccessibleAccounts(sessionUser.id);
    return reply.send(accessibleAccounts);
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch accounts' })
  }
}

/**
 * Retrieves a specific account by ID if the authenticated user has access.
 *
 * Returns the account details if found and accessible. Responds with appropriate HTTP status codes for unauthorized access, forbidden access, account not found, or server errors.
 */
export async function getAccountById(request, reply) {
  try {
    const { id } = request.params
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Check if user has access to this account
    const canAccess = await authService.canAccessAccount(sessionUser.id, id);
    if (!canAccess) {
      return reply.code(403).send({ error: 'Access denied to this account' });
    }

    const [account] = await db
      .select()
      .from(accounts)
      .where(eq(accounts.id, id))
      .limit(1)

    if (!account) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    return reply.send(account)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch account' })
  }
}

/**
 * Creates a new account with the provided details and assigns the creator as the account owner.
 *
 * Accepts `name`, `logo_url`, `type`, and `onboarding_step` from the request body. The creator is added as an owner in the userAccounts table. Returns the created account with a 201 status code on success, or a 500 error if creation fails.
 */
export async function createAccount(request, reply) {
  try {
    const { name, logo_url, type, onboarding_step } = request.body

    const [account] = await db
      .insert(accounts)
      .values({
        name,
        ownerId: request.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
        logoUrl: logo_url,
        type,
        onboardingStep: onboarding_step
      })
      .returning()

    // Insert the creator as owner in userAccounts
    const [userAccount] = await db
      .insert(userAccounts)
      .values({
        accountId: account.id,
        userId: request.user.id,
        role: 'owner',
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    return reply.code(201).send(account)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create account' })
  }
}

/**
 * Updates an account with the specified fields if the authenticated user has permission.
 *
 * Only fields present in the request body are updated. Returns the updated account on success, or an error response if the user is unauthorized, lacks permission, or the account does not exist.
 */
export async function updateAccount(request, reply) {
  try {
    const { id } = request.params
    const { name, status, logo_url, type, onboarding_step, has_payment_method } = request.body
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Check if user can modify this account
    const canModify = await authService.canModifyAccount(sessionUser.id, id);
    if (!canModify) {
      return reply.code(403).send({ error: 'Access denied - You do not have permission to modify this account' });
    }

    const updateData = {
      updatedAt: new Date()
    }

    // Only include fields that are present in the request body
    if (name !== undefined) updateData.name = name
    if (status !== undefined) updateData.status = status
    if (logo_url !== undefined) updateData.logoUrl = logo_url
    if (type !== undefined) updateData.type = type
    if (onboarding_step !== undefined) updateData.onboardingStep = onboarding_step
    if (has_payment_method !== undefined) updateData.hasPaymentMethod = has_payment_method

    const [account] = await db
      .update(accounts)
      .set(updateData)
      .where(eq(accounts.id, id))
      .returning()

    if (!account) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    return reply.send(account)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update account' })
  }
}

/**
 * Soft deletes an account by setting its status to 'deleted' if the session user is the owner.
 * 
 * Returns the updated account on success, or an appropriate error response if the user is unauthorized, lacks permission, or the account does not exist.
 */
export async function deleteAccount(request, reply) {
  try {
    const { id } = request.params
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Check if user can delete this account (owner only)
    const canDelete = await authService.canDeleteAccount(sessionUser.id, id);
    if (!canDelete) {
      return reply.code(403).send({ error: 'Access denied - You do not have permission to delete this account' });
    }

    const [updatedAccount] = await db
      .update(accounts)
      .set({
        status: 'deleted',
        updatedAt: new Date()
      })
      .where(eq(accounts.id, id))
      .returning()

    if (!updatedAccount) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    return reply.send(updatedAccount)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to soft delete account' })
  }
}

/**
 * Permanently deletes an account if the authenticated user is the owner.
 *
 * Returns a 401 error if no session is found, 403 if the user lacks permission, 404 if the account does not exist, and 204 on successful deletion.
 */
export async function hardDeleteAccount(request, reply) {
  try {
    const { id } = request.params
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Check if user can delete this account (owner only)
    const canDelete = await authService.canDeleteAccount(sessionUser.id, id);
    if (!canDelete) {
      return reply.code(403).send({ error: 'Access denied - You do not have permission to delete this account' });
    }

    const [deletedAccount] = await db
      .delete(accounts)
      .where(eq(accounts.id, id))
      .returning()

    if (!deletedAccount) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete account and related data' })
  }
}



/**
 * Updates the Stripe customer ID for a specified account if the authenticated user has modification permissions.
 * 
 * Returns the updated account on success. Responds with appropriate HTTP status codes if the session is missing, the user lacks permission, the Stripe customer ID is missing, or the account does not exist.
 */
export async function updateAccountStripeCustomerId(request, reply) {
  try {
    const { id } = request.params
    const { stripeCustomerId } = request.body
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Check if user can modify this account
    const canModify = await authService.canModifyAccount(sessionUser.id, id);
    if (!canModify) {
      return reply.code(403).send({ error: 'Access denied - You do not have permission to modify this account' });
    }

    if (!stripeCustomerId) {
      return reply.code(400).send({ error: 'stripeCustomerId is required' })
    }

    const [account] = await db
      .update(accounts)
      .set({
        stripeCustomerId,
        updatedAt: new Date()
      })
      .where(eq(accounts.id, id))
      .returning()

    if (!account) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    return reply.send(account)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update account stripe customer ID' })
  }
}

/**
 * Retrieves all members of a specified account, including their roles, status, and associated brands, retailers, and plugins.
 * Also includes pending invitations for the account.
 *
 * Only users with 'admin' or 'owner' roles on the account are authorized to access this information. The response includes each member's details, their brands and retailers (with counts), and plugins (credential sets) they have access to within the account.
 * 
 * @returns {Array<Object>} An array of enriched member objects for the account, including invited users.
 */
export async function getAccountMembers(request, reply) {
  try {
    const { accountId } = request.params
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // First verify the account exists
    const [account] = await db
      .select()
      .from(accounts)
      .where(eq(accounts.id, accountId))
      .limit(1)

    if (!account) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    // Check if session user has permission to view account members
    const [userAccountRole] = await db
      .select()
      .from(userAccounts)
      .where(
        and(
          eq(userAccounts.accountId, accountId),
          eq(userAccounts.userId, sessionUser.id)
        )
      )
      .limit(1)

    // 1. Get the base list of users for the account
    const members = await db
      .select({
        id: users.id,
        auth0Id: users.auth0Id,
        name: users.name,
        email: users.email,
        jobTitle: users.jobTitle,
        role: userAccounts.role,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        status: users.status,
        lastLoginAt: users.lastLoginAt
      })
      .from(userAccounts)
      .innerJoin(users, eq(userAccounts.userId, users.id))
      .where(eq(userAccounts.accountId, accountId))

    // 2. Get invited users from invitations table
    const invitedUsers = await db
      .select({
        id: invitations.id,
        email: invitations.email,
        role: invitations.role,
        status: invitations.status,
        type: invitations.type,
        brandIds: invitations.brandIds,
        createdAt: invitations.createdAt,
        updatedAt: invitations.updatedAt
      })
      .from(invitations)
      .where(and(
        eq(invitations.accountId, accountId),
        eq(invitations.status, 'pending')
      ))

    if (members.length === 0 && invitedUsers.length === 0) {
      return reply.send([])
    }

    const userIds = members.map(m => m.id)

    // 3. Fetch all related data in parallel for existing members only
    const [brandsData, retailersData, pluginsData] = await Promise.all([
      // Get all brands for these users
      userIds.length > 0 ? db.select({
          userId: brandUsers.userId,
          brandId: brands.id,
          brandName: brands.name
        })
        .from(brandUsers)
        .innerJoin(brands, eq(brandUsers.brandId, brands.id))
        .where(and(
          inArray(brandUsers.userId, userIds),
          eq(brands.accountId, accountId)
        )) : [],

      // Get all retailers for these users (via brands they have access to)
      userIds.length > 0 ? db.selectDistinct({
          userId: brandUsers.userId,
          retailerId: retailers.id,
          retailerName: retailers.name
        })
        .from(brandUsers)
        .innerJoin(brands, eq(brandUsers.brandId, brands.id))
        .innerJoin(brandRetailers, eq(brandRetailers.brandId, brands.id))
        .innerJoin(retailers, eq(brandRetailers.retailerId, retailers.id))
        .where(and(
          inArray(brandUsers.userId, userIds),
          eq(brands.accountId, accountId)
        )) : [],

      // Get all "plugins" (credential sets) for these users
      userIds.length > 0 ? db.select({
          userId: userCredentialSets.userId,
          pluginId: credentialSets.id,
          pluginName: credentialSets.type,
          pluginDisplayName: credentialSets.name
        })
        .from(userCredentialSets)
        .innerJoin(credentialSets, eq(userCredentialSets.credentialSetId, credentialSets.id))
        .where(and(
          inArray(userCredentialSets.userId, userIds),
          eq(credentialSets.accountId, accountId)
        )) : []
    ])

    // 4. Process and map the data for efficient lookup for existing members
    const brandsMap = brandsData.reduce((acc, { userId, brandName }) => {
      if (!acc[userId]) acc[userId] = []
      acc[userId].push(brandName)
      return acc
    }, {})

    const retailersMap = retailersData.reduce((acc, { userId, retailerName }) => {
      if (!acc[userId]) acc[userId] = []
      if (!acc[userId].includes(retailerName)) {
        acc[userId].push(retailerName)
      }
      return acc
    }, {})
    
    const pluginsMap = pluginsData
      .filter(({ pluginName }) => pluginName === 'plugin')
      .reduce((acc, { userId, pluginName, pluginDisplayName }) => {
        if (!acc[userId]) acc[userId] = []
        // Use display name if available, otherwise format the type name
        const displayName = pluginDisplayName || (pluginName.charAt(0).toUpperCase() + pluginName.slice(1));
        if (!acc[userId].includes(displayName)) {
          acc[userId].push(displayName)
        }
        return acc
      }, {})

    // 5. Combine existing members with their data
    const enrichedMembers = members.map(member => {
      const userBrands = brandsMap[member.id] || []
      const userRetailers = retailersMap[member.id] || []
      const userPlugins = pluginsMap[member.id] || []

      return {
        id: member.id,
        name: member.name,
        email: member.email,
        jobTitle: member.jobTitle,
        role: member.role,
        auth0Id: member.auth0Id,
        lastLoginAt: member.lastLoginAt,
        status: member.status,
        brands: userBrands,
        brandsCount: userBrands.length,
        retailers: userRetailers,
        retailersCount: userRetailers.length,
        plugins: userPlugins,
        createdAt: member.createdAt,
        updatedAt: member.updatedAt,
        isInvited: false
      }
    })


    // 6. Add invited users to the response
    const invitedMembersFormatted = invitedUsers.length > 0 ? await (async () => {
      // Batch query for all invited users' retailer counts
      const allInvitedBrandIds = invitedUsers
        .filter(invitation => invitation.brandIds && invitation.brandIds.length > 0)
        .flatMap(invitation => invitation.brandIds.map(brandId => ({ 
          invitationId: invitation.id, 
          brandId 
        })));

      let retailerCountsMap = {};
      
      if (allInvitedBrandIds.length > 0) {
        const brandIds = [...new Set(allInvitedBrandIds.map(item => item.brandId))];
        
        // Single query to get all retailers for all brands
        const allRetailers = await db.selectDistinct({
          brandId: brandRetailers.brandId,
          retailerId: retailers.id
        })
        .from(brandRetailers)
        .innerJoin(retailers, eq(brandRetailers.retailerId, retailers.id))
        .where(inArray(brandRetailers.brandId, brandIds));
        
        // Build map of brandId -> retailer count
        const brandRetailerCounts = allRetailers.reduce((acc, { brandId, retailerId }) => {
          if (!acc[brandId]) acc[brandId] = new Set();
          acc[brandId].add(retailerId);
          return acc;
        }, {});
        
        // Calculate retailer counts per invitation
        retailerCountsMap = invitedUsers.reduce((acc, invitation) => {
          if (invitation.brandIds && invitation.brandIds.length > 0) {
            const uniqueRetailers = new Set();
            invitation.brandIds.forEach(brandId => {
              if (brandRetailerCounts[brandId]) {
                brandRetailerCounts[brandId].forEach(retailerId => uniqueRetailers.add(retailerId));
              }
            });
            acc[invitation.id] = uniqueRetailers.size;
          } else {
            acc[invitation.id] = 0;
          }
          return acc;
        }, {});
      }

      return invitedUsers.map(invitation => ({
        id: invitation.id,
        name: null,
        email: invitation.email,
        jobTitle: null,
        role: invitation.role,
        auth0Id: null,
        lastLoginAt: null,
        status: invitation.status,
        invitationType: invitation.type,
        brandIds: invitation.brandIds,
        brands: [],
        brandsCount: invitation.brandIds?.length || 0,
        retailers: [],
        retailersCount: retailerCountsMap[invitation.id] || 0,
        plugins: [],
        createdAt: invitation.createdAt,
        updatedAt: invitation.updatedAt,
        isInvited: true
      }));
    })() : [];

    // 7. Combine both existing members and invited users
    const allMembers = [...enrichedMembers, ...invitedMembersFormatted]

    return reply.send(allMembers)

  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch account members' })
  }
}

export async function addUserToAccount(request, reply) {
  try {
    const { accountId, userId } = request.params
    const { role = 'member' } = request.body

    // Get user info from session
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Verify the account exists
    const [account] = await db
      .select()
      .from(accounts)
      .where(eq(accounts.id, accountId))
      .limit(1)

    if (!account) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    // Check if user can manage account users
    const canManage = await authService.canManageAccountUsers(sessionUser.id, accountId)
    if (!canManage) {
      return reply.code(403).send({ error: 'Forbidden - You do not have permission to add users to this account' })
    }

    // Verify the target user exists
    const [targetUser] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1)

    if (!targetUser) {
      return reply.code(404).send({ error: 'User not found' })
    }

    // Validate role
    const validRoles = ['admin', 'member', 'owner'];
    if (!validRoles.includes(role)) {
      return reply.code(400).send({ error: 'Invalid role. Must be one of: admin, member, owner' })
    }

    // Check if user is already associated with the account
    const existingRelationship = await db
      .select()
      .from(userAccounts)
      .where(
        and(
          eq(userAccounts.accountId, accountId),
          eq(userAccounts.userId, userId)
        )
      )
      .limit(1)

    if (existingRelationship.length) {
      // Update the existing relationship with new role
      const [updatedRelationship] = await db
        .update(userAccounts)
        .set({
          role,
          updatedAt: new Date()
        })
        .where(
          and(
            eq(userAccounts.accountId, accountId),
            eq(userAccounts.userId, userId)
          )
        )
        .returning()

      return reply.send({
        message: 'User role in account updated',
        data: {
          accountId,
          userId,
          role: updatedRelationship.role,
          userName: targetUser.name,
          userEmail: targetUser.email
        }
      })
    }

    // Create new relationship
    const [relationship] = await db
      .insert(userAccounts)
      .values({
        accountId,
        userId,
        role,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    return reply.code(201).send({
      message: 'User added to account',
      data: {
        accountId,
        userId,
        role: relationship.role,
        userName: targetUser.name,
        userEmail: targetUser.email
      }
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({
      error: 'Failed to add user to account',
      message: error.message
    })
  }
}

export async function updateAccountMemberRole(request, reply) {
  try {
    const { accountId, userId } = request.params
    const { role } = request.body
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Validate role
    const validRoles = ['admin', 'member', 'owner'];
    if (!validRoles.includes(role)) {
      return reply.code(400).send({ error: 'Invalid role. Must be one of: admin, member, owner' })
    }

    // Check if user can manage account users
    const canManage = await authService.canManageAccountUsers(sessionUser.id, accountId)
    if (!canManage) {
      return reply.code(403).send({ error: 'Forbidden - You do not have permission to modify roles in this account' })
    }

    // Update the role
    const [updatedRelationship] = await db
      .update(userAccounts)
      .set({
        role,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(userAccounts.accountId, accountId),
          eq(userAccounts.userId, userId)
        )
      )
      .returning()

    if (!updatedRelationship) {
      return reply.code(404).send({ error: 'User not found in this account' })
    }

    return reply.send({
      message: 'User role updated',
      data: updatedRelationship
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update member role' })
  }
}

export async function removeUserFromAccount(request, reply) {
  try {
    const { accountId, userId } = request.params
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Check if user can manage account users
    const canManage = await authService.canManageAccountUsers(sessionUser.id, accountId)
    if (!canManage) {
      return reply.code(403).send({ error: 'Forbidden - You do not have permission to remove users from this account' })
    }

    // Remove user from account
    const [removedRelationship] = await db
      .delete(userAccounts)
      .where(
        and(
          eq(userAccounts.accountId, accountId),
          eq(userAccounts.userId, userId)
        )
      )
      .returning()

    if (!removedRelationship) {
      return reply.code(404).send({ error: 'User not found in this account' })
    }

    // Also remove user from all brands in this account
    const accountBrands = await db
      .select({ id: brands.id })
      .from(brands)
      .where(eq(brands.accountId, accountId))

    if (accountBrands.length > 0) {
      const brandIds = accountBrands.map(b => b.id)
      await db
        .delete(brandUsers)
        .where(
          and(
            eq(brandUsers.userId, userId),
            inArray(brandUsers.brandId, brandIds)
          )
        )
    }

    return reply.send({
      message: 'User removed from account and all associated brands'
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to remove user from account' })
  }
}