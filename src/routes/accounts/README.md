# Accounts Module

The Accounts module is responsible for managing `account` entities, which represent the central organizational pillar for users and resources. An account typically represents a customer or a company.

## Data Model

An account has an `owner` and can have multiple `members` through the `userAccounts` join table. This allows for a flexible multi-tenancy model with role-based access control.

```mermaid
erDiagram
    users {
        uuid id PK
        text email UK
    }
    accounts {
        uuid id PK
        text name
        uuid ownerId FK
        text type
        boolean is_trial
        integer seats
    }
    userAccounts {
        uuid userId FK
        uuid accountId FK
        text role
    }

    users ||--o{ accounts : "owns"
    users |o--o| userAccounts : "has role in"
    accounts |o--o| userAccounts : "has member"
```

## API Endpoints

All endpoints require authentication.

---

### GET /api/accounts

-   **Description:** Retrieves a list of all accounts the authenticated user has access to.
-   **Controller Logic:** Uses `authService.getUserAccessibleAccounts` to fetch only the accounts the user is a member of.
-   **Successful Response `200`:**
    ```json
    [
      {
        "id": "uuid",
        "name": "Example Account Inc.",
        "ownerId": "uuid",
        "status": "active",
        "userRole": "admin",
        ...
      }
    ]
    ```

---

### GET /api/accounts/:id

-   **Description:** Retrieves a single account by its ID.
-   **Authorization:** The user must be a member of the account.
-   **Successful Response `200`:**
    ```json
    {
      "id": "uuid",
      "name": "Example Account Inc.",
      ...
    }
    ```

---

### POST /api/accounts

-   **Description:** Creates a new account. The user making the request is automatically assigned as the `owner`.
-   **Request Body:**
    ```json
    {
      "name": "New Company",
      "logo_url": "https://example.com/logo.png",
      "type": "agency",
      "onboarding_step": 1
    }
    ```
-   **Successful Response `201`:** Returns the newly created account object.

---

### PUT /api/accounts/:id

-   **Description:** Updates an existing account's details.
-   **Authorization:** User must be an `owner` or `admin` of the account.
-   **Request Body:**
    ```json
    {
      "name": "Updated Company Name",
      "status": "inactive"
    }
    ```
-   **Successful Response `200`:** Returns the updated account object.

---

### DELETE /api/accounts/:id

-   **Description:** Soft-deletes an account by setting its `status` to `deleted`.
-   **Authorization:** User must be the `owner` of the account.
-   **Successful Response `200`:** Returns the updated account object with the `deleted` status.

---

### GET /api/accounts/:accountId/members

-   **Description:** Retrieves a detailed list of all members for a specific account, including their roles and associated resources.
-   **Authorization:** User must be an `owner` or `admin` of the account.
-   **Successful Response `200`:**
    ```json
    [
      {
        "id": "uuid",
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "admin",
        "status": "active",
        "brands": ["Brand A", "Brand B"],
        "brandsCount": 2,
        ...
      }
    ]
    ```

---

### POST /api/accounts/:accountId/users

-   **Description:** Adds a user to an account with a specified role. If the user is already a member, it updates their role.
-   **Authorization:** User must be an `owner` or `admin` of the account.
-   **Request Body:**
    ```json
    {
      "userId": "uuid",
      "role": "member"
    }
    ```
-   **Successful Response `201` (Created) or `200` (Updated):** Returns a confirmation message and details of the association.
