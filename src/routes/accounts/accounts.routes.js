import {
  getAllAccounts,
  getAccountById,
  createAccount,
  updateAccount,
  deleteAccount,
  hardDeleteAccount,
  updateAccountStripeCustomerId,
  getAccountMembers,
  addUserToAccount,
  updateAccountMemberRole,
  removeUserFromAccount
} from "./accounts.controllers.js";
import { accountSchema } from "./accounts.schemas.js";

export default async function accountRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator("verifyAuth")) {
    throw new Error("Auth plugin must be registered before routes");
  }

  // Get all accounts
  fastify.get("/", {
    onRequest: [fastify.verifyAuth],
    handler: getAllAccounts,
  });

  // Get account by ID
  fastify.get("/:id", {
    onRequest: [fastify.verifyAuth],
    handler: getAccountById,
  });

  // Create account
  fastify.post("/", {
    onRequest: [fastify.verifyAuth],
    schema: accountSchema.create,
    handler: createAccount,
  });

  // Soft delete account
  fastify.put("/:id/delete", {
    onRequest: [fastify.verifyAuth],
    handler: deleteAccount,
  });

  // Update account
  fastify.put("/:id", {
    onRequest: [fastify.verifyAuth],
    schema: accountSchema.update,
    handler: updateAccount,
  });

  // Hard delete account
  fastify.delete("/:id", {
    onRequest: [fastify.verifyAuth],
    handler: hardDeleteAccount,
  });

  // Update account stripe customer ID
  fastify.put("/:id/stripe-customer-id", {
    onRequest: [fastify.verifyAuth],
    handler: updateAccountStripeCustomerId,
  });

  // Account member management routes
  // Get account members
  fastify.get('/:accountId/members', {
    onRequest: [fastify.verifyAuth],
    handler: getAccountMembers
  })

  // Add user to account
  fastify.post('/:accountId/members/:userId', {
    onRequest: [fastify.verifyAuth],
    handler: addUserToAccount
  })

  // Update member role
  fastify.put('/:accountId/members/:userId/role', {
    onRequest: [fastify.verifyAuth],
    handler: updateAccountMemberRole
  })

  // Remove member from account
  fastify.delete('/:accountId/members/:userId', {
    onRequest: [fastify.verifyAuth],
    handler: removeUserFromAccount
  })
}
