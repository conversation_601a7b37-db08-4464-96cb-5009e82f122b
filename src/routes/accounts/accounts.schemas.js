export const accountSchema = {
  create: {
    body: {
      type: 'object',
      required: ['name'],
      properties: {
        name: { type: 'string', minLength: 1 },
        status: { 
          type: 'string', 
          enum: ['active', 'inactive', 'deleted'],
          default: 'active'
        }
      }
    }
  },
  update: {
    body: {
      type: 'object',
      properties: {
        name: { type: 'string', minLength: 1 },
        status: { 
          type: 'string', 
          enum: ['active', 'inactive', 'deleted']
        }
      }
    },
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    }
  }
} 