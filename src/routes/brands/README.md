# Brands Module

The Brands module manages `brand` entities, which are owned by `accounts`. Brands are the primary containers for products, creative projects, and brand-specific assets like guidelines.

## Data Model

A brand belongs to a single `account`. Users are associated with brands through the `brandUsers` join table, which defines their role (`admin`, `member`, etc.) for that specific brand.

```mermaid
erDiagram
    accounts {
        uuid id PK
        text name
    }
    brands {
        uuid id PK
        uuid accountId FK
        text name
        jsonb colors
        text logo_asset_url
    }
    users {
        uuid id PK
        text email
    }
    brandUsers {
        uuid brandId FK
        uuid userId FK
        text role
    }
    brandGuidelines {
        uuid id PK
        uuid brandId FK
        text assetUrl
        uuid uploadedBy FK
    }

    accounts ||--o{ brands : "owns"
    brands |o--o| brandUsers : "has"
    users |o--o| brandUsers : "is member of"
    brands ||--|{ brandGuidelines : "has"
    users ||--o{ brandGuidelines : "uploads"
```

## API Endpoints

All endpoints require authentication.

---

### GET /api/brands/account/:accountId

-   **Description:** Retrieves all brands associated with a specific account ID.
-   **Successful Response `200`:** An array of brand objects.

---

### GET /api/brands/account/:accountId/details

-   **Description:** Retrieves a paginated list of brands for an account, enriched with detailed metrics like product counts, campaign counts, and assigned retailers.
-   **Query Parameters:** `page` (integer), `limit` (integer).
-   **Controller Logic:** This is a complex, read-only endpoint designed for dashboards. It uses `Promise.allSettled` to fetch and aggregate data from multiple tables (`products`, `projects`, `generations`, `retailers`) for each brand, ensuring the API remains resilient even if some metrics fail to load.
-   **Successful Response `200`:**
    ```json
    {
      "data": [
        {
          "brandId": "uuid",
          "brandName": "Example Brand",
          "totalProductCount": 150,
          "totalLiveCampaigns": 12,
          "assignedRetailers": ["Walmart", "Amazon"],
          "lastModified": "2023-10-27T10:00:00Z"
        }
      ],
      "pagination": {
        "total": 1,
        "page": 1,
        "limit": 10,
        "totalPages": 1
      }
    }
    ```

---

### POST /api/brands

-   **Description:** Creates a new brand within an account. The user making the request is automatically assigned as an `admin` for the new brand.
-   **Request Body:**
    ```json
    {
      "name": "New Awesome Brand",
      "accountId": "uuid",
      "description": "A description of the brand.",
      "colors": ["#FF0000", "#00FF00"],
      "logoAssetUrl": "https://example.com/logo.png"
    }
    ```
-   **Successful Response `201`:** Returns the newly created brand object.

---

### POST /api/brands/:brandId/users/:userId

-   **Description:** Associates a user with a brand and assigns them a role.
-   **Authorization:** The requesting user must be an `admin` or `owner` of the brand or its parent account.
-   **Request Body:**
    ```json
    {
      "role": "member"
    }
    ```
-   **Successful Response `201` (Created) or `200` (Updated):** Returns a confirmation message.

---

### DELETE /api/brands/:brandId/users/:userId

-   **Description:** Removes a user's association from a brand.
-   **Authorization:** Requesting user must be an `admin` or `owner`. A user can also remove themselves.
-   **Controller Logic:** The `leaveBrand` controller function contains special logic to prevent the last `owner` or `admin` from leaving the brand, which would orphan it.

---

### POST /api/brands/:brandId/guidelines

-   **Description:** Uploads a brand guideline file. The file is sent to Google Cloud Storage, and a record is created in the `brandGuidelines` table.
-   **Authorization:** User must have permission to upload guidelines (as determined by `authService.canUploadBrandGuidelines`).
-   **Request Body:** A `multipart/form-data` request containing the file and metadata.
-   **Successful Response `201`:** Returns a confirmation message and the guideline record.
