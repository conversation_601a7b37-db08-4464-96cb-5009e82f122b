import { eq, sql, and, inArray, count, desc } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { brands } from '../../db/schema/brands.ts'
import { brandUsers } from '../../db/schema/brandUsers.ts'
import { users } from '../../db/schema/users.ts'
import { brandGuidelines } from '../../db/schema/brandGuidelines.ts'
import { products } from '../../db/schema/products.ts'
import { retailers } from '../../db/schema/retailers.ts'
import { brandRetailers } from '../../db/schema/retailers.ts'
import { retailerProducts } from '../../db/schema/retailers.ts'
import { projects } from '../../db/schema/projects.ts'
import { generations } from '../../db/schema/generations.ts'
import { generationVersions } from '../../db/schema/generationVersions.ts'
import { getOrUploadFile } from '../../services/google.service.js'
import { authService } from '../../services/authorization.service.js'
import { v4 as uuidv4 } from 'uuid'

export async function getAllBrands(request, reply) {
  try {
    const allBrands = await db.select().from(brands)
    return reply.send(allBrands)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch brands' })
  }
}

export async function getBrandsByAccountId(request, reply) {
  try {
    const { accountId } = request.params
    const brands = await db.select().from(brands).where(eq(brands.accountId, accountId))

    if (!brands.length) {
      return reply.code(404).send({ error: 'Brands not found for this account' })
    }

    return reply.send(brands)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch brands by account ID' })
  }
}

/**
 * Retrieves a brand's logo asset URL by brand ID.
 * 
 * Returns the logoAssetUrl for the specified brand, or a 404 error if the brand is not found.
 */
export async function getBrandLogoUrl(request, reply) {
  try {
    const { id } = request.params

    const [brand] = await db
      .select({
        id: brands.id,
        logoAssetUrl: brands.logoAssetUrl
      })
      .from(brands)
      .where(eq(brands.id, id))
      .limit(1)

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    return reply.send({
      id: brand.id,
      logoAssetUrl: brand.logoAssetUrl
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch brand logo URL' })
  }
}

/**
 * Retrieves a brand by its ID, including its details and associated brand guidelines with uploader information.
 *
 * If the brand exists, returns an object containing brand data and an array of guideline resources, each with uploader user details. Returns a 404 error if the brand is not found.
 */
export async function getBrandById(request, reply) {
  try {
    const { id } = request.params

    // Get brand basic info
    const [brand] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, id))
      .limit(1)

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    // Get brand guidelines with uploader info
    const guidelines = await db
      .select({
        id: brandGuidelines.id,
        label: brandGuidelines.label,
        fileName: brandGuidelines.fileName,
        assetUrl: brandGuidelines.assetUrl,
        status: brandGuidelines.status,
        uploadDate: brandGuidelines.createdAt,
        uploadedBy: {
          id: users.id,
          name: users.name,
          email: users.email
        }
      })
      .from(brandGuidelines)
      .leftJoin(users, eq(brandGuidelines.uploadedBy, users.id))
      .where(eq(brandGuidelines.brandId, id))

    // Format response
    const response = {
      id: brand.id,
      name: brand.name,
      logoUrl: brand.logoAssetUrl,
      colors: brand.colors,
      notes: brand.notes,
      createdAt: brand.createdAt,
      updatedAt: brand.updatedAt,
      guidelineResources: guidelines
    }

    return reply.send(response)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch brand' })
  }
}

/**
 * Creates a new brand with the provided details and associates the current user as an admin of the brand.
 *
 * Accepts brand information from the request body, including optional colors and logo asset URL. If colors are not provided, defaults are set based on primary and secondary color fields or left to the database schema. Requires a valid session and account ID. Returns the created brand on success.
 */
export async function createBrand(request, reply) {
  try {
    let { name, accountId, description, primaryColor, secondaryColor, colors, logoAssetUrl } = request.body

    // Get user info from session
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    if (!accountId) {
      return reply.code(400).send({ error: 'Account ID is required' });
    }

    if (!description) {
      description = ''
    }

    // Handle colors
    if (!colors) {
      // If colors aren't provided but primaryColor and secondaryColor are
      if (primaryColor || secondaryColor) {
        colors = [
          primaryColor || '#000000',
          secondaryColor || '#000000'
        ]
      }
      // Otherwise let the DB schema default handle it
    }

    const [brand] = await db
      .insert(brands)
      .values({
        name,
        accountId,
        description,
        colors,
        logoAssetUrl,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    // Associate the current user with the brand as an admin
    await db
      .insert(brandUsers)
      .values({
        brandId: brand.id,
        userId: sessionUser.id,
        role: 'admin', // Using text-based role instead of roleId
        createdAt: new Date(),
        updatedAt: new Date()
      })

    return reply.code(201).send(brand)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create brand' })
  }
}

/**
 * Updates an existing brand's details by ID, excluding the account association.
 * 
 * Returns the updated brand record, or a 404 error if the brand does not exist.
 */
export async function updateBrand(request, reply) {
  try {
    const { id } = request.params
    const updateData = request.body

    // Sanitize updateData to ensure we don't modify accountId
    // once a brand is created
    const { accountId, ...allowedUpdates } = updateData

    const [brand] = await db
      .update(brands)
      .set({
        ...allowedUpdates,
        updatedAt: new Date()
      })
      .where(eq(brands.id, id))
      .returning()

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    return reply.send(brand)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update brand' })
  }
}

export async function deleteBrand(request, reply) {
  try {
    const { id } = request.params

    const [deletedBrand] = await db
      .delete(brands)
      .where(eq(brands.id, id))
      .returning()

    if (!deletedBrand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete brand' })
  }
}

export async function getBrandsByAccountIdWithUsers(request, reply) {
  const { accountId } = request.params;
  const { page = 1, limit = 10 } = request.query;
  const offset = (page - 1) * limit;

  try {
    // First get total count
    const [{ count }] = await db
      .select({ count: sql`count(distinct ${brands.id})` })
      .from(brands)
      .where(eq(brands.accountId, accountId));

    // Step 1: Get paginated brand IDs
    const paginatedBrandIds = await db
      .select({ id: brands.id })
      .from(brands)
      .where(eq(brands.accountId, accountId))
      .limit(limit)
      .offset(offset);

    if (!paginatedBrandIds.length) {
      return reply.code(404).send({ error: 'Brands not found for this account' });
    }

    // Extract just the IDs into an array
    const brandIds = paginatedBrandIds.map(brand => brand.id);

    // Step 2: Get all data for these specific brands using inArray
    const brandsWithUsers = await db
      .select({
        brand: brands,
        user: users,
        role: brandUsers.role // Changed from roleId to role
      })
      .from(brands)
      .leftJoin(brandUsers, eq(brandUsers.brandId, brands.id))
      .leftJoin(users, eq(users.id, brandUsers.userId))
      .where(inArray(brands.id, brandIds));

    if (!brandsWithUsers.length) {
      return reply.code(404).send({ error: 'Brands not found' })
    }

    // Group users by brand
    const groupedResults = brandsWithUsers.reduce((acc, row) => {
      const brand = row.brand
      const user = row.user
      const role = row.role

      if (!acc[brand.id]) {
        acc[brand.id] = {
          ...brand,
          users: []
        }
      }

      if (user && !acc[brand.id].users.some(u => u.id === user.id)) {
        acc[brand.id].users.push({
          ...user,
          role: role // Now directly using the text-based role
        })
      }

      return acc
    }, {});

    const response = {
      data: Object.values(groupedResults),
      pagination: {
        total: Number(count),
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(count / limit)
      }
    };

    return reply.send(response);
  } catch (error) {
    request.log.error(error);
    return reply.code(500).send({ error: 'Failed to fetch brands with users' });
  }
}

export async function addUserToBrand(request, reply) {
  try {
    const { brandId, userId } = request.params
    const { role } = request.body // Changed from roleId to role

    // Get user info from session
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Verify the brand exists
    const [brand] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, brandId))
      .limit(1)

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    // Check if user can manage brand users
    const canManage = await authService.canManageBrandUsers(sessionUser.id, brandId)
    if (!canManage) {
      return reply.code(403).send({ error: 'Forbidden - You do not have permission to add users to this brand' })
    }

    // Verify the user exists
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1)

    if (!user) {
      return reply.code(404).send({ error: 'User not found' })
    }

    // Validate role
    const validRoles = ['owner', 'admin', 'member'];
    if (!validRoles.includes(role)) {
      return reply.code(400).send({ error: 'Invalid role. Must be one of: owner, admin, member' })
    }

    // Check if user is already associated with the brand
    const existingRelationship = await db
      .select()
      .from(brandUsers)
      .where(
        and(
          eq(brandUsers.brandId, brandId),
          eq(brandUsers.userId, userId)
        )
      )
      .limit(1)

    if (existingRelationship.length) {
      // Update the existing relationship with new role
      const [updatedRelationship] = await db
        .update(brandUsers)
        .set({
          role,
          updatedAt: new Date()
        })
        .where(
          and(
            eq(brandUsers.brandId, brandId),
            eq(brandUsers.userId, userId)
          )
        )
        .returning()

      return reply.send({
        brandId,
        userId,
        role: updatedRelationship.role,
        message: 'User role in brand updated'
      })
    }

    // Create new relationship
    const [relationship] = await db
      .insert(brandUsers)
      .values({
        brandId,
        userId,
        role,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    return reply.code(201).send({
      brandId,
      userId,
      role: relationship.role,
      message: 'User added to brand'
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({
      error: 'Failed to add user to brand',
      message: error.message
    })
  }
}

export async function removeUserFromBrand(request, reply) {
  try {
    const { brandId, userId } = request.params

    // Get user info from session
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Verify the brand exists
    const [brand] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, brandId))
      .limit(1)

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    // If removing self, redirect to leaveBrand which has its own checks
    if (userId === sessionUser.id) {
      return leaveBrand(request, reply);
    }

    // Check if user can manage brand users
    const canManage = await authService.canManageBrandUsers(sessionUser.id, brandId)
    if (!canManage) {
      return reply.code(403).send({ error: 'Forbidden - You do not have permission to remove users from this brand' })
    }

    // Verify the relationship exists
    const existingRelationship = await db
      .select()
      .from(brandUsers)
      .where(
        and(
          eq(brandUsers.brandId, brandId),
          eq(brandUsers.userId, userId)
        )
      )
      .limit(1)

    if (!existingRelationship.length) {
      return reply.code(404).send({
        error: 'User-brand relationship not found',
        details: {
          userId,
          brandId
        }
      })
    }

    // Delete the user-brand relationship
    await db
      .delete(brandUsers)
      .where(
        and(
          eq(brandUsers.brandId, brandId),
          eq(brandUsers.userId, userId)
        )
      )

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({
      error: 'Failed to remove user from brand',
      message: error.message
    })
  }
}

export async function leaveBrand(request, reply) {
  try {
    const { brandId, userId } = request.params;
    const sessionUser = request.user;

    if (!sessionUser || !sessionUser.id) {
      return reply.code(401).send({ error: 'Unauthorized - Session invalid' });
    }

    // Verify the user performing the action matches the userId in the request
    if (sessionUser.id !== userId) {
      return reply.code(403).send({ error: 'Forbidden - Cannot leave brand for another user' });
    }

    // Fetch the user from DB to double-check
    const [authenticatedUser] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!authenticatedUser) {
      return reply.code(404).send({ error: 'User not found' });
    }

    // Verify the brand exists
    const [brand] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, brandId))
      .limit(1)

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' });
    }

    // Verify the user belongs to the brand
    const existingRelationship = await db
      .select()
      .from(brandUsers)
      .where(
        and(
          eq(brandUsers.userId, userId),
          eq(brandUsers.brandId, brandId)
        )
      )
      .limit(1)

    if (!existingRelationship.length) {
      return reply.code(404).send({
        error: 'User-brand relationship not found',
        details: {
          userId,
          brandId
        }
      })
    }

    // Check if user can leave the brand (prevents last owner/admin from leaving)
    const canLeave = await authService.canLeaveBrand(userId, brandId)
    if (!canLeave) {
      return reply.code(400).send({
        error: 'Cannot leave brand',
        message: 'You are the last owner/admin for this brand. Transfer ownership/admin rights to another user first.'
      });
    }

    // Delete the user-brand relationship
    await db
      .delete(brandUsers)
      .where(
        and(
          eq(brandUsers.userId, userId),
          eq(brandUsers.brandId, brandId)
        )
      )

    return reply.code(204).send()
  } catch (error) {
    console.error('Error in leaveBrand:', error)
    request.log.error(error)
    return reply.code(500).send({
      error: 'Failed to leave brand',
      message: error.message
    })
  }
}

export async function getBrandUsers(request, reply) {
  try {
    const { brandId } = request.params

    // Verify the brand exists
    const [brand] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, brandId))
      .limit(1)

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    // Get all users with their roles for this brand
    const brandUsersList = await db
      .select({
        userId: users.id,
        email: users.email,
        name: users.name,
        jobTitle: users.jobTitle,
        role: brandUsers.role,
        createdAt: brandUsers.createdAt,
        updatedAt: brandUsers.updatedAt
      })
      .from(users)
      .innerJoin(brandUsers, eq(users.id, brandUsers.userId))
      .where(eq(brandUsers.brandId, brandId))

    return reply.send(brandUsersList)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch brand users' })
  }
}

export async function updateBrandUserRole(request, reply) {
  try {
    const { brandId, userId } = request.params
    const { role } = request.body

    // Get user info from session
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Validate role
    const validRoles = ['owner', 'admin', 'member'];
    if (!validRoles.includes(role)) {
      return reply.code(400).send({
        error: 'Invalid role. Must be one of: owner, admin, member'
      })
    }

    // Verify the brand exists
    const [brand] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, brandId))
      .limit(1)

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    // Check if user can manage brand users
    const canManage = await authService.canManageBrandUsers(sessionUser.id, brandId)
    if (!canManage) {
      return reply.code(403).send({
        error: 'Forbidden - You do not have permission to modify roles in this brand'
      })
    }

    // Verify the target user exists in this brand
    const [existingRelationship] = await db
      .select()
      .from(brandUsers)
      .where(
        and(
          eq(brandUsers.brandId, brandId),
          eq(brandUsers.userId, userId)
        )
      )
      .limit(1)

    if (!existingRelationship) {
      return reply.code(404).send({
        error: 'User not found in this brand'
      })
    }

    // Update the user's role
    const [updatedRelationship] = await db
      .update(brandUsers)
      .set({
        role,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(brandUsers.brandId, brandId),
          eq(brandUsers.userId, userId)
        )
      )
      .returning()

    return reply.send({
      message: 'User role updated successfully',
      data: {
        brandId,
        userId,
        role: updatedRelationship.role,
        updatedAt: updatedRelationship.updatedAt
      }
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({
      error: 'Failed to update user role in brand',
      message: error.message
    })
  }
}

/**
 * Creates a new brand guideline record for a specified brand using provided metadata.
 *
 * Validates user authentication, permissions, and required fields. Parses and verifies guideline metadata, ensures the brand and user exist, and inserts a new guideline record into the database. Returns the created guideline on success, or an appropriate error response on failure.
 */
export async function uploadBrandGuidelines(request, reply) {
  try {
    const { brandId } = request.params

    // Parse the request body if it's a string
    let bodyData;
    if (typeof request.body === 'string') {
      try {
        bodyData = JSON.parse(request.body);
      } catch (parseError) {
        console.log('Request body parse error:', parseError)
        return reply.code(400).send({ 
          error: 'Invalid request body format: must be valid JSON' 
        });
      }
    } else {
      bodyData = request.body;
    }

    // Now extract from the parsed body
    const userId = bodyData.userId;
    const guidelines = bodyData.guidelines;

    // Get user info from session
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Validate required fields
    if (!userId || !guidelines) {
      return reply.code(400).send({ 
        error: 'Missing required fields: userId and guidelines are required' 
      });
    }

    // Parse the guidelines JSON string
    let guidelineData;
    try {
      guidelineData = JSON.parse(guidelines);
    } catch (parseError) {
      console.log('JSON Parse Error:', parseError)
      return reply.code(400).send({ 
        error: 'Invalid guidelines format: must be valid JSON' 
      });
    }

    // Extract values from parsed guidelines
    const { filename: fileName, fileUrl: guidelineUrl, status } = guidelineData;
    const label = fileName || 'Brand Guideline'; // Use filename as label or provide default

    if (!guidelineUrl || !fileName) {
      return reply.code(400).send({ 
        error: 'Missing required guideline fields: filename and fileUrl are required' 
      });
    }

    // Verify the brand exists
    const [brand] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, brandId))
      .limit(1)

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    // Check if user can upload brand guidelines
    const canUpload = await authService.canUploadBrandGuidelines(sessionUser.id, brandId)
    if (!canUpload) {
      return reply.code(403).send({
        error: 'Forbidden - You do not have permission to upload guidelines for this brand'
      })
    }

    // Verify the specified user exists
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1)

    if (!user) {
      return reply.code(404).send({ error: 'User not found' })
    }

    // Create new brand guideline record
    const [newGuideline] = await db
      .insert(brandGuidelines)
      .values({
        brandId,
        label,
        assetUrl: guidelineUrl,
        uploadedBy: userId,
        fileName,
        status: status,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    return reply.code(201).send({
      message: 'Brand guideline saved successfully',
      guideline: newGuideline
    })
  } catch (error) {
    console.error('Error in uploadBrandGuidelines:', error)
    request.log.error(error)
    return reply.code(500).send({
      error: 'Failed to save brand guideline',
      details: error.message
    })
  }
}

/**
 * Retrieves all guideline files for a specific brand, including uploader user details.
 *
 * Responds with a list of guideline resources for the given brand, or a 404 error if the brand does not exist.
 */
export async function getBrandGuidelines(request, reply) {
  try {
    const { brandId } = request.params

    // Verify brand exists
    const [brand] = await db
      .select({ id: brands.id })
      .from(brands)
      .where(eq(brands.id, brandId))
      .limit(1)

    if (!brand) {
      return reply.code(404).send({ error: 'Brand not found' })
    }

    // Get all guidelines for this brand
    const guidelines = await db
      .select({
        id: brandGuidelines.id,
        label: brandGuidelines.label,
        fileName: brandGuidelines.fileName,
        assetUrl: brandGuidelines.assetUrl,
        status: brandGuidelines.status,
        uploadDate: brandGuidelines.createdAt,
        updatedAt: brandGuidelines.updatedAt,
        uploadedBy: {
          id: users.id,
          name: users.name,
          email: users.email
        }
      })
      .from(brandGuidelines)
      .leftJoin(users, eq(brandGuidelines.uploadedBy, users.id))
      .where(eq(brandGuidelines.brandId, brandId))
      .orderBy(brandGuidelines.createdAt)

    return reply.send({
      brandId,
      guidelines
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch brand guidelines' })
  }
}

export async function updateBrandGuideline(request, reply) {
  try {
    const { brandId, guidelineId } = request.params
    const { filename, status } = request.body.guidelines
    // Validate at least one field is provided
    if (!filename && !status) {
      console.log('no fields provided')
      return reply.code(400).send({ 
        error: 'At least one field (fileName or status) must be provided for update' 
      });
    }

    // Validate status if provided
    if (status && !['active', 'archived'].includes(status)) {
      console.log('invalid status', status)
      return reply.code(400).send({ 
        error: 'Invalid status. Must be either "active" or "archived"' 
      });
    }

    // Verify the guideline exists and belongs to the brand
    const [existingGuideline] = await db
      .select()
      .from(brandGuidelines)
      .where(
        and(
          eq(brandGuidelines.id, guidelineId),
          eq(brandGuidelines.brandId, brandId)
        )
      )
      .limit(1)

    if (!existingGuideline) {
      return reply.code(404).send({ 
        error: 'Brand guideline not found or does not belong to this brand' 
      })
    }

    // Build update object with only provided fields
    const updateData = {
      updatedAt: new Date()
    }

    if (filename !== undefined) {
      updateData.fileName = filename
    }

    if (status !== undefined) {
      updateData.status = status
    }

    // Update the guideline
    const [updatedGuideline] = await db
      .update(brandGuidelines)
      .set(updateData)
      .where(eq(brandGuidelines.id, guidelineId))
      .returning()

    return reply.send({
      message: 'Brand guideline updated successfully',
      guideline: updatedGuideline
    })

  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update brand guideline' })
  }
}

/**
 * Retrieves paginated brands for a given account, including detailed metrics for each brand.
 *
 * For each brand, returns product counts (total and by retailer), live campaign counts (total and by retailer), assigned retailers, and last modification date. Handles partial failures in metric retrieval by providing default values and logs errors for individual metric or brand processing failures. Returns an empty array if no brands are found for the account. Responds with HTTP 400 if the account ID is missing, or HTTP 500 for unexpected errors.
 */
export async function getBrandsDetailsForAccount(request, reply) {
  const { accountId } = request.params
  const { page = 1, limit = 20, search, userId } = request.query

  try {
    // Validate accountId
    if (!accountId) {
      request.log.warn('Missing accountId parameter')
      return reply.code(400).send({ error: 'Account ID is required' })
    }

    // Convert page and limit to numbers and calculate offset
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Build where conditions
    let whereConditions = and(
      eq(brands.accountId, accountId),
      sql`${brands.primaryCredentialId} IS NOT NULL`
    )
    
    if (search && search.trim()) {
      const searchTerm = `%${search.trim()}%`
      whereConditions = and(
        eq(brands.accountId, accountId),
        sql`${brands.primaryCredentialId} IS NOT NULL`,
        sql`${brands.name} ILIKE ${searchTerm}`
      )
    }

    // Build base query - conditionally join brandUsers if userId is provided
    let baseQuery = db
      .select({
        id: brands.id,
        name: brands.name,
        logoAssetUrl: brands.logoAssetUrl,
        primaryCredentialId: brands.primaryCredentialId,
        updatedAt: brands.updatedAt
      })
      .from(brands)

    // If userId is provided, join with brandUsers to filter by user assignment
    if (userId) {
      baseQuery = baseQuery
        .innerJoin(brandUsers, eq(brands.id, brandUsers.brandId))
        .where(and(
          whereConditions,
          eq(brandUsers.userId, userId)
        ))
    } else {
      baseQuery = baseQuery.where(whereConditions)
    }

    // Get total count for pagination with all filters
    let countQuery = db
      .select({ count: count() })
      .from(brands)

    if (userId) {
      countQuery = countQuery
        .innerJoin(brandUsers, eq(brands.id, brandUsers.brandId))
        .where(and(
          whereConditions,
          eq(brandUsers.userId, userId)
        ))
    } else {
      countQuery = countQuery.where(whereConditions)
    }

    const [{ count: totalCount }] = await countQuery

    if (totalCount === 0) {
      return reply.send({
        data: [],
        pagination: {
          total: totalCount,
          page: pageNum,
          limit: limitNum,
          totalPages: 0
        }
      })
    }

    // Get paginated brands with all filters applied
    let accountBrands
    try {
      accountBrands = await baseQuery
        .orderBy(desc(brands.updatedAt))
        .limit(limitNum)
        .offset(offset)
    } catch (dbError) {
      request.log.error({ error: dbError, accountId }, 'Failed to fetch brands from database')
      return reply.code(500).send({ error: 'Database error while fetching brands' })
    }

    // Get detailed metrics for each brand with individual error handling
    const brandsWithDetails = await Promise.allSettled(
      accountBrands.map(async (brand) => {
        try {
          const [
            totalProducts,
            productsByRetailer,
            liveCampaigns,
            liveCampaignsByRetailer,
            assignedRetailers
          ] = await Promise.allSettled([
            getTotalProductCount(brand.id),
            getProductCountByRetailer(brand.id),
            getLiveCampaignCount(brand.id),
            getLiveCampaignCountByRetailer(brand.id),
            getAssignedRetailers(brand.id)
          ])

          // Extract values and handle individual failures
          const getValueOrDefault = (result, defaultValue = 0) => {
            if (result.status === 'fulfilled') {
              return result.value
            }
            request.log.warn({ brandId: brand.id, error: result.reason }, 'Failed to fetch metric for brand')
            return defaultValue
          }

          const getArrayValueOrDefault = (result, defaultValue = []) => {
            if (result.status === 'fulfilled') {
              return result.value
            }
            request.log.warn({ brandId: brand.id, error: result.reason }, 'Failed to fetch array metric for brand')
            return defaultValue
          }

          return {
            brandId: brand.id,
            brandName: brand.name,
            brandLogoUrl: brand.logoAssetUrl,
            primaryCredentialId: brand.primaryCredentialId ?? null,
            totalProductCount: getValueOrDefault(totalProducts),
            productCountByRetailer: getArrayValueOrDefault(productsByRetailer),
            totalLiveCampaigns: getValueOrDefault(liveCampaigns),
            liveCampaignsByRetailer: getArrayValueOrDefault(liveCampaignsByRetailer),
            assignedRetailers: getArrayValueOrDefault(assignedRetailers).map(r => r.name || 'Unknown'),
            lastModified: brand.updatedAt
          }
        } catch (brandError) {
          request.log.error({ error: brandError, brandId: brand.id }, 'Failed to process brand details')
          // Return brand with minimal data if individual brand processing fails
          return {
            brandId: brand.id,
            brandName: brand.name,
            brandLogoUrl: brand.logoAssetUrl,
            primaryCredentialId: brand.primaryCredentialId ?? null,
            totalProductCount: 0,
            productCountByRetailer: [],
            totalLiveCampaigns: 0,
            liveCampaignsByRetailer: [],
            assignedRetailers: [],
            lastModified: brand.updatedAt,
            error: 'Failed to load complete brand details'
          }
        }
      })
    )

    // Process results and handle any that failed
    const processedBrands = brandsWithDetails.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        const brand = accountBrands[index]
        request.log.error({
          error: result.reason,
          brandId: brand.id,
          brandName: brand.name
        }, 'Brand processing completely failed')

        // Return minimal brand data even if everything failed
        return {
          brandId: brand.id,
          brandName: brand.name,
          brandLogoUrl: brand.logoAssetUrl,
          primaryCredentialId: brand.primaryCredentialId ?? null,
          totalProductCount: 0,
          productCountByRetailer: [],
          totalLiveCampaigns: 0,
          liveCampaignsByRetailer: [],
          assignedRetailers: [],
          lastModified: brand.updatedAt,
          error: 'Failed to load brand details'
        }
      }
    })

    return reply.send({
      data: processedBrands,
      pagination: {
        total: totalCount,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(totalCount / limitNum)
      }
    })
  } catch (error) {
    request.log.error({
      error: error.message,
      stack: error.stack,
      accountId
    }, 'Unexpected error in getBrandsDetailsForAccount')

    return reply.code(500).send({
      error: 'An unexpected error occurred while fetching brand details',
      accountId: accountId
    })
  }
}

/**
 * Returns the total number of products associated with a given brand.
 * @param {string} brandId - The unique identifier of the brand.
 * @return {number} The total product count for the brand.
 */
async function getTotalProductCount(brandId) {
  const result = await db
    .select({ count: count() })
    .from(products)
    .where(eq(products.brandId, brandId))

  return result[0]?.count || 0
}

/**
 * Retrieves the count of products for a given brand, grouped by retailer name.
 * @param {string|number} brandId - The unique identifier of the brand.
 * @returns {Array<{retailerName: string, count: number}>} An array of objects containing retailer names and their corresponding product counts for the brand.
 */
async function getProductCountByRetailer(brandId) {
  const result = await db
    .select({
      retailerName: retailers.name,
      count: count()
    })
    .from(products)
    .innerJoin(retailerProducts, eq(products.productId, retailerProducts.productId))
    .innerJoin(retailers, eq(retailerProducts.retailerId, retailers.id))
    .where(eq(products.brandId, brandId))
    .groupBy(retailers.name)

  return result.map(r => ({ retailerName: r.retailerName, count: r.count }))
}

/**
 * Counts the number of live campaigns for a brand, where a live campaign is a project with at least one incomplete generation version.
 * @param {string} brandId - The unique identifier of the brand.
 * @return {number} The number of live campaigns for the specified brand.
 */
async function getLiveCampaignCount(brandId) {
  const result = await db
    .select({ count: count() })
    .from(projects)
    .innerJoin(generations, eq(projects.id, generations.projectId))
    .innerJoin(generationVersions, eq(generations.id, generationVersions.generationId))
    .where(and(
      eq(projects.brandId, brandId),
      eq(generationVersions.hasCompleted, false)
    ))

  return result[0]?.count || 0
}

/**
 * Returns the number of live campaigns for a brand, grouped by retailer name.
 *
 * A live campaign is defined as a project for the brand with at least one associated generation version that has not been completed.
 *
 * @param {string|number} brandId - The unique identifier of the brand.
 * @return {Array<{retailerName: string, count: number}>} An array where each object contains a retailer's name and the count of live campaigns for that retailer.
 */
async function getLiveCampaignCountByRetailer(brandId) {
  const result = await db
    .select({
      retailerName: retailers.name,
      count: count()
    })
    .from(projects)
    .innerJoin(generations, eq(projects.id, generations.projectId))
    .innerJoin(generationVersions, eq(generations.id, generationVersions.generationId))
    .innerJoin(products, eq(generations.productId, products.productId))
    .innerJoin(retailerProducts, eq(products.productId, retailerProducts.productId))
    .innerJoin(retailers, eq(retailerProducts.retailerId, retailers.id))
    .where(and(
      eq(projects.brandId, brandId),
      eq(generationVersions.hasCompleted, false)
    ))
    .groupBy(retailers.name)

  return result.map(r => ({ retailerName: r.retailerName, count: r.count }))
}

/**
 * Retrieves the list of retailers assigned to a specific brand.
 * @param {string|number} brandId - The unique identifier of the brand.
 * @returns {Promise<Array<{id: string|number, name: string}>>} An array of retailer objects assigned to the brand.
 */
async function getAssignedRetailers(brandId) {
  const result = await db
    .select({
      id: retailers.id,
      name: retailers.name
    })
    .from(brandRetailers)
    .innerJoin(retailers, eq(brandRetailers.retailerId, retailers.id))
    .where(eq(brandRetailers.brandId, brandId))

  return result
}