export const brandSchema = {
  create: {
    body: {
      type: 'object',
      required: ['name', 'accountId'],
      properties: {
        name: { type: 'string', minLength: 1 },
        accountId: { type: 'string', format: 'uuid' },
        description: { type: 'string' },
        colors: {
          type: 'array',
          items: { type: 'string' },
        },
        logoAssetUrl: { type: 'string' },
        logoAssetId: { type: 'string', format: 'uuid' }
      }
    }
  },
  update: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      properties: {
        name: { type: 'string', minLength: 1 },
        description: { type: 'string' },
        colors: {
          type: 'array',
          items: { type: 'string' },
        },
        logoAssetUrl: { type: 'string' },
      }
    }
  },
  addUser: {
    params: {
      type: 'object',
      required: ['brandId', 'userId'],
      properties: {
        brandId: { type: 'string', format: 'uuid' },
        userId: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      required: ['role'],
      properties: {
        role: { type: 'string', enum: ['owner', 'admin', 'member'] }
      }
    }
  },
  updateUserRole: {
    params: {
      type: 'object',
      required: ['brandId', 'userId'],
      properties: {
        brandId: { type: 'string', format: 'uuid' },
        userId: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      required: ['role'],
      properties: {
        role: { type: 'string', enum: ['owner', 'admin', 'member'] }
      }
    }
  },
  uploadGuidelines: {
    params: {
      type: 'object',
      required: ['brandId'],
      properties: {
        brandId: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      required: ['userId', 'guidelines'],
      properties: {
        userId: { type: 'string', format: 'uuid' },
        guidelines: { type: 'string' } // JSON string that will be parsed
      }
    }
  },
  getBrandsDetails: {
    params: {
      type: 'object',
      required: ['accountId'],
      properties: {
        accountId: { type: 'string', format: 'uuid' }
      }
    },
    querystring: {
      type: 'object',
      properties: {
        page: { type: 'integer', minimum: 1, default: 1 },
        limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
        search: { type: 'string', minLength: 1 },
        userId: { type: 'string', format: 'uuid' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                brandId: { type: 'string', format: 'uuid' },
                brandName: { type: 'string' },
                brandLogoUrl: { type: 'string', nullable: true },
                primaryCredentialId: { type: 'string', format: 'uuid', nullable: true },
                primaryCredentialName: { type: 'string', nullable: true },
                totalProductCount: { type: 'number' },
                productCountByRetailer: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      retailerName: { type: 'string' },
                      count: { type: 'number' }
                    }
                  }
                },
                totalLiveCampaigns: { type: 'number' },
                liveCampaignsByRetailer: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      retailerName: { type: 'string' },
                      count: { type: 'number' }
                    }
                  }
                },
                assignedRetailers: {
                  type: 'array',
                  items: { type: 'string' }
                },
                lastModified: { type: 'string', format: 'date-time' },
                error: { type: 'string' }
              }
            }
          },
          pagination: {
            type: 'object',
            properties: {
              total: { type: 'number' },
              page: { type: 'number' },
              limit: { type: 'number' },
              totalPages: { type: 'number' }
            },
            required: ['total', 'page', 'limit', 'totalPages']
          }
        },
        required: ['data', 'pagination']
      }
    }
  }
}