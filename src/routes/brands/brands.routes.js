import {
  getAllBrands,
  getBrandById,
  getBrandLogoUrl,
  createBrand,
  update<PERSON>rand,
  deleteBrand,
  addUserTo<PERSON>rand,
  removeUser<PERSON>romBrand,
  leaveBrand,
  getBrandUsers,
  getBrandsByAccountId,
  getBrandsByAccountIdWithUsers,
  updateBrandUserRole,
  uploadBrandGuidelines,
  getBrandGuidelines,
  getBrandsDetailsForAccount,
  updateBrandGuideline
} from './brands.controllers.js'
import { brandSchema } from './brands.schemas.js'

/**
 * Registers HTTP routes for managing brands and their users on a Fastify server, enforcing authentication on all endpoints.
 *
 * Defines routes for creating, retrieving, updating, and deleting brands; managing brand users and their roles; handling brand guidelines; and retrieving brand details and metrics. All routes require authentication via the `verifyAuth` decorator, and relevant routes apply schema validation.
 *
 * Throws an error if the authentication decorator is not registered on the Fastify instance before route registration.
 */
export default async function brandRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all brands
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllBrands
  })

  // Get brands by account ID
  fastify.get('/account/:accountId', {
    onRequest: [fastify.verifyAuth],
    handler: getBrandsByAccountId
  })

  // Get brands by account ID with users
  fastify.get('/account/:accountId/users', {
    onRequest: [fastify.verifyAuth],
    handler: getBrandsByAccountIdWithUsers
  })

  // Get brands details for account (with metrics)
  fastify.get('/account/:accountId/details', {
    onRequest: [fastify.verifyAuth],
    schema: brandSchema.getBrandsDetails,
    handler: getBrandsDetailsForAccount
  })

  // Get brand by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: getBrandById
  })

  // Get brand logo URL by ID
  fastify.get('/:id/logo', {
    onRequest: [fastify.verifyAuth],
    handler: getBrandLogoUrl
  })

  // Create brand
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    schema: brandSchema.create,
    handler: createBrand
  })

  // Update brand
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: brandSchema.update,
    handler: updateBrand
  })

  // Delete brand
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: deleteBrand
  })

  // Get all users for a brand
  fastify.get('/:brandId/users', {
    onRequest: [fastify.verifyAuth],
    handler: getBrandUsers
  })

  // Add user to brand
  fastify.post('/:brandId/users/:userId', {
    onRequest: [fastify.verifyAuth],
    schema: brandSchema.addUser,
    handler: addUserToBrand
  })

  // Update user role in brand
  fastify.put('/:brandId/users/:userId/role', {
    onRequest: [fastify.verifyAuth],
    schema: brandSchema.updateUserRole,
    handler: updateBrandUserRole
  })

  // Remove user from brand
  fastify.delete('/:brandId/users/:userId', {
    onRequest: [fastify.verifyAuth],
    handler: removeUserFromBrand
  })

  // Leave brand (user removes themselves)
  fastify.delete('/:brandId/leave/:userId', {
    onRequest: [fastify.verifyAuth],
    handler: leaveBrand
  })

  // Upload brand guidelines
  fastify.post('/:brandId/guidelines', {
    onRequest: [fastify.verifyAuth],
    schema: brandSchema.uploadGuidelines,
    handler: uploadBrandGuidelines
  })

  // Get brand guidelines
  fastify.get('/:brandId/guidelines', {
    onRequest: [fastify.verifyAuth],
    handler: getBrandGuidelines
  })

  // Update brand guideline
  fastify.put('/:brandId/guidelines/:guidelineId', {
    onRequest: [fastify.verifyAuth],
    handler: updateBrandGuideline
  })
}