import { eq, and, inArray, or, desc } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { invitations } from '../../db/schema/invitations.ts'
import { accounts } from '../../db/schema/accounts.ts'
import { brands } from '../../db/schema/brands.ts'
import { brandUsers } from '../../db/schema/brandUsers.ts'
import { userAccounts } from '../../db/schema/userAccounts.ts'
import { emailQueue } from '../../services/emailQueue.service.js'
import { notifications } from '../../db/schema/notifications.ts'
import { users } from '../../db/schema/users.ts'
import { AuthService } from '../../services/auth.service.ts'
import { sendMessageToUsers } from '../../services/notifications.js'

export async function getAllInvitations(request, reply) {
  try {
    const allInvitations = await db.select().from(invitations)
    return reply.send(allInvitations)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch invitations' })
  }
}

export async function getInvitationById(request, reply) {
  try {
    const { id } = request.params

    const [invitation] = await db
      .select()
      .from(invitations)
      .where(eq(invitations.id, id))
      .limit(1)

    if (!invitation) {
      return reply.code(404).send({ error: 'Invitation not found' })
    }

    return reply.send(invitation)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch invitation' })
  }
}

/**
 * Creates a new invitation for a user to join an account.
 *
 * If the invited user already exists, checks for existing membership and sends an in-app notification. If the user does not exist, creates the invitation and attempts to send an invitation email. Returns the created invitation or an appropriate error response.
 */
export async function createInvitation(request, reply) {
  try {
    // Get user info from session
    const sessionUser = request.user;

    if (!sessionUser) {
      // This should theoretically be caught by verifyAuth, but good practice to check
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    const { accountId, email, type, brandIds, role } = request.body

    if (!accountId) {
      return reply.code(400).send({ error: 'Account ID is required' })
    }

    // Get account to verify it exists
    const [account] = await db
      .select()
      .from(accounts)
      .where(eq(accounts.id, accountId))
      .limit(1)

    if (!account) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    // Check if user already exists
    const [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1)

    if (existingUser) {
      // Check if user is already associated with this account
      const [existingAssociation] = await db
        .select()
        .from(userAccounts)
        .where(and(
          eq(userAccounts.userId, existingUser.id),
          eq(userAccounts.accountId, accountId)
        ))
        .limit(1)

      if (existingAssociation) {
        return reply.code(400).send({ error: 'User is already a member of this account' })
      }

      // Create invitation
      const [invitation] = await db
        .insert(invitations)
        .values({
          accountId,
          email,
          type,
          role,
          brandIds,
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning()

      // Send in-app notification if user exists
      await sendMessageToUsers(existingUser.id, {
        type: 'account_invitation',
        title: 'New Account Invitation',
        message: `You've been invited to join ${account.name}`,
        data: {
          invitationId: invitation.id,
          accountId: accountId,
          accountName: account.name,
          invitationType: type
        },
        actions: ['accept', 'decline']
      })

      return reply.code(201).send(invitation)
    }

    // If user doesn't exist, create invitation and send email
    const [invitation] = await db
      .insert(invitations)
      .values({
        accountId,
        email,
        type,
        role,
        brandIds,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    // Send the invitation email
    try {
      await emailQueue.addInvitationEmail({
        to: email,
        invitationType: type,
        invitationId: invitation.id,
        organizationName: account.name,
        senderName: sessionUser?.name || 'Someone'
      })
    } catch (emailError) {
      request.log.error('Failed to send invitation email:', emailError)
    }

    return reply.code(201).send(invitation)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create invitation' })
  }
}

export async function updateInvitation(request, reply) {
  try {
    const { id } = request.params
    const updateData = {
      ...request.body,
      updatedAt: new Date()
    }

    const [invitation] = await db
      .update(invitations)
      .set(updateData)
      .where(eq(invitations.id, id))
      .returning()

    if (!invitation) {
      return reply.code(404).send({ error: 'Invitation not found' })
    }

    return reply.send(invitation)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update invitation' })
  }
}

export async function deleteInvitation(request, reply) {
  try {
    const { id } = request.params

    const [deletedInvitation] = await db
      .delete(invitations)
      .where(eq(invitations.id, id))
      .returning()

    if (!deletedInvitation) {
      return reply.code(404).send({ error: 'Invitation not found' })
    }

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete invitation' })
  }
}

export async function getInvitationsByAccountId(request, reply) {
  try {
    const { accountId } = request.params

    const invitationsList = await db
      .select()
      .from(invitations)
      .where(eq(invitations.accountId, accountId))

    return reply.send(invitationsList)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch invitations' })
  }
}

// Removed getInvitationsByOrganizationId - no longer needed with flattened structure

export async function createJoinRequest(request, reply) {
  try {
    const { accountId, email } = request.body

    if (!accountId) {
      return reply.code(400).send({ error: 'Account ID is required' })
    }

    // Get account
    const [account] = await db
      .select()
      .from(accounts)
      .where(eq(accounts.id, accountId))
      .limit(1)

    if (!account) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    // Check if user already exists and is part of this account
    const [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1)

    if (existingUser) {
      const [existingAssociation] = await db
        .select()
        .from(userAccounts)
        .where(and(
          eq(userAccounts.userId, existingUser.id),
          eq(userAccounts.accountId, accountId)
        ))
        .limit(1)

      if (existingAssociation) {
        return reply.code(400).send({ error: 'User is already a member of this account' })
      }
    }

    // Create the join request invitation
    const [invitation] = await db
      .insert(invitations)
      .values({
        accountId: account.id,
        email,
        type: 'request',
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    // Create notification for account admins
    try {
      // Get account admins
      const accountAdmins = await db
        .select({ userId: userAccounts.userId })
        .from(userAccounts)
        .where(and(
          eq(userAccounts.accountId, accountId),
          eq(userAccounts.role, 'admin')
        ))

      // Send notification to each admin
      for (const admin of accountAdmins) {
        await sendMessageToUsers(admin.userId, {
          type: 'join_request',
          title: 'New Join Request',
          message: `${email} has requested to join your account`,
          data: {
            invitationId: invitation.id,
            accountId: account.id,
            accountName: account.name,
            email: email
          },
          actions: ['accept', 'decline']
        })
      }
    } catch (notificationError) {
      request.log.error('Failed to create join request notification:', notificationError)
    }

    return reply.code(201).send(invitation)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create join request' })
  }
}

export async function acceptJoinRequest(request, reply) {
  try {
    const { invitationId } = request.params
    const { notificationId } = request.body

    console.log("Accepting join request", invitationId)

    const [invitation] = await db
      .select()
      .from(invitations)
      .where(eq(invitations.id, invitationId))
      .limit(1)

    if (!invitation) {
      return reply.code(404).send({ error: 'Invitation not found' })
    }

    if (invitation.status !== 'pending') {
      return reply.code(400).send({ error: 'Invitation is no longer pending' })
    }

    // Find user by invitation email
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.email, invitation.email))
      .limit(1)

    if (!user) {
      return reply.code(404).send({ error: 'User not found with the invitation email' })
    }

    // Get account details
    const [account] = await db
      .select()
      .from(accounts)
      .where(eq(accounts.id, invitation.accountId))
      .limit(1)

    if (!account) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    // Start a transaction
    const result = await db.transaction(async (tx) => {
      // Delete invitation instead of updating status
      const [deletedInvitation] = await tx
        .delete(invitations)
        .where(eq(invitations.id, invitationId))
        .returning()

      // Create user-account relationship with default role
      await tx
        .insert(userAccounts)
        .values({
          userId: user.id,
          accountId: invitation.accountId,
          role: 'member' // Default member role
        })

      // If notificationId is present, delete that specific notification
      if (notificationId) {
        await tx
          .delete(notifications)
          .where(eq(notifications.id, notificationId))
      } else {
        // Otherwise update related notification status as before
        await tx
          .update(notifications)
          .set({
            status: 'read',
            updated_at: new Date()
          })
          .where(eq(notifications.account_id, invitation.accountId))
      }

      // Create new notification with consistent structure
      await tx.insert(notifications).values({
        message: {
          type: 'account_invitation',
          title: 'Account Joined',
          message: `You have successfully joined ${account.name}`,
          data: {
            invitationId: invitation.id,
            accountId: invitation.accountId,
            accountName: account.name
          }
        },
        user_id: user.id,
        account_id: invitation.accountId,
        created_at: new Date(),
        updated_at: new Date()
      })

      return deletedInvitation
    })

    if (result) {
      console.log("Invitation accepted", result)
      return reply.send({ message: "Invitation accepted" })
    } else {
      console.log("Failed to accept join request")
      return reply.code(500).send({ error: 'Failed to accept join request' })
    }
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to accept join request' })
  }
}

export async function declineJoinRequest(request, reply) {
  try {
    const { invitationId } = request.params
    const { notificationId } = request.body
    // Get the invitation and verify it exists and is pending
    const [invitation] = await db
      .select()
      .from(invitations)
      .where(eq(invitations.id, invitationId))
      .limit(1)

    if (!invitation) {
      return reply.code(404).send({ error: 'Invitation not found' })
    }

    if (invitation.status !== 'pending') {
      return reply.code(400).send({ error: 'Invitation is no longer pending' })
    }

    if (invitation.type !== 'request') {
      return reply.code(400).send({ error: 'Invalid invitation type' })
    }

    // Start a transaction
    const result = await db.transaction(async (tx) => {
      // Delete the invitation
      const [deletedInvitation] = await tx
        .delete(invitations)
        .where(eq(invitations.id, invitationId))
        .returning()

      // If notificationId is present, delete that specific notification
      if (notificationId) {
        await tx
          .delete(notifications)
          .where(eq(notifications.id, notificationId))
      } else {
        // Otherwise update related notification status as before
        await tx
          .update(notifications)
          .set({
            status: 'read',
            updated_at: new Date()
          })
          .where(and(
            eq(notifications.account_id, invitation.accountId),
            eq(notifications.message['type'].cast('text'), 'join_request'),
            eq(notifications.message['invitationId'].cast('uuid'), invitationId)
          ))
      }

      return deletedInvitation
    })

    return reply.send("Invitation declined")
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to decline join request' })
  }
}

export async function resendInvitation(request, reply) {
  try {
    const { invitationId } = request.params
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Find the invitation
    const [invitation] = await db
      .select()
      .from(invitations)
      .where(eq(invitations.id, invitationId))
      .limit(1)

    if (!invitation) {
      return reply.code(404).send({ error: 'Invitation not found' })
    }

    if (invitation.status !== 'pending') {
      return reply.code(400).send({ error: 'Can only resend pending invitations' })
    }

    // Get account details for the invitation
    const [account] = await db
      .select()
      .from(accounts)
      .where(eq(accounts.id, invitation.accountId))
      .limit(1)

    if (!account) {
      return reply.code(404).send({ error: 'Account not found' })
    }

    // Send the invitation email
    try {
      await emailQueue.addInvitationEmail({
        to: invitation.email,
        invitationType: invitation.type,
        invitationId: invitation.id,
        organizationName: account.name,
        senderName: sessionUser?.name || 'Someone'
      })

      return reply.send({ message: 'Invitation resent successfully' })
    } catch (emailError) {
      request.log.error('Failed to resend invitation email:', emailError)
      return reply.code(500).send({ error: 'Failed to send invitation email' })
    }
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to resend invitation' })
  }
}