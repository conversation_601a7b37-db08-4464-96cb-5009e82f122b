import {
  getAllInvitations,
  getInvitationById,
  createInvitation,
  updateInvitation,
  deleteInvitation,
  getInvitationsByAccountId,
  createJoinRequest,
  acceptJoinRequest,
  declineJoinRequest,
  resendInvitation
} from './invitations.controllers.js'
import { invitationSchema } from './invitations.schemas.js'

export default async function invitationRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all invitations
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllInvitations
  })

  // Get invitation by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: getInvitationById
  })

  // Create invitation
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    schema: invitationSchema.create,
    handler: createInvitation
  })

  // Update invitation
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: invitationSchema.update,
    handler: updateInvitation
  })

  // Delete invitation
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: deleteInvitation
  })

  // Get invitations by account ID
  fastify.get('/account/:accountId', {
    onRequest: [fastify.verifyAuth],
    handler: getInvitationsByAccountId
  })

  // Accept join request (moved before the general join-request route)
  fastify.post('/join-request/:invitationId/accept', {
    onRequest: [fastify.verifyAuth],
    schema: invitationSchema.acceptJoinRequest,
    handler: acceptJoinRequest
  })

  // Create join request
  fastify.post('/join-request', {
    handler: createJoinRequest
  })

  // Decline join request
  fastify.delete('/decline-join-request/:invitationId', {
    onRequest: [fastify.verifyAuth],
    handler: declineJoinRequest
  })

  fastify.post('/resend-email/:invitationId', {
    onRequest: [fastify.verifyAuth],
    handler: resendInvitation
  })
}