export const invitationSchema = {
  create: {
    body: {
      type: 'object',
      required: ['accountId', 'email', 'type'],
      properties: {
        accountId: { type: 'string', format: 'uuid' },
        email: { type: 'string', format: 'email' },
        type: { type: 'string', enum: ['request', 'invite'] },
        role: { type: 'string' },
        brandIds: { 
          type: 'array',
          items: { type: 'string', format: 'uuid' }
        }
      }
    }
  },
  update: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      properties: {
        email: { type: 'string', format: 'email' },
        type: { type: 'string', enum: ['request', 'invite'] }
      }
    }
  }
}