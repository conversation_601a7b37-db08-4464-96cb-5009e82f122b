import { eq } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { reviewers } from '../../db/schema/reviewers.ts'

/**
 * Updates the status of a reviewer
 * @param {Object} request - Fastify request object
 * @param {Object} reply - Fastify reply object
 * @returns {Object} Updated reviewer object
 */
export async function updateReviewerStatus(request, reply) {
  try {
    const { id } = request.params
    const { status } = request.body

    // Validate required fields
    if (!id) {
      return reply.code(400).send({ error: 'Reviewer ID is required' })
    }

    if (!status) {
      return reply.code(400).send({ error: 'Status is required' })
    }

    // Validate status values
    const validStatuses = ['pending', 'approved', 'request_change']
    if (!validStatuses.includes(status)) {
      return reply.code(400).send({ 
        error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` 
      })
    }

    // Check if reviewer exists
    const [existingReviewer] = await db
      .select()
      .from(reviewers)
      .where(eq(reviewers.id, id))
      .limit(1)

    if (!existingReviewer) {
      return reply.code(404).send({ error: 'Reviewer not found' })
    }

    // Update the reviewer status
    const [updatedReviewer] = await db
      .update(reviewers)
      .set({
        status,
        updatedAt: new Date()
      })
      .where(eq(reviewers.id, id))
      .returning()

    return reply.send(updatedReviewer)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update reviewer status' })
  }
}
