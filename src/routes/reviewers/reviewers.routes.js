import { updateReviewerStatus } from './reviewers.contollers.js'

export default async function reviewerRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Update reviewer status
  fastify.put('/:id/status', {
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid' }
        }
      },
      body: {
        type: 'object',
        required: ['status'],
        properties: {
          status: { 
            type: 'string',
            enum: ['pending', 'approved', 'request_change']
          }
        }
      }
    },
    handler: updateReviewerStatus
  })
}
