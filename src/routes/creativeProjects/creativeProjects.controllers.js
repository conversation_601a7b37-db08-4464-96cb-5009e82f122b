import { db } from '../../db/index.js'
import { projects } from '../../db/schema/projects.ts'
import { brands } from '../../db/schema/brands.ts'
import { users } from '../../db/schema/users.ts'
import { generations } from '../../db/schema/generations.ts'
import { creatives } from '../../db/schema/creatives.ts'
import { generationVersions } from '../../db/schema/generationVersions.ts'
import { retailers } from '../../db/schema/retailers.ts'
import { brandRetailers } from '../../db/schema/retailers.ts'
import { keywords } from '../../db/schema/keywords.ts'
import { products } from '../../db/schema/products.ts'
import { credentialSets } from '../../db/schema/credentialSets.ts'
import { proofs } from '../../db/schema/proofs.ts'
import { proofComments } from '../../db/schema/proofComments.ts'
import { reviewers } from '../../db/schema/reviewers.ts'
import { externalReviewers } from '../../db/schema/externalReviewers.ts'
import { eq, and, or, desc, count, inArray, max, isNotNull, isNull, exists, sql } from 'drizzle-orm'
import { authService } from '../../services/authorization.service.js'

/**
 * Helper function to check Walmart campaign statuses
 * @param {Object} wmCampaigns - The wmCampaigns object from the project
 * @param {string} authToken - Authorization token for Walmart API
 * @param {string} advertiserId - Advertiser ID for the request
 * @returns {Promise<Object>} - Object containing campaign statuses and any active campaigns
 */
async function checkWalmartCampaignStatuses(wmCampaigns, authToken, advertiserId) {
  if (!wmCampaigns || Object.keys(wmCampaigns).length === 0) {
    return { hasActiveCampaigns: false, campaignStatuses: {} }
  }

  const campaignIds = Object.keys(wmCampaigns)
  const campaignStatuses = {}
  let hasActiveCampaigns = false

  try {
    // Make request to Walmart API to get campaign statuses
    const response = await fetch(
      `${process.env.WALMART_MANAGER_API_URL}/display/campaigns/list`,
      {
        method: 'POST',
        headers: {
          'Authorization': authToken,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          advertiserId: parseInt(advertiserId),
          "Filter[campaignId]": campaignIds,
          startIndex: 0,
          count: campaignIds.length
        })
      }
    )

    if (!response.ok) {
      const errorData = await response.json()

      // Check for the specific "zero campaigns" error - this means campaigns don't exist
      if (errorData.error === "Walmart API Error" &&
          Array.isArray(errorData.details)) {
        const hasNoCampaignsError = errorData.details.some(
          (detail) => detail.details &&
          Array.isArray(detail.details) &&
          detail.details.includes("UNABLE_TO_FETCH_CAMPAIGN_DETAIL")
        )

        if (hasNoCampaignsError) {
          // Campaigns don't exist, safe to delete
          return { hasActiveCampaigns: false, campaignStatuses: {} }
        }
      }

      throw new Error(`Walmart API Error: ${errorData.message || 'Failed to fetch campaigns'}`)
    }

    const data = await response.json()

    if (data.response && Array.isArray(data.response)) {
      data.response.forEach(campaign => {
        const campaignId = campaign.campaignId.toString()
        const status = campaign.status

        campaignStatuses[campaignId] = {
          name: campaign.name,
          status: status,
          startDate: campaign.startDate,
          endDate: campaign.endDate
        }

        // Check if campaign is LIVE or SCHEDULED
        if (status === 'LIVE' || status === 'SCHEDULED') {
          hasActiveCampaigns = true
        }
      })
    }

    return { hasActiveCampaigns, campaignStatuses }
  } catch (error) {
    // If we can't check the campaign statuses, we should err on the side of caution
    // and not allow deletion
    throw new Error(`Failed to check campaign statuses: ${error.message}`)
  }
}

/**
 * Retrieves a paginated list of projects for a specific brand, including creator information.
 *
 * Responds with project data and pagination metadata. Returns a 500 error if retrieval fails.
 */
export async function getProjectsByBrandId(request, reply) {
  try {
    const { brandId } = request.params
    const { page = 1, limit = 10 } = request.query

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Check if user has access to this brand
    const accessibleBrands = await authService.getUserAccessibleBrands(sessionUser.id)
    const accessibleBrandIds = accessibleBrands.map(brand => brand.id)

    if (!accessibleBrandIds.includes(brandId)) {
      return reply.code(403).send({ error: 'Forbidden - You do not have access to this brand' })
    }

    // Convert page and limit to numbers and calculate offset
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Get total count for pagination
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(projects)
      .where(eq(projects.brandId, brandId))

    // Get paginated projects with creator info and credential set
    const projectsData = await db
      .select({
        id: projects.id,
        brandId: projects.brandId,
        name: projects.name,
        wmCampaigns: projects.wmCampaigns,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        creator: {
          id: users.id,
          name: users.name,
          email: users.email
        },
        brand: {
          id: brands.id,
          name: brands.name,
          primaryCredentialId: brands.primaryCredentialId
        },
        credentialSet: {
          id: credentialSets.id,
          name: credentialSets.name,
          type: credentialSets.type,
          credentials: credentialSets.credentials,
          isActive: credentialSets.isActive,
          isShared: credentialSets.isShared
        }
      })
      .from(projects)
      .leftJoin(users, eq(projects.createdBy, users.id))
      .leftJoin(brands, eq(projects.brandId, brands.id))
      .leftJoin(credentialSets, eq(brands.primaryCredentialId, credentialSets.id))
      .where(eq(projects.brandId, brandId))
      .orderBy(desc(projects.createdAt))
      .limit(limitNum)
      .offset(offset)

    return reply.send({
      data: projectsData,
      pagination: {
        total: totalCount,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(totalCount / limitNum)
      }
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch projects' })
  }
}

/**
 * Retrieves a single project by its ID, including creator, brand, retailer, and credential set details, along with the count of associated generations.
 *
 * Returns a 404 error if the project does not exist.
 */
export async function getProjectById(request, reply) {
  try {
    const { id } = request.params

    // Log the project ID being requested
    request.log.info(`Fetching project with ID: ${id}`)

    // Get project with creator, brand, and credential set info
    const [project] = await db
      .select({
        id: projects.id,
        brandId: projects.brandId,
        retailerId: projects.retailerId,
        name: projects.name,
        wmCampaigns: projects.wmCampaigns,
        currentStep: projects.currentStep,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        creator: {
          id: users.id,
          name: users.name,
          email: users.email
        },
        brand: {
          id: brands.id,
          name: brands.name,
          primaryCredentialId: brands.primaryCredentialId
        },
        retailer: {
          id: retailers.id,
          name: retailers.name,
          slug: retailers.slug,
          logoUrl: retailers.logoUrl,
          url: retailers.url
        },
        credentialSet: {
          id: credentialSets.id,
          name: credentialSets.name,
          type: credentialSets.type,
          credentials: credentialSets.credentials,
          isActive: credentialSets.isActive,
          isShared: credentialSets.isShared
        }
      })
      .from(projects)
      .leftJoin(users, eq(projects.createdBy, users.id))
      .leftJoin(brands, eq(projects.brandId, brands.id))
      .leftJoin(credentialSets, eq(brands.primaryCredentialId, credentialSets.id))
      .leftJoin(retailers, eq(projects.retailerId, retailers.id))
      .where(eq(projects.id, id))
      .limit(1)

    if (!project) {
      request.log.warn(`Project not found with ID: ${id}`)
      return reply.code(404).send({ error: 'Project not found' })
    }

    request.log.info(`Found project: ${project.name}`)

    // Get associated generations count
    const [{ generationsCount }] = await db
      .select({ generationsCount: count() })
      .from(generations)
      .where(eq(generations.projectId, id))

    const response = {
      ...project,
      generationsCount
    }

    return reply.send(response)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch project' })
  }
}

/**
 * Creates a new project associated with a specified retailer.
 *
 * Validates the user session and required fields, ensures the retailer exists, and inserts a new project record. Returns the created project with a 201 status code, or an appropriate error response if validation fails.
 */
export async function createProject(request, reply) {
  try {
    const { retailerId, name, wmCampaigns = {} } = request.body

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    if (!retailerId) {
      return reply.code(400).send({ error: 'Retailer ID is required' })
    }

    if (!name) {
      return reply.code(400).send({ error: 'Project name is required' })
    }

    // Verify retailer exists
    const [retailer] = await db
      .select({
        id: retailers.id,
        name: retailers.name
      })
      .from(retailers)
      .where(eq(retailers.id, retailerId))
      .limit(1)

    if (!retailer) {
      return reply.code(404).send({ error: 'Retailer not found' })
    }

    // Create the project with retailerId only (no brandId yet)
    const [project] = await db
      .insert(projects)
      .values({
        retailerId,
        name,
        wmCampaigns,
        createdBy: sessionUser.id,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    return reply.code(201).send(project)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create project' })
  }
}

/**
 * Updates an existing project with new attributes such as name, campaigns, brand, retailer, or workflow step.
 *
 * Only provided fields are updated. For workflow step changes that require business logic (steps 1 or 3), use the advanceProjectStep endpoint instead.
 * Returns the updated project, or an error if the project, retailer, or brand does not exist.
 */
export async function updateProject(request, reply) {
  try {
    const { id } = request.params
    const { name, wmCampaigns, brandId, retailerId, currentStep, status } = request.body

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Check if project exists
    const [existingProject] = await db
      .select()
      .from(projects)
      .where(eq(projects.id, id))
      .limit(1)

    if (!existingProject) {
      return reply.code(404).send({ error: 'Project not found' })
    }

    // Validate retailerId if provided (retailerId is required, so we need to verify it exists)
    if (retailerId !== undefined) {
      const [retailer] = await db
        .select({ id: retailers.id })
        .from(retailers)
        .where(eq(retailers.id, retailerId))
        .limit(1)

      if (!retailer) {
        return reply.code(404).send({ error: 'Retailer not found' })
      }
    }

    // Validate brandId if provided (brandId is optional, but if provided must exist)
    if (brandId !== undefined && brandId !== null) {
      const [brand] = await db
        .select({ id: brands.id })
        .from(brands)
        .where(eq(brands.id, brandId))
        .limit(1)

      if (!brand) {
        return reply.code(404).send({ error: 'Brand not found' })
      }
    }

    // Prepare update data (only include fields that are provided)
    const updateData = {
      updatedAt: new Date()
    }

    if (name !== undefined) {
      updateData.name = name
    }

    if (wmCampaigns !== undefined) {
      updateData.wmCampaigns = wmCampaigns
    }

    if (brandId !== undefined) {
      updateData.brandId = brandId
    }

    if (retailerId !== undefined) {
      updateData.retailerId = retailerId
    }

    if (currentStep !== undefined) {
      updateData.currentStep = currentStep
    }

    if (status !== undefined) {
      updateData.status = status
    }

    // Update the project
    const [updatedProject] = await db
      .update(projects)
      .set(updateData)
      .where(eq(projects.id, id))
      .returning()

    return reply.send(updatedProject)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update project' })
  }
}

/**
 * Deletes a project by its ID if it has no associated generations.
 *
 * Validates the user session and project existence. Returns an error if the project does not exist or if it has associated generations. On successful deletion, returns a success message with the project ID.
 */
export async function deleteProject(request, reply) {
  try {
    const { id } = request.params

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Check if project exists and get project details with brand/credential info
    const [existingProject] = await db
      .select({
        id: projects.id,
        name: projects.name,
        wmCampaigns: projects.wmCampaigns,
        brand: {
          id: brands.id,
          name: brands.name,
          primaryCredentialId: brands.primaryCredentialId
        },
        credentialSet: {
          id: credentialSets.id,
          credentials: credentialSets.credentials
        }
      })
      .from(projects)
      .leftJoin(brands, eq(projects.brandId, brands.id))
      .leftJoin(credentialSets, eq(brands.primaryCredentialId, credentialSets.id))
      .where(eq(projects.id, id))
      .limit(1)

    if (!existingProject) {
      return reply.code(404).send({ error: 'Project not found' })
    }

    // Get all generations associated with this project for cascade deletion
    const projectGenerations = await db
      .select({ id: generations.id })
      .from(generations)
      .where(eq(generations.projectId, id))

    const generationIds = projectGenerations.map(gen => gen.id)

    // Check Walmart campaign statuses if there are campaigns
    if (existingProject.wmCampaigns && Object.keys(existingProject.wmCampaigns).length > 0) {
      try {
        // We need credentials to check campaign status
        if (!existingProject.credentialSet || !existingProject.credentialSet.credentials) {
          return reply.code(400).send({
            error: 'Cannot verify campaign status - missing credentials'
          })
        }

        const credentials = existingProject.credentialSet.credentials
        const authToken = `Bearer ${credentials.accessToken}`
        const advertiserId = credentials.advertiserId

        if (!authToken || !advertiserId) {
          return reply.code(400).send({
            error: 'Cannot verify campaign status - invalid credentials'
          })
        }

        const { hasActiveCampaigns, campaignStatuses } = await checkWalmartCampaignStatuses(
          existingProject.wmCampaigns,
          authToken,
          advertiserId
        )

        if (hasActiveCampaigns) {
          // Find which campaigns are active
          const activeCampaigns = Object.entries(campaignStatuses)
            .filter(([_, campaign]) => campaign.status === 'LIVE' || campaign.status === 'SCHEDULED')
            .map(([campaignId, campaign]) => ({
              id: campaignId,
              name: campaign.name,
              status: campaign.status
            }))

          return reply.code(400).send({
            error: 'Cannot delete project with active campaigns',
            activeCampaigns,
            message: 'Please pause or stop all LIVE and SCHEDULED campaigns before deleting this project'
          })
        }

        request.log.info(`Campaign status check passed for project ${id}. No active campaigns found.`)
      } catch (error) {
        request.log.error(`Failed to check campaign statuses for project ${id}:`, error)
        return reply.code(500).send({
          error: 'Failed to verify campaign status',
          details: error.message
        })
      }
    }

    // Cascade delete all related data if there are generations
    if (generationIds.length > 0) {
      request.log.info(`Deleting ${generationIds.length} generations and their related data for project ${id}`)

      // Get all generation versions for these generations
      const generationVersionsData = await db
        .select({ id: generationVersions.id })
        .from(generationVersions)
        .where(inArray(generationVersions.generationId, generationIds))

      const versionIds = generationVersionsData.map(v => v.id)

      if (versionIds.length > 0) {
        // Get all proofs for these versions
        const proofsData = await db
          .select({ id: proofs.id })
          .from(proofs)
          .where(inArray(proofs.generationVersionId, versionIds))

        const proofIds = proofsData.map(p => p.id)

        if (proofIds.length > 0) {
          // Delete proof comments first (they reference proofs)
          await db
            .delete(proofComments)
            .where(inArray(proofComments.proofId, proofIds))

          // Delete reviewers (they reference proofs)
          await db
            .delete(reviewers)
            .where(inArray(reviewers.proofId, proofIds))

          // Delete proofs
          await db
            .delete(proofs)
            .where(inArray(proofs.id, proofIds))
        }

        // Delete generation versions
        await db
          .delete(generationVersions)
          .where(inArray(generationVersions.id, versionIds))
      }

      // Delete creatives that reference these generations
      await db
        .delete(creatives)
        .where(inArray(creatives.generationId, generationIds))

      // Delete the generations themselves
      await db
        .delete(generations)
        .where(inArray(generations.id, generationIds))
    }

    // Delete the project
    await db
      .delete(projects)
      .where(eq(projects.id, id))

    return reply.code(200).send({
      success: true,
      message: 'Project deleted successfully',
      projectId: id,
      deletedGenerations: generationIds.length
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete project' })
  }
}

/**
 * Retrieves a paginated list of all projects accessible to the current user, including associated generations, creatives, and the latest generation versions for each project.
 *
 * The response includes project details, nested generations with their creatives and latest version, and pagination metadata.
 */
export async function getAllUserProjects(request, reply) {
  try {
    const { page = 1, limit = 10 } = request.query

    // Get user info from session
    const sessionUser = request.user

    console.log('sessionUser', sessionUser)

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Get brands accessible to this user
    const accessibleBrands = await authService.getUserAccessibleBrands(sessionUser.id)
    const accessibleBrandIds = accessibleBrands.map(brand => brand.id)

    // Convert page and limit to numbers and calculate offset
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Build where conditions based on whether user has accessible brands
    let whereConditions
    if (accessibleBrandIds.length > 0) {
      whereConditions = or(
        // Projects with brands the user has access to
        and(
          isNotNull(projects.brandId),
          inArray(projects.brandId, accessibleBrandIds)
        ),
        // Projects with null brandId that were created by this user
        and(
          isNull(projects.brandId),
          eq(projects.createdBy, sessionUser.id)
        )
      )
    } else {
      // If no accessible brands, only show user's own projects with null brandId
      whereConditions = and(
        isNull(projects.brandId),
        eq(projects.createdBy, sessionUser.id)
      )
    }

    console.log('getAllUserProjects - whereConditions built, hasAccessibleBrands:', accessibleBrandIds.length > 0)

    // Debug: Check if there are any projects with null brandId created by this user
    const nullBrandProjects = await db
      .select({
        id: projects.id,
        name: projects.name,
        brandId: projects.brandId,
        createdBy: projects.createdBy
      })
      .from(projects)
      .where(
        and(
          isNull(projects.brandId),
          eq(projects.createdBy, sessionUser.id)
        )
      )
    console.log('getAllUserProjects - Projects with null brandId for user:', nullBrandProjects)

    // Get projects - fetch more records to ensure we have enough unique ones after deduplication
    const fetchLimit = limitNum * 3 // Fetch 3x to account for potential duplicates
    const projectsData = await db
      .select({
        id: projects.id,
        brandId: projects.brandId,
        name: projects.name,
        wmCampaigns: projects.wmCampaigns,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        currentStep: projects.currentStep,
        status: projects.status,
        creator: {
          id: users.id,
          name: users.name,
          email: users.email
        },
        brand: {
          id: brands.id,
          name: brands.name
        },
        retailer: {
          id: retailers.id,
          name: retailers.name,
          slug: retailers.slug,
          logoUrl: retailers.logoUrl,
          url: retailers.url
        },
        credentialSet: {
          id: credentialSets.id,
          name: credentialSets.name,
          type: credentialSets.type,
          credentials: credentialSets.credentials,
          isActive: credentialSets.isActive,
          isShared: credentialSets.isShared
        }
      })
      .from(projects)
      .leftJoin(users, eq(projects.createdBy, users.id))
      .leftJoin(brands, eq(projects.brandId, brands.id)) // Only join brands when brandId exists
      .leftJoin(credentialSets, eq(brands.primaryCredentialId, credentialSets.id))
      .leftJoin(retailers, eq(projects.retailerId, retailers.id))
      .where(whereConditions)
      .orderBy(desc(projects.createdAt))
      .limit(fetchLimit)

    // Deduplicate projects (one entry per project id)
    const uniqueProjectsMap = new Map()
    for (const proj of projectsData) {
      if (!uniqueProjectsMap.has(proj.id)) {
        uniqueProjectsMap.set(proj.id, proj)
      }
    }
    const uniqueProjectsDataOrdered = Array.from(uniqueProjectsMap.values())

    // Apply pagination on the unique list
    const paginatedUnique = uniqueProjectsDataOrdered.slice(offset, offset + limitNum)

    // Get all generations for the fetched projects
    const projectIds = paginatedUnique.map(project => project.id)

    let generationsData = []
    let creativesData = []
    let latestVersionsData = []

    if (projectIds.length > 0) {
      // Get generations with brand and keyword info
      generationsData = await db
        .select({
          id: generations.id,
          projectId: generations.projectId,
          productId: generations.productId,
          keywordId: generations.keywordId,
          brandId: generations.brandId,
          createdAt: generations.createdAt,
          updatedAt: generations.updatedAt,
          brand: {
            id: brands.id,
            name: brands.name,
            logoAssetUrl: brands.logoAssetUrl
          },
          keyword: {
            id: keywords.id,
            keyword: keywords.keyword
          }
        })
        .from(generations)
        .leftJoin(brands, eq(generations.brandId, brands.id))
        .leftJoin(keywords, eq(generations.keywordId, keywords.id))
        .where(inArray(generations.projectId, projectIds))

      // Get generation IDs for fetching related data
      const generationIds = generationsData.map(gen => gen.id)

      if (generationIds.length > 0) {
        // Get creatives for these generations
        creativesData = await db
          .select({
            id: creatives.id,
            generationId: creatives.generationId,
            projectId: creatives.projectId,
            wmCreativeId: creatives.wmCreativeId,
            brandId: creatives.brandId,
            userId: creatives.userId,
            previewUrls: creatives.previewUrls,
            previewStatus: creatives.previewStatus,
            folderId: creatives.folderId,
            status: creatives.status,
            keyword: creatives.keyword,
            adUnits: creatives.adUnits,
            reviewComments: creatives.reviewComments,
            createdAt: creatives.createdAt,
            updatedAt: creatives.updatedAt
          })
          .from(creatives)
          .where(inArray(creatives.generationId, generationIds))

        // Get latest generation versions for each generation
        const latestVersionsSubquery = db
          .select({
            generationId: generationVersions.generationId,
            maxVersion: max(generationVersions.version).as('maxVersion')
          })
          .from(generationVersions)
          .where(inArray(generationVersions.generationId, generationIds))
          .groupBy(generationVersions.generationId)
          .as('latestVersions')

        latestVersionsData = await db
          .select({
            id: generationVersions.id,
            generationId: generationVersions.generationId,
            sessionId: generationVersions.sessionId,
            unitFields: generationVersions.unitFields,
            hasCompleted: generationVersions.hasCompleted,
            createdBy: generationVersions.createdBy,
            version: generationVersions.version,
            adPreviews: generationVersions.adPreviews,
            previewImagePositions: generationVersions.previewImagePositions,
            applyToAll: generationVersions.applyToAll,
            createdAt: generationVersions.createdAt,
            updatedAt: generationVersions.updatedAt
          })
          .from(generationVersions)
          .innerJoin(
            latestVersionsSubquery,
            and(
              eq(generationVersions.generationId, latestVersionsSubquery.generationId),
              eq(generationVersions.version, latestVersionsSubquery.maxVersion)
            )
          )
      }
    }

    // Group creatives by generationId
    const creativesByGeneration = creativesData.reduce((acc, creative) => {
      if (!acc[creative.generationId]) {
        acc[creative.generationId] = []
      }
      acc[creative.generationId].push(creative)
      return acc
    }, {})

    // Group latest versions by generationId
    const versionsByGeneration = latestVersionsData.reduce((acc, version) => {
      acc[version.generationId] = version
      return acc
    }, {})

    // Group generations by projectId and add creatives and versions
    const generationsByProject = generationsData.reduce((acc, generation) => {
      if (!acc[generation.projectId]) {
        acc[generation.projectId] = []
      }

      const generationWithExtras = {
        id: generation.id,
        projectId: generation.projectId,
        productId: generation.productId,
        keywordId: generation.keywordId,
        brandId: generation.brandId,
        createdAt: generation.createdAt,
        updatedAt: generation.updatedAt,
        brandLogoAssetUrl: generation.brand?.logoAssetUrl || null,
        keywordName: generation.keyword?.keyword || null,
        creatives: creativesByGeneration[generation.id] || [],
        latestVersion: versionsByGeneration[generation.id] || null
      }

      acc[generation.projectId].push(generationWithExtras)
      return acc
    }, {})

    // Add generations to each project
    const projectsWithGenerations = paginatedUnique.map(project => {
      // Only keep brand data if it actually exists, don't fabricate it
      return {
        ...project,
        brand: project.brand && project.brand.id ? project.brand : null,
        credentialSet: project.credentialSet && project.credentialSet.id ? project.credentialSet : null,
        generations: generationsByProject[project.id] || []
      }
    })

    // Get total count - also filtered by accessible brands and user's own projects
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(projects)
      .where(whereConditions)

    return reply.send({
      data: projectsWithGenerations,
      pagination: {
        total: totalCount,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(totalCount / limitNum)
      }
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch user projects' })
  }
}

/**
 * Retrieves all available filter values for projects, including creators, retailers, brands, and campaign IDs.
 *
 * Returns arrays of users who have created projects, brands with associated projects, retailers linked to those brands, and unique campaign IDs from creatives related to project generations.
 */
export async function getProjectFilterValues(request, reply) {
  try {
    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Get brands accessible to this user
    const accessibleBrands = await authService.getUserAccessibleBrands(sessionUser.id)
    const accessibleBrandIds = accessibleBrands.map(brand => brand.id)

    // Build where conditions the same way as getAllUserProjects
    let whereConditions
    if (accessibleBrandIds.length > 0) {
      whereConditions = or(
        // Projects with brands the user has access to
        and(
          isNotNull(projects.brandId),
          inArray(projects.brandId, accessibleBrandIds)
        ),
        // Projects with null brandId that were created by this user
        and(
          isNull(projects.brandId),
          eq(projects.createdBy, sessionUser.id)
        )
      )
    } else {
      // If no accessible brands, only show user's own projects with null brandId
      whereConditions = and(
        isNull(projects.brandId),
        eq(projects.createdBy, sessionUser.id)
      )
    }

    // Get all creators (users who have created projects that the user can access)
    const creatorsData = await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email
      })
      .from(users)
      .innerJoin(projects, eq(projects.createdBy, users.id))
      .where(whereConditions)
      .groupBy(users.id, users.name, users.email)
      .orderBy(users.name)

    // Get all brands that have projects and are accessible to the user
    const brandsData = await db
      .select({
        id: brands.id,
        name: brands.name
      })
      .from(brands)
      .innerJoin(projects, eq(projects.brandId, brands.id))
      .where(whereConditions)
      .groupBy(brands.id, brands.name)
      .orderBy(brands.name)

    // Get all retailers that are directly associated with accessible projects
    const retailersData = await db
      .select({
        id: retailers.id,
        name: retailers.name,
        slug: retailers.slug
      })
      .from(retailers)
      .innerJoin(projects, eq(projects.retailerId, retailers.id))
      .where(whereConditions)
      .groupBy(retailers.id, retailers.name, retailers.slug)
      .orderBy(retailers.name)

    // Get all campaign IDs from projects.wmCampaigns field for accessible projects
    const projectsWithCampaigns = await db
      .select({
        wmCampaigns: projects.wmCampaigns
      })
      .from(projects)
      .where(
        and(
          isNotNull(projects.wmCampaigns),
          whereConditions
        )
      )

    // Extract unique campaign IDs from the wmCampaigns JSON objects
    const campaignIds = []
    for (const project of projectsWithCampaigns) {
      if (project.wmCampaigns && typeof project.wmCampaigns === 'object') {
        const projectCampaignIds = Object.keys(project.wmCampaigns)
        campaignIds.push(...projectCampaignIds)
      }
    }

    // Remove duplicates and sort
    const uniqueCampaignIds = [...new Set(campaignIds)].sort()

    // Get all unique project statuses that are currently in use for accessible projects
    const statusData = await db
      .select({
        status: projects.status
      })
      .from(projects)
      .where(
        and(
          isNotNull(projects.status),
          whereConditions
        )
      )
      .groupBy(projects.status)
      .orderBy(projects.status)

    // Extract unique status values
    const statuses = statusData.map(statusItem => statusItem.status)

    return reply.send({
      creators: creatorsData,
      retailers: retailersData,
      brands: brandsData,
      campaigns: uniqueCampaignIds,
      statuses: statuses
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch filter values' })
  }
}

/**
 * Searches for projects using optional filters such as creator, retailer, brand, campaign, and text search on project names.
 *
 * Retrieves paginated project results matching the specified filters, including nested generations, creatives, and the latest generation versions for each project. Deduplicates results to ensure unique projects and returns pagination metadata along with the applied filters.
 *
 * @returns {Promise<void>} Sends a response containing the filtered projects, their associated data, pagination details, and the filters used.
 */
export async function searchProjects(request, reply) {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      creatorId,
      retailerId,
      brandId,
      campaign,
      search // text search for project names
    } = request.query

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Get brands accessible to this user
    const accessibleBrands = await authService.getUserAccessibleBrands(sessionUser.id)
    const accessibleBrandIds = accessibleBrands.map(brand => brand.id)

    // Convert page and limit to numbers and calculate offset
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Fetch more records to account for potential duplicates
    const fetchLimit = limitNum * 3

    // Build the base query with joins
    let baseQuery = db
      .select({
        id: projects.id,
        brandId: projects.brandId,
        name: projects.name,
        wmCampaigns: projects.wmCampaigns,
        status: projects.status,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        creator: {
          id: users.id,
          name: users.name,
          email: users.email
        },
        brand: {
          id: brands.id,
          name: brands.name
        },
        retailer: {
          id: retailers.id,
          name: retailers.name,
          slug: retailers.slug,
          logoUrl: retailers.logoUrl,
          url: retailers.url
        },
        credentialSet: {
          id: credentialSets.id,
          name: credentialSets.name,
          type: credentialSets.type,
          credentials: credentialSets.credentials,
          isActive: credentialSets.isActive,
          isShared: credentialSets.isShared
        }
      })
      .from(projects)
      .leftJoin(users, eq(projects.createdBy, users.id))
      .leftJoin(brands, eq(projects.brandId, brands.id)) // Only join brands when brandId exists
      .leftJoin(credentialSets, eq(brands.primaryCredentialId, credentialSets.id))
      .leftJoin(retailers, eq(projects.retailerId, retailers.id))

    // Build conditions array - start with brand access filter
    const conditions = [
      accessibleBrandIds.length > 0
        ? or(
            // Projects with brands the user has access to
            and(
              isNotNull(projects.brandId),
              inArray(projects.brandId, accessibleBrandIds)
            ),
            // Projects with null brandId that were created by this user
            and(
              isNull(projects.brandId),
              eq(projects.createdBy, sessionUser.id)
            )
          )
        : // If no accessible brands, only show user's own projects with null brandId
          and(
            isNull(projects.brandId),
            eq(projects.createdBy, sessionUser.id)
          )
    ]

    // Filter by status
    if (status) {
      conditions.push(eq(projects.status, status))
    }

    // Filter by creator
    if (creatorId) {
      conditions.push(eq(projects.createdBy, creatorId))
    }

    // Filter by brand
    if (brandId) {
      conditions.push(eq(projects.brandId, brandId))
    }

    // Filter by retailer (direct project retailer match)
    if (retailerId) {
      conditions.push(eq(projects.retailerId, retailerId))
    }

    // Filter by campaign (check if campaignId exists as a key in wmCampaigns JSONB)
    if (campaign) {
      // Use jsonb_exists function instead of ? operator to avoid parameter placeholder issues
      const jsonbKeyExists = sql`jsonb_exists(${projects.wmCampaigns}, ${campaign})`
      conditions.push(jsonbKeyExists)
    }

    // Complex search: project names OR keyword names
    if (search) {
      // Create a subquery to find projects that have generations with matching keywords
      const keywordSearchSubquery = db
        .select({ projectId: generations.projectId })
        .from(generations)
        .leftJoin(keywords, eq(generations.keywordId, keywords.id))
        .where(sql`${keywords.keyword} ILIKE ${`%${search}%`}`)

      conditions.push(
        or(
          // Search in project names
          sql`${projects.name} ILIKE ${`%${search}%`}`,
          // Search in keywords through generations
          sql`${projects.id} IN (${keywordSearchSubquery})`
        )
      )
    }

    // Apply conditions
    if (conditions.length > 0) {
      baseQuery = baseQuery.where(and(...conditions))
    }

    // Get paginated results
    const rawProjectsData = await baseQuery
      .groupBy(
        projects.id,
        projects.brandId,
        projects.name,
        projects.wmCampaigns,
        projects.createdAt,
        projects.updatedAt,
        users.id,
        users.name,
        users.email,
        brands.id,
        brands.name,
        credentialSets.id,
        credentialSets.name,
        credentialSets.type,
        credentialSets.credentials,
        credentialSets.isActive,
        credentialSets.isShared,
        retailers.id,
        retailers.name,
        retailers.slug,
        retailers.logoUrl,
        retailers.url
      )
      .orderBy(desc(projects.createdAt))
      .limit(fetchLimit)
      .offset(offset)

    // Deduplicate raw results (one entry per project)
    const uniqueProjectsMap = new Map()
    for (const proj of rawProjectsData) {
      if (!uniqueProjectsMap.has(proj.id)) {
        uniqueProjectsMap.set(proj.id, proj)
      }
    }
    const uniqueProjectsDataOrdered = Array.from(uniqueProjectsMap.values())

    // Apply pagination on unique results
    const paginatedUnique = uniqueProjectsDataOrdered.slice(offset, offset + limitNum)

    // Get all generations, creatives, and versions for the found projects (same as getAllUserProjects)
    const projectIds = paginatedUnique.map(project => project.id)

    let generationsData = []
    let creativesData = []
    let latestVersionsData = []

    if (projectIds.length > 0) {
      // Get generations with brand and keyword info
      generationsData = await db
        .select({
          id: generations.id,
          projectId: generations.projectId,
          productId: generations.productId,
          keywordId: generations.keywordId,
          brandId: generations.brandId,
          createdAt: generations.createdAt,
          updatedAt: generations.updatedAt,
          brand: {
            id: brands.id,
            name: brands.name,
            logoAssetUrl: brands.logoAssetUrl
          },
          keyword: {
            id: keywords.id,
            keyword: keywords.keyword
          }
        })
        .from(generations)
        .leftJoin(brands, eq(generations.brandId, brands.id))
        .leftJoin(keywords, eq(generations.keywordId, keywords.id))
        .where(inArray(generations.projectId, projectIds))

      // Get generation IDs for fetching related data
      const generationIds = generationsData.map(gen => gen.id)

      if (generationIds.length > 0) {
        // Get creatives for these generations
        creativesData = await db
          .select({
            id: creatives.id,
            generationId: creatives.generationId,
            projectId: creatives.projectId,
            wmCreativeId: creatives.wmCreativeId,
            brandId: creatives.brandId,
            userId: creatives.userId,
            previewUrls: creatives.previewUrls,
            previewStatus: creatives.previewStatus,
            folderId: creatives.folderId,
            status: creatives.status,
            keyword: creatives.keyword,
            adUnits: creatives.adUnits,
            reviewComments: creatives.reviewComments,
            createdAt: creatives.createdAt,
            updatedAt: creatives.updatedAt
          })
          .from(creatives)
          .where(inArray(creatives.generationId, generationIds))

        // Get latest generation versions for each generation
        const latestVersionsSubquery = db
          .select({
            generationId: generationVersions.generationId,
            maxVersion: max(generationVersions.version).as('maxVersion')
          })
          .from(generationVersions)
          .where(inArray(generationVersions.generationId, generationIds))
          .groupBy(generationVersions.generationId)
          .as('latestVersions')

        latestVersionsData = await db
          .select({
            id: generationVersions.id,
            generationId: generationVersions.generationId,
            sessionId: generationVersions.sessionId,
            unitFields: generationVersions.unitFields,
            hasCompleted: generationVersions.hasCompleted,
            createdBy: generationVersions.createdBy,
            version: generationVersions.version,
            adPreviews: generationVersions.adPreviews,
            previewImagePositions: generationVersions.previewImagePositions,
            applyToAll: generationVersions.applyToAll,
            createdAt: generationVersions.createdAt,
            updatedAt: generationVersions.updatedAt
          })
          .from(generationVersions)
          .innerJoin(
            latestVersionsSubquery,
            and(
              eq(generationVersions.generationId, latestVersionsSubquery.generationId),
              eq(generationVersions.version, latestVersionsSubquery.maxVersion)
            )
          )
      }
    }

    // Group creatives by generationId
    const creativesByGeneration = creativesData.reduce((acc, creative) => {
      if (!acc[creative.generationId]) {
        acc[creative.generationId] = []
      }
      acc[creative.generationId].push(creative)
      return acc
    }, {})

    // Group latest versions by generationId
    const versionsByGeneration = latestVersionsData.reduce((acc, version) => {
      acc[version.generationId] = version
      return acc
    }, {})

    // Group generations by projectId and add creatives and versions
    const generationsByProject = generationsData.reduce((acc, generation) => {
      if (!acc[generation.projectId]) {
        acc[generation.projectId] = []
      }

      const generationWithExtras = {
        id: generation.id,
        projectId: generation.projectId,
        productId: generation.productId,
        keywordId: generation.keywordId,
        brandId: generation.brandId,
        createdAt: generation.createdAt,
        updatedAt: generation.updatedAt,
        brandLogoAssetUrl: generation.brand?.logoAssetUrl || null,
        keywordName: generation.keyword?.keyword || null,
        creatives: creativesByGeneration[generation.id] || [],
        latestVersion: versionsByGeneration[generation.id] || null
      }

      acc[generation.projectId].push(generationWithExtras)
      return acc
    }, {})

    // Add generations to each project
    const projectsWithGenerations = paginatedUnique.map(project => {
      // Only keep brand data if it actually exists, don't fabricate it
      return {
        ...project,
        brand: project.brand && project.brand.id ? project.brand : null,
        credentialSet: project.credentialSet && project.credentialSet.id ? project.credentialSet : null,
        generations: generationsByProject[project.id] || []
      }
    })

    // Get total count with same filters (simplified - count unique projects)
    const totalCount = uniqueProjectsDataOrdered.length

    return reply.send({
      data: projectsWithGenerations,
      pagination: {
        total: totalCount,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(totalCount / limitNum)
      },
      filters: {
        status,
        creatorId,
        retailerId,
        brandId,
        campaign,
        search
      }
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to search projects' })
  }
}

/**
 * Advances a project to the specified workflow step, applying step-specific business logic such as creating generations or updating campaigns.
 *
 * For step 1, validates and sets the product and brand, then creates an initial generation. For step 3, validates keywords and creates a generation for each, with transactional cleanup on failure. For other steps, updates the current step and optionally campaign data.
 *
 * Returns the updated project and any created generations as applicable. Responds with appropriate error codes for validation failures, missing entities, or business rule violations.
 */
export async function advanceProjectStep(request, reply) {
  try {
    const { id } = request.params
    const { currentStep, productId, brandId, keywords: incomingKeywords, wmCampaigns } = request.body

    console.log('advanceProjectStep request.body', request.body)

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    if (currentStep === undefined) {
      return reply.code(400).send({ error: 'currentStep is required' })
    }

    // Check if project exists
    const [existingProject] = await db
      .select()
      .from(projects)
      .where(eq(projects.id, id))
      .limit(1)

    if (!existingProject) {
      return reply.code(404).send({ error: 'Project not found' })
    }

    // Handle step-specific validation and logic
    if (currentStep === 1) {
      console.log('currentStep === 1')
      // Step 1: Update project with product and brand info
      if (!productId || !brandId) {
        return reply.code(400).send({ error: 'productId and brandId are required for step 1' })
      }

      // Verify product exists
      const [product] = await db
        .select({ productId: products.id })
        .from(products)
        .where(eq(products.id, productId))
        .limit(1)

      if (!product) {
        return reply.code(404).send({ error: 'Product not found' })
      }

      // Verify brand exists
      const [brand] = await db
        .select({ id: brands.id })
        .from(brands)
        .where(eq(brands.id, brandId))
        .limit(1)

      if (!brand) {
        return reply.code(404).send({ error: 'Brand not found' })
      }

      // Update project
      const [updatedProject] = await db
        .update(projects)
        .set({
          currentStep,
          brandId, // Also update brandId since it's required for step 1
          updatedAt: new Date()
        })
        .where(eq(projects.id, id))
        .returning()

      return reply.send({
        project: updatedProject,
        // generation,
        message: 'Project advanced to step 1'
      })

    } else if (currentStep === 3) {
      console.log('currentStep === 3')

      const updateData = {
        currentStep,
        updatedAt: new Date()
      }

      if (brandId !== undefined) {
        updateData.brandId = brandId
      }

      if (productId !== undefined) {
        updateData.productId = productId
      }

      // Update project step
      const [updatedProject] = await db
        .update(projects)
        .set(updateData)
        .where(eq(projects.id, id))
        .returning()

        return reply.send({
          project: updatedProject,
          // generations: createdGenerations,
          message: `Project advanced to step 3`
        })
    } else {
      // For other steps, update currentStep and wmCampaigns if provided
      const updateData = {
        currentStep,
        updatedAt: new Date()
      }

      if (brandId !== undefined) {
        updateData.brandId = brandId
      }

      if (productId !== undefined) {
        updateData.productId = productId
      }

      if (wmCampaigns !== undefined) {
        updateData.wmCampaigns = wmCampaigns
      }

      console.log('updateData for non-step 1/3', updateData)

      const [updatedProject] = await db
        .update(projects)
        .set(updateData)
        .where(eq(projects.id, id))
        .returning()

      return reply.send({
        project: updatedProject,
        message: `Project advanced to step ${currentStep}`
      })
    }

  } catch (error) {
    request.log.error(error)

    // Handle specific error types
    if (error.message.includes('required') || error.message.includes('must be') || error.message.includes('must have')) {
      return reply.code(400).send({ error: error.message })
    }
    if (error.message.includes('not found')) {
      return reply.code(404).send({ error: error.message })
    }

    return reply.code(500).send({ error: 'Failed to advance project step' })
  }
}

/**
 * Retrieves the product associated with a project by querying its linked generations.
 *
 * Returns product details such as title, type, and summary if found; otherwise, responds with appropriate error codes if the project or product does not exist or if the user is unauthorized.
 */
export async function getProjectProduct(request, reply) {
  try {
    const { id } = request.params

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Log the project ID being requested
    request.log.info(`Fetching product for project with ID: ${id}`)

    // Check if project exists
    const [project] = await db
      .select({ id: projects.id, name: projects.name })
      .from(projects)
      .where(eq(projects.id, id))
      .limit(1)

    if (!project) {
      request.log.warn(`Project not found with ID: ${id}`)
      return reply.code(404).send({ error: 'Project not found' })
    }

    // Get the product associated with this project through generations
    const [productData] = await db
      .select({
        productTitle: products.productTitle,
        productType: products.productType,
        pdpSummary: products.pdpSummary
      })
      .from(products)
      .innerJoin(generations, eq(products.id, generations.productId))
      .where(eq(generations.projectId, id))
      .limit(1)

    if (!productData) {
      request.log.warn(`No product found for project with ID: ${id}`)
      return reply.code(404).send({ error: 'No product found for this project' })
    }

    request.log.info(`Found product: ${productData.productTitle} for project: ${project.name}`)

    return reply.send(productData)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch project product' })
  }
}

/**
 * Retrieves comprehensive data for a project, including its brand, associated product, keywords, campaigns, and ad groups.
 *
 * Returns an object containing project details, brand information (if available), product details (if available), a list of unique keywords, and extracted campaigns and ad groups from the project's campaign data.
 */
export async function getProjectData(request, reply) {
  try {
    const { id } = request.params

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Log the project ID being requested
    request.log.info(`Fetching comprehensive data for project with ID: ${id}`)

    // Get project with brand info
    const [projectData] = await db
      .select({
        project: {
          id: projects.id,
          name: projects.name,
          currentStep: projects.currentStep,
          wmCampaigns: projects.wmCampaigns
        },
        brand: {
          id: brands.id,
          name: brands.name
        }
      })
      .from(projects)
      .leftJoin(brands, eq(projects.brandId, brands.id))
      .where(eq(projects.id, id))
      .limit(1)

    if (!projectData) {
      request.log.warn(`Project not found with ID: ${id}`)
      return reply.code(404).send({ error: 'Project not found' })
    }

    request.log.info(`Found project: ${projectData.project.name}`)

    // Get product info through generations (should be the same for all generations in a project)
    const [productData] = await db
      .select({
        id: products.id,
        productTitle: products.productTitle,
        productType: products.productType,
        pdpSummary: products.pdpSummary
      })
      .from(products)
      .innerJoin(generations, eq(products.id, generations.productId))
      .where(eq(generations.projectId, id))
      .limit(1)

    // Get unique keywords through generations with all keyword data
    const keywordsData = await db
      .select({
        id: keywords.id,
        keyword: keywords.keyword,
        accountId: keywords.accountId,
        include: keywords.include,
        exclude: keywords.exclude,
        createdAt: keywords.createdAt,
        updatedAt: keywords.updatedAt
      })
      .from(keywords)
      .innerJoin(generations, eq(keywords.id, generations.keywordId))
      .where(eq(generations.projectId, id))
      .groupBy(keywords.id, keywords.keyword, keywords.accountId, keywords.include, keywords.exclude, keywords.createdAt, keywords.updatedAt)
      .orderBy(keywords.keyword)

    // Extract campaigns and adGroups from wmCampaigns field on the project
    const campaigns = []
    const adGroups = []

    const wmCampaigns = projectData.project.wmCampaigns || {}

    Object.keys(wmCampaigns).forEach(campaignId => {
      const campaign = wmCampaigns[campaignId]

      if (campaign) {
        campaigns.push({
          id: campaignId,
          name: campaign.name || campaignId
        })

        // Extract adGroups from this campaign
        if (campaign.adGroups && typeof campaign.adGroups === 'object') {
          Object.keys(campaign.adGroups).forEach(adGroupId => {
            const adGroupName = campaign.adGroups[adGroupId]
            adGroups.push({
              id: adGroupId,
              name: adGroupName || adGroupId,
              campaignId: campaignId
            })
          })
        }
      }
    })

    // Build response with proper null handling
    const response = {
      project: projectData.project,
      brand: projectData.brand && projectData.brand.id ? projectData.brand : null,
      product: productData || null,
      keywords: keywordsData || [],
      campaigns: campaigns,
      adGroups: adGroups
    }

    return reply.send(response)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch project data' })
  }
}

/**
 * Retrieves comprehensive review data for all keywords in a specified project, including associated generations, creatives, generation versions, proofs, reviewers, comments, and campaign/ad group selections.
 *
 * The response includes review status, user selection flags, ad unit data, proof configuration, and aggregated review comments for each keyword. Returns a structured JSON object with all relevant review information for the project.
 *
 * Returns a 401 error if the user session is missing, or a 404 error if the project does not exist.
 */
export async function getProjectReviewData(request, reply) {
  try {
    const { id } = request.params

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Log the project ID being requested
    request.log.info(`Fetching review data for project with ID: ${id}`)

    // Get project with brand info and credential set
    const [projectData] = await db
      .select({
        id: projects.id,
        name: projects.name,
        currentStep: projects.currentStep,
        wmCampaigns: projects.wmCampaigns,
        brandId: projects.brandId,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        brand: {
          id: brands.id,
          name: brands.name,
          logoAssetUrl: brands.logoAssetUrl,
          primaryCredentialId: brands.primaryCredentialId
        },
        credentialSet: {
          id: credentialSets.id,
          name: credentialSets.name,
          type: credentialSets.type,
          credentials: credentialSets.credentials,
          isActive: credentialSets.isActive,
          isShared: credentialSets.isShared
        }
      })
      .from(projects)
      .leftJoin(brands, eq(projects.brandId, brands.id))
      .leftJoin(credentialSets, eq(brands.primaryCredentialId, credentialSets.id))
      .where(eq(projects.id, id))
      .limit(1)

    if (!projectData) {
      request.log.warn(`Project not found with ID: ${id}`)
      return reply.code(404).send({ error: 'Project not found' })
    }

    // Get product info through generations (should be the same for all generations in a project)
    const [productData] = await db
      .select({
        id: products.id,
        productTitle: products.productTitle,
        productType: products.productType,
        pdpSummary: products.pdpSummary
      })
      .from(products)
      .innerJoin(generations, eq(products.id, generations.productId))
      .where(eq(generations.projectId, id))
      .limit(1)

    // Get all generations for this project with keyword data
    const generationsData = await db
      .select({
        generationId: generations.id,
        keywordId: generations.keywordId,
        keyword: {
          id: keywords.id,
          text: keywords.keyword,
          include: keywords.include,
          exclude: keywords.exclude
        }
      })
      .from(generations)
      .leftJoin(keywords, eq(generations.keywordId, keywords.id))
      .where(eq(generations.projectId, id))

    // Get only the latest generation version for each generation
    const generationIds = generationsData.map(g => g.generationId)
    let generationVersionsData = []

    if (generationIds.length > 0) {
      // Get the latest version for each generation using a subquery
      const latestVersionsSubquery = db
        .select({
          generationId: generationVersions.generationId,
          maxVersion: max(generationVersions.version).as('maxVersion')
        })
        .from(generationVersions)
        .where(inArray(generationVersions.generationId, generationIds))
        .groupBy(generationVersions.generationId)
        .as('latestVersions')

      generationVersionsData = await db
        .select({
          id: generationVersions.id,
          generationId: generationVersions.generationId,
          sessionId: generationVersions.sessionId,
          unitFields: generationVersions.unitFields,
          hasCompleted: generationVersions.hasCompleted,
          createdBy: generationVersions.createdBy,
          version: generationVersions.version,
          adPreviews: generationVersions.adPreviews,
          previewImagePositions: generationVersions.previewImagePositions,
          applyToAll: generationVersions.applyToAll,
          createdAt: generationVersions.createdAt,
          updatedAt: generationVersions.updatedAt
        })
        .from(generationVersions)
        .innerJoin(
          latestVersionsSubquery,
          and(
            eq(generationVersions.generationId, latestVersionsSubquery.generationId),
            eq(generationVersions.version, latestVersionsSubquery.maxVersion)
          )
        )
    }

    // Get creatives for these generations
    let creativesData = []
    if (generationIds.length > 0) {
      creativesData = await db
        .select({
          id: creatives.id,
          generationId: creatives.generationId,
          projectId: creatives.projectId,
          adUnits: creatives.adUnits,
          reviewComments: creatives.reviewComments,
          status: creatives.status,
          keyword: creatives.keyword,
          wmCreativeId: creatives.wmCreativeId,
          createdAt: creatives.createdAt,
          updatedAt: creatives.updatedAt
        })
        .from(creatives)
        .where(inArray(creatives.generationId, generationIds))
    }



    // Get proofs and reviewers data
    const versionIds = generationVersionsData.map(v => v.id)
    let proofsData = []
    let reviewersData = []
    let proofCommentsData = []

    if (versionIds.length > 0) {
      // Get proofs
      proofsData = await db
        .select({
          id: proofs.id,
          generationVersionId: proofs.generationVersionId,
          title: proofs.title,
          emailDescription: proofs.emailDescription,
          adUnitIds: proofs.adUnitIds,
          reviewers: proofs.reviewers,
          createdAt: proofs.createdAt,
          updatedAt: proofs.updatedAt
        })
        .from(proofs)
        .where(inArray(proofs.generationVersionId, versionIds))

      // Get reviewers
      const proofIds = proofsData.map(p => p.id)
      if (proofIds.length > 0) {
        reviewersData = await db
          .select({
            id: reviewers.id,
            proofId: reviewers.proofId,
            name: reviewers.name,
            email: reviewers.email,
            userId: reviewers.userId,
            isExternal: reviewers.isExternal,
            status: reviewers.status,
            requestedBy: reviewers.requestedBy,
            lastRequestedAt: reviewers.lastRequestedAt,
            createdAt: reviewers.createdAt,
            updatedAt: reviewers.updatedAt
          })
          .from(reviewers)
          .where(inArray(reviewers.proofId, proofIds))

        // Get proof comments
        proofCommentsData = await db
          .select({
            id: proofComments.id,
            proofId: proofComments.proofId,
            reviewerId: proofComments.reviewerId,
            comment: proofComments.comment,
            replyToId: proofComments.replyToId,
            createdAt: proofComments.createdAt,
            updatedAt: proofComments.updatedAt
          })
          .from(proofComments)
          .where(inArray(proofComments.proofId, proofIds))
      }
    }

    // Get internal users (from the users table)
    const internalUsersData = await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email
      })
      .from(users)

    // Get external reviewers
    const externalReviewersData = await db
      .select({
        id: externalReviewers.id,
        name: externalReviewers.name,
        email: externalReviewers.email,
        jobTitle: externalReviewers.jobTitle,
        accountId: externalReviewers.accountId,
        retailerId: externalReviewers.retailerId,
        createdAt: externalReviewers.createdAt,
        updatedAt: externalReviewers.updatedAt
      })
      .from(externalReviewers)

    // Extract campaign and ad group info from wmCampaigns
    const wmCampaigns = projectData.wmCampaigns || {}
    let selectedCampaignId = null
    let selectedAdGroupId = null

    // Extract campaigns and adGroups for the response
    const campaigns = []
    const adGroups = []

    Object.keys(wmCampaigns).forEach(campaignId => {
      const campaign = wmCampaigns[campaignId]

      if (campaign) {
        campaigns.push({
          id: campaignId,
          name: campaign.name || campaignId
        })

        // Extract adGroups from this campaign
        if (campaign.adGroups && typeof campaign.adGroups === 'object') {
          Object.keys(campaign.adGroups).forEach(adGroupId => {
            const adGroupName = campaign.adGroups[adGroupId]
            adGroups.push({
              id: adGroupId,
              name: adGroupName || adGroupId,
              campaignId: campaignId
            })
          })
        }
      }
    })

    // Get the first campaign and ad group as selected (you may want to modify this logic)
    const campaignIds = Object.keys(wmCampaigns)
    if (campaignIds.length > 0) {
      selectedCampaignId = campaignIds[0]
      const campaign = wmCampaigns[selectedCampaignId]
      if (campaign && campaign.adGroups) {
        const adGroupIds = Object.keys(campaign.adGroups)
        if (adGroupIds.length > 0) {
          selectedAdGroupId = adGroupIds[0]
        }
      }
    }

    // Build keyword review data structure
    const keywordReviewData = {}
    const selectedKeywords = []

    // Group data by keyword
    const keywordMap = new Map()

    for (const gen of generationsData) {
      if (gen.keyword && gen.keyword.id) {
        const keywordText = gen.keyword.text

        if (!keywordMap.has(keywordText)) {
          keywordMap.set(keywordText, {
            keyword: gen.keyword,
            generationId: gen.generationId,
            versions: [],
            creatives: [],
            proofs: [],
            reviewers: [],
            comments: []
          })
        }

        const keywordData = keywordMap.get(keywordText)

        // Add versions for this generation
        const versions = generationVersionsData.filter(v => v.generationId === gen.generationId)
        keywordData.versions.push(...versions)

        // Add creatives for this generation
        const creatives = creativesData.filter(c => c.generationId === gen.generationId)
        keywordData.creatives.push(...creatives)

        // Add proofs for versions of this generation
        const versionIdsForGen = versions.map(v => v.id)
        const proofs = proofsData.filter(p => versionIdsForGen.includes(p.generationVersionId))
        keywordData.proofs.push(...proofs)

        // Add reviewers for these proofs
        const proofIdsForGen = proofs.map(p => p.id)
        const reviewers = reviewersData.filter(r => proofIdsForGen.includes(r.proofId))
        keywordData.reviewers.push(...reviewers)

        // Add comments for these proofs
        const comments = proofCommentsData.filter(c => proofIdsForGen.includes(c.proofId))
        keywordData.comments.push(...comments)
      }
    }

    // Build the response structure
    for (const [keywordText, data] of keywordMap) {
      const keyword = data.keyword

      // Add to selected keywords
      selectedKeywords.push({
        id: keyword.id,
        text: keyword.text,
        include: keyword.include || {
          word: null,
          tone: null,
          considerations: null
        },
        exclude: keyword.exclude || {
          word: null,
          tone: null,
          considerations: null
        }
      })

      // Determine review status
      let reviewStatus = 'setup'
      if (data.proofs.length > 0) {
        reviewStatus = 'inReview'
        // Check if all proofs are completed (you may need to add completion logic)
        const hasCompletedProofs = data.proofs.some(p => p.status === 'completed')
        if (hasCompletedProofs) {
          reviewStatus = 'completed'
        }
      }

      // Build internal users list - include actual reviewer status
      const internalUsers = internalUsersData.map(user => {
        // Find the reviewer record for this user if they are a reviewer for this keyword's proofs
        const reviewerRecord = data.reviewers.find(r => r.userId === user.id && !r.isExternal)

        return {
          id: user.id,
          name: user.name,
          role: 'Internal User', // You may want to get this from a roles table
          type: 'Internal',
          selected: data.proofs.some(proof =>
            proof.reviewers &&
            proof.reviewers.internal &&
            proof.reviewers.internal.includes(user.id)
          ),
          email: user.email,
          status: reviewerRecord ? reviewerRecord.status : 'not_assigned', // Include reviewer status
          reviewerId: reviewerRecord ? reviewerRecord.id : null,
          lastRequestedAt: reviewerRecord ? reviewerRecord.lastRequestedAt : null
        }
      })

      // Build external users list - include actual reviewer status
      const externalUsers = externalReviewersData.map(user => {
        // Find the reviewer record for this user if they are a reviewer for this keyword's proofs
        const reviewerRecord = data.reviewers.find(r =>
          (r.userId === user.id || r.email === user.email) && r.isExternal
        )

        return {
          id: user.id,
          name: user.name,
          role: user.jobTitle || 'External Reviewer',
          type: 'External',
          selected: data.proofs.some(proof =>
            proof.reviewers &&
            proof.reviewers.external &&
            proof.reviewers.external.includes(user.id)
          ),
          email: user.email,
          status: reviewerRecord ? reviewerRecord.status : 'not_assigned', // Include reviewer status
          reviewerId: reviewerRecord ? reviewerRecord.id : null,
          lastRequestedAt: reviewerRecord ? reviewerRecord.lastRequestedAt : null
        }
      })

       // Get ad units from generation versions unitFields
       let adUnits = null
       // Use the versions we already filtered for this keyword's generation
       if (data.versions && data.versions.length > 0) {
         const latestVersion = data.versions[0] // Should be the latest since we only get one per generation

         if (latestVersion && latestVersion.unitFields) {
           // Ensure proper serialization by deep cloning the object
           try {
             // Force conversion to plain object
             const unitFieldsStr = JSON.stringify(latestVersion.unitFields)
             adUnits = JSON.parse(unitFieldsStr)
            //  console.log('  adUnits after JSON parse/stringify:', adUnits)
            //  console.log('  adUnits is plain object:', Object.getPrototypeOf(adUnits) === Object.prototype)
            //  console.log('  adUnits constructor:', adUnits.constructor.name)
           } catch (error) {
             console.log('  Error serializing unitFields:', error)
             // Fallback: manually construct a plain object
             adUnits = Object.assign({}, latestVersion.unitFields)
           }
         }
       } else {
         console.log(`No versions found for ${keywordText}`)
       }

      // Get proof config from the latest proof
      const latestProof = data.proofs.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0]
      const proofConfig = latestProof ? {
        title: latestProof.title,
        emailDescription: latestProof.emailDescription || '',
        password: '', // Don't expose actual password
        expiration: null // Add expiration logic if needed
      } : {
        title: `${keywordText} Review`,
        emailDescription: '',
        password: '',
        expiration: null
      }

      // Build review comments
      const reviewComments = []
      for (const comment of data.comments) {
        const reviewer = data.reviewers.find(r => r.id === comment.reviewerId)
        reviewComments.push({
          id: comment.id,
          user: reviewer ? reviewer.name : 'Unknown User',
          message: comment.comment,
          timestamp: comment.createdAt,
          resolved: false, // You may want to add a resolved field to the schema
          asset: null, // Add asset reference if needed
          adUnit: null // Add ad unit reference if needed
        })
      }

      // Add creative review comments if they exist
      for (const creative of data.creatives) {
        if (creative.reviewComments && Array.isArray(creative.reviewComments)) {
          for (const comment of creative.reviewComments) {
            reviewComments.push({
              id: comment.id || `creative-${creative.id}-${reviewComments.length}`,
              user: comment.user || 'Unknown User',
              message: comment.message || comment.comment,
              timestamp: comment.timestamp || creative.updatedAt,
              resolved: comment.resolved || false,
              asset: comment.asset || null,
              adUnit: comment.adUnit || null
            })
          }
        }
      }

      keywordReviewData[keywordText] = {
        id: keyword.id,
        text: keyword.text,
        generationId: data.generationId,
        generationVersionId: data.versions.length > 0 ? data.versions[0].id : null,
        reviewStatus,
        internalUsers,
        externalUsers,
        adUnits,
        proofConfig,
        reviewComments,
        proofs: data.proofs.map(proof => ({
          id: proof.id,
          generationVersionId: proof.generationVersionId,
          title: proof.title,
          emailDescription: proof.emailDescription,
          adUnitIds: proof.adUnitIds,
          reviewers: proof.reviewers,
          createdAt: proof.createdAt,
          updatedAt: proof.updatedAt
        }))
      }
    }

    // Get the latest session ID from generation versions
    const latestVersion = generationVersionsData.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0]
    const sessionId = latestVersion ? latestVersion.sessionId : null

    // Build the final response
    const response = {
      success: true,
      data: {
        sessionId,
        projectName: projectData.name,
        currentStep: projectData.currentStep?.toString() || '0',
        selectedBrandId: projectData.brandId || null,
        selectedProductId: productData ? productData.id : null,
        selectedCampaignId: selectedCampaignId,
        selectedAdGroupId: selectedAdGroupId,
        selectedKeywords,
        keywordReviewData,
        campaigns,
        adGroups,
        brand: projectData.brand && projectData.brand.id ? projectData.brand : null,
        product: productData || null,
        credentialSet: projectData.credentialSet && projectData.credentialSet.id ? projectData.credentialSet : null,
        createdAt: projectData.createdAt,
        updatedAt: projectData.updatedAt
      }
    }

    // Try manual JSON serialization to see if that helps
    try {
      const jsonResponse = JSON.stringify(response)

      // Parse it back to check
      const parsed = JSON.parse(jsonResponse)
    } catch (error) {
      console.log('Error in manual JSON serialization:', error)
    }

    // Try sending as raw JSON to bypass any Fastify serialization issues
    return reply
      .type('application/json')
      .send(JSON.stringify(response))
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch project review data' })
  }
}

/**
 * Helper function to get detailed information about a project's generations
 * @param {string} projectId - The project ID to analyze
 * @returns {Promise<Object>} - Object containing generation details
 */
async function getProjectGenerationDetails(projectId) {
  try {
    // Get all generations with their versions
    const generationsWithVersions = await db
      .select({
        generationId: generations.id,
        productId: generations.productId,
        keywordId: generations.keywordId,
        brandId: generations.brandId,
        generationCreatedAt: generations.createdAt,
        versionId: generationVersions.id,
        version: generationVersions.version,
        sessionId: generationVersions.sessionId,
        hasCompleted: generationVersions.hasCompleted,
        versionCreatedAt: generationVersions.createdAt
      })
      .from(generations)
      .leftJoin(generationVersions, eq(generations.id, generationVersions.generationId))
      .where(eq(generations.projectId, projectId))
      .orderBy(generations.createdAt, generationVersions.version)

    // Group by generation
    const generationMap = new Map()
    for (const row of generationsWithVersions) {
      if (!generationMap.has(row.generationId)) {
        generationMap.set(row.generationId, {
          id: row.generationId,
          productId: row.productId,
          keywordId: row.keywordId,
          brandId: row.brandId,
          createdAt: row.generationCreatedAt,
          versions: []
        })
      }

      if (row.versionId) {
        generationMap.get(row.generationId).versions.push({
          id: row.versionId,
          version: row.version,
          sessionId: row.sessionId,
          hasCompleted: row.hasCompleted,
          createdAt: row.versionCreatedAt
        })
      }
    }

    return {
      totalGenerations: generationMap.size,
      generations: Array.from(generationMap.values()),
      totalVersions: generationsWithVersions.filter(row => row.versionId).length
    }
  } catch (error) {
    console.error('Error getting project generation details:', error)
    return { totalGenerations: 0, generations: [], totalVersions: 0, error: error.message }
  }
}

/**
 * Duplicates an existing project with all its generations and latest generation versions.
 *
 * Creates a new project with the specified name and copies all generations from the source project.
 * For each generation, creates a new generation version with the same data as the latest version.
 * Sets the new project's current step to 3.
 *
 * Returns the newly created project along with information about the duplicated generations.
 */
export async function duplicateProject(request, reply) {
  try {
    const { id } = request.params
    const { name } = request.body

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    if (!name) {
      return reply.code(400).send({ error: 'Project name is required' })
    }

    request.log.info(`Starting project duplication for project ID: ${id}`)

    // Get the original project
    const [originalProject] = await db
      .select({
        id: projects.id,
        brandId: projects.brandId,
        retailerId: projects.retailerId,
        name: projects.name,
        wmCampaigns: projects.wmCampaigns,
        status: projects.status,
        currentStep: projects.currentStep
      })
      .from(projects)
      .where(eq(projects.id, id))
      .limit(1)

    if (!originalProject) {
      return reply.code(404).send({ error: 'Original project not found' })
    }

    request.log.info(`Found original project: ${originalProject.name}`)

    // Get detailed information about the original project's generations
    const originalProjectDetails = await getProjectGenerationDetails(id)
    request.log.info(`Original project has ${originalProjectDetails.totalGenerations} generations with ${originalProjectDetails.totalVersions} total versions`)

    if (originalProjectDetails.error) {
      request.log.error(`Error getting original project details: ${originalProjectDetails.error}`)
    }

    // Create the new project
    const [newProject] = await db
      .insert(projects)
      .values({
        brandId: originalProject.brandId,
        retailerId: originalProject.retailerId,
        name,
        wmCampaigns: {},
        status: originalProject.status,
        currentStep: originalProject.currentStep < 3 ? originalProject.currentStep : 3, // Set to step 3 as requested
        createdBy: sessionUser.id,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    request.log.info(`Created new project: ${newProject.name} with ID: ${newProject.id}`)

    // Get all generations from the original project
    const originalGenerations = await db
      .select({
        id: generations.id,
        productId: generations.productId,
        keywordId: generations.keywordId,
        brandId: generations.brandId
      })
      .from(generations)
      .where(eq(generations.projectId, id))

    request.log.info(`Found ${originalGenerations.length} generations in original project`)

    if (originalGenerations.length === 0) {
      return reply.send({
        project: newProject,
        duplicatedGenerations: 0,
        message: 'Project duplicated successfully (no generations to copy)'
      })
    }

    // Log details about each generation
    originalGenerations.forEach((gen, index) => {
      request.log.info(`Generation ${index + 1}: ID=${gen.id}, productId=${gen.productId}, keywordId=${gen.keywordId}, brandId=${gen.brandId}`)
    })

    // Get the latest generation version for each original generation
    const originalGenerationIds = originalGenerations.map(g => g.id)

    const latestVersionsSubquery = db
      .select({
        generationId: generationVersions.generationId,
        maxVersion: max(generationVersions.version).as('maxVersion')
      })
      .from(generationVersions)
      .where(inArray(generationVersions.generationId, originalGenerationIds))
      .groupBy(generationVersions.generationId)
      .as('latestVersions')

    const latestVersionsData = await db
      .select({
        id: generationVersions.id,
        generationId: generationVersions.generationId,
        sessionId: generationVersions.sessionId,
        unitFields: generationVersions.unitFields,
        hasCompleted: generationVersions.hasCompleted,
        version: generationVersions.version,
        adPreviews: generationVersions.adPreviews,
        previewImagePositions: generationVersions.previewImagePositions,
        applyToAll: generationVersions.applyToAll
      })
      .from(generationVersions)
      .innerJoin(
        latestVersionsSubquery,
        and(
          eq(generationVersions.generationId, latestVersionsSubquery.generationId),
          eq(generationVersions.version, latestVersionsSubquery.maxVersion)
        )
      )

    request.log.info(`Found ${latestVersionsData.length} generation versions to copy`)

    // Create new generations and their versions
    const newGenerations = []
    const newGenerationVersions = []
    const errors = []

    for (let i = 0; i < originalGenerations.length; i++) {
      const originalGen = originalGenerations[i]

      try {
        request.log.info(`Processing generation ${i + 1}/${originalGenerations.length}: ${originalGen.id}`)

        // Validate required fields before creation
        if (!originalGen.productId) {
          throw new Error(`Generation ${originalGen.id} missing productId`)
        }
        if (!originalGen.brandId) {
          throw new Error(`Generation ${originalGen.id} missing brandId`)
        }

        // Create new generation
        const [newGeneration] = await db
          .insert(generations)
          .values({
            productId: originalGen.productId,
            keywordId: originalGen.keywordId,
            projectId: newProject.id,
            brandId: originalGen.brandId,
            createdAt: new Date(),
            updatedAt: new Date()
          })
          .returning()

        request.log.info(`Created new generation: ${newGeneration.id}`)
        newGenerations.push(newGeneration)

        // Find the latest version for this generation
        const latestVersion = latestVersionsData.find(v => v.generationId === originalGen.id)

        if (latestVersion) {
          request.log.info(`Found latest version for generation ${originalGen.id}: version ${latestVersion.version}`)

          // Create new generation version with the same data
          const [newGenerationVersion] = await db
            .insert(generationVersions)
            .values({
              generationId: newGeneration.id,
              sessionId: latestVersion.sessionId,
              unitFields: latestVersion.unitFields,
              hasCompleted: latestVersion.hasCompleted,
              createdBy: sessionUser.id,
              version: 1, // Start with version 1 for the new generation
              adPreviews: latestVersion.adPreviews,
              previewImagePositions: latestVersion.previewImagePositions,
              applyToAll: latestVersion.applyToAll,
              createdAt: new Date(),
              updatedAt: new Date()
            })
            .returning()

          request.log.info(`Created new generation version: ${newGenerationVersion.id}`)
          newGenerationVersions.push(newGenerationVersion)
        } else {
          request.log.warn(`No latest version found for generation ${originalGen.id}`)
        }

      } catch (genError) {
        request.log.error(`Error processing generation ${originalGen.id}:`, genError)
        errors.push({
          generationId: originalGen.id,
          error: genError.message
        })
      }
    }

    request.log.info(`Duplication completed: ${newGenerations.length} generations, ${newGenerationVersions.length} versions created`)

    if (errors.length > 0) {
      request.log.error(`Duplication completed with ${errors.length} errors:`, errors)
    }

    return reply.code(201).send({
      project: newProject,
      duplicatedGenerations: newGenerations.length,
      duplicatedVersions: newGenerationVersions.length,
      errors: errors.length > 0 ? errors : undefined,
      message: `Project duplicated successfully with ${newGenerations.length} generations and ${newGenerationVersions.length} generation versions${errors.length > 0 ? ` (${errors.length} errors encountered)` : ''}`
    })

  } catch (error) {
    request.log.error('Fatal error in duplicateProject:', error)
    return reply.code(500).send({
      error: 'Failed to duplicate project',
      details: error.message
    })
  }
}

/**
 * Debug endpoint to inspect a project's generations and versions
 * This helps diagnose issues with project duplication
 */
export async function debugProjectGenerations(request, reply) {
  try {
    const { id } = request.params

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Get the project
    const [project] = await db
      .select({
        id: projects.id,
        name: projects.name,
        brandId: projects.brandId,
        retailerId: projects.retailerId,
        currentStep: projects.currentStep,
        createdAt: projects.createdAt
      })
      .from(projects)
      .where(eq(projects.id, id))
      .limit(1)

    if (!project) {
      return reply.code(404).send({ error: 'Project not found' })
    }

    // Get detailed generation information
    const generationDetails = await getProjectGenerationDetails(id)

    // Get raw generation data for comparison
    const rawGenerations = await db
      .select({
        id: generations.id,
        productId: generations.productId,
        keywordId: generations.keywordId,
        brandId: generations.brandId,
        projectId: generations.projectId,
        createdAt: generations.createdAt,
        updatedAt: generations.updatedAt
      })
      .from(generations)
      .where(eq(generations.projectId, id))
      .orderBy(generations.createdAt)

    // Get raw generation versions
    const rawVersions = await db
      .select({
        id: generationVersions.id,
        generationId: generationVersions.generationId,
        version: generationVersions.version,
        sessionId: generationVersions.sessionId,
        hasCompleted: generationVersions.hasCompleted,
        createdBy: generationVersions.createdBy,
        createdAt: generationVersions.createdAt
      })
      .from(generationVersions)
      .innerJoin(generations, eq(generationVersions.generationId, generations.id))
      .where(eq(generations.projectId, id))
      .orderBy(generationVersions.createdAt)

    return reply.send({
      project,
      summary: {
        totalGenerations: generationDetails.totalGenerations,
        totalVersions: generationDetails.totalVersions,
        hasError: !!generationDetails.error
      },
      detailedGenerations: generationDetails.generations,
      rawGenerations,
      rawVersions,
      error: generationDetails.error
    })

  } catch (error) {
    request.log.error('Error in debugProjectGenerations:', error)
    return reply.code(500).send({
      error: 'Failed to debug project generations',
      details: error.message
    })
  }
}

/**
 * Gets all generations for a project and their associated creatives.
 * 
 * Returns generations with their creative data, including brand and user information.
 * Useful for getting a complete view of all content generated for a project.
 */
export async function getProjectGenerationsWithCreatives(request, reply) {
  try {
    const { id } = request.params;

    // Get user info from session
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Log the project ID being requested
    request.log.info(`Fetching generations and creatives for project with ID: ${id}`);

    // Verify project exists
    const [project] = await db
      .select({
        id: projects.id,
        name: projects.name,
        brandId: projects.brandId,
        wmCampaigns: projects.wmCampaigns
      })
      .from(projects)
      .where(eq(projects.id, id))
      .limit(1);

    if (!project) {
      return reply.code(404).send({ error: 'Project not found' });
    }

    // Get all generations for the project with their creatives
    const generationsWithCreatives = await db
      .select({
        generation: {
          id: generations.id,
          productId: generations.productId,
          keywordId: generations.keywordId,
          projectId: generations.projectId,
          brandId: generations.brandId,
          generationVersionId: generations.generationVersionId,
          createdAt: generations.createdAt,
          updatedAt: generations.updatedAt
        },
        creative: {
          id: creatives.id,
          projectId: creatives.projectId,
          wmCreativeId: creatives.wmCreativeId,
          brandId: creatives.brandId,
          userId: creatives.userId,
          generationId: creatives.generationId,
          previewUrls: creatives.previewUrls,
          previewStatus: creatives.previewStatus,
          folderId: creatives.folderId,
          status: creatives.status,
          keyword: creatives.keyword,
          adUnits: creatives.adUnits,
          reviewComments: creatives.reviewComments,
          createdAt: creatives.createdAt,
          updatedAt: creatives.updatedAt
        },
        brand: {
          id: brands.id,
          name: brands.name,
          logoAssetUrl: brands.logoAssetUrl,
          primaryCredentialId: brands.primaryCredentialId
        },
        user: {
          id: users.id,
          name: users.name,
          email: users.email
        },
        keyword: {
          id: keywords.id,
          keyword: keywords.keyword
        },
        product: {
          id: products.id,
          productTitle: products.productTitle,
          productType: products.productType
        },
        credentialSet: {
          id: credentialSets.id,
          name: credentialSets.name,
          type: credentialSets.type,
          credentials: credentialSets.credentials,
          isActive: credentialSets.isActive,
          isShared: credentialSets.isShared
        }
      })
      .from(generations)
      .leftJoin(creatives, eq(generations.id, creatives.generationId))
      .leftJoin(brands, eq(generations.brandId, brands.id))
      .leftJoin(credentialSets, eq(brands.primaryCredentialId, credentialSets.id))
      .leftJoin(users, eq(creatives.userId, users.id))
      .leftJoin(keywords, eq(generations.keywordId, keywords.id))
      .leftJoin(products, eq(generations.productId, products.id))
      .where(eq(generations.projectId, id))
      .orderBy(generations.createdAt, creatives.createdAt);

    // Group the results by generation
    const generationsMap = new Map();
    let credentialSet = null;
    
    for (const row of generationsWithCreatives) {
      const generationId = row.generation.id;
      
      // Capture credential set from the first row (should be the same for all)
      if (!credentialSet && row.credentialSet && row.credentialSet.id) {
        credentialSet = row.credentialSet;
      }
      
      if (!generationsMap.has(generationId)) {
        generationsMap.set(generationId, {
          ...row.generation,
          brandLogoAssetUrl: row.brand?.logoAssetUrl || null,
          keywordName: row.keyword?.keyword || null,
          brand: row.brand,
          keyword: row.keyword,
          product: row.product,
          creatives: []
        });
      }
      
      // Add creative to the generation if it exists
      if (row.creative && row.creative.id) {
        generationsMap.get(generationId).creatives.push({
          ...row.creative,
          user: row.user
        });
      }
    }

    // Convert map to array
    const result = Array.from(generationsMap.values());

    request.log.info(`Found ${result.length} generations with ${result.reduce((total, gen) => total + gen.creatives.length, 0)} total creatives for project: ${project.name}`);

    return reply.send({
      project: {
        id: project.id,
        name: project.name,
        brandId: project.brandId,
        wmCampaigns: project.wmCampaigns
      },
      credentialSet: credentialSet,
      generations: result,
      summary: {
        totalGenerations: result.length,
        totalCreatives: result.reduce((total, gen) => total + gen.creatives.length, 0)
      }
    });

  } catch (error) {
    request.log.error(error);
    return reply.code(500).send({ error: 'Failed to fetch project generations and creatives' });
  }
}

