import {
  getProjectsByBrandId,
  getProjectById,
  createProject,
  updateProject,
  deleteProject,
  getAllUserProjects,
  getProjectFilterValues,
  searchProjects,
  advanceProjectStep,
  getProjectProduct,
  getProjectData,
  getProjectReviewData,
  duplicateProject,
  debugProjectGenerations,
  getProjectGenerationsWithCreatives
} from './creativeProjects.controllers.js'
import { creativeProjectSchema } from './creativeProjects.schemas.js'

/**
 * Registers authenticated HTTP routes for managing creative projects on a Fastify server.
 *
 * Sets up endpoints for creating, retrieving, updating, advancing, and deleting creative projects, as well as retrieving related data such as filters, products, and reviews. All routes require authentication via the `verifyAuth` decorator.
 *
 * Throws an error if the authentication decorator is not registered on the Fastify instance.
 */
export default async function creativeProjectRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all projects for the current user across all brands
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.getAllUserProjects,
    handler: getAllUserProjects
  })

  // Get filter values for projects
  fastify.get('/filters', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.getFilterValues,
    handler: getProjectFilterValues
  })

  // Search projects with filters
  fastify.get('/search', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.search,
    handler: searchProjects
  })

  // Get projects by brand ID
  fastify.get('/brand/:brandId', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.getByBrandId,
    handler: getProjectsByBrandId
  })

  // Get project by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.getById,
    handler: getProjectById
  })

  // Create project
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.create,
    handler: createProject
  })

  // Update project
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.update,
    handler: updateProject
  })

  // Advance project step (with business logic)
  fastify.post('/:id/advance-step', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.advanceStep,
    handler: advanceProjectStep
  })

  // Get product associated with project
  fastify.get('/:id/product', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.getProjectProduct,
    handler: getProjectProduct
  })

  // Get comprehensive project data (brand, product, keywords)
  fastify.get('/:id/data', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.getProjectData,
    handler: getProjectData
  })

  // Get all generations and their creatives for a project
  fastify.get('/:id/generations-with-creatives', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.getById, // Reuse the same schema as getById since it just needs project ID
    handler: getProjectGenerationsWithCreatives
  })

  // Get comprehensive review data for all keywords in project
  fastify.get('/:id/review', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.getProjectReviewData,
    handler: getProjectReviewData
  })

  // Duplicate project
  fastify.post('/:id/duplicate', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.duplicate,
    handler: duplicateProject
  })

  // Debug project generations (for troubleshooting)
  fastify.get('/:id/debug-generations', {
    onRequest: [fastify.verifyAuth],
    handler: debugProjectGenerations
  })

  // Delete project
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: creativeProjectSchema.delete,
    handler: deleteProject
  })
} 