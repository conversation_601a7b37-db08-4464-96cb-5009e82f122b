export const creativeProjectSchema = {
  create: {
    body: {
      type: 'object',
      required: ['retailerId', 'name'],
      properties: {
        retailerId: { type: 'string', format: 'uuid' },
        name: { type: 'string', minLength: 1 },
        wmCampaigns: { 
          type: 'object',
          default: {}
        }
      }
    }
  },
  update: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      properties: {
        name: { type: 'string', minLength: 1 },
        wmCampaigns: { type: 'object' },
        brandId: { 
          type: ['string', 'null'], 
          format: 'uuid',
          description: 'Brand ID - can be null to unassign from brand'
        },
        retailerId: { 
          type: 'string', 
          format: 'uuid',
          description: 'Retailer ID - required field'
        },
        currentStep: { 
          type: 'integer', 
          minimum: 0,
          description: 'Current step in project workflow'
        }
      }
    }
  },
  getById: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    }
  },
  getByBrandId: {
    params: {
      type: 'object',
      required: ['brandId'],
      properties: {
        brandId: { type: 'string', format: 'uuid' }
      }
    },
    querystring: {
      type: 'object',
      properties: {
        page: { type: 'integer', minimum: 1, default: 1 },
        limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
      }
    }
  },
  getAllUserProjects: {
    querystring: {
      type: 'object',
      properties: {
        page: { type: 'integer', minimum: 1, default: 1 },
        limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
      }
    }
  },
  delete: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    }
  },
  duplicate: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      required: ['name'],
      properties: {
        name: { type: 'string', minLength: 1 }
      }
    }
  },
  getFilterValues: {
    response: {
      200: {
        type: 'object',
        properties: {
          creators: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                name: { type: 'string' },
                email: { type: 'string', format: 'email' }
              }
            }
          },
          retailers: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                name: { type: 'string' },
                slug: { type: 'string' }
              }
            }
          },
          brands: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                name: { type: 'string' }
              }
            }
          },
          campaigns: {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          statuses: {
            type: 'array',
            items: {
              type: 'string'
            }
          }
        }
      }
    }
  },
  search: {
    querystring: {
      type: 'object',
      properties: {
        page: { type: 'integer', minimum: 1, default: 1 },
        limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
        status: { type: 'string' },
        creatorId: { type: 'string', format: 'uuid' },
        retailerId: { type: 'string', format: 'uuid' },
        brandId: { type: 'string', format: 'uuid' },
        campaignId: { type: 'string' },
        search: { type: 'string', minLength: 1 }
      }
    }
  },
  advanceStep: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      required: ['currentStep'],
      properties: {
        currentStep: { 
          type: 'integer', 
          minimum: 0,
          description: 'The step to advance the project to'
        },
        productId: { 
          type: 'string',
          description: 'Product ID - required when advancing to step 1'
        },
        brandId: { 
          type: 'string', 
          format: 'uuid',
          description: 'Brand ID - required when advancing to step 1'
        },
        keywords: {
          type: 'array',
          items: { type: 'string', minLength: 1 },
          minItems: 1,
          description: 'Array of keywords - required when advancing to step 3'
        }
      }
    }
  },
  getProjectProduct: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          productTitle: { type: 'string' },
          productType: { type: ['string', 'null'] },
          pdpSummary: { type: ['string', 'null'] }
        }
      }
    }
  },
  getProjectData: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          project: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              name: { type: 'string' },
              currentStep: { type: ['integer', 'null'] },
              wmCampaigns: { type: 'object' }
            }
          },
          brand: {
            type: ['object', 'null'],
            properties: {
              id: { type: 'string', format: 'uuid' },
              name: { type: 'string' }
            }
          },
          product: {
            type: ['object', 'null'],
            properties: {
              id: { type: 'string', format: 'uuid' },
              productTitle: { type: 'string' },
              productType: { type: ['string', 'null'] },
              pdpSummary: { type: ['string', 'null'] }
            }
          },
          keywords: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                keyword: { type: 'string' },
                accountId: { type: ['string', 'null'], format: 'uuid' },
                include: { type: ['object', 'null'] },
                exclude: { type: ['object', 'null'] },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' }
              }
            }
          },
          campaigns: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' }
              }
            }
          },
          adGroups: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                campaignId: { type: 'string' }
              }
            }
          }
        }
      }
    }
  },
  getProjectReviewData: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          data: {
            type: 'object',
            properties: {
              sessionId: { type: ['string', 'null'] },
              currentStep: { type: 'string' },
              selectedBrandId: { type: ['string', 'null'], format: 'uuid' },
              selectedProductId: { type: ['string', 'null'], format: 'uuid' },
              selectedCampaignId: { type: ['string', 'null'] },
              selectedAdGroupId: { type: ['string', 'null'] },
              selectedKeywords: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    text: { type: 'string' },
                    include: {
                      type: 'object',
                      properties: {
                        word: { type: ['string', 'null'] },
                        tone: { type: ['string', 'null'] },
                        considerations: { type: ['string', 'null'] }
                      }
                    },
                    exclude: {
                      type: 'object',
                      properties: {
                        word: { type: ['string', 'null'] },
                        tone: { type: ['string', 'null'] },
                        considerations: { type: ['string', 'null'] }
                      }
                    }
                  }
                }
              },
              keywordReviewData: {
                type: 'object',
                additionalProperties: {
                  type: 'object',
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    text: { type: 'string' },
                    reviewStatus: { type: 'string', enum: ['setup', 'inReview', 'completed'] },
                    internalUsers: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'string', format: 'uuid' },
                          name: { type: 'string' },
                          role: { type: 'string' },
                          type: { type: 'string', enum: ['Internal'] },
                          selected: { type: 'boolean' },
                          email: { type: ['string', 'null'] }
                        }
                      }
                    },
                    externalUsers: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'string', format: 'uuid' },
                          name: { type: 'string' },
                          role: { type: 'string' },
                          type: { type: 'string', enum: ['External'] },
                          selected: { type: 'boolean' },
                          email: { type: 'string' }
                        }
                      }
                    },
                    adUnits: {
                      type: ['array', 'object', 'null'],
                      description: 'Ad units from generation version unitFields - can be array, object, or null'
                    },
                    proofConfig: {
                      type: 'object',
                      properties: {
                        title: { type: 'string' },
                        emailDescription: { type: 'string' },
                        password: { type: 'string' },
                        expiration: { type: ['string', 'null'], format: 'date-time' }
                      }
                    },
                    reviewComments: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          user: { type: 'string' },
                          message: { type: 'string' },
                          timestamp: { type: 'string', format: 'date-time' },
                          resolved: { type: 'boolean' },
                          asset: { type: ['string', 'null'] },
                          adUnit: { type: ['string', 'null'] }
                        }
                      }
                    }
                  }
                }
              },
              campaigns: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' }
                  },
                  required: ['id', 'name']
                }
              },
              adGroups: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    campaignId: { type: 'string' }
                  },
                  required: ['id', 'name', 'campaignId']
                }
              },
              brand: {
                type: ['object', 'null'],
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  name: { type: 'string' },
                  logoAssetUrl: { type: ['string', 'null'] },
                  primaryCredentialId: { type: ['string', 'null'], format: 'uuid' }
                }
              },
              product: {
                type: ['object', 'null'],
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  productTitle: { type: 'string' },
                  productType: { type: 'string' },
                  pdpSummary: { type: ['string', 'null'] }
                }
              },
              credentialSet: {
                type: ['object', 'null'],
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  name: { type: 'string' },
                  type: { type: 'string' },
                  credentials: { type: 'object' },
                  isActive: { type: 'boolean' },
                  isShared: { type: 'boolean' }
                }
              },
              createdAt: { type: 'string', format: 'date-time' },
              updatedAt: { type: 'string', format: 'date-time' }
            }
          }
        }
      }
    }
  }
} 