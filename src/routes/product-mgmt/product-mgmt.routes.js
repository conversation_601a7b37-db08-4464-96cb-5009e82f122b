import { fetchProducts, fetchAllProducts, productSetupFlow, batchProcessProducts, updateProductMetadata } from "./product-mgmt.controller.js";

export default async function productMgmtRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator("verifyAuth")) {
    throw new Error("Auth plugin must be registered before routes");
  }

  fastify.post("/products/all", {
    onRequest: [fastify.verifyAuth],
    handler: fetchAllProducts,
  });

  fastify.post("/products", {
    onRequest: [fastify.verifyAuth],
    handler: fetchProducts,
  });

//Used for testing in postman to trigger n8n workflow to generate pdp summary and image metadata for single product
  fastify.post("/products/setup", {
    onRequest: [fastify.verifyAuth],
    handler: productSetupFlow,
  });

  // Temporary route for batch processing products to generate pdp summaries and image metadata through postman
  fastify.post("/products/batch-process", {
    onRequest: [fastify.verifyAuth],
    handler: batchProcessProducts,
  });

  // Route for n8n workflow to update product metadata
  fastify.post("/products/metadata", {
    onRequest: [fastify.verifyAuth],
    handler: updateProductMetadata,
  });
}
