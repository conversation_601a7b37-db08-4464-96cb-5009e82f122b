# Product Management & Ingestion Module

This module is a critical ETL (Extract, Transform, Load) pipeline responsible for ingesting product data from an external source (the "Walmart Manager API") and enriching it through an n8n workflow automation service. It is the primary mechanism by which product data enters the AdVid system.

## Architecture & Data Flow

The process is initiated by a user action in the frontend, which triggers an API call to this module. The module then orchestrates a series of steps to fetch, validate, store, and enrich the product data.

```mermaid
sequenceDiagram
    participant Client as User's Browser
    participant AdVid as AdVid Server
    participant WalmartAPI as Walmart Manager API
    participant n8n as n8n Workflow Service
    participant DB as AdVid Database

    Client->>+AdVid: POST /api/product-mgmt/products (Fetch products)
    AdVid->>+WalmartAPI: GET /sp/items/list (Fetch product list)
    WalmartAPI-->>-AdVid: Paginated list of product IDs
    AdVid->>+WalmartAPI: POST /sp/items/details/batch (Fetch product details)
    WalmartAPI-->>-AdVid: Full product details
    AdVid->>AdVid: Validate and transform product data
    AdVid->>+DB: INSERT/UPDATE products table
    DB-->>-AdVid: Confirm storage
    AdVid->>+n8n: POST /webhook/... (Start enrichment)
    Note over AdVid,n8n: This is a fire-and-forget call
    n8n-->>-AdVid: (Async) POST /api/product-mgmt/products/metadata
    AdVid->>+DB: UPDATE products and image_metadata tables
    DB-->>-AdVid: Confirm enrichment
    AdVid-->>-Client: 200 OK (Products are being processed)
```

## Key Controller Functions

-   `fetchProducts`: The main entry point. It orchestrates the entire process of fetching product IDs, getting their details, validating the data, inserting/updating the local database, and then triggering the n8n enrichment workflow for each product.
-   `fetchAllProductIds`: A helper function that handles the pagination logic required to fetch a complete list of product IDs from the Walmart Manager API.
-   `productSetupFlow`: Triggers the n8n webhook for a single product. This is the "enrichment" step, which likely involves AI-based processing (like generating descriptions or analyzing images) based on the function name `genAiDescription` in the schema.
-   `updateProductMetadata`: This is the endpoint that the n8n workflow calls back into when its processing is complete. It updates the product record with the enriched data (like `pdpSummary`) and saves the `imageMetadata`.
-   `batchProcessProducts`: An endpoint to manually trigger the enrichment workflow for a list of existing product IDs.

## API Endpoints

---

### POST /api/product-mgmt/products

-   **Description:** The primary endpoint to initiate the product ingestion and enrichment process.
-   **Request Body:**
    ```json
    {
      "productIds": ["123", "456"],
      "accountId": "uuid",
      "brandId": "uuid",
      "isSelectAll": false,
      "clientType": "SELLER"
    }
    ```
-   **Logic:**
    1.  If `isSelectAll` is true, it first calls `fetchAllProductIds` to get all products from the Walmart API.
    2.  It then calls the Walmart API's batch details endpoint to get full data for the specified products.
    3.  The received data is validated.
    4.  Valid products are inserted into the local `products` table using an `onConflictDoUpdate` (upsert) operation.
    5.  For each new or updated product, it triggers the `productSetupFlow` to start the asynchronous enrichment process.
-   **Response:** A `200 OK` response is sent back to the client quickly, confirming that the process has started. The actual data enrichment happens in the background.

---

### POST /api/product-mgmt/products/metadata

-   **Description:** The webhook endpoint for the n8n service to call back with enriched data.
-   **Authorization:** This endpoint is protected by the same `verifyAuth` decorator, implying that the n8n workflow must present a valid token. This is a secure way to handle webhooks.
-   **Request Body:**
    ```json
    {
      "productId": "123",
      "pdpSummary": "This is an AI-generated summary.",
      "imageData": [
        {
          "imageUrl": "https://example.com/image.jpg",
          "imageText": "AI-extracted text from image.",
          "classification": "Lifestyle"
        }
      ]
    }
    ```
-   **Logic:** Updates the `products` table with the `pdpSummary` and upserts the `image_metadata` table with the new image analysis data.
