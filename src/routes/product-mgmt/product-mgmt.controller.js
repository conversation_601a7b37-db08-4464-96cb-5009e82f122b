import { db, sql } from '../../db/index.js'
import { products } from '../../db/schema/products.js'
import { imageMetadata } from '../../db/schema/imageMetadata.js'
import { eq } from 'drizzle-orm'
import axios from 'axios'
import { accounts } from '../../db/schema/accounts.js'
import { brands } from '../../db/schema/brands.js'

//This used to fetch from a scraper. It is now adding products to the database.
export async function fetchProducts(request, reply) {
    const { productIds, accountId, brandId, isSelectAll, clientType } = request.body

    if (!accountId) {
        console.log('Account ID is required', accountId)
        return reply.code(400).send({ error: 'Account ID is required' })
    }

    try {
        let productIdsWithType = [];

        if (isSelectAll) {
            try {
                productIdsWithType = await fetchAllProductIds(request, reply)
                console.log(`Fetched all product IDs: ${productIdsWithType.length} products`)

                if (productIdsWithType.length === 0) {
                    return reply.code(400).send({ error: 'No products found' })
                }
            } catch (error) {
                console.log('Error fetching all product IDs', error.message, error.code)
                request.log.error(error, 'Error during fetchAllProductIds')

                // Check if the error is from Axios and has a response status
                if (error.isAxiosError && error.response?.status === 401) {
                    // Specific error for expired/invalid token used for the external call
                    return reply.code(401).send({
                        error: 'Authentication failed for external service',
                        details: 'Your session with the external service may have expired. Please log out and log back in.'
                    })
                } else {
                    // Generic internal server error for other issues
                    return reply.code(500).send({
                        error: 'Failed to fetch all product IDs',
                        details: 'An internal error occurred.' // Avoid leaking detailed internal errors
                    })
                }
            }
        } else {
            productIdsWithType = productIds;
        }

        let batchItemRoute = '/sp/items/details/batch';

        if (clientType === 'SELLER' || clientType === 'seller') {
            batchItemRoute = '/marketplace/items/details/batch';
        }

        const response = await axios({
            method: 'post',
            url: `${process.env.WALMART_MANAGER_API_URL}${batchItemRoute}`,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': request.headers.authorization,
                'x-account-id': accountId,
            },
            data: {
                productIds: productIdsWithType,
                accountId,
                brandId
            }
        })

        console.log('Batch response', response.data)

        const items = response.data.items;
        const errors = response.data.errors;

        if (!items) {
            console.log('No products found', response.data)
            return reply.code(400).send({ error: 'No products found' })
        }

        if (errors && errors.length > 0) {
            console.log('Errors:', errors)
        }

        // Validate products against schema before insertion
        const validProductsToInsert = [];
        const invalidProducts = [];

        for (const product of items) {
            // Check required fields
            if (!product.productId || typeof product.productId !== 'string') {
                invalidProducts.push({ productId: product.productId, reason: 'Missing or invalid productId' });
                continue;
            }

            if (!product.productTitle || typeof product.productTitle !== 'string') {
                invalidProducts.push({ productId: product.productId, reason: 'Missing or invalid productTitle' });
                continue;
            }

            // All validation passed, add to valid products
            validProductsToInsert.push({
                productId: product.productId,
                productTitle: product.productTitle,
                productType: product.productType || null,
                category: product.shelf || product.productType || null,
                thumbnailUrl: product.thumbnailUrl || null,
                shortDescription: product.shortDescription || null,
                longDescription: product.longDescription || null,
                genAiDescription: product.genAiDescription || null,
                specifications: product.specifications || [],
                productHighlights: product.productHighlights || [],
                classType: product.classType || null,
                upc: product.upc || null,
                images: product.images || [],
                accountId,
                brandId: brandId || null
            });
        }

        if (invalidProducts.length > 0) {
            console.log('Invalid products not meeting schema requirements:', invalidProducts);
        }

        if (validProductsToInsert.length === 0) {
            return reply.code(400).send({
                error: 'No valid products to insert',
                invalidProducts
            });
        }

        const productsToInsert = validProductsToInsert;

        console.log('Products to insert', productsToInsert)

        const result = await db
            .insert(products)
            .values(productsToInsert)
            .onConflictDoUpdate({
                target: products.productId,
                set: {
                    productTitle: sql`excluded.product_title`,
                    productType: sql`excluded.product_type`,
                    category: sql`excluded.category`,
                    thumbnailUrl: sql`excluded.thumbnail_url`,
                    shortDescription: sql`excluded.short_description`,
                    longDescription: sql`excluded.long_description`,
                    genAiDescription: sql`excluded.gen_ai_description`,
                    specifications: sql`excluded.specifications`,
                    productHighlights: sql`excluded.product_highlights`,
                    classType: sql`excluded.class_type`,
                    upc: sql`excluded.upc`,
                    images: sql`excluded.images`,
                    updatedAt: new Date()
                }
            });

        // Trigger product setup workflow for each product
        for (const product of validProductsToInsert) {
            try {
                productSetupFlow({
                    body: {
                        productId: product.productId,
                        productTitle: product.productTitle,
                        longDescription: product.longDescription,
                        shortDescription: product.shortDescription,
                        images: product.images
                    }
                }, reply);
            } catch (error) {
                console.log(`Error triggering product setup for product ${product.productId}:`, error);
                // Continue with other products even if one fails
                continue;
            }
        }

        return reply.send(result)
    } catch (error) {
        console.log('Error fetching products', error.message, error.code)
        request.log.error(error, 'Error during fetchProducts')

        // Check if the error is from Axios and has a response status
        if (error.isAxiosError && error.response?.status === 401) {
            // Specific error for expired/invalid token used for the external call
            return reply.code(401).send({
                error: 'Authentication failed for external service',
                details: 'Your session with the external service may have expired. Please log out and log back in.'
            })
        } else {
            // Generic internal server error for other issues
            return reply.code(500).send({
                error: 'Failed to fetch products',
                details: 'An internal error occurred.' // Avoid leaking detailed internal errors
            })
        }
    }
}

export async function fetchAllProductIds(request, reply) {
    const { accountId, brandId, clientType } = request.body

    if (!accountId) {
        console.log('Account ID is required', accountId)
        return reply.code(400).send({ error: 'Account ID is required' })
    }

    try {
        let correspondingItemRoute = '/sp/items/list';
        if (clientType === 'SELLER' || clientType === 'seller') {
            correspondingItemRoute = '/marketplace/items';
        }

        let allProductIds = []
        let nextCursor = null
        let hasMoreItems = true

        while (hasMoreItems) {
            const response = await axios({
                method: 'get',
                url: `${process.env.WALMART_MANAGER_API_URL}${correspondingItemRoute}`,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': request.headers.authorization,
                    'x-account-id': accountId,
                },
                params: {
                    accountId,
                    ...(nextCursor && { nextCursor: nextCursor })
                }
            })

            console.log('Fetch Products Page response', response.data)
            const prods = response.data.ItemResponse || []

            // Extract product IDs with correct logic
            const pageProductIds = prods
                .map(product => {
                    // Determine ID and type
                    let id = product.productId;
                    let type = 'productId';

                    // First try to use SKU with integers
                    if (product.sku && /\d/.test(product.sku)) {
                        id = product.sku;
                        type = 'SKU';
                    }
                    // If SKU has no integers, try GTIN
                    else if (product.sku && product.gtin) {
                        id = product.gtin;
                        type = 'GTIN';
                    }

                    return { id, type };
                })
                .filter(item => item.id != null && item.id !== '');

            allProductIds = [...allProductIds, ...pageProductIds]

            // Check if there are more pages to fetch
            nextCursor = response.data.nextCursor
            hasMoreItems = nextCursor !== null && nextCursor !== undefined

            console.log(`Fetched ${pageProductIds.length} products, total: ${allProductIds.length}, nextCursor: ${nextCursor}`)
        }

        if (allProductIds.length === 0) {
            console.log('No products found')
            return []
        }

        return allProductIds
    } catch (error) {
        console.log('Error fetching all product IDs', error.message, error.code)
        request.log.error(error, 'Error during fetchAllProductIds')

        // Check if the error is from Axios and has a response status
        if (error.isAxiosError && error.response?.status === 401) {
            // Throw a specific error or handle as needed
            // Since this is a helper, throwing might be better
            const authError = new Error('Authentication failed for external service. Session may require refresh.')
            authError.statusCode = 401 // Add status code for potential upstream handling
            throw authError
        } else {
            // Re-throw other errors for the calling function to handle
            throw new Error(`Failed to fetch all product IDs: ${error.message || 'Internal error'}`)
        }
    }
}

export async function fetchAllProducts(request, reply) {
    const { accountId, brandId, clientType } = request.body

    if (!accountId) {
        console.log('Account ID is required', accountId)
        return reply.code(400).send({ error: 'Account ID is required' })
    }

    try {
        let correspondingItemRoute = '/sp/items/list';
        if (clientType === 'SELLER' || clientType === 'seller') {
            correspondingItemRoute = '/marketplace/items';
        }
        const response = await axios({
            method: 'get',
            url: `${process.env.WALMART_MANAGER_API_URL}${correspondingItemRoute}`,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': request.headers.authorization,
                'x-account-id': accountId,
            },
            params: {
                accountId,
            }
        })

        console.log('Fetch All Products response', response.data)
        const prods = response.data.ItemResponse;

        if (!prods) {
            console.log('No products found', prods)
            return reply.code(400).send({ error: 'No products found' })
        }

        return reply.send(prods)
    } catch (error) {
        console.log('Error fetching all products', error.message, error.code)
        request.log.error(error, 'Error during fetchAllProducts')

        // Check if the error is from Axios and has a response status
        if (error.isAxiosError && error.response?.status === 401) {
            // Specific error for expired/invalid token used for the external call
            return reply.code(401).send({
                error: 'Authentication failed for external service',
                details: 'Your session with the external service may have expired. Please log out and log back in.'
            })
        } else {
            // Generic internal server error for other issues
            return reply.code(500).send({
                error: 'Failed to fetch all products',
                details: 'An internal error occurred.'
            })
        }
    }
}

// Update product with pdp summary and image metadata
export async function updateProductMetadata(request, reply) {
    const { productId, pdpSummary, imageData } = request.body;

    if (!productId) {
        console.log('Missing required productId');
        return reply.code(400).send({ error: 'productId is required' });
    }

    if (!pdpSummary && !imageData) {
        console.log('No data to update provided');
        return reply.code(400).send({ error: 'Either pdpSummary or imageData must be provided' });
    }

    try {
        const updates = {};

        // Only update pdpSummary if provided
        if (pdpSummary) {
            updates.pdpSummary = pdpSummary;
        }

        // Add updatedAt timestamp if we're making any updates
        if (Object.keys(updates).length > 0) {
            updates.updatedAt = new Date();
        }

        // Update product if we have any updates
        if (Object.keys(updates).length > 0) {
            await db
                .update(products)
                .set(updates)
                .where(eq(products.productId, productId));
        }

        // Process images if provided
        if (imageData && Array.isArray(imageData)) {
            for (const image of imageData) {
                try {
                    await db
                        .insert(imageMetadata)
                        .values({
                            productId: productId,
                            imageUrl: image.imageUrl,
                            imageText: image.imageText,
                            classification: image.classification,
                            createdAt: new Date(),
                            updatedAt: new Date()
                        })
                        .onConflictDoUpdate({
                            target: [imageMetadata.productId, imageMetadata.imageUrl],
                            set: {
                                imageText: sql`excluded.image_text`,
                                classification: image.classification,
                                updatedAt: new Date()
                            }
                        });
                } catch (error) {
                    console.log(`Error inserting image metadata for product ${productId}, image ${image.imageUrl}:`, error);
                    // Continue with other images even if one fails
                    continue;
                }
            }
        }

        return reply.send({
            success: true,
            message: 'Product metadata updated successfully',
            data: {
                productId,
                pdpSummary: pdpSummary ? 'updated' : 'not provided',
                imageCount: imageData ? imageData.length : 0
            }
        });

    } catch (error) {
        console.log('Error updating product metadata:', {
            message: error.message,
            code: error.code,
            productId
        });
        request.log.error(error, 'Error during updateProductMetadata');

        return reply.code(500).send({
            error: 'Failed to update product metadata',
            details: error.message || 'An internal error occurred'
        });
    }
}

//Trigger n8n workflow to generate pdp summary and image metadata
export async function productSetupFlow(request, reply) {
    const { productId, productTitle, longDescription, shortDescription, images } = request.body;

    try {
        // Combine descriptions
        const productDetails = `${shortDescription}\n\n${longDescription}`;
        const searchKeyword = "";

        // Make request to n8n workflow webhook
        const response = await axios({
            method: 'post',
            url: 'https://primary-production-35c9.up.railway.app/webhook/************************************',
            headers: {
                'Content-Type': 'application/json',
            },
            data: {
                productId,
                productTitle,
                productDetails,
                searchKeyword,
                images
            }
        });

        // Log the complete response for debugging
        console.log('N8n workflow response:', {
            status: response.status,
            statusText: response.statusText,
            data: response.data,
            headers: response.headers
        });

        if (reply) {
            return reply.send({
                success: true,
                message: 'Product setup workflow started successfully',
                data: {
                    productId,
                    workflowStarted: true
                }
            });
        }

        return {
            success: true,
            productId,
            workflowStarted: true
        };

    } catch (error) {
        console.log('Error in product setup workflow:', {
            message: error.message,
            code: error.code,
            response: error.response?.data,
            status: error.response?.status
        });
        request.log.error(error, 'Error during productSetupFlow');

        if (reply) {
            if (error.isAxiosError) {
                return reply.code(error.response?.status || 500).send({
                    error: 'Failed to trigger product setup workflow',
                    details: error.response?.data?.message || error.message
                });
            }

            return reply.code(500).send({
                error: 'Failed to process product setup workflow',
                details: error.message || 'An internal error occurred'
            });
        }

        throw error;
    }
}

//Batch process products to generate pdp summaries and image metadata
export async function batchProcessProducts(request, reply) {
    const { productIds } = request.body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
        console.log('Invalid or empty productIds array');
        return reply.code(400).send({ error: 'Valid array of productIds is required' });
    }

    try {
        const results = [];
        const errors = [];

        for (const productId of productIds) {
            try {
                // Get product details from database
                const product = await db
                    .select({
                        productId: products.productId,
                        productTitle: products.productTitle,
                        longDescription: products.longDescription,
                        shortDescription: products.shortDescription,
                        images: products.images
                    })
                    .from(products)
                    .where(eq(products.productId, productId))
                    .limit(1);

                if (!product || product.length === 0) {
                    errors.push({ productId, error: 'Product not found in database' });
                    continue;
                }

                const productData = product[0];

                // Log start of workflow
                console.log(`Starting workflow for product ${productId}`);

                // Call productSetupFlow for each product without awaiting and without reply
                productSetupFlow({
                    body: {
                        productId: productData.productId,
                        productTitle: productData.productTitle,
                        longDescription: productData.longDescription,
                        shortDescription: productData.shortDescription,
                        images: productData.images
                    }
                }).catch(error => {
                    console.log(`Error in workflow for product ${productId}:`, error);
                    errors.push({ productId, error: error.message });
                });

                results.push({ productId, status: 'workflow started' });
            } catch (error) {
                console.log(`Error processing product ${productId}:`, error);
                errors.push({ productId, error: error.message });
            }
        }

        return reply.send({
            success: true,
            message: 'Batch processing completed',
            results: {
                total: productIds.length,
                successful: results.length,
                failed: errors.length,
                results,
                errors
            }
        });

    } catch (error) {
        console.log('Error in batch processing:', error);
        request.log.error(error, 'Error during batchProcessProducts');

        return reply.code(500).send({
            error: 'Failed to process batch',
            details: error.message || 'An internal error occurred'
        });
    }
}