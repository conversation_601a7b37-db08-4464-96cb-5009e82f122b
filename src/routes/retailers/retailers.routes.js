import {
  getAllRetailers,
  getRetailersByAccountId
} from './retailers.controllers.js'

export default async function retailerRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all retailers
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllRetailers
  })

  // Get retailers by account ID
  fastify.get('/account/:accountId', {
    onRequest: [fastify.verifyAuth],
    handler: getRetailersByAccountId
  })
}