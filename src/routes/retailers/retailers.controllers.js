import { eq, sql } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { retailers } from '../../db/schema/retailers.ts'
import { brands } from '../../db/schema/brands.ts'
import { brandRetailers, retailerProducts } from '../../db/schema/retailers.ts'

export async function getAllRetailers(request, reply) {
  try {
    const allRetailers = await db.select().from(retailers)
    return reply.send(allRetailers)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch retailers' })
  }
}

export async function getRetailersByAccountId(request, reply) {
  try {
    const { accountId } = request.params

    const retailersWithStats = await db
      .select({
        id: retailers.id,
        name: retailers.name,
        logoUrl: retailers.logoUrl,
        url: retailers.url,
        slug: retailers.slug,
        createdAt: retailers.createdAt,
        updatedAt: retailers.updatedAt,
        brandCount: sql`COUNT(DISTINCT ${brandRetailers.brandId})::int`,
        totalProducts: sql`COUNT(DISTINCT ${retailerProducts.productId})::int`
      })
      .from(retailers)
      .innerJoin(brandRetailers, eq(retailers.id, brandRetailers.retailerId))
      .innerJoin(brands, eq(brandRetailers.brandId, brands.id))
      .leftJoin(retailerProducts, eq(retailers.id, retailerProducts.retailerId))
      .where(eq(brands.accountId, accountId))
      .groupBy(
        retailers.id,
        retailers.name,
        retailers.logoUrl,
        retailers.url,
        retailers.slug,
        retailers.createdAt,
        retailers.updatedAt
      )

    return reply.send(retailersWithStats)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch retailers for account' })
  }
}