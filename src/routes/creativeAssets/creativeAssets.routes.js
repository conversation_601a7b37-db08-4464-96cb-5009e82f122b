import {
  getAllCreativeAssets,
  getCreativeAssetById,
  createCreativeAsset,
  updateCreativeAsset,
  deleteCreativeAsset
} from './creativeAssets.controller.js'

export default async function creativeAssetRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all creative assets
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllCreativeAssets
  })

  // Get creative asset by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: getCreativeAssetById
  })

  // Create creative asset
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    handler: createCreativeAsset
  })

  // Update creative asset
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: updateCreativeAsset
  })

  // Delete creative asset
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: deleteCreativeAsset
  })
}
