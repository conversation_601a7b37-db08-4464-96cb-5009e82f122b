import { eq } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { creativeAssets } from '../../db/schema/creativeAssets.ts'

export async function getAllCreativeAssets(request, reply) {
  try {
    const allCreativeAssets = await db.select().from(creativeAssets)
    return reply.send(allCreativeAssets)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch creative assets' })
  }
}

export async function getCreativeAssetById(request, reply) {
  try {
    const { id } = request.params
    
    const [creativeAsset] = await db
      .select()
      .from(creativeAssets)
      .where(eq(creativeAssets.id, id))
      .limit(1)

    if (!creativeAsset) {
      return reply.code(404).send({ error: 'Creative asset not found' })
    }

    return reply.send(creativeAsset)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch creative asset' })
  }
}

export async function createCreativeAsset(request, reply) {
  try {
    const { keywordGenerationId, assetId } = request.body

    const [creativeAsset] = await db
      .insert(creativeAssets)
      .values({
        keywordGenerationId,
        assetId
      })
      .returning()

    return reply.code(201).send(creativeAsset)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create creative asset' })
  }
}

export async function updateCreativeAsset(request, reply) {
  try {
    const { id } = request.params
    const { keywordGenerationId, assetId } = request.body

    const [updatedCreativeAsset] = await db
      .update(creativeAssets)
      .set({
        keywordGenerationId,
        assetId,
        updatedAt: new Date()
      })
      .where(eq(creativeAssets.id, id))
      .returning()

    if (!updatedCreativeAsset) {
      return reply.code(404).send({ error: 'Creative asset not found' })
    }

    return reply.send(updatedCreativeAsset)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update creative asset' })
  }
}

export async function deleteCreativeAsset(request, reply) {
  try {
    const { id } = request.params

    const [deletedCreativeAsset] = await db
      .delete(creativeAssets)
      .where(eq(creativeAssets.id, id))
      .returning()

    if (!deletedCreativeAsset) {
      return reply.code(404).send({ error: 'Creative asset not found' })
    }

    return reply.send({ message: 'Creative asset deleted successfully' })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete creative asset' })
  }
}
