export const generationSchema = {
  create: {
    body: {
      type: 'object',
      required: ['productId', 'prompt', 'model'],
      properties: {
        productId: { type: 'string', format: 'uuid' },
        prompt: { type: 'string' },
        model: { type: 'string' },
        settings: { 
          type: 'object',
          additionalProperties: true
        }
      }
    }
  },
  update: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      properties: {
        prompt: { type: 'string' },
        model: { type: 'string' },
        settings: { 
          type: 'object',
          additionalProperties: true
        },
        status: { 
          type: 'string',
          enum: ['pending', 'processing', 'completed', 'failed']
        },
        result: { 
          type: 'object',
          additionalProperties: true
        }
      }
    }
  },
  saveVersion: {
    body: {
      type: 'object',
      required: ['projectId', 'keyword'],
      properties: {
        projectId: { type: 'string', format: 'uuid' },
        keyword: { type: 'string' },
        headline: { type: 'string' },
        subhead: { type: 'string' },
        marquee: { type: 'string' },
        gallery: { type: 'string' },
        brandbox: { type: 'string' },
        adUnits: {
          type: 'object',
          additionalProperties: {
            type: 'object',
            properties: {
              headline: { type: 'string' },
              subhead: { type: 'string' },
              cta: { type: 'string' },
              disclaimer: { type: ['string', 'null'] },
              logoAltText: { type: 'string' },
              imageAltText: { type: 'string' },
              image: { type: 'string' },
              x: { type: 'number' },
              y: { type: 'number' },
              scale: { type: 'number' }
            }
          }
        }
      }
    }
  },
  getVersion: {
    querystring: {
      type: 'object',
      required: ['projectId', 'keyword'],
      properties: {
        projectId: { type: 'string', format: 'uuid' },
        keyword: { type: 'string' }
      }
    }
  },
  getVersionHistory: {
    querystring: {
      type: 'object',
      required: ['projectId', 'keyword'],
      properties: {
        projectId: { type: 'string', format: 'uuid' },
        keyword: { type: 'string' }
      }
    }
  },
  getProjectKeywordGenerations: {
    params: {
      type: 'object',
      required: ['projectId'],
      properties: {
        projectId: { type: 'string', format: 'uuid' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          projectId: { type: 'string', format: 'uuid' },
          project: {
            type: ['object', 'null'],
            properties: {
              id: { type: 'string', format: 'uuid' },
              name: { type: 'string' },
              status: { 
                type: 'string',
                enum: ['in_progress', 'in_review', 'published', 'inactive', 'archived']
              },
              currentStep: { type: 'integer' }
            }
          },
          keywordGenerations: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string' },
                keywordId: { type: 'string', format: 'uuid' },
                generationId: { type: 'string', format: 'uuid' },
                productId: { type: 'string', format: 'uuid' },
                brandId: { type: 'string', format: 'uuid' },
                generationCreatedAt: { type: 'string', format: 'date-time' },
                latestVersion: {
                  type: ['object', 'null'],
                  properties: {
                    id: { type: 'string', format: 'uuid' },
                    generationId: { type: 'string', format: 'uuid' },
                    sessionId: { type: ['string', 'null'] },
                    unitFields: { type: 'object', additionalProperties: true },
                    hasCompleted: { type: 'boolean' },
                    createdBy: { type: 'string', format: 'uuid' },
                    version: { type: 'integer' },
                    adPreviews: { type: 'object', additionalProperties: true },
                    previewImagePositions: { type: 'object', additionalProperties: true },
                    applyToAll: { type: 'boolean' },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' },
                    creator: {
                      type: ['object', 'null'],
                      properties: {
                        id: { type: 'string', format: 'uuid' },
                        name: { type: 'string' },
                        email: { type: 'string', format: 'email' }
                      }
                    }
                  }
                },
                hasActiveVersion: { type: 'boolean' }
              }
            }
          },
          totalKeywords: { type: 'integer' },
          totalGenerations: { type: 'integer' },
          product: {
            type: ['object', 'null'],
            properties: {
              id: { type: 'string', format: 'uuid' },
              productId: { type: 'string' },
              thumbnailUrl: { type: ['string', 'null'] },
              images: { type: 'object', additionalProperties: true },
              customImages: { type: 'object', additionalProperties: true },
              accountId: { type: ['string', 'null'], format: 'uuid' },
              brandId: { type: ['string', 'null'], format: 'uuid' },
              productTitle: { type: 'string' }
            }
          },
          brand: {
            type: ['object', 'null'],
            properties: {
              id: { type: 'string', format: 'uuid' },
              name: { type: 'string' },
              logoAssetUrl: { type: ['string', 'null'] }
            }
          }
        }
      }
    }
  },
  createBulkWithVersions: {
    body: {
      type: 'object',
      required: ['keywords', 'projectId', 'brandId', 'productId'],
      properties: {
        keywords: {
          type: 'array',
          items: { type: 'string' },
          minItems: 1
        },
        projectId: { type: 'string', format: 'uuid' },
        brandId: { type: 'string', format: 'uuid' },
        productId: { type: 'string', format: 'uuid' },
        sessionId: { type: 'string' },
        wmCampaignId: { type: 'string' },
        wmAdGroupId: { type: 'string' },
        unitFields: {
          type: 'object',
          additionalProperties: true
        }
      }
    }
  }
} 