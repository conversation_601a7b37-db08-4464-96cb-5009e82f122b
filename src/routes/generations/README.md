# Generations & Versioning Module

This module is at the heart of the application's creative workflow. It manages "generations," which are requests to create advertising content, and meticulously tracks changes to that content through a robust versioning system.

## Data Model & Workflow

A `generation` is linked to a `project`, a `product`, and a `keyword`. It represents the initial request or idea. The actual content created during the generation process is stored in `generationVersions`. This creates a one-to-many relationship, where a single generation request can have a full history of revisions.

```mermaid
erDiagram
    projects {
        uuid id PK
        text name
    }
    products {
        text productId PK
        text productTitle
    }
    keywords {
        uuid id PK
        text keyword
    }
    generations {
        uuid id PK
        uuid projectId FK
        text productId FK
        uuid keywordId FK
    }
    generationVersions {
        uuid id PK
        uuid generationId FK
        integer version
        jsonb unitFields
        uuid createdBy FK
    }
    users {
        uuid id PK
        text name
    }

    projects ||--|{ generations : "has"
    products ||--|{ generations : "is subject of"
    keywords ||--|{ generations : "targets"
    generations ||--|{ generationVersions : "has history of"
    users ||--o{ generationVersions : "creates"
```

## API Endpoints

All endpoints require authentication.

---

### POST /api/generations

-   **Description:** Creates a new generation record. This is the starting point of the creative process.
-   **Controller Logic:** The system first checks if the provided `keyword` exists in the database. If not, it creates a new keyword record. Then, it creates the `generation` record, linking it to the product, project, and keyword.
-   **Request Body:**
    ```json
    {
      "productId": "product-id-string",
      "projectId": "uuid",
      "brandId": "uuid",
      "keyword": "high performance running shoes",
      "createdBy": "uuid"
    }
    ```
-   **Successful Response `201`:** Returns the newly created generation object, including the keyword record.

---

### POST /api/generations/bulk

-   **Description:** Creates multiple generation records for a list of keywords in a single request.
-   **Controller Logic:** This endpoint efficiently processes a list of keywords. For each keyword, it either finds the existing record or creates a new one, then creates a corresponding generation record. The use of `Promise.all` allows for parallel processing of these operations.

---

### POST /api/generations/save

-   **Description:** Saves a new version of a generation's content. This is the core of the versioning system.
-   **Controller Logic:**
    1.  Finds the parent `generation` record based on the `projectId` and `keyword`.
    2.  Queries the `generationVersions` table to find the highest existing version number for that generation.
    3.  Inserts a new `generationVersions` record with the incremented version number and the new content (`unitFields`).
-   **Request Body:**
    ```json
    {
      "projectId": "uuid",
      "keyword": "high performance running shoes",
      "adUnits": {
        "unit1": { "headline": "New Headline", "cta": "Buy Now" },
        "unit2": { "headline": "Another Headline", "cta": "Learn More" }
      }
    }
    ```
-   **Successful Response `201`:** Returns the newly created `generationVersion` object.

---

### GET /api/generations/version

-   **Description:** Retrieves the most recent version of a generation for a given project and keyword.
-   **Controller Logic:** Finds the parent `generation` and then fetches the `generationVersion` with the highest `version` number.

---

### GET /api/generations/version/history

-   **Description:** Retrieves the complete version history for a single generation, ordered from newest to oldest.
-   **Successful Response `200`:** An array of `generationVersion` objects, each including details about the user who created it.

---

### GET /api/generations/project/:projectId

-   **Description:** Retrieves all keyword-based generations within a single project, including the latest version of each.
-   **Controller Logic:** This is a complex data aggregation endpoint. It first fetches all generations for the project. Then, using a subquery, it efficiently finds the latest version for each of those generations and joins all the relevant data (generation, keyword, version, creator) together.
-   **Successful Response `200`:** An array of generation objects, each containing a `latestVersion` object.
