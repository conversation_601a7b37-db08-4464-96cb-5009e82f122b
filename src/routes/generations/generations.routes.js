import {
  getAllGenerations,
  getGenerationById,
  createGeneration,
  createBulkGenerations,
  createBulkGenerationsWithVersions,
  updateGeneration,
  deleteGeneration,
  getGenerations,
  saveGenerationVersion,
  getGenerationVersion,
  getGenerationVersionHistory,
  getProjectKeywordGenerations
} from './generations.controllers.js'
import { generationSchema } from './generations.schemas.js'

/**
 * Registers HTTP routes for generation-related resources on a Fastify instance, including CRUD operations, version management, and project-specific queries. All routes require authentication.
 * 
 * Throws an error if the authentication decorator is not registered on the Fastify instance.
 */
export default async function generationRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all generations
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllGenerations
  })

  // Get generation by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: getGenerationById
  })

  // Get generations
  fastify.post('/getGenerations', {
    onRequest: [fastify.verifyAuth],
    handler: getGenerations
  })

  // Create generation
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    handler: createGeneration
  })

  // Create bulk generations
  fastify.post('/bulk', {
    onRequest: [fastify.verifyAuth],
    handler: createBulkGenerations
  })

  // Create bulk generations with versions
  fastify.post('/bulk-with-versions', {
    onRequest: [fastify.verifyAuth],
    schema: generationSchema.createBulkWithVersions,
    handler: createBulkGenerationsWithVersions
  })

  // Update generation
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: updateGeneration
  })

  // Delete generation
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: deleteGeneration
  })

  // Save generation version
  fastify.post('/save', {
    onRequest: [fastify.verifyAuth],
    schema: generationSchema.saveVersion,
    handler: saveGenerationVersion
  })

  // Get latest generation version
  fastify.get('/version', {
    onRequest: [fastify.verifyAuth],
    schema: generationSchema.getVersion,
    handler: getGenerationVersion
  })

  // Get generation version history
  fastify.get('/version/history', {
    onRequest: [fastify.verifyAuth],
    schema: generationSchema.getVersionHistory,
    handler: getGenerationVersionHistory
  })

  // Get all keyword generations for a project (latest versions only)
  fastify.get('/project/:projectId', {
    onRequest: [fastify.verifyAuth],
    schema: generationSchema.getProjectKeywordGenerations,
    handler: getProjectKeywordGenerations
  })
}