import { eq, inArray, sql, and } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { generations } from '../../db/schema/generations.ts'
import { keywords } from '../../db/schema/keywords.ts'
import { products } from '../../db/schema/products.ts'
import { brands } from '../../db/schema/brands.ts'
import { generationVersions } from '../../db/schema/generationVersions.ts'
import { users } from '../../db/schema/users.ts'
import { projects } from '../../db/schema/projects.ts'

/**
 * Retrieves all generations with their associated keyword, product, and brand data.
 * 
 * Sends an array of generation records, each including related keyword, product, and brand information. Returns a 500 error if retrieval fails.
 */
export async function getAllGenerations(request, reply) {
  try {
    const allGenerations = await db
      .select({
        generation: generations,
        keyword: keywords,
        product: products,
        brand: brands
      })
      .from(generations)
      .leftJoin(keywords, eq(generations.keywordId, keywords.id))
      .leftJoin(products, eq(generations.productId, products.productId))
      .leftJoin(brands, eq(generations.brandId, brands.id))

    return reply.send(allGenerations)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch generations' })
  }
}

export async function getGenerationById(request, reply) {
  try {
    const { id } = request.params

    // Get the generation with its related data
    const [result] = await db
      .select({
        generation: generations,
        keyword: keywords,
        product: products,
        brand: brands
      })
      .from(generations)
      .leftJoin(keywords, eq(generations.keywordId, keywords.id))
      .leftJoin(products, eq(generations.productId, products.productId))
      .leftJoin(brands, eq(generations.brandId, brands.id))
      .where(eq(generations.id, id))
      .limit(1)

    if (!result) {
      return reply.code(404).send({ error: 'Generation not found' })
    }

    // Flatten the response
    const response = {
      ...result.generation,
      keyword: result.keyword,
      product: result.product,
      brand: result.brand
    }

    return reply.send(response)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch generation' })
  }
}

export async function createGeneration(request, reply) {
  try {
    const {
      productId,
      projectId,
      brandId,
      keyword: keywordText,
      sessionId,
      wmCampaignId,
      wmAdGroupId,
      unitFields,
      createdBy
    } = request.body

    // First, ensure the keyword exists in the keywords table
    let keywordRecord

    // Try to find existing keyword
    const [existingKeyword] = await db
      .select()
      .from(keywords)
      .where(eq(keywords.keyword, keywordText))
      .limit(1)

    if (existingKeyword) {
      keywordRecord = existingKeyword
    } else {
      // If keyword doesn't exist, create it
      const [newKeyword] = await db
        .insert(keywords)
        .values({
          keyword: keywordText,
          productId: productId,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning()

      keywordRecord = newKeyword
    }

    // Create the generation record
    const [generation] = await db
      .insert(generations)
      .values({
        productId,
        keywordId: keywordRecord.id,
        projectId,
        brandId,
        sessionId: sessionId || null,
        wmCampaignId: wmCampaignId || null,
        wmAdGroupId: wmAdGroupId || null,
        unitFields: unitFields || {},
        hasCompleted: false,
        createdBy,
        version: 1,
        adPreviews: [],
        previewImagePositions: {},
        applyToAll: false,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    return reply.code(201).send({
      ...generation,
      keyword: keywordRecord
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create generation' })
  }
}

export async function createBulkGenerations(request, reply) {
  try {
    const {
      productId,
      projectId,
      brandId,
      keywords: keywordsList,
      sessionId,
      wmCampaignId,
      wmAdGroupId,
      unitFields,
      createdBy
    } = request.body

    // Process all keywords
    const keywordPromises = keywordsList.map(async keywordText => {
      // Try to find existing keyword
      const [existingKeyword] = await db
        .select()
        .from(keywords)
        .where(eq(keywords.keyword, keywordText))
        .limit(1)

      if (existingKeyword) {
        return existingKeyword
      }

      // If keyword doesn't exist, create it
      const [newKeyword] = await db
        .insert(keywords)
        .values({
          keyword: keywordText,
          productId: productId,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning()

      return newKeyword
    })

    const keywordRecords = await Promise.all(keywordPromises)

    // Create generation records for each keyword
    const generationPromises = keywordRecords.map(keywordRecord => {
      return db
        .insert(generations)
        .values({
          productId,
          keywordId: keywordRecord.id,
          projectId,
          brandId,
          sessionId: sessionId || null,
          wmCampaignId: wmCampaignId || null,
          wmAdGroupId: wmAdGroupId || null,
          unitFields: unitFields || {},
          hasCompleted: false,
          createdBy,
          version: 1,
          adPreviews: [],
          previewImagePositions: {},
          applyToAll: false,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning()
    })

    const generationResults = await Promise.all(generationPromises)
    const createdGenerations = generationResults.map(([gen]) => gen)

    return reply.code(201).send({
      generations: createdGenerations,
      keywords: keywordRecords
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create generations' })
  }
}

/**
 * Creates bulk generations with initial generation versions for multiple keywords.
 * 
 * Takes multiple keywords, projectId, brandId, productId, and accountId. Creates keyword records 
 * (if they don't exist) with accountId, then creates a generation and initial generation version 
 * for each keyword. Returns a list of generationIds with their respective keyword and generationVersionId.
 */
export async function createBulkGenerationsWithVersions(request, reply) {
  try {
    const {
      keywords: keywordsList,
      projectId,
      brandId,
      productId,
      sessionId,
      wmCampaignId,
      wmAdGroupId,
      unitFields
    } = request.body

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    // Get accountId and createdBy from session user
    const accountId = sessionUser.accountId
    const createdBy = sessionUser.id

    if (!accountId) {
      return reply.code(400).send({ error: 'User must be associated with an account' })
    }

    // Validate required fields
    if (!keywordsList || !Array.isArray(keywordsList) || keywordsList.length === 0) {
      return reply.code(400).send({ error: 'Keywords array is required and cannot be empty' })
    }

    if (!projectId || !brandId || !productId) {
      return reply.code(400).send({ error: 'projectId, brandId, and productId are required' })
    }

    // Process all keywords - create or find existing ones with accountId
    const keywordPromises = keywordsList.map(async keywordText => {
      // Try to find existing keyword for this account
      const [existingKeyword] = await db
        .select()
        .from(keywords)
        .where(and(
          eq(keywords.keyword, keywordText),
          eq(keywords.accountId, accountId)
        ))
        .limit(1)

      if (existingKeyword) {
        return existingKeyword
      }

      // If keyword doesn't exist for this account, create it
      const [newKeyword] = await db
        .insert(keywords)
        .values({
          keyword: keywordText,
          accountId: accountId,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning()

      return newKeyword
    })

    const keywordRecords = await Promise.all(keywordPromises)

    // Create generation records for each keyword
    const generationPromises = keywordRecords.map(keywordRecord => {
      return db
        .insert(generations)
        .values({
          productId,
          keywordId: keywordRecord.id,
          projectId,
          brandId
        })
        .returning()
    })

    const generationResults = await Promise.all(generationPromises)
    const createdGenerations = generationResults.map(([gen]) => gen)

    // Create initial generation versions for each generation
    const generationVersionPromises = createdGenerations.map((generation, index) => {
      const keywordText = keywordRecords[index].keyword
      const keywordUnitFields = unitFields ? unitFields[keywordText] || {} : {}
      
      return db
        .insert(generationVersions)
        .values({
          generationId: generation.id,
          sessionId: sessionId || null,
          unitFields: keywordUnitFields,
          hasCompleted: false,
          createdBy: createdBy,
          version: 1,
          adPreviews: [],
          previewImagePositions: {},
          applyToAll: false,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning()
    })

    const generationVersionResults = await Promise.all(generationVersionPromises)
    const createdGenerationVersions = generationVersionResults.map(([genVer]) => genVer)

    // Format the response with generationId, keyword, and generationVersionId
    const results = createdGenerations.map((generation, index) => ({
      generationId: generation.id,
      keyword: keywordRecords[index].keyword,
      keywordId: keywordRecords[index].id,
      generationVersionId: createdGenerationVersions[index].id
    }))

    return reply.code(201).send({
      success: true,
      results,
      totalCreated: results.length,
      message: `Successfully created ${results.length} generations with initial versions`
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to create bulk generations with versions' })
  }
}

export async function updateGeneration(request, reply) {
  try {
    const { id } = request.params
    const updateData = {
      ...request.body,
      updatedAt: new Date()
    }

    // Remove fields that shouldn't be updated
    delete updateData.id
    delete updateData.createdAt
    delete updateData.createdBy

    const [generation] = await db
      .update(generations)
      .set(updateData)
      .where(eq(generations.id, id))
      .returning()

    if (!generation) {
      return reply.code(404).send({ error: 'Generation not found' })
    }

    return reply.send(generation)
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to update generation' })
  }
}

export async function deleteGeneration(request, reply) {
  try {
    const { id } = request.params

    const [deletedGeneration] = await db
      .delete(generations)
      .where(eq(generations.id, id))
      .returning()

    if (!deletedGeneration) {
      return reply.code(404).send({ error: 'Generation not found' })
    }

    return reply.code(204).send()
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to delete generation' })
  }
}

/**
 * Retrieves generations with optional filtering and pagination, including related keyword, product, and brand data.
 *
 * Supports filtering by brandId, projectId, and productId. Returns paginated results with total count and related entity details.
 */
export async function getGenerations(request, reply) {
  try {
    const { brandId, projectId, productId, page = 1, pageSize = 10 } = request.query

    const offset = (page - 1) * pageSize

    // Build query conditions
    const conditions = []
    if (brandId) conditions.push(eq(generations.brandId, brandId))
    if (projectId) conditions.push(eq(generations.projectId, projectId))
    if (productId) conditions.push(eq(generations.productId, productId))

    // Get filtered generations with joins
    const query = db
      .select({
        generation: generations,
        keyword: keywords,
        product: products,
        brand: brands
      })
      .from(generations)
      .leftJoin(keywords, eq(generations.keywordId, keywords.id))
      .leftJoin(products, eq(generations.productId, products.productId))
      .leftJoin(brands, eq(generations.brandId, brands.id))

    if (conditions.length > 0) {
      query.where(and(...conditions))
    }

    const results = await query.limit(pageSize).offset(offset)

    // Get total count for pagination
    const countQuery = db
      .select({ count: sql`count(*)` })
      .from(generations)

    if (conditions.length > 0) {
      countQuery.where(and(...conditions))
    }

    const [{ count }] = await countQuery

    // Flatten the results
    const formattedGenerations = results.map(result => ({
      ...result.generation,
      keyword: result.keyword,
      product: result.product,
      brand: result.brand
    }))

    return reply.send({
      data: formattedGenerations,
      pagination: {
        total: Number(count),
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(count / pageSize)
      }
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to fetch generations' })
  }
}

/**
 * Creates and saves a new version of a generation for a specified project and keyword.
 *
 * Validates user session and required parameters, locates the relevant keyword and generation, determines the next version number, and inserts a new generation version record with the provided data. Returns the created version and a success message, or an appropriate error response if validation or lookup fails.
 */
export async function saveGenerationVersion(request, reply) {
  try {
    const { projectId, keyword, ...generationData } = request.body

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    if (!projectId) {
      return reply.code(400).send({ error: 'projectId is required' })
    }

    if (!keyword) {
      return reply.code(400).send({ error: 'keyword is required' })
    }

    // Find the keyword record
    const [keywordRecord] = await db
      .select({ id: keywords.id })
      .from(keywords)
      .where(eq(keywords.keyword, keyword))
      .limit(1)

    if (!keywordRecord) {
      return reply.code(404).send({ error: 'Keyword not found' })
    }

    // Find the generation record
    const [generation] = await db
      .select({ id: generations.id })
      .from(generations)
      .where(
        and(
          eq(generations.projectId, projectId),
          eq(generations.keywordId, keywordRecord.id)
        )
      )
      .limit(1)

    if (!generation) {
      return reply.code(404).send({ error: 'Generation not found for this project and keyword' })
    }

    // Get the current highest version number for this generation
    const [{ maxVersion }] = await db
      .select({ 
        maxVersion: sql`COALESCE(MAX(version), 0)`.as('maxVersion')
      })
      .from(generationVersions)
      .where(eq(generationVersions.generationId, generation.id))

    const nextVersion = Number(maxVersion) + 1

    // Create the generation version record
    const [generationVersion] = await db
      .insert(generationVersions)
      .values({
        generationId: generation.id,
        unitFields: generationData.adunits,
        hasCompleted: true,
        createdBy: sessionUser.id,
        version: nextVersion,
        adPreviews: [],
        previewImagePositions: {},
        applyToAll: false,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning()

    // Update the generation record to reference the new generation version
    await db
      .update(generations)
      .set({
        generationVersionId: generationVersion.id,
        updatedAt: new Date()
      })
      .where(eq(generations.id, generation.id))

    return reply.code(201).send({
      success: true,
      generationVersion,
      message: `Generation version ${nextVersion} saved successfully`
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to save generation version' })
  }
}

/**
 * Retrieves the latest generation version for a given project and keyword, including creator information.
 *
 * Validates user session and required query parameters. Looks up the keyword and associated generation, then fetches the most recent version and its creator. Returns a 404 error if any entity is not found.
 * @returns {Object} An object containing the latest generation version data, creator info, keyword, and projectId.
 */
export async function getGenerationVersion(request, reply) {
  try {
    const { projectId, keyword } = request.query

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    if (!projectId) {
      return reply.code(400).send({ error: 'projectId is required' })
    }

    if (!keyword) {
      return reply.code(400).send({ error: 'keyword is required' })
    }

    // Find the keyword record
    const [keywordRecord] = await db
      .select({ id: keywords.id })
      .from(keywords)
      .where(eq(keywords.keyword, keyword))
      .limit(1)

    if (!keywordRecord) {
      return reply.code(404).send({ error: 'Keyword not found' })
    }

    // Find the generation record
    const [generation] = await db
      .select({ id: generations.id })
      .from(generations)
      .where(
        and(
          eq(generations.projectId, projectId),
          eq(generations.keywordId, keywordRecord.id)
        )
      )
      .limit(1)

    if (!generation) {
      return reply.code(404).send({ error: 'Generation not found for this project and keyword' })
    }

    // Get the latest generation version
    const [latestVersion] = await db
      .select({
        id: generationVersions.id,
        generationId: generationVersions.generationId,
        sessionId: generationVersions.sessionId,
        unitFields: generationVersions.unitFields,
        hasCompleted: generationVersions.hasCompleted,
        createdBy: generationVersions.createdBy,
        version: generationVersions.version,
        adPreviews: generationVersions.adPreviews,
        previewImagePositions: generationVersions.previewImagePositions,
        applyToAll: generationVersions.applyToAll,
        createdAt: generationVersions.createdAt,
        updatedAt: generationVersions.updatedAt
      })
      .from(generationVersions)
      .where(eq(generationVersions.generationId, generation.id))
      .orderBy(sql`${generationVersions.version} DESC`)
      .limit(1)

    if (!latestVersion) {
      return reply.code(404).send({ error: 'No generation version found' })
    }

    // Get creator info
    const [creator] = await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email
      })
      .from(users)
      .where(eq(users.id, latestVersion.createdBy))
      .limit(1)

    return reply.send({
      success: true,
      generationVersion: {
        ...latestVersion,
        creator: creator || null
      },
      keyword,
      projectId
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to retrieve generation version' })
  }
}

/**
 * Retrieves the complete version history for a generation identified by projectId and keyword.
 *
 * Validates user session and input parameters, locates the corresponding generation, and returns all associated generation versions along with creator information. Responds with appropriate error codes if the session, projectId, keyword, or generation is not found.
 */
export async function getGenerationVersionHistory(request, reply) {
  try {
    const { projectId, keyword } = request.query

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    if (!projectId) {
      return reply.code(400).send({ error: 'projectId is required' })
    }

    if (!keyword) {
      return reply.code(400).send({ error: 'keyword is required' })
    }

    // Find the keyword record
    const [keywordRecord] = await db
      .select({ id: keywords.id })
      .from(keywords)
      .where(eq(keywords.keyword, keyword))
      .limit(1)

    if (!keywordRecord) {
      return reply.code(404).send({ error: 'Keyword not found' })
    }

    // Find the generation record
    const [generation] = await db
      .select({ id: generations.id })
      .from(generations)
      .where(
        and(
          eq(generations.projectId, projectId),
          eq(generations.keywordId, keywordRecord.id)
        )
      )
      .limit(1)

    if (!generation) {
      return reply.code(404).send({ error: 'Generation not found for this project and keyword' })
    }

    // Get all generation versions with creator info
    const versions = await db
      .select({
        id: generationVersions.id,
        generationId: generationVersions.generationId,
        sessionId: generationVersions.sessionId,
        unitFields: generationVersions.unitFields,
        hasCompleted: generationVersions.hasCompleted,
        createdBy: generationVersions.createdBy,
        version: generationVersions.version,
        adPreviews: generationVersions.adPreviews,
        previewImagePositions: generationVersions.previewImagePositions,
        applyToAll: generationVersions.applyToAll,
        createdAt: generationVersions.createdAt,
        updatedAt: generationVersions.updatedAt,
        creator: {
          id: users.id,
          name: users.name,
          email: users.email
        }
      })
      .from(generationVersions)
      .leftJoin(users, eq(generationVersions.createdBy, users.id))
      .where(eq(generationVersions.generationId, generation.id))
      .orderBy(sql`${generationVersions.version} DESC`)

    return reply.send({
      success: true,
      versions,
      keyword,
      projectId,
      totalVersions: versions.length
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to retrieve generation version history' })
  }
}

/**
 * Retrieves all generations for a given project, including their keywords and the latest version for each generation.
 *
 * Only generations with at least one version are included in the response. Returns metadata such as total keywords and generations, along with the latest version details and creator information for each generation.
 */
export async function getProjectKeywordGenerations(request, reply) {
  try {
    const { projectId } = request.params

    // Get user info from session
    const sessionUser = request.user

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' })
    }

    if (!projectId) {
      return reply.code(400).send({ error: 'projectId is required' })
    }

    // Get all generations for this project with their keywords
    const projectGenerations = await db
      .select({
        generationId: generations.id,
        keywordId: generations.keywordId,
        keyword: keywords.keyword,
        productId: generations.productId,
        brandId: generations.brandId,
        createdAt: generations.createdAt
      })
      .from(generations)
      .innerJoin(keywords, eq(generations.keywordId, keywords.id))
      .where(eq(generations.projectId, projectId))
      .orderBy(keywords.keyword)

    if (projectGenerations.length === 0) {
      // Get project information even when there are no generations
      const [projectInfo] = await db
        .select({
          id: projects.id,
          name: projects.name,
          status: projects.status,
          currentStep: projects.currentStep
        })
        .from(projects)
        .where(eq(projects.id, projectId))
        .limit(1)

      return reply.send({
        success: true,
        projectId,
        project: projectInfo || null,
        keywordGenerations: [],
        totalKeywords: 0,
        product: null,
        brand: null
      })
    }

    // Get project information
    const [projectInfo] = await db
      .select({
        id: projects.id,
        name: projects.name,
        status: projects.status,
        currentStep: projects.currentStep
      })
      .from(projects)
      .where(eq(projects.id, projectId))
      .limit(1)

    // Get product information for the first generation (all generations in a project use the same product)
    const firstProductId = projectGenerations[0].productId
    console.log('firstProductId', projectGenerations[0])
    const [productInfo] = await db
      .select({
        id: products.id,
        productId: products.productId,
        thumbnailUrl: products.thumbnailUrl,
        images: products.images,
        customImages: products.customImages,
        accountId: products.accountId,
        brandId: products.brandId,
        productTitle: products.productTitle
      })
      .from(products)
      .where(eq(products.id, firstProductId))
      .limit(1)

    // Get brand information using the first generation's brandId
    const firstBrandId = projectGenerations[0].brandId
    const [brandInfo] = await db
      .select({
        id: brands.id,
        name: brands.name,
        logoAssetUrl: brands.logoAssetUrl
      })
      .from(brands)
      .where(eq(brands.id, firstBrandId))
      .limit(1)

    // Get the latest version for each generation
    const generationIds = projectGenerations.map(g => g.generationId)
    
    // Subquery to get the latest version for each generation
    const latestVersionsSubquery = db
      .select({
        generationId: generationVersions.generationId,
        maxVersion: sql`MAX(${generationVersions.version})`.as('maxVersion')
      })
      .from(generationVersions)
      .where(inArray(generationVersions.generationId, generationIds))
      .groupBy(generationVersions.generationId)
      .as('latestVersions')

    // Get the actual latest version records with creator info
    const latestVersions = await db
      .select({
        id: generationVersions.id,
        generationId: generationVersions.generationId,
        sessionId: generationVersions.sessionId,
        unitFields: generationVersions.unitFields,
        hasCompleted: generationVersions.hasCompleted,
        createdBy: generationVersions.createdBy,
        version: generationVersions.version,
        adPreviews: generationVersions.adPreviews,
        previewImagePositions: generationVersions.previewImagePositions,
        applyToAll: generationVersions.applyToAll,
        createdAt: generationVersions.createdAt,
        updatedAt: generationVersions.updatedAt,
        creator: {
          id: users.id,
          name: users.name,
          email: users.email
        }
      })
      .from(generationVersions)
      .innerJoin(
        latestVersionsSubquery,
        and(
          eq(generationVersions.generationId, latestVersionsSubquery.generationId),
          eq(generationVersions.version, latestVersionsSubquery.maxVersion)
        )
      )
      .leftJoin(users, eq(generationVersions.createdBy, users.id))

    // Group versions by generationId for easy lookup
    const versionsByGenerationId = latestVersions.reduce((acc, version) => {
      acc[version.generationId] = version
      return acc
    }, {})

    // Combine generation data with their latest versions
    const keywordGenerations = projectGenerations.map(generation => {
      const latestVersion = versionsByGenerationId[generation.generationId]
      
      return {
        keyword: generation.keyword,
        keywordId: generation.keywordId,
        generationId: generation.generationId,
        productId: generation.productId,
        brandId: generation.brandId,
        generationCreatedAt: generation.createdAt,
        latestVersion: latestVersion || null,
        hasActiveVersion: !!latestVersion
      }
    })

    // Filter to only include generations that have at least one version
    const activeKeywordGenerations = keywordGenerations.filter(kg => kg.hasActiveVersion)

    return reply.send({
      success: true,
      projectId,
      project: projectInfo || null,
      keywordGenerations: activeKeywordGenerations,
      totalKeywords: activeKeywordGenerations.length,
      totalGenerations: projectGenerations.length,
      product: productInfo || null,
      brand: brandInfo || null
    })
  } catch (error) {
    request.log.error(error)
    return reply.code(500).send({ error: 'Failed to retrieve project keyword generations' })
  }
}