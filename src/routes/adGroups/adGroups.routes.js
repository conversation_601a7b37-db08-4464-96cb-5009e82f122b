import {
  getAllAdGroups,
  getAdGroupById,
  createAdGroup,
  updateAdGroup,
  deleteAdGroup
} from './adGroups.controllers.js'
import { adGroupSchema } from './adGroups.schemas.js'

export default async function adGroupRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes')
  }

  // Get all ad groups
  fastify.get('/', {
    onRequest: [fastify.verifyAuth],
    handler: getAllAdGroups
  })

  // Get ad group by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: getAdGroupById
  })

  // Create ad group
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    schema: adGroupSchema.create,
    handler: createAdGroup
  })

  // Update ad group
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    schema: adGroupSchema.update,
    handler: updateAdGroup
  })

  // Delete ad group
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: deleteAdGroup
  })
} 