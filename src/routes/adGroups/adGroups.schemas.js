export const adGroupSchema = {
  create: {
    body: {
      type: 'object',
      required: ['name', 'brandId'],
      properties: {
        name: { type: 'string', minLength: 1 },
        brandId: { type: 'string', format: 'uuid' },
        keywords: { 
          type: 'array',
          items: { type: 'string' }
        }
      }
    }
  },
  update: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      properties: {
        name: { type: 'string', minLength: 1 },
        brandId: { type: 'string', format: 'uuid' },
        keywords: { 
          type: 'array',
          items: { type: 'string' }
        }
      }
    }
  }
} 