import axios from 'axios'
import { logger } from '../../utils/logger.js'

export async function getAllAdGroups(request, reply) {
  try {
    const accountId = request.headers['x-account-id']

    if (!accountId) {
      return reply.code(400).send({ error: 'Account ID is required in headers' })
    }

    const response = await axios({
      method: 'get',
      url: `${process.env.WALMART_MANAGER_API_URL}/display/ad-groups/`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.authorization,
        'x-account-id': accountId,
      }
    })

    logger.info('Walmart API - getAllAdGroups response received', {
      accountId,
      responseCode: response.data.code,
      totalResults: response.data.totalResults || 0
    })

    return reply.send(response.data)
  } catch (error) {
    logger.error('Error fetching ad groups from Walmart API', error, {
      accountId: request.headers['x-account-id']
    })

    // Handle specific error cases
    if (error.response?.status === 401) {
      return reply.code(401).send({
        error: 'Authentication failed with Walmart API',
        details: 'Your session with the external service may have expired. Please log out and log back in.'
      })
    } else if (error.response?.status === 404) {
      return reply.code(404).send({ error: 'Ad groups not found' })
    } else if (error.response?.status >= 400 && error.response?.status < 500) {
      return reply.code(error.response.status).send({
        error: 'Request rejected by Walmart API',
        details: error.response?.data?.message || 'Invalid request'
      })
    } else {
      return reply.code(500).send({ error: 'Walmart service unavailable - please try again later' })
    }
  }
}

export async function getAdGroupById(request, reply) {
  try {
    const { id } = request.params
    const accountId = request.headers['x-account-id']

    if (!accountId) {
      return reply.code(400).send({ error: 'Account ID is required in headers' })
    }

    const response = await axios({
      method: 'get',
      url: `${process.env.WALMART_MANAGER_API_URL}/display/ad-groups/${id}`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.authorization,
        'x-account-id': accountId,
      }
    })

    logger.info('Walmart API - getAdGroupById response received', {
      accountId,
      adGroupId: id,
      responseCode: response.data.code
    })

    return reply.send(response.data)
  } catch (error) {
    logger.error('Error fetching ad group from Walmart API', error, {
      accountId: request.headers['x-account-id'],
      adGroupId: request.params.id
    })

    if (error.response?.status === 401) {
      return reply.code(401).send({
        error: 'Authentication failed with Walmart API',
        details: 'Your session with the external service may have expired. Please log out and log back in.'
      })
    } else if (error.response?.status === 404) {
      return reply.code(404).send({ error: 'Ad group not found' })
    } else if (error.response?.status >= 400 && error.response?.status < 500) {
      return reply.code(error.response.status).send({
        error: 'Request rejected by Walmart API',
        details: error.response?.data?.message || 'Invalid request'
      })
    } else {
      return reply.code(500).send({ error: 'Walmart service unavailable - please try again later' })
    }
  }
}

export async function createAdGroup(request, reply) {
  try {
    const { name, brandId, keywords } = request.body
    const accountId = request.headers['x-account-id']

    if (!accountId) {
      return reply.code(400).send({ error: 'Account ID is required in headers' })
    }

    const response = await axios({
      method: 'post',
      url: `${process.env.WALMART_MANAGER_API_URL}/display/ad-groups/`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.authorization,
        'x-account-id': accountId,
      },
      data: {
        name,
        brandId,
        keywords,
        accountId
      }
    })

    logger.info('Walmart API - createAdGroup response received', {
      accountId,
      adGroupName: name,
      brandId,
      responseCode: response.data.code
    })

    return reply.code(201).send(response.data)
  } catch (error) {
    logger.error('Error creating ad group in Walmart API', error, {
      accountId: request.headers['x-account-id'],
      adGroupName: request.body.name
    })

    if (error.response?.status === 401) {
      return reply.code(401).send({
        error: 'Authentication failed with Walmart API',
        details: 'Your session with the external service may have expired. Please log out and log back in.'
      })
    } else if (error.response?.status === 400) {
      return reply.code(400).send({
        error: 'Invalid ad group data',
        details: error.response?.data?.message || 'Please check your input data'
      })
    } else if (error.response?.status >= 400 && error.response?.status < 500) {
      return reply.code(error.response.status).send({
        error: 'Request rejected by Walmart API',
        details: error.response?.data?.message || 'Invalid request'
      })
    } else {
      return reply.code(500).send({ error: 'Walmart service unavailable - please try again later' })
    }
  }
}

export async function updateAdGroup(request, reply) {
  try {
    const { id } = request.params
    const updateData = request.body
    const accountId = request.headers['x-account-id']

    if (!accountId) {
      return reply.code(400).send({ error: 'Account ID is required in headers' })
    }

    const response = await axios({
      method: 'put',
      url: `${process.env.WALMART_MANAGER_API_URL}/display/ad-groups/${id}`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.authorization,
        'x-account-id': accountId,
      },
      data: {
        ...updateData,
        accountId
      }
    })

    logger.info('Walmart API - updateAdGroup response received', {
      accountId,
      adGroupId: id,
      responseCode: response.data.code
    })

    return reply.send(response.data)
  } catch (error) {
    logger.error('Error updating ad group in Walmart API', error, {
      accountId: request.headers['x-account-id'],
      adGroupId: request.params.id
    })

    if (error.response?.status === 401) {
      return reply.code(401).send({
        error: 'Authentication failed with Walmart API',
        details: 'Your session with the external service may have expired. Please log out and log back in.'
      })
    } else if (error.response?.status === 404) {
      return reply.code(404).send({ error: 'Ad group not found' })
    } else if (error.response?.status === 400) {
      return reply.code(400).send({
        error: 'Invalid update data',
        details: error.response?.data?.message || 'Please check your input data'
      })
    } else if (error.response?.status >= 400 && error.response?.status < 500) {
      return reply.code(error.response.status).send({
        error: 'Request rejected by Walmart API',
        details: error.response?.data?.message || 'Invalid request'
      })
    } else {
      return reply.code(500).send({ error: 'Walmart service unavailable - please try again later' })
    }
  }
}

export async function deleteAdGroup(request, reply) {
  try {
    const { id } = request.params
    const accountId = request.headers['x-account-id']

    if (!accountId) {
      return reply.code(400).send({ error: 'Account ID is required in headers' })
    }

    const response = await axios({
      method: 'delete',
      url: `${process.env.WALMART_MANAGER_API_URL}/display/ad-groups/${id}`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.authorization,
        'x-account-id': accountId,
      }
    })

    logger.info('Walmart API - deleteAdGroup response received', {
      accountId,
      adGroupId: id,
      responseCode: response.data.code
    })

    return reply.code(204).send()
  } catch (error) {
    logger.error('Error deleting ad group in Walmart API', error, {
      accountId: request.headers['x-account-id'],
      adGroupId: request.params.id
    })

    if (error.response?.status === 401) {
      return reply.code(401).send({
        error: 'Authentication failed with Walmart API',
        details: 'Your session with the external service may have expired. Please log out and log back in.'
      })
    } else if (error.response?.status === 404) {
      return reply.code(404).send({ error: 'Ad group not found' })
    } else if (error.response?.status >= 400 && error.response?.status < 500) {
      return reply.code(error.response.status).send({
        error: 'Request rejected by Walmart API',
        details: error.response?.data?.message || 'Invalid request'
      })
    } else {
      return reply.code(500).send({ error: 'Walmart service unavailable - please try again later' })
    }
  }
}