# Debug Module

This module provides powerful, and potentially dangerous, endpoints for debugging and managing user sessions directly in Redis.

**⚠️ Security Warning:** These endpoints should be disabled or heavily restricted in a production environment. Exposing session data or providing an endpoint to delete all sessions is a significant security risk.

## Architecture

The controller functions in this module interact directly with the Redis client decorated onto the Fastify instance (`request.server.redis`). They use raw Redis commands to scan for, retrieve, and delete session keys.

## API Endpoints

---

### GET /debug/sessions

-   **Description:** Retrieves all active user sessions currently stored in Redis.
-   **Use Case:** In a development environment, this is extremely useful for inspecting the contents of user sessions to debug permission issues or verify that session data is being stored correctly after login.
-   **Controller Logic:** Scans Redis for all keys matching the `sess:*` pattern and then uses the `MGET` command to efficiently fetch the data for all keys at once.
-   **Successful Response `200`:**
    ```json
    {
      "sessions": [
        {
          "key": "sess:some-session-id",
          "data": {
            "user": {
              "id": "uuid",
              "email": "<EMAIL>"
            },
            "cookie": { ... }
          }
        }
      ],
      "sessionCount": 1
    }
    ```

---

### DELETE /debug/sessions

-   **Description:** Deletes all active user sessions from Redis.
-   **Use Case:** Provides a "nuke" option during development to log everyone out and test the login flow from a clean state.
-   **Controller Logic:** Scans for all `sess:*` keys and deletes them in chunks to avoid blocking Redis if there are a very large number of sessions.

---

### GET /debug/my-session

-   **Description:** A utility endpoint to inspect the session of the user making the request.
-   **Use Case:** Helps debug issues with a specific user's session by showing the session data as seen by the server and as stored in Redis.
