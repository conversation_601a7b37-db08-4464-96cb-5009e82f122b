import { getAllSessions, deleteAllSessions } from './debug.controllers.js';

export default async function debugRoutes(fastify, options) {


  fastify.get('/sessions', {
    // Add specific authentication/authorization if required, e.g.:
    // onRequest: [fastify.verifyAuth, fastify.requireAdminPermission],
    handler: getAllSessions
  });

  // Add DELETE endpoint to clear all sessions
  fastify.delete('/sessions', {
    // In production, this should be highly restricted
    // onRequest: [fastify.verifyAuth, fastify.requireAdminPermission],
    handler: deleteAllSessions
  });

  // Add a route to check the current session
  fastify.get('/my-session', async (request, reply) => {
    // Get all cookies from the request
    const cookies = request.cookies || {};
    console.log('Cookies in request:', cookies);
    
    // Check if session exists in the request
    console.log('Session in request:', !!request.session);
    
    // Extract the session ID
    let sessionId = null;
    if (request.session && request.session.sessionId) {
      sessionId = request.session.sessionId;
    } else if (request.session && request.session.id) {
      sessionId = request.session.id;
    } else {
      // Try to extract from the session cookie directly
      const sessionCookie = cookies.sessionId; // or whatever your cookie name is
      if (sessionCookie) {
        sessionId = sessionCookie;
      }
    }
    
    let redisSessionData = null;
    if (sessionId) {
      try {
        // Try to get the session data from Redis directly
        const redis = fastify.redis;
        const redisKey = `sess:${sessionId}`;
        const rawData = await redis.get(redisKey);
        
        if (rawData) {
          redisSessionData = JSON.parse(rawData);
        }
      } catch (error) {
        console.error('Error fetching session from Redis:', error);
      }
    }
    
    return {
      hasSession: !!request.session,
      sessionId,
      sessionData: request.session || null,
      cookies,
      fromRedis: redisSessionData
    };
  });
} 