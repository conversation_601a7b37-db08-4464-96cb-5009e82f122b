// NOTE: Exposing session data is a security risk. Use with caution and ideally only in dev.

export async function getAllSessions(request, reply) {

    const redis = request.server.redis; // Access the decorated redis client

    try {
        // Log Redis connection status
        console.log('Redis status check:');
        try {
            const pingResult = await redis.ping();
            console.log('Redis PING response:', pingResult);
        } catch (pingError) {
            console.error('Redis PING failed:', pingError);
        }

        // Scan for keys matching the session prefix (usually 'sess:')
        const keys = await redis.keys('sess:*');
        console.log('Sessions found in Redis:', keys.length, keys);
        
        if (!keys || keys.length === 0) {
            return reply.send({ 
                message: 'No active sessions found.', 
                sessions: [],
                redisStatus: 'connected'
            });
        }

        // Fetch the data for each key
        // MGET is efficient for fetching multiple keys
        const sessionDataStrings = await redis.mget(keys);

        const sessions = keys.map((key, index) => {
            const dataString = sessionDataStrings[index];
            let data = null;
            try {
                // Session data is typically stored as a JSON string
                data = dataString ? JSON.parse(dataString) : null;
            } catch (parseError) {
                request.log.error({ key, dataString, error: parseError }, 'Failed to parse session data from Redis');
                data = { error: 'Failed to parse data', raw: dataString };
            }
            return { key, data };
        });

        // Check for a specific session if ID provided in query
        let specificSession = null;
        if (request.query.id) {
            const sessionKey = `sess:${request.query.id}`;
            const sessionData = await redis.get(sessionKey);
            
            if (sessionData) {
                try {
                    specificSession = {
                        key: sessionKey,
                        data: JSON.parse(sessionData)
                    };
                } catch (parseError) {
                    specificSession = {
                        key: sessionKey,
                        error: 'Failed to parse data',
                        raw: sessionData
                    };
                }
            }
        }

        reply.send({ 
            sessions,
            specificSession,
            redisStatus: 'connected',
            sessionCount: keys.length
        });

    } catch (error) {
        request.log.error(error, 'Error fetching sessions from Redis');
        reply.code(500).send({ error: 'Failed to fetch sessions', details: error.message });
    }
}

export async function deleteAllSessions(request, reply) {
    const redis = request.server.redis; // Access the decorated redis client

    try {
        // Scan for keys matching the session prefix (usually 'sess:')
        const keys = await redis.keys('sess:*');
        console.log('Sessions found for deletion:', keys.length, keys);
        
        if (!keys || keys.length === 0) {
            return reply.send({ 
                message: 'No active sessions found to delete.',
                redisStatus: 'connected'
            });
        }

        // Store count for the response
        const count = keys.length;
        
        // Use DEL command with multiple keys for efficient deletion
        // Note: Redis DEL has a limit on how many keys it can delete at once
        // For safety, we'll chunk the deletion if we have a large number of keys
        const CHUNK_SIZE = 100; // Adjust based on Redis configuration if needed
        
        let deletedCount = 0;
        
        // Delete keys in chunks
        for (let i = 0; i < keys.length; i += CHUNK_SIZE) {
            const chunk = keys.slice(i, i + CHUNK_SIZE);
            const result = await redis.del(...chunk);
            deletedCount += result;
        }

        reply.send({ 
            message: `Successfully deleted ${deletedCount} of ${count} sessions.`,
            redisStatus: 'connected',
            deletedCount,
            totalFound: count
        });

    } catch (error) {
        request.log.error(error, 'Error deleting sessions from Redis');
        reply.code(500).send({ 
            error: 'Failed to delete sessions', 
            details: error.message 
        });
    }
} 