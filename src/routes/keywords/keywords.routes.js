import { addKeywordsToProduct, removeK<PERSON>wordFromProduct, getKeywordsByAccount } from './keywords.controllers.js'

export default async function keywordRoutes(fastify, options) {
    // Ensure auth decorator is available
    if (!fastify.hasDecorator('verifyAuth')) {
        throw new Error('Auth plugin must be registered before routes')
    }

    fastify.post('/products/:productId/keywords', {
        onRequest: [fastify.verifyAuth],
        handler: addKeywordsToProduct
    })

    fastify.delete('/products/:productId/keywords/:keywordId', {
        onRequest: [fastify.verifyAuth],
        handler: removeKeywordFromProduct
    })

    fastify.get('/accounts/:accountId/keywords', {
        onRequest: [fastify.verifyAuth],
        handler: getKeywordsByAccount
    })
} 