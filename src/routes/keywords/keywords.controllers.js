import { db } from '../../db/index.js'
import { keywords } from '../../db/schema/keywords.js'
import { generations } from '../../db/schema/generations.js'
import { products } from '../../db/schema/products.js'
import { eq, and } from 'drizzle-orm'

export async function getKeywordsByAccount(request, reply) {
    const { accountId } = request.params

    if (!accountId) {
        return reply.code(400).send({ error: 'Account ID is required' })
    }

    try {
        const accountKeywords = await db
            .select()
            .from(keywords)
            .where(eq(keywords.accountId, accountId))
            .orderBy(keywords.createdAt)

        return reply.send(accountKeywords)
    } catch (error) {
        request.log.error(error)
        return reply.code(500).send({ error: 'Failed to fetch keywords' })
    }
}


export async function addKeywordsToProduct(request, reply) {
    const { productId, accountId, keywords: keywordsToAdd } = request.body

    if (!productId || !accountId || !keywordsToAdd || !Array.isArray(keywordsToAdd)) {
        return reply.code(400).send({ error: 'Product ID, Account ID, and keywords array are required' })
    }

    try {
        // Verify product exists and belongs to account
        const product = await db
            .select()
            .from(products)
            .where(and(
                eq(products.productId, productId),
                eq(products.accountId, accountId)
            ))
            .limit(1)

        if (!product.length) {
            return reply.code(404).send({ error: 'Product not found or access denied' })
        }

        const results = []

        for (const keywordData of keywordsToAdd) {
            // Check if keyword exists for this account, if not create it
            let keywordRecord = await db
                .select()
                .from(keywords)
                .where(and(
                    eq(keywords.keyword, keywordData.content),
                    eq(keywords.accountId, accountId)
                ))
                .limit(1)

            let keywordId
            if (!keywordRecord.length) {
                // Create new keyword for this account
                const newKeyword = await db
                    .insert(keywords)
                    .values({ 
                        keyword: keywordData.content,
                        accountId,
                        include: keywordData.include || {},
                        exclude: keywordData.exclude || {}
                    })
                    .returning()
                keywordId = newKeyword[0].id
                results.push({ keyword: newKeyword[0], action: 'created' })
            } else {
                // Update existing keyword
                const updatedKeyword = await db
                    .update(keywords)
                    .set({
                        include: keywordData.include || keywordRecord[0].include,
                        exclude: keywordData.exclude || keywordRecord[0].exclude
                    })
                    .where(eq(keywords.id, keywordRecord[0].id))
                    .returning()
                results.push({ keyword: updatedKeyword[0], action: 'updated' })
            }
        }

        return reply.send(results)
    } catch (error) {
        request.log.error(error)
        return reply.code(500).send({ error: 'Failed to add keywords to product' })
    }
}

export async function removeKeywordFromProduct(request, reply) {
    const { productId, keywordId } = request.params
    const { accountId } = request.query

    if (!productId || !keywordId || !accountId) {
        return reply.code(400).send({ error: 'Product ID, Keyword ID, and Account ID are required' })
    }

    try {
        // Verify keyword belongs to account
        const keyword = await db
            .select()
            .from(keywords)
            .where(and(
                eq(keywords.id, keywordId),
                eq(keywords.accountId, accountId)
            ))
            .limit(1)

        if (!keyword.length) {
            return reply.code(404).send({ error: 'Keyword not found or access denied' })
        }

        // Check if keyword is being used in any generations for this product
        const activeGenerations = await db
            .select()
            .from(generations)
            .where(and(
                eq(generations.productId, productId),
                eq(generations.keywordId, keywordId)
            ))
            .limit(1)

        if (activeGenerations.length) {
            return reply.code(409).send({ 
                error: 'Cannot delete keyword - it is currently being used in active generations' 
            })
        }

        // Safe to delete keyword
        await db
            .delete(keywords)
            .where(eq(keywords.id, keywordId))

        return reply.send({ success: true, message: 'Keyword deleted successfully' })
    } catch (error) {
        request.log.error(error)
        return reply.code(500).send({ error: 'Failed to remove keyword' })
    }
}