import { db, sql } from '../../db/index.js'
import { creatives } from '../../db/schema/creatives.ts';
import { brands } from '../../db/schema/brands.ts';
import { users } from '../../db/schema/users.ts';
import { eq } from 'drizzle-orm';
import { generations } from '../../db/schema/generations.ts';
import { keywords } from '../../db/schema/keywords.ts';

export async function getCreatives(request, reply) {
  try {
    const { brandId, page = 1, pageSize = 10 } = request.body
    console.log('request.body', request.body)

    const offset = (page - 1) * pageSize

    // First get the filtered creatives with joined data
    let query = db
      .select({
        creative: creatives,
        brandName: brands.name,
        userName: users.name
      })
      .from(creatives)
      .leftJoin(brands, eq(creatives.brandId, brands.id))
      .leftJoin(users, eq(creatives.userId, users.id))

    // Apply optional brand filter
    if (brandId) {
      query = query.where(eq(creatives.brandId, brandId))
    }

    // Add pagination
    query = query.limit(pageSize).offset(offset)

    const results = await query

    // Transform results to include joined data in a clean format
    const filteredCreatives = results.map(result => ({
      ...result.creative,
      brandName: result.brandName,
      userName: result.userName
    }))

    // Get total count for pagination
    let countQuery = db
      .select({ count: sql`count(*)` })
      .from(creatives)

    // Apply the same filter to the count query
    if (brandId) {
      countQuery = countQuery.where(eq(creatives.brandId, brandId))
    }
    const [{ count }] = await countQuery

    console.log('Filtered creatives:', filteredCreatives)

    return reply.send({
      data: filteredCreatives,
      pagination: {
        total: Number(count),
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(count / pageSize)
      }
    })
  } catch (error) {
    request.log.error(error);
    return reply.code(500).send({ error: 'Failed to fetch creatives' });
  }
}

export async function getCreativeById(request, reply) {
  try {
    const { id } = request.params;
    const [creative] = await db
      .select()
      .from(creatives)
      .where(eq(creatives.id, id))
      .limit(1);

    if (!creative) {
      return reply.code(404).send({ error: 'Creative not found' });
    }

    return reply.send(creative);
  } catch (error) {
    request.log.error(error);
    return reply.code(500).send({ error: 'Failed to fetch creative' });
  }
}

export async function createCreative(request, reply) {
  try {
    const {
      wmAdGroupId,
      wmCampaignId,
      previewUrls,
      folderId,
      status,
      keyword,
      adUnits,
      wmCreativeId,
      brandId,
      userId,
      generationId,
      projectId
    } = request.body;

    // Validate required fields
    if (!brandId || !userId || !generationId) {
      return reply.code(400).send({ error: 'brandId, userId, and generationId are required fields' });
    }

    const [newCreative] = await db.insert(creatives).values({
      wmAdGroupId,
      wmCampaignId,
      previewUrls: previewUrls || {},
      folderId,
      status: status || 'DRAFT',
      keyword,
      adUnits: adUnits || {}, // Ensure adUnits has a default empty object if not provided
      wmCreativeId,
      brandId,
      userId,
      generationId,
      projectId
    }).returning();

    return reply.code(201).send(newCreative);
  } catch (error) {
    request.log.error(error);
    return reply.code(500).send({ error: 'Failed to create creative' });
  }
}

export async function updateCreative(request, reply) {
  try {
    const { id } = request.params;
    const updateData = request.body;

    const [updatedCreative] = await db
      .update(creatives)
      .set({ ...updateData, updatedAt: new Date() })
      .where(eq(creatives.id, id))
      .returning();

    if (!updatedCreative) {
      return reply.code(404).send({ error: 'Creative not found' });
    }

    return reply.send(updatedCreative);
  } catch (error) {
    request.log.error(error);
    return reply.code(500).send({ error: 'Failed to update creative' });
  }
}

export async function deleteCreative(request, reply) {
  try {
    const { id } = request.params;
    const [deletedCreative] = await db
      .delete(creatives)
      .where(eq(creatives.id, id))
      .returning();

    if (!deletedCreative) {
      return reply.code(404).send({ error: 'Creative not found' });
    }

    return reply.code(204).send();
  } catch (error) {
    request.log.error(error);
    return reply.code(500).send({ error: 'Failed to delete creative' });
  }
}

export async function getCreativeForPreview(request, reply) {
  try {
    const { id } = request.params;

    // First, get the creative by ID
    const [creative] = await db
      .select()
      .from(creatives)
      .where(eq(creatives.id, id))
      .limit(1);

    if (!creative) {
      return reply.code(404).send({ error: 'Creative not found' });
    }

    // Get the generation using the creative's generationId
    const [generation] = await db
      .select()
      .from(generations)
      .where(eq(generations.id, creative.generationId))
      .limit(1);

    if (!generation) {
      return reply.code(404).send({ error: 'Generation not found for this creative' });
    }

    // Fetch the keyword using the generation's keywordId
    const [keyword] = await db
      .select()
      .from(keywords)
      .where(eq(keywords.id, generation.keywordId))
      .limit(1);

    // Return the combined data with keyword if available
    return reply.send({
      creative,
      generation,
      keyword: keyword || null
    });
  } catch (error) {
    request.log.error(error);
    return reply.code(500).send({ error: 'Failed to fetch creative preview data' });
  }
}

