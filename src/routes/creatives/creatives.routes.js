import {
  getCreatives,
  getCreativeById,
  createCreative,
  updateCreative,
  deleteCreative,
  getCreativeForPreview
} from './creatives.controllers.js';

export default async function creativeRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before routes');
  }

  // Get all creatives
  fastify.post('/get-creatives', {
    onRequest: [fastify.verifyAuth],
    handler: getCreatives
  });

  // Get creative for preview with generation and keyword data
  fastify.get('/:id/preview', {
    onRequest: [fastify.verifyAuth],
    handler: getCreativeForPreview
  });

  // Get creative by ID
  fastify.get('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: getCreativeById
  });

  // Create creative
  fastify.post('/', {
    onRequest: [fastify.verifyAuth],
    handler: createCreative
  });

  // Update creative
  fastify.put('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: updateCreative
  });

  // Delete creative
  fastify.delete('/:id', {
    onRequest: [fastify.verifyAuth],
    handler: deleteCreative
  });
}
