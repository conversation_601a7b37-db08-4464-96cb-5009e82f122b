# Creatives Module

The Creatives module manages the final output of the generation process: the `creative`. A creative is a concrete piece of advertising material that is linked to a specific `generation`.

## Data Model

A `creative` is the child of a `generation`. This one-to-many relationship allows a single generation request to result in multiple creative variations. The `creatives` table stores references to the final ad assets (`previewUrls`) and their status.

```mermaid
erDiagram
    generations {
        uuid id PK
        text name
    }
    creatives {
        uuid id PK
        uuid generationId FK
        text status
        jsonb previewUrls
        text keyword
    }
    brands {
        uuid id PK
        text name
    }
    users {
        uuid id PK
        text name
    }

    generations ||--|{ creatives : "produces"
    creatives }|--|| brands : "belongs to"
    creatives }|--|| users : "created by"
```

## API Endpoints

All endpoints require authentication.

---

### POST /api/creatives/get-creatives

-   **Description:** Retrieves a paginated list of creatives, with optional filtering by `brandId`.
-   **Controller Logic:** Fetches creatives and joins them with `brands` and `users` to include the brand and user names in the response.
-   **Request Body:**
    ```json
    {
      "brandId": "uuid",
      "page": 1,
      "pageSize": 20
    }
    ```
-   **Successful Response `200`:**
    ```json
    {
      "data": [
        {
          "id": "uuid",
          "status": "DRAFT",
          "keyword": "running shoes",
          "brandName": "Example Brand",
          "userName": "John Doe"
        }
      ],
      "pagination": {
        "total": 1,
        "page": 1,
        "pageSize": 20,
        "totalPages": 1
      }
    }
    ```

---

### GET /api/creatives/:id/preview

-   **Description:** Retrieves all the necessary data to render a preview of a single creative.
-   **Controller Logic:** This is a data aggregation endpoint that fetches the specified `creative`, its parent `generation`, and the `keyword` associated with that generation. This provides the client with all the context needed to display a meaningful preview.
-   **Successful Response `200`:**
    ```json
    {
      "creative": { ... },
      "generation": { ... },
      "keyword": { ... }
    }
    ```

---

### POST /api/creatives

-   **Description:** Creates a new creative record.
-   **Controller Logic:** This operation is wrapped in a database transaction (`db.transaction`) to ensure atomicity. If any part of the insertion fails, the entire operation is rolled back, preventing orphaned data.
-   **Request Body:**
    ```json
    {
      "brandId": "uuid",
      "userId": "uuid",
      "generationId": "uuid",
      "status": "DRAFT",
      "keyword": "running shoes",
      "previewUrls": {
        "desktop": "https://example.com/preview_desktop.png",
        "mobile": "https://example.com/preview_mobile.png"
      }
    }
    ```
-   **Successful Response `201`:** Returns the newly created creative object.

---

### PUT /api/creatives/:id

-   **Description:** Updates an existing creative.
-   **Successful Response `200`:** Returns the updated creative object.

---

### DELETE /api/creatives/:id

-   **Description:** Deletes a creative.
-   **Successful Response `204`:** No content.
