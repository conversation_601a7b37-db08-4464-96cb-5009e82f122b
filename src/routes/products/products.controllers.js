import { db, sql } from '../../db/index.js'
import { products } from '../../db/schema/products.js'
import { keywords } from '../../db/schema/keywords.js'
import { imageMetadata } from '../../db/schema/imageMetadata.js'
import { brands } from '../../db/schema/brands.ts'
import { credentialSets } from '../../db/schema/credentialSets.ts'
import { eq, or, and, like, count } from 'drizzle-orm'
import axios from "axios";
import { logger } from "../../utils/logger.js";
import { executeServiceDbQuery } from '../../db/serviceDb.js'

/**
 * Retrieves all products with pagination
 */
export async function getProducts(request, reply) {
    try {
        const { page = 1, limit = 10 } = request.query

        // Convert page and limit to numbers and calculate offset
        const pageNum = parseInt(page)
        const limitNum = parseInt(limit)
        const offset = (pageNum - 1) * limitNum

        // Get total count for pagination
        const [{ count: totalCount }] = await db
            .select({ count: count() })
            .from(products)

        // Get paginated products
        const prods = await db
            .select()
            .from(products)
            .limit(limitNum)
            .offset(offset)

        return reply.send({
            data: prods,
            pagination: {
                total: totalCount,
                page: pageNum,
                limit: limitNum,
                totalPages: Math.ceil(totalCount / limitNum)
            }
        })
    } catch (error) {
        request.log.error(error)
        return reply.code(500).send({ error: 'Failed to fetch products' })
    }
}

/**
 * Retrieves a paginated list of products for a given account, with optional filtering by brand, category, and search term.
 *
 * Combines each product with its associated image metadata and an empty keywords array (keyword fetching is not yet implemented).
 * Returns the product data along with pagination metadata.
 *
 * Responds with HTTP 500 and an error message if an error occurs.
 */
export async function getProductsByAccountId(request, reply) {
    try {
        const { accountId } = request.params
        const { brandId, category, search, page = 1, limit = 10 } = request.query

        // Convert page and limit to numbers and calculate offset
        const pageNum = parseInt(page)
        const limitNum = parseInt(limit)
        const offset = (pageNum - 1) * limitNum

        // Build the where conditions
        let whereConditions = [eq(products.accountId, accountId)]

        if (brandId !== undefined) {
            whereConditions.push(eq(products.brandId, brandId))
        }

        if (category !== undefined) {
            whereConditions.push(eq(products.category, category))
        }

        if (search !== undefined) {
            whereConditions.push(
                or(
                    like(products.productId, `%${search}%`),
                    like(products.productTitle, `%${search}%`)
                )
            )
        }

        // Get total count for pagination
        const [{ count: totalCount }] = await db
            .select({ count: count() })
            .from(products)
            .where(and(...whereConditions))

        // Get paginated products
        const prods = await db
            .select()
            .from(products)
            .where(and(...whereConditions))
            .limit(limitNum)
            .offset(offset)


        // Fetch image metadata for all products
        const imageMetadataData = await db
            .select()
            .from(imageMetadata)
            .where(
                sql`${imageMetadata.productId} IN (${prods.map(p => `'${p.productId}'`).join(',')})`
            );

        // Combine products with their keywords and image metadata
        const productsWithKeywords = prods.map(product => ({
            ...product,
            keywords: [], // TODO: Implement keyword fetching through generations
            /*
            keywords: productKeywordsData
                .filter(kw => kw.productId === product.id && kw.keywordId !== null)
                .map(kw => ({
                    id: kw.keywordId,
                    keyword: kw.keyword,
                    include: kw.include,
                    exclude: kw.exclude
                })) || [],
            */
            imageMetadata: imageMetadataData
                .filter(meta => meta.productId === product.productId)
                .map(meta => ({
                    imageUrl: meta.imageUrl,
                    imageText: meta.imageText,
                    classification: meta.classification,
                    createdAt: meta.createdAt,
                    updatedAt: meta.updatedAt
                })) || []
        }));

        return reply.send({
            data: productsWithKeywords,
            pagination: {
                total: totalCount,
                page: pageNum,
                limit: limitNum,
                totalPages: Math.ceil(totalCount / limitNum)
            }
        })
    } catch (error) {
        request.log.error(error)
        return reply.code(500).send({ error: 'Failed to fetch products' })
    }
}

export async function getProductByProductId(request, reply) {
    try {
        const { productId } = request.params

        // Get the single product
        const [product] = await db
            .select()
            .from(products)
            .where(eq(products.productId, productId))

        if (!product) {
            return reply.code(404).send({ error: 'Product not found' })
        }


        // Fetch image metadata for the product
        const imageMetadataData = await db
            .select()
            .from(imageMetadata)
            .where(eq(imageMetadata.productId, productId));

        // Combine product with its keywords and image metadata
        const productWithKeywords = {
            ...product,
            keywords: [], // TODO: Implement keyword fetching through generations
            /*
            keywords: productKeywordsData
                .filter(kw => kw.keywordId !== null)
                .map(kw => ({
                    id: kw.keywordId,
                    keyword: kw.keyword,
                    include: kw.include,
                    exclude: kw.exclude
                })) || [],
            */
            imageMetadata: imageMetadataData.map(meta => ({
                imageUrl: meta.imageUrl,
                imageText: meta.imageText,
                classification: meta.classification,
                createdAt: meta.createdAt,
                updatedAt: meta.updatedAt
            })) || []
        };

        return reply.send(productWithKeywords)
    } catch (error) {
        request.log.error(error)
        return reply.code(500).send({ error: 'Failed to fetch product' })
    }
}

export async function deleteProduct(request, reply) {
    try {
        const { productId, accountId } = request.body

        if (!productId || !accountId) {
            return reply.code(400).send({ error: 'Product ID and Account ID are required' })
        }

        console.log('checking product', productId, accountId)

        // First check if the product exists
        const [existingProduct] = await db
            .select()
            .from(products)
            .where(
                and(
                    eq(products.id, productId),
                    eq(products.accountId, accountId)
                )
            )

        if (!existingProduct) {
            return reply.code(404).send({ error: 'Product not found' })
        }

        console.log('deleting product', productId, accountId)

        // IMPORTANT: First delete related image metadata
        await db
            .delete(imageMetadata)
            .where(eq(imageMetadata.productId, existingProduct.productId))


        // Now delete the product
        await db
            .delete(products)
            .where(
                and(
                    eq(products.id, productId),
                    eq(products.accountId, accountId)
                )
            )

        return reply.code(200).send({
            success: true,
            message: 'Product deleted successfully',
            productId
        })
    } catch (error) {
        request.log.error(error)
        return reply.code(500).send({
            error: 'Failed to delete product',
            details: error.message
        })
    }
}

export async function saveCustomImages(request, reply) {
    try {
        const { productId, customImages } = request.body

        if (!productId) {
            return reply.code(400).send({ error: 'Product ID is required' })
        }

        if (!customImages || !Array.isArray(customImages)) {
            return reply.code(400).send({ error: 'Custom images must be an array' })
        }

        // Validate that all items in customImages are valid URLs or objects with imageUrl
        const validatedImages = customImages.map(image => {
            if (typeof image === 'string') {
                // If it's a string, treat it as a URL
                return image
            } else if (typeof image === 'object' && image.imageUrl) {
                // If it's an object with imageUrl, validate it
                return image.imageUrl
            } else {
                throw new Error('Invalid image format - must be URL string or object with imageUrl')
            }
        })

        // First check if the product exists
        const [existingProduct] = await db
            .select()
            .from(products)
            .where(eq(products.id, productId))

        if (!existingProduct) {
            return reply.code(404).send({ error: 'Product not found' })
        }

        console.log('existingProduct', existingProduct)

        // Get existing custom images and merge with new ones
        const existingCustomImages = existingProduct.customImages || []
        const mergedCustomImages = [...existingCustomImages, ...validatedImages]

        // Update the product's customImages field
        const [updatedProduct] = await db
            .update(products)
            .set({ 
                customImages: mergedCustomImages
            })
            .where(eq(products.id, productId))
            .returning()

        console.log('updatedProduct', updatedProduct)

        return reply.code(200).send({
            success: true,
            message: 'Custom images saved successfully',
            productId,
            customImages: updatedProduct.customImages
        })
    } catch (error) {
        request.log.error(error)
        return reply.code(500).send({
            error: 'Failed to save custom images',
            details: error.message
        })
    }
}

/**
 * Fetches products for each brand from Walmart API and stores them in the database.
 * This runs asynchronously in the background after credential set creation.
 * Step 2: Background Product Fetching (Asynchronous, Decoupled) - SINGLE API CALL APPROACH
 *
 * @param {Array} brands - Array of brand objects from database
 * @param {string} accountId - Account ID to associate with products
 * @param {object} request - HTTP request object containing authorization headers
 */
export async function fetchProductsForBrandsInBackground(brands, accountId, request) {
  console.log(`Starting single API call product fetch for ${brands.length} brands`);
  
  const BATCH_SIZE = 500;
  let totalProductsFound = 0;
  let totalProductsStored = 0;
  let successfulBrands = 0;
  let failedBrands = 0;
  
  try {
    // Get the credential set (all brands should have same advertiser ID)
    if (brands.length === 0) {
      console.log('No brands to process');
      return;
    }
    
    const [firstCredentialSet] = await db
      .select()
      .from(credentialSets)
      .where(eq(credentialSets.id, brands[0].primaryCredentialId))
      .limit(1);
    
    if (!firstCredentialSet) {
      console.error('No credential set found');
      return;
    }
    
    const wmAdvertiserId = firstCredentialSet.credentials?.wmAdvertiserId;
    if (!wmAdvertiserId) {
      console.error('No advertising ID found');
      return;
    }
    
    // Phase 1: Fetch ALL products for this advertiser ID in one go
    console.log(`Phase 1: Fetching ALL products for advertiser ID: ${wmAdvertiserId}`);
    const allProducts = await fetchAllProductsForAdvertiser(wmAdvertiserId, request);
    totalProductsFound = allProducts.length;
    console.log(`Found ${totalProductsFound} total products for advertiser`);
    
    if (allProducts.length === 0) {
      console.log('No products found for this advertiser');
      return;
    }
    
    // Phase 2: Map products to brands and transform for database
    console.log('Phase 2: Mapping products to brands...');
    const brandNameToIdMap = new Map();
    brands.forEach(brand => {
      brandNameToIdMap.set(brand.name.toLowerCase(), brand);
    });
    
    const allTransformedProducts = [];
    const brandProductCounts = new Map();
    
    for (const product of allProducts) {
      const productBrandName = (product.brandName || '').toLowerCase();
      const matchedBrand = brandNameToIdMap.get(productBrandName);
      
      if (matchedBrand) {
        const transformedProduct = transformSingleProduct(product, accountId, matchedBrand.id);
        if (transformedProduct) {
          allTransformedProducts.push(transformedProduct);
          brandProductCounts.set(matchedBrand.id, (brandProductCounts.get(matchedBrand.id) || 0) + 1);
        }
      }
    }
    
    console.log(`Mapped ${allTransformedProducts.length} products to ${brandProductCounts.size} brands`);
    
    // Phase 3: Batch insert all products
    if (allTransformedProducts.length > 0) {
      console.log('Phase 3: Batch inserting all products...');
      const batchResult = await batchInsertProducts(allTransformedProducts, BATCH_SIZE);
      totalProductsStored = batchResult.totalInserted;
      console.log(`Phase 3 Complete: Inserted ${totalProductsStored} products`);
    }
    
    // Phase 4: Update brand product counts based on actual stored products
    console.log('Phase 4: Updating brand product counts...');
    for (const [brandId, count] of brandProductCounts) {
      try {
        await db.execute(`UPDATE brands SET product_count = ${count}, updated_at = NOW() WHERE id = '${brandId}'`);
        successfulBrands++;
      } catch (error) {
        console.error(`Error updating brand ${brandId}:`, error);
        failedBrands++;
      }
    }
    
    console.log(`Products stored: ${totalProductsStored}`);
    
    
  } catch (error) {
    console.error('Error in single API call approach:', error);
  }
}

/**
 * Fetches ALL products for an advertiser ID in one API call with pagination
 */
async function fetchAllProductsForAdvertiser(wmAdvertiserId, request) {
  const allProducts = [];
  const pageSize = 100;
  let startIndex = 0;
  let hasMorePages = true;
  
  while (hasMorePages) {
    try {
      console.log(`Fetching products page: startIndex=${startIndex}, pageSize=${pageSize}`);
      
      const response = await axios({
        method: 'post',
        url: `${process.env.WALMART_MANAGER_API_URL}/display/catalog/items/list/`,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': request.headers.authorization,
          'x-account-id': request.headers['x-account-id'],
        },
        data: {
          advertiserId: wmAdvertiserId,
          "filter[catalog]": "online",
          startIndex: startIndex,
          count: pageSize
        }
      });

      if (response.status === 200 && response.data.code === 'success') {
        const products = response.data.data || [];
        allProducts.push(...products);
        
        console.log(`Page complete: Got ${products.length} products (total: ${allProducts.length})`);
        
        hasMorePages = products.length === pageSize;
        startIndex += pageSize;
        
        if (hasMorePages) {
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      } else {
        console.error('API error:', response.data.message);
        hasMorePages = false;
      }
      
    } catch (error) {
      if (error.response?.status === 429) {
        console.warn(`Rate limit hit, waiting 2 seconds...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      } else {
        console.error('Error fetching products:', error.message);
        hasMorePages = false;
      }
    }
  }
  
  return allProducts;
}

/**
 * Transform single product to database format
 */
function transformSingleProduct(productData, accountId, brandId) {
  try {
    const productId = productData.itemId || productData.productId;
    
    if (!productId || (!productData.itemName && !productData.productTitle)) {
      return null;
    }
    
    return {
      productId: String(productId),
      productTitle: productData.itemName || productData.productTitle,
      productType: productData.productType || null,
      category: productData.category || productData.department || null,
      thumbnailUrl: productData.imageUrl || productData.thumbnailUrl || null,
      shortDescription: productData.shortDescription || null,
      longDescription: productData.longDescription || null,
      genAiDescription: null,
      specifications: productData.specifications || [],
      productHighlights: productData.productHighlights || [],
      classType: productData.classType || null,
      upc: productData.upc || null,
      images: productData.images || [],
      customImages: [],
      accountId: accountId,
      brandId: brandId,
      pdpSummary: null
    };
  } catch (error) {
    console.error('Error transforming product:', error);
    return null;
  }
}

/**
 * Inserts products into database in batches for optimal performance
 * @param {Array} allProducts - All products to insert
 * @param {number} batchSize - Size of each batch
 * @returns {object} Insert results with counts
 */
async function batchInsertProducts(allProducts, batchSize) {
  let totalInserted = 0;
  let totalBatches = 0;
  let totalErrors = 0;
  
  // Remove duplicates across all products (same productId)
  const uniqueProducts = new Map();
  for (const product of allProducts) {
    // Keep the first occurrence of each productId
    if (!uniqueProducts.has(product.productId)) {
      uniqueProducts.set(product.productId, product);
    }
  }
  
  const productsToInsert = Array.from(uniqueProducts.values());
  console.log(`Deduplicated: ${allProducts.length} → ${productsToInsert.length} unique products`);
  
  // Insert in batches
  for (let i = 0; i < productsToInsert.length; i += batchSize) {
    const batch = productsToInsert.slice(i, i + batchSize);
    totalBatches++;
    
    try {
      console.log(`Inserting batch ${totalBatches}: ${batch.length} products (${i + 1}-${Math.min(i + batchSize, productsToInsert.length)} of ${productsToInsert.length})`);
      
      const result = await db
        .insert(products)
        .values(batch)
        .returning({ productId: products.productId });
      
      totalInserted += result.length;
      console.log(`✓ Batch ${totalBatches} completed: ${result.length} products inserted/updated`);
      
      // Small delay between batches to avoid overwhelming the database
      if (i + batchSize < productsToInsert.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
    } catch (error) {
      console.error(`✗ Batch ${totalBatches} failed:`, error.message);
      totalErrors++;
      
      // Try to insert products individually in this batch to identify problematic ones
      for (const product of batch) {
        try {
          await db
            .insert(products)
            .values([product]);
          totalInserted++;
        } catch (individualError) {
          console.error(`Failed to insert individual product ${product.productId}:`, individualError.message);
        }
      }
    }
  }
  
  return {
    totalInserted,
    batches: totalBatches,
    errors: totalErrors,
    deduplicatedCount: productsToInsert.length,
    originalCount: allProducts.length
  };
}

/**
 * Updates product counts for brands based on actual stored products
 * @param {Array} brandResults - Results from product collection phase
 */
async function updateBrandProductCounts(brandResults) {
  const updatePromises = brandResults
    .filter(result => result.success)
    .map(async (result) => {
      try {
        // Count actual products stored for this brand
        const [countResult] = await db
          .select({ count: sql`count(*)` })
          .from(products)
          .where(eq(products.brandId, result.brand.id));
        
        const actualProductCount = parseInt(countResult.count) || 0;
        
        // Update brand with actual product count
        await db
          .update(brands)
          .set({
            productCount: actualProductCount,
            updatedAt: new Date()
          })
          .where(eq(brands.id, result.brand.id));
        
        console.log(`Updated brand ${result.brand.name}: ${actualProductCount} products`);
        
      } catch (error) {
        console.error(`Error updating product count for brand ${result.brand.name}:`, error);
      }
    });
  
  await Promise.allSettled(updatePromises);
  console.log(`Updated product counts for ${updatePromises.length} brands`);
}

/**
 * Test endpoint to manually trigger background product fetching for brands.
 * This is useful for testing the background product fetch workflow.
 * 
 * @param {object} request - HTTP request object
 * @param {object} reply - HTTP reply object
 */
export async function testBackgroundProductFetch(request, reply) {
  try {
    //const { 'credential-set-id': credentialSetId } = request.params;
    const { credentialSetId } = request.params;
    
    if (!credentialSetId) {
      return reply.code(400).send({ error: 'Credential set ID is required' });
    }
    
    // Get brands for this credential set
    const brandsToFetch = await db
      .select()
      .from(brands)
      .where(eq(brands.primaryCredentialId, credentialSetId));
    
    if (brandsToFetch.length === 0) {
      return reply.code(404).send({ error: 'No brands found for this credential set' });
    }
    
    // Get the credential set to find the account ID
    const [credentialSet] = await db
      .select()
      .from(credentialSets)
      .where(eq(credentialSets.id, credentialSetId))
      .limit(1);
    
    if (!credentialSet) {
      return reply.code(404).send({ error: 'Credential set not found' });
    }
    
    // Send immediate response
    const response = {
      message: `Starting background product fetch for ${brandsToFetch.length} brands`,
      credentialSetId,
      accountId: credentialSet.accountId,
      brands: brandsToFetch.map(b => ({ id: b.id, name: b.name }))
    };
    
    // Start background process
    setImmediate(async () => {
      try {
        console.log(`[TEST] Starting background product fetch for credential set: ${credentialSetId}`);
        await fetchProductsForBrandsInBackground(brandsToFetch, credentialSet.accountId, request);
        console.log(`[TEST] Completed background product fetch for credential set: ${credentialSetId}`);
      } catch (error) {
        console.error(`[TEST] Background product fetch failed for credential set ${credentialSetId}:`, error);
      }
    });
    
    return reply.send(response);
    
  } catch (error) {
    console.error('Test background product fetch error:', error);
    return reply.code(500).send({ error: 'Internal server error' });
  }
}

// Clipdrop Bg Removal n8n flow hits this endpoint to save the transparent hero image to the product
export async function saveTransparentHeroImage(request, reply) {
  try {
    const { id, transparentHeroImage } = request.body;

    if (!id || !transparentHeroImage) {
      return reply.code(400).send({ error: 'Product ID and transparent hero image URL are required' });
    }

    // Check if the product exists
    const [existingProduct] = await db
      .select()
      .from(products)
      .where(eq(products.id, id));

    if (!existingProduct) {
      return reply.code(404).send({ error: 'Product not found' });
    }

    // Update the transparent hero image URL
    const [updatedProduct] = await db
      .update(products)
      .set({ transparentThumbnailUrl: transparentHeroImage })
      .where(eq(products.id, id))
      .returning();

    return reply.code(200).send({
      success: true,
      message: 'Transparent hero image saved successfully',
      product: updatedProduct
    });
  } catch (error) {
    console.error('Error saving transparent hero image:', error);
    return reply.code(500).send({ error: 'Internal server error' });
  }
}

export async function getItemsToScrape(request, reply) {
  try {

    const batchSize = request.query.batchSize || 30;

    // Validate batch size to prevent abuse
    if (batchSize < 1 || batchSize > 100) {
      return reply.code(400).send({ error: 'Batch size must be between 1 and 100' });
    }

    // Use the centralized database connection utility
    const query = `
      SELECT *
      FROM "item-jobs"
      WHERE status = 'queued'
      ORDER BY created_at ASC
      LIMIT $1
    `;
    
    const result = await executeServiceDbQuery(query, [batchSize]);

    const items = result.rows.map(item => item.key)

    return reply.send({
      success: true,
      data: items,
      count: result.rowCount
    });

  } catch (error) {
    console.error('Error getting items to scrape:', error);
    return reply.code(500).send({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

export async function processScrapeResponse(request, reply) {
  try {
    const { data } = request.body;

    // Input validation
    if (!data || !Array.isArray(data)) {
      return reply.code(400).send({ 
        error: 'Invalid data format. Expected array of items.' 
      });
    }

    if (data.length === 0) {
      return reply.send({
        success: true,
        message: 'No items to process',
        results: []
      });
    }

    const results = [];
    const errors = [];

    // Process items sequentially to avoid overwhelming the database
    for (const item of data) {
      try {
        // Validate item structure
        if (!item.itemId || !item.data) {
          errors.push({
            itemId: item.itemId || 'unknown',
            error: 'Missing itemId or data'
          });
          continue;
        }

        // Prepare update data, only including fields that exist
        const updateData = {};
        if (item.data.title !== undefined) updateData.productTitle = item.data.title;
        if (item.data.shortDescription !== undefined) updateData.shortDescription = item.data.shortDescription;
        if (item.data.longDescription !== undefined) updateData.longDescription = item.data.longDescription;
        if (item.data.productType !== undefined) updateData.productType = item.data.productType;
        if (item.data.images !== undefined) updateData.images = item.data.images;
        if (item.data.specifications !== undefined) updateData.specifications = item.data.specifications;
        if (item.data.upc !== undefined) updateData.upc = item.data.upc;

        // Add updatedAt timestamp
        updateData.updatedAt = new Date();

        const [updatedProduct] = await db
          .update(products)
          .set(updateData)
          .where(eq(products.productId, item.itemId))
          .returning({ id: products.id, productId: products.productId });

        if (updatedProduct) {

          const updateQuery = `
            UPDATE "item-jobs"
            SET status = 'completed', updated_at = NOW()
            WHERE key = $1
          `;

          await executeServiceDbQuery(updateQuery, [item.itemId]);

          results.push({
            itemId: item.itemId,
            success: true,
            productId: updatedProduct.id
          });
        } else {
          errors.push({
            itemId: item.itemId,
            error: 'Product not found'
          });
        }

      } catch (updateError) {
        console.error(`Error updating product ${item.itemId}:`, updateError);
        errors.push({
          itemId: item.itemId,
          error: updateError.message
        });
      }
    }

    const response = {
      success: true,
      processed: results.length,
      errors: errors.length,
      total: data.length,
      results: results
    };

    if (errors.length > 0) {
      response.errors = errors;
    }

    console.log(`Processed ${results.length}/${data.length} items successfully`);

    return reply.send(response);

  } catch (error) {
    console.error('Error processing scrape response:', error);
    return reply.code(500).send({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
}