export const productSchema = {
  // GET /:accountId - Get products by account with optional filtering
  getByAccountId: {
    params: {
      type: 'object',
      required: ['accountId'],
      properties: {
        accountId: { type: 'string', format: 'uuid' }
      }
    },
    querystring: {
      type: 'object',
      properties: {
        brandId: { type: 'string', format: 'uuid' },
        category: { type: 'string' },
        search: { type: 'string' },
        page: { type: 'integer', minimum: 1, default: 1 },
        limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                productId: { type: 'string' },
                productTitle: { type: 'string' },
                productType: { type: ['string', 'null'] },
                category: { type: ['string', 'null'] },
                thumbnailUrl: { type: ['string', 'null'] },
                transparentThumbnailUrl: { type: ['string', 'null'] },
                shortDescription: { type: ['string', 'null'] },
                longDescription: { type: ['string', 'null'] },
                genAiDescription: { type: ['string', 'null'] },
                specifications: { type: 'array' },
                productHighlights: { type: 'array' },
                classType: { type: ['string', 'null'] },
                upc: { type: ['string', 'null'] },
                gtin: { type: ['string', 'null'] },
                images: { type: 'array' },
                customImages: { type: 'array' },
                accountId: { type: ['string', 'null'], format: 'uuid' },
                brandId: { type: ['string', 'null'], format: 'uuid' },
                pdpSummary: { type: ['string', 'null'] },
                keywords: { type: 'array' },
                imageMetadata: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      imageUrl: { type: 'string' },
                      imageText: { type: ['string', 'null'] },
                      classification: { type: ['string', 'null'] },
                      createdAt: { type: 'string' },
                      updatedAt: { type: 'string' }
                    }
                  }
                }
              }
            }
          },
          pagination: {
            type: 'object',
            properties: {
              total: { type: 'integer' },
              page: { type: 'integer' },
              limit: { type: 'integer' },
              totalPages: { type: 'integer' }
            }
          }
        }
      },
      500: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      }
    }
  },

  // GET /product/:productId - Get single product by ID
  getByProductId: {
    params: {
      type: 'object',
      required: ['productId'],
      properties: {
        productId: { type: 'string', minLength: 1 }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          productId: { type: 'string' },
          productTitle: { type: 'string' },
          productType: { type: ['string', 'null'] },
          category: { type: ['string', 'null'] },
          thumbnailUrl: { type: ['string', 'null'] },
          transparentThumbnailUrl: { type: ['string', 'null'] },
          shortDescription: { type: ['string', 'null'] },
          longDescription: { type: ['string', 'null'] },
          genAiDescription: { type: ['string', 'null'] },
          specifications: { type: 'array' },
          productHighlights: { type: 'array' },
          classType: { type: ['string', 'null'] },
          upc: { type: ['string', 'null'] },
          gtin: { type: ['string', 'null'] },
          images: { type: 'array' },
          customImages: { type: 'array' },
          accountId: { type: ['string', 'null'], format: 'uuid' },
          brandId: { type: ['string', 'null'], format: 'uuid' },
          pdpSummary: { type: ['string', 'null'] },
          imageMetadata: { type: 'array' },
          keywords: { type: 'array' }
        }
      },
      404: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      }
    }
  },

  // POST /delete - Delete a product
  delete: {
    body: {
      type: 'object',
      required: ['productId', 'accountId'],
      properties: {
        productId: { type: 'string', format: 'uuid' },
        accountId: { type: 'string', format: 'uuid' }
      }
    }
  },

  // POST /save-custom-images - Save custom images for a product
  saveCustomImages: {
    body: {
      type: 'object',
      required: ['productId', 'customImages'],
      properties: {
        productId: { type: 'string', format: 'uuid' },
        customImages: {
          type: 'array',
          items: {
            oneOf: [
              { type: 'string', format: 'uri' },
              {
                type: 'object',
                required: ['imageUrl'],
                properties: {
                  imageUrl: { type: 'string', format: 'uri' },
                  altText: { type: 'string' },
                  caption: { type: 'string' }
                }
              }
            ]
          }
        }
      }
    }
  },

  // POST /test-background-fetch/:credentialSetId - Test background product fetching
  testBackgroundFetch: {
    params: {
      type: 'object',
      required: ['credentialSetId'],
      properties: {
        credentialSetId: { type: 'string', format: 'uuid' }
      }
    }
  },

  // POST /save-transparent-hero-image - Save transparent hero image
  saveTransparentHeroImage: {
    body: {
      type: 'object',
      required: ['id', 'transparentHeroImage'],
      properties: {
        id: { type: 'string', format: 'uuid' },
        transparentHeroImage: { type: 'string', format: 'uri' }
      }
    }
  },

  // Product object schema for responses and future create/update operations
  product: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
      productId: { type: 'string' },
      productTitle: { type: 'string' },
      productType: { type: 'string' },
      category: { type: 'string' },
      thumbnailUrl: { type: ['string', 'null'], format: 'uri' },
      transparentThumbnailUrl: { type: ['string', 'null'], format: 'uri' },
      shortDescription: { type: ['string', 'null'] },
      longDescription: { type: ['string', 'null'] },
      genAiDescription: { type: ['string', 'null'] },
      specifications: { type: 'array', default: [] },
      productHighlights: { type: 'array', default: [] },
      classType: { type: ['string', 'null'] },
      upc: { type: ['string', 'null'] },
      gtin: { type: ['string', 'null'] },
      images: { type: 'array', default: [] },
      customImages: { type: 'array', default: [] },
      accountId: { type: ['string', 'null'], format: 'uuid' },
      brandId: { type: ['string', 'null'], format: 'uuid' },
      pdpSummary: { type: ['string', 'null'] }
    }
  },

  // Future-ready schemas for create/update operations
  create: {
    body: {
      type: 'object',
      required: ['productId', 'productTitle', 'accountId'],
      properties: {
        productId: { type: 'string', minLength: 1 },
        productTitle: { type: 'string', minLength: 1 },
        productType: { type: 'string' },
        category: { type: 'string' },
        thumbnailUrl: { type: 'string', format: 'uri' },
        transparentThumbnailUrl: { type: 'string', format: 'uri' },
        shortDescription: { type: 'string' },
        longDescription: { type: 'string' },
        genAiDescription: { type: 'string' },
        specifications: { type: 'array', default: [] },
        productHighlights: { type: 'array', default: [] },
        classType: { type: 'string' },
        upc: { type: 'string' },
        gtin: { type: 'string' },
        images: { type: 'array', default: [] },
        customImages: { type: 'array', default: [] },
        accountId: { type: 'string', format: 'uuid' },
        brandId: { type: 'string', format: 'uuid' },
        pdpSummary: { type: 'string' }
      }
    }
  },

  update: {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string', format: 'uuid' }
      }
    },
    body: {
      type: 'object',
      properties: {
        productTitle: { type: 'string', minLength: 1 },
        productType: { type: 'string' },
        category: { type: 'string' },
        thumbnailUrl: { type: ['string', 'null'], format: 'uri' },
        transparentThumbnailUrl: { type: ['string', 'null'], format: 'uri' },
        shortDescription: { type: ['string', 'null'] },
        longDescription: { type: ['string', 'null'] },
        genAiDescription: { type: ['string', 'null'] },
        specifications: { type: 'array' },
        productHighlights: { type: 'array' },
        classType: { type: ['string', 'null'] },
        upc: { type: ['string', 'null'] },
        gtin: { type: ['string', 'null'] },
        images: { type: 'array' },
        customImages: { type: 'array' },
        brandId: { type: ['string', 'null'], format: 'uuid' },
        pdpSummary: { type: ['string', 'null'] }
      }
    }
  }
}; 