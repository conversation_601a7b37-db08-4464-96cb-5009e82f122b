import {
  getProducts,
  getProductsByAccountId,
  getProductByProductId,
  deleteProduct,
  saveCustomImages,
  testBackgroundProductFetch,
  saveTransparentHeroImage,
  getItemsToScrape,
  processScrapeResponse,
} from "./products.controllers.js";
import { eq, and, inArray } from "drizzle-orm";
import { db } from "../../db/index.js";
import { userAccounts } from "../../db/schema/userAccounts.ts";
import { credentialSets } from "../../db/schema/credentialSets.ts";
import { authService } from "../../services/authorization.service.js";

/**
 * Middleware that verifies the authenticated user's access to a specific account based on their role.
 *
 * Responds with 401 if no session user is found, 403 if the user lacks access to the account, or 500 on internal errors. Attaches the user's role on the account to the request object for downstream handlers.
 */
async function verifyAccountAccess(request, reply) {
  try {
    const { accountId } = request.params;
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: "Unauthorized - No session found" });
    }

    // Use AuthorizationService to check account access
    const userRole = await authService.getUserAccountRole(
      sessionUser.id,
      accountId,
    );

    if (!userRole) {
      return reply.code(403).send({ error: "Access denied to this account" });
    }

    // Add role to request for use in controllers
    request.userAccountRole = userRole;
  } catch (error) {
    console.error("Error verifying account access:", error);
    return reply.code(500).send({ error: "Internal server error" });
  }
}

/**
 * Middleware that verifies account access for routes that use credentialSetId instead of accountId.
 * Looks up the accountId from the credentialSetId and then verifies access.
 */
async function verifyAccountAccessViaCredentialSet(request, reply) {
  try {
    const { credentialSetId } = request.params;
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: "Unauthorized - No session found" });
    }

    // Get the credential set to find the accountId
    const [credentialSet] = await db
      .select({ accountId: credentialSets.accountId })
      .from(credentialSets)
      .where(eq(credentialSets.id, credentialSetId))
      .limit(1);

    if (!credentialSet) {
      return reply.code(404).send({ error: "Credential set not found" });
    }

    // Use AuthorizationService to check account access
    const userRole = await authService.getUserAccountRole(
      sessionUser.id,
      credentialSet.accountId,
    );

    if (!userRole) {
      return reply.code(403).send({ error: "Access denied to this account" });
    }

    // Add role to request for use in controllers
    request.userAccountRole = userRole;
  } catch (error) {
    console.error("Error verifying account access via credential set:", error);
    return reply.code(500).send({ error: "Internal server error" });
  }
}

export default async function productMgmtRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator("verifyAuth")) {
    throw new Error("Auth plugin must be registered before routes");
  }

  fastify.get("/", {
    onRequest: [fastify.verifyAuth],
    handler: getProducts,
  });

  fastify.get("/:accountId", {
    onRequest: [fastify.verifyAuth],
    handler: getProductsByAccountId,
  });

  fastify.get("/product/:productId", {
    onRequest: [fastify.verifyAuth],
    handler: getProductByProductId,
  });

  fastify.post("/delete", {
    onRequest: [fastify.verifyAuth],
    handler: deleteProduct,
  });

  fastify.post("/save-custom-images", {
    onRequest: [fastify.verifyAuth],
    handler: saveCustomImages,
  });

  // Test background product fetching
  fastify.post("/test-background-fetch/:credentialSetId", {
    onRequest: [fastify.verifyAuth, verifyAccountAccessViaCredentialSet],
    handler: testBackgroundProductFetch,
  });

  fastify.post("/save-transparent-hero-image", {
    onRequest: [fastify.verifyAuth],
    handler: saveTransparentHeroImage,
  });

  //Walmart Scraping Routes
  //Get Items Needed to Scrape
  fastify.get("/get-items-to-scrape", {
    onRequest: [fastify.verifyAuth],
    handler: getItemsToScrape,
  });

  //Process and Save Scrape Response
  fastify.post("/process-scrape-response", {
    onRequest: [fastify.verifyAuth],
    handler: processScrapeResponse,
  });
}
