import { emailQueue } from '../../services/emailQueue.service.js';

/**
 * Test Email Routes
 * 
 * Provides endpoints for testing email functionality in staging/development environments.
 * These routes allow direct testing of the email queue service and Redis integration.
 */

/**
 * @param {import('fastify').FastifyInstance} fastify
 * @param {Object} options
 */
export default async function emailRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator('verifyAuth')) {
    throw new Error('Auth plugin must be registered before email routes');
  }

  // Enhanced rate limiting for email endpoints to prevent abuse
  const emailRateLimit = {
    max: 10, // 10 requests per window
    timeWindow: '1 minute',
    errorResponseBuilder: (request, context) => ({
      code: 429,
      error: 'Too Many Email Requests',
      message: `Email rate limit exceeded. You can send ${context.max} emails per ${context.timeWindow}. Retry in ${context.ttl} seconds.`,
      retryAfter: context.ttl
    })
  };

  // Security validation helper for trusted email domains
  const validateTrustedEmail = (email, fieldName = 'email') => {
    if (!email) return;
    
    const allowedDomains = ['adfury.ai', 'adfury.com'];
    const domain = email.split('@')[1];
    
    if (!allowedDomains.includes(domain)) {
      throw new Error(`${fieldName} must be from a trusted domain (${allowedDomains.join(', ')})`);
    }
  };

  // POST /api/email/test-invitation
  fastify.post('/test-invitation', {
    onRequest: [fastify.verifyAuth],
    config: { rateLimit: emailRateLimit },
    schema: {
      description: 'Send a test invitation email',
      tags: ['email', 'test'],
      body: {
        type: 'object',
        required: ['to', 'invitationId'],
        properties: {
          to: { 
            type: 'string', 
            format: 'email',
            description: 'Recipient email address' 
          },
          invitationId: { 
            type: 'string',
            description: 'Unique invitation ID' 
          },
          invitationType: { 
            type: 'string', 
            enum: ['invite', 'request'],
            default: 'invite',
            description: 'Type of invitation' 
          },
          organizationName: { 
            type: 'string',
            description: 'Organization name (optional)' 
          },
          senderName: { 
            type: 'string',
            description: 'Sender name (optional)' 
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { to, invitationId, invitationType = 'invite', organizationName, senderName } = request.body;

      await emailQueue.addInvitationEmail({
        to,
        invitationId,
        invitationType,
        organizationName: organizationName || 'Test Organization',
        senderName: senderName || 'Test User'
      });

      reply.send({
        success: true,
        message: 'Test invitation email queued successfully',
        recipient: to,
        invitationId
      });
    } catch (error) {
      fastify.log.error('Test invitation email error:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to queue test invitation email',
        details: error.message
      });
    }
  });

  // POST /api/email/test-password-reset
  fastify.post('/test-password-reset', {
    onRequest: [fastify.verifyAuth],
    config: { rateLimit: emailRateLimit },
    schema: {
      description: 'Send a test password reset email',
      tags: ['email', 'test'],
      body: {
        type: 'object',
        required: ['to'],
        properties: {
          to: { 
            type: 'string', 
            format: 'email',
            description: 'Recipient email address' 
          },
          resetUrl: { 
            type: 'string',
            description: 'Password reset URL (optional - will use default if not provided)' 
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { to, resetUrl } = request.body;
      
      // Generate a test reset URL if not provided
      const testResetUrl = resetUrl || `${process.env.AUTH0_PASSWORD_RESET_REDIRECT_URL || 'http://localhost:3000/reset-password'}?token=test-token-${Date.now()}`;

      await emailQueue.addPasswordResetEmail({
        to,
        resetUrl: testResetUrl
      });

      reply.send({
        success: true,
        message: 'Test password reset email queued successfully',
        recipient: to,
        resetUrl: testResetUrl
      });
    } catch (error) {
      fastify.log.error('Test password reset email error:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to queue test password reset email',
        details: error.message
      });
    }
  });

  // POST /api/email/test-email-verification
  fastify.post('/test-email-verification', {
    onRequest: [fastify.verifyAuth],
    config: { rateLimit: emailRateLimit },
    schema: {
      description: 'Send a test email verification email',
      tags: ['email', 'test'],
      body: {
        type: 'object',
        required: ['to'],
        properties: {
          to: { 
            type: 'string', 
            format: 'email',
            description: 'Recipient email address' 
          },
          verificationUrl: { 
            type: 'string',
            description: 'Email verification URL (optional - will use default if not provided)' 
          },
          userName: { 
            type: 'string',
            description: 'User name (optional)' 
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { to, verificationUrl, userName } = request.body;
      
      // Generate a test verification URL if not provided
      const baseUrl = process.env.APP_BASE_URL || 'http://localhost:3000';
      const testVerificationUrl = verificationUrl || `${baseUrl}/email-verified?token=test-token-${Date.now()}`;

      await emailQueue.addCustomEmail({
        to,
        subject: 'Verify your email address',
        heading: 'Email Verification Test',
        content: `
          <p>Hello ${userName || 'Test User'},</p>
          <p>This is a test email verification. Please click the button below to verify your email address:</p>
        `,
        buttonText: 'Verify Email Address',
        buttonLink: testVerificationUrl,
        footerText: 'This is a test email from AdFury.',
        fromName: 'AdFury Team'
      });

      reply.send({
        success: true,
        message: 'Test email verification email queued successfully',
        recipient: to,
        verificationUrl: testVerificationUrl
      });
    } catch (error) {
      fastify.log.error('Test email verification email error:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to queue test email verification email',
        details: error.message
      });
    }
  });

  // POST /api/email/test-beta-invitation
  fastify.post('/test-beta-invitation', {
    onRequest: [fastify.verifyAuth],
    config: { rateLimit: emailRateLimit },
    schema: {
      description: 'Send a test beta invitation email',
      tags: ['email', 'test'],
      body: {
        type: 'object',
        required: ['to', 'senderEmail', 'senderName', 'betaCode'],
        properties: {
          to: { 
            type: 'string', 
            format: 'email',
            description: 'Recipient email address' 
          },
          senderEmail: { 
            type: 'string', 
            format: 'email',
            description: 'Sender email address' 
          },
          senderName: { 
            type: 'string',
            description: 'Sender name' 
          },
          betaCode: { 
            type: 'string',
            description: 'Beta access code' 
          },
          personalMessage: { 
            type: 'string',
            description: 'Personal message (optional)' 
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { to, senderEmail, senderName, betaCode, personalMessage } = request.body;

      await emailQueue.addBetaInvitationEmail({
        to,
        senderEmail,
        senderName,
        betaCode,
        personalMessage: personalMessage || 'You\'ve been invited to try our beta!'
      });

      reply.send({
        success: true,
        message: 'Test beta invitation email queued successfully',
        recipient: to,
        sender: senderEmail,
        betaCode
      });
    } catch (error) {
      fastify.log.error('Test beta invitation email error:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to queue test beta invitation email',
        details: error.message
      });
    }
  });

  // POST /api/email/send-custom
  fastify.post('/send-custom', {
    onRequest: [fastify.verifyAuth],
    config: { rateLimit: emailRateLimit },
    schema: {
      description: 'Send a custom email with flexible content',
      tags: ['email', 'custom'],
      body: {
        type: 'object',
        required: ['to', 'subject', 'content'],
        properties: {
          to: { 
            type: 'string', 
            format: 'email',
            description: 'Recipient email address' 
          },
          subject: { 
            type: 'string',
            description: 'Email subject line' 
          },
          heading: { 
            type: 'string',
            description: 'Main heading in email (optional)' 
          },
          content: { 
            type: 'string',
            description: 'Main email content (supports HTML)' 
          },
          buttonText: { 
            type: 'string',
            description: 'Call-to-action button text (optional)' 
          },
          buttonLink: { 
            type: 'string',
            description: 'Call-to-action button URL (required if buttonText provided)' 
          },
          footerText: { 
            type: 'string',
            description: 'Custom footer text (optional)' 
          },
          fromName: { 
            type: 'string',
            description: 'Custom sender name (optional)' 
          },
          replyTo: { 
            type: 'string',
            format: 'email',
            description: 'Reply-to email address (optional)' 
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { 
        to, 
        subject, 
        heading, 
        content, 
        buttonText, 
        buttonLink, 
        footerText, 
        fromName, 
        replyTo 
      } = request.body;

      // Security validation for custom email fields
      if (replyTo) {
        validateTrustedEmail(replyTo, 'replyTo');
      }

      await emailQueue.addCustomEmail({
        to,
        subject,
        heading,
        content,
        buttonText,
        buttonLink,
        footerText,
        fromName,
        replyTo
      });

      reply.send({
        success: true,
        message: 'Custom email queued successfully',
        recipient: to,
        subject,
        hasButton: !!(buttonText && buttonLink)
      });
    } catch (error) {
      fastify.log.error('Custom email error:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to queue custom email',
        details: error.message
      });
    }
  });

  // POST /api/email/test-custom
  fastify.post('/test-custom', {
    onRequest: [fastify.verifyAuth],
    config: { rateLimit: emailRateLimit },
    schema: {
      description: 'Send a test custom email with sample content',
      tags: ['email', 'test'],
      body: {
        type: 'object',
        required: ['to'],
        properties: {
          to: { 
            type: 'string', 
            format: 'email',
            description: 'Recipient email address' 
          },
          subject: { 
            type: 'string',
            description: 'Email subject (optional - will use default)' 
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { to, subject } = request.body;

      await emailQueue.addCustomEmail({
        to,
        subject: subject || 'Test Custom Email from AdFury',
        heading: 'Welcome to AdFury!',
        content: `
          <p>This is a test custom email to verify that our email service is working correctly.</p>
          <p>Custom emails allow you to send flexible content with:</p>
          <ul>
            <li>Custom headings and subjects</li>
            <li>Rich HTML content</li>
            <li>Call-to-action buttons</li>
            <li>Custom sender names and reply-to addresses</li>
          </ul>
          <p>If you're seeing this email, congratulations! Your email service is working perfectly.</p>
        `,
        buttonText: 'Visit AdFury Dashboard',
        buttonLink: 'https://app.adfury.ai',
        footerText: 'This is a test email sent from the AdFury email service.',
        fromName: 'AdFury Team'
      });

      reply.send({
        success: true,
        message: 'Test custom email queued successfully',
        recipient: to,
        subject: subject || 'Test Custom Email from AdFury'
      });
    } catch (error) {
      fastify.log.error('Test custom email error:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to queue test custom email',
        details: error.message
      });
    }
  });

  // GET /api/email/queue-status
  fastify.get('/queue-status', {
    onRequest: [fastify.verifyAuth],
    schema: {
      description: 'Get email queue status and statistics',
      tags: ['email', 'monitoring']
    }
  }, async (request, reply) => {
    try {
      const stats = await emailQueue.getQueueStats();
      
      reply.send({
        success: true,
        queueEnabled: emailQueue.isEnabled(),
        stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      fastify.log.error('Queue status error:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to get queue status',
        details: error.message
      });
    }
  });

  // POST /api/email/proof-invitation
  fastify.post('/proof-invitation', {
    onRequest: [fastify.verifyAuth],
    config: { rateLimit: emailRateLimit },
    schema: {
      description: 'Send a proof review invitation email',
      tags: ['email', 'proof'],
      body: {
        type: 'object',
        required: ['to', 'reviewerName', 'proofTitle', 'proofUrl'],
        properties: {
          to: { 
            type: 'string', 
            format: 'email',
            description: 'Recipient email address' 
          },
          reviewerName: { 
            type: 'string',
            description: 'Name of the reviewer' 
          },
          proofTitle: { 
            type: 'string',
            description: 'Title of the proof to review' 
          },
          proofUrl: { 
            type: 'string',
            description: 'URL to access the proof' 
          },
          emailDescription: { 
            type: 'string',
            description: 'Optional description of the proof' 
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { to, reviewerName, proofTitle, proofUrl, emailDescription } = request.body;

      await emailQueue.addCustomEmail({
        to,
        subject: `Proof Review Request: ${proofTitle}`,
        heading: `You've been invited to review a creative proof`,
        content: `
          <p>Hello ${reviewerName},</p>
          <p>You've been invited to review a creative proof titled "<strong>${proofTitle}</strong>".</p>
          ${emailDescription ? `<p><strong>Description:</strong> ${emailDescription}</p>` : ''}
          <p>Please click the button below to review the proof and provide your feedback.</p>
        `,
        buttonText: 'Review Proof',
        buttonLink: proofUrl,
        footerText: 'This is an automated message from AdFury. Please do not reply to this email.',
        fromName: 'AdFury Team'
      });

      reply.send({
        success: true,
        message: 'Proof invitation email queued successfully',
        recipient: to,
        proofTitle
      });
    } catch (error) {
      fastify.log.error('Proof invitation email error:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to queue proof invitation email',
        details: error.message
      });
    }
  });

  // GET /api/email/health
  fastify.get('/health', {
    onRequest: [fastify.verifyAuth],
    schema: {
      description: 'Check email service health',
      tags: ['email', 'health']
    }
  }, async (request, reply) => {
    try {
      const isEnabled = emailQueue.isEnabled();
      const stats = await emailQueue.getQueueStats();
      
      reply.send({
        success: true,
        healthy: isEnabled && !stats.disabled,
        service: 'email-queue',
        enabled: isEnabled,
        redisUrl: process.env.QUEUE_REDIS_URL ? 'configured' : 'not configured',
        stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      fastify.log.error('Email health check error:', error);
      reply.code(500).send({
        success: false,
        healthy: false,
        error: 'Email service health check failed',
        details: error.message
      });
    }
  });

}