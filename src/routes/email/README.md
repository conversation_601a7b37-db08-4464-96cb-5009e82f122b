# Email & Notifications Module

This module is responsible for handling all outbound email communications. It is architected around a robust, Redis-backed queueing system (`BullMQ`) to ensure that email sending is reliable and does not block the main application thread.

## Architecture

The system is composed of two main parts:
1.  **`emailQueue.service.ts`**: A singleton service that provides methods to add different types of email jobs to a Redis queue. It handles all the logic for connecting to Redis, managing the queue, and defining the structure of email jobs. It is designed to be called by other services and controllers throughout the application.
2.  **`email.routes.js`**: A set of API endpoints primarily used for **testing and monitoring** the email queue during development and for administrative purposes. Application-critical emails (like password resets) are typically queued directly by other services, not through these API endpoints.

```mermaid
graph TD
    subgraph "Application Logic"
        A[Users Controller]
        B[Invitations Service]
        C[...]
    end

    subgraph "Email Module"
        D[emailQueue.service.ts]
        E[Redis (BullMQ) Queue]
        F[Email Worker/Processor]
        G[email.routes.js (for Testing)]
    end
    
    subgraph "External"
        H[Email Provider (Resend)]
    end

    A -- Calls `addPasswordResetEmail()` --> D
    B -- Calls `addInvitationEmail()` --> D
    C -- Calls queue methods --> D
    D -- Adds Job --> E
    G -- Adds Test Job --> E
    F -- Pulls Job --> E
    F -- Sends Email via --> H
```
*(Note: The `Email Worker/Processor` is an inferred component that reads from the queue and uses the `resend` library to send emails. It likely runs as a separate process.)*

## API Endpoints

All endpoints require authentication and are rate-limited to prevent abuse. These are primarily for testing and debugging.

---

### POST /api/email/test-invitation

-   **Description:** Queues a test invitation email.
-   **Use Case:** Allows developers to test the invitation email template and flow without a full invitation process.

---

### POST /api/email/test-password-reset

-   **Description:** Queues a test password reset email.
-   **Use Case:** Allows developers to test the password reset email template.

---

### POST /api/email/send-custom

-   **Description:** Queues a fully custom email, allowing for a dynamic subject, content, and call-to-action button.
-   **Use Case:** Useful for administrative announcements or other one-off communications.

---

### GET /api/email/queue-status

-   **Description:** Provides statistics about the email queue, such as the number of waiting, active, completed, and failed jobs.
-   **Use Case:** Monitoring the health and throughput of the email queue.
-   **Successful Response `200`:**
    ```json
    {
      "success": true,
      "queueEnabled": true,
      "stats": {
        "waiting": 0,
        "active": 0,
        "completed": 125,
        "failed": 2
      }
    }
    ```

---

### GET /api/email/health

-   **Description:** A health check endpoint for the email service.
-   **Use Case:** Can be used by automated monitoring systems to verify that the email service is configured and running correctly.
