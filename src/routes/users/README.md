# Users Module

The Users module handles all aspects of user management, from profile information to permissions and assignments. It serves as a bridge between the application's internal user representation and the Auth0 authentication provider.

## Data Model & Auth0 Integration

A `user` record in the local database is linked to an Auth0 user via the `auth0Id`. This allows the application to store internal user data while relying on Auth0 for secure authentication. The controller communicates with the Auth0 Management API for tasks like password resets and profile updates, ensuring that data is synchronized between the two systems.

```mermaid
graph TD
    subgraph "External"
        Auth0
    end
    subgraph "AdVid Server"
        A[API Endpoints]
        B[Users Controller]
        C[Users DB Table]
    end

    Auth0 -- Authenticates User --> A
    A -- Verifies JWT --> Auth0
    A --> B
    B -- CRUD --> C
    B -- Manages User --> Auth0
```

## API Endpoints

All endpoints require authentication.

---

### GET /api/users/:id/profile

-   **Description:** Retrieves a comprehensive profile for a single user. This is the primary endpoint for fetching all data related to a user in one call.
-   **Controller Logic:** This is a complex data aggregation endpoint. It fetches the user's core data, their account memberships, all brands (both assigned and unassigned), associated retailers for each brand, credential sets, and settings. It also attempts to fetch the latest user metadata (like `last_login`) directly from the Auth0 Management API.
-   **Successful Response `200`:** A large JSON object containing the user's complete profile.

---

### PUT /api/users/:id/profile-info

-   **Description:** Updates a user's basic, non-critical profile information (name, phone, job title, etc.).
-   **Controller Logic:** Updates the local database. If the `name` is changed, it also calls the Auth0 Management API to keep the user's name synchronized.
-   **Request Body:**
    ```json
    {
      "name": "Jane Doe",
      "jobTitle": "Creative Director",
      "phone": "************"
    }
    ```

---

### PUT /api/users/:id/brands

-   **Description:** Updates a user's brand assignments in bulk.
-   **Request Body:** An array of assignment objects, each with a `brandId`, `role`, and an `action` (`add`, `remove`, or `update`).
    ```json
    {
      "brandAssignments": [
        { "brandId": "uuid-1", "role": "member", "action": "add" },
        { "brandId": "uuid-2", "role": "admin", "action": "update" },
        { "brandId": "uuid-3", "role": "member", "action": "remove" }
      ]
    }
    ```

---

### PUT /api/users/:id/role

-   **Description:** Updates a user's role (`owner`, `admin`, `member`) for a specific account.
-   **Authorization:** Requires the requesting user to have permission to manage users in that account.
-   **Request Body:**
    ```json
    {
      "accountId": "uuid-of-account",
      "role": "admin"
    }
    ```

---

### POST /api/users/:id/reset-password

-   **Description:** Triggers a password reset email to the user via Auth0.
-   **Controller Logic:** Fetches the user's email from the local database and then makes a request to the Auth0 Management API to send the password change ticket.

---

### PUT /api/users/:id/profile-picture

-   **Description:** Updates a user's profile picture.
-   **Controller Logic:** The endpoint accepts a `multipart/form-data` request. The image file is uploaded to Google Cloud Storage via `google.service.ts`. The public URL of the stored image is then sent to the Auth0 Management API to update the user's `picture` attribute in their Auth0 profile.
