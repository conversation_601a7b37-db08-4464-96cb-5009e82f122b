import * as userController from "./users.controller.js";
import * as userSchemas from "./users.schema.js";

/**
 * Registers all user-related HTTP routes on the Fastify instance, including endpoints for user management, profile updates, brand and retailer assignments, plugin connections, and invitation handling. Each route is configured with appropriate JSON schema validation and mapped to its corresponding controller method.
 */
export default async function userRoutes(fastify) {
  // Get all users
  fastify.get("/", userController.getAllUsers);

  // Get user by id
  fastify.get("/:id", {
    schema: {
      params: userSchemas.userParamsSchema
    }
  }, userController.getUserById);

  // Create user
  fastify.post("/", {
    schema: {
      body: userSchemas.createUserSchema
    }
  }, userController.createUser);

  // Update user's Auth0 profile picture
  fastify.put("/:id/profile-picture", {
    schema: {
      params: userSchemas.userParamsSchema
    }
  }, userController.updateProfilePicture);

  // Update user
  fastify.put("/:id", {
    schema: {
      params: userSchemas.userParamsSchema,
      body: userSchemas.updateUserSchema
    }
  }, userController.updateUser);

  // Delete user
  fastify.delete("/:id", {
    schema: {
      params: userSchemas.userParamsSchema
    }
  }, userController.deleteUser);

  // Update user's Auth0 profile (name/email)
  fastify.put("/:id/profile", {
    schema: {
      params: userSchemas.userParamsSchema,
      body: userSchemas.updateAuth0ProfileSchema
    }
  }, userController.updateAuth0Profile);

  // Reset user's Auth0 password
  fastify.post("/:id/reset-password", {
    schema: {
      params: userSchemas.userParamsSchema
    }
  }, userController.resetPassword);

  // Get complete user profile with all relationships
  fastify.get("/:id/profile", {
    schema: {
      params: userSchemas.userParamsSchema
    }
  }, userController.getUserProfile);

  // Update basic profile info
  fastify.put("/:id/profile-info", {
    schema: {
      params: userSchemas.userParamsSchema,
      body: userSchemas.updateProfileInfoSchema
    }
  }, userController.updateProfileInfo);

  // Update user's brand assignments
  fastify.put("/:id/brands", {
    schema: {
      params: userSchemas.userParamsSchema,
      body: userSchemas.updateBrandAssignmentsSchema
    }
  }, userController.updateBrandAssignments);

  // Get all brands assigned to user
  fastify.get("/:id/brands", {
    schema: {
      params: userSchemas.userParamsSchema
    }
  }, userController.getUserBrands);

  // Update user's account role
  fastify.put("/:id/role", {
    schema: {
      params: userSchemas.userParamsSchema,
      body: userSchemas.updateUserRoleSchema
    }
  }, userController.updateUserRole);

  // Resend invitation email
  fastify.post("/:id/resend-invite", {
    schema: {
      params: userSchemas.userParamsSchema
    }
  }, userController.resendInvite);

  // Get user's connected plugins
  fastify.get("/:id/plugins", {
    schema: {
      params: userSchemas.userParamsSchema
    }
  }, userController.getUserPlugins);

  // Connect a plugin to user
  fastify.post("/:id/plugins", {
    schema: {
      params: userSchemas.userParamsSchema,
      body: userSchemas.connectPluginSchema
    }
  }, userController.connectPlugin);

  // Disconnect plugin
  fastify.delete("/:id/plugins/:pluginName", {
    schema: {
      params: userSchemas.pluginParamsSchema
    }
  }, userController.disconnectPlugin);

  // Get brands available for assignment to user
  fastify.get("/:id/available-brands", {
    schema: {
      params: userSchemas.userParamsSchema
    }
  }, userController.getAvailableBrands);

  // Get all retailers with assignment status for a specific brand
  fastify.get("/:id/brands/:brandId/available-retailers", {
    schema: {
      params: userSchemas.brandRetailerParamsSchema
    }
  }, userController.getAvailableRetailersForBrand);

  // Update brand-retailer assignments (add/remove retailers from brands)
  fastify.put("/:id/brands/:brandId/retailers", {
    schema: {
      params: userSchemas.brandRetailerParamsSchema,
      body: userSchemas.updateBrandRetailerAssignmentsSchema
    }
  }, userController.updateBrandRetailerAssignments);

  // Comprehensive user profile update
  fastify.put("/:id/update-profile", {
    schema: {
      params: userSchemas.userParamsSchema,
      body: userSchemas.updateUserProfileSchema
    }
  }, userController.updateUserProfile);
}