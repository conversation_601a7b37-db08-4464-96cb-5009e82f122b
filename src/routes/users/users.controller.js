import { eq, and, inArray } from "drizzle-orm";
import { users } from "../../db/schema/users.js";
import { randomUUID } from "crypto";
import { db } from "../../db/index.js";
import * as schema from "../../db/schema/index.js";
import axios from "axios";
import { getOrUploadFile } from "../../services/google.service.js";

// Helper function to get Auth0 Management API token
async function getManagementApiToken() {
  try {
    const response = await axios.post(
      `https://${process.env.AUTH0_DOMAIN}/oauth/token`,
      {
        grant_type: "client_credentials",
        client_id: process.env.AUTH0_CLIENT_ID,
        client_secret: process.env.AUTH0_CLIENT_SECRET,
        audience: `https://${process.env.AUTH0_DOMAIN}/api/v2/`,
      }
    );
    return response.data.access_token;
  } catch (error) {
    console.error("Error getting management token:", error);
    throw error;
  }
}

// Get all users
export const getAllUsers = async (request, reply) => {
  const allUsers = await db.select().from(users);
  return allUsers;
};

// Get user by id
export const getUserById = async (request, reply) => {
  const { id } = request.params;

  const results = await db
    .select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1);

  if (!results || results.length === 0) {
    return reply.code(404).send({ error: "User not found" });
  }

  return results[0];
};

// Create user
export const createUser = async (request, reply) => {
  const userData = request.body;
  const id = randomUUID();

  await db.insert(users).values({ ...userData, id });

  // Fetch the created user
  const newUser = await db
    .select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1);

  if (!newUser || newUser.length === 0) {
    return reply.code(500).send({ error: "Failed to create user" });
  }

  return reply.code(201).send(newUser[0]);
};

// Update user's Auth0 profile picture
export const updateProfilePicture = async (request, reply) => {
  try {
    const { id } = request.params;

    if (!request.isMultipart()) {
      return reply.code(400).send({ error: 'Request must be multipart/form-data' })
    }

    const body = await request.body;

    if (!body || !body.file) {
      return reply.code(400).send({
        error: 'No file uploaded',
        body: body,
        keys: body ? Object.keys(body) : []
      })
    }

    const fileData = body.file;

    // Convert file buffer and generate unique filename using UUID
    const buffer = fileData._buf;

    // Generate unique filename
    const fileName = `${id}-${Date.now()}.${fileData.filename.split('.').pop()}`;

    // Upload to Google Cloud Storage
    const picture_url = await getOrUploadFile(
      'adfury-profile-picutures',
      buffer,
      fileName
    );

    // First, get the user from our database to get their Auth0 ID
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!user || !user.auth0Id) {
      return reply.code(404).send({ error: "User not found" });
    }

    // Get Management API token
    const token = await getManagementApiToken();

    // Update Auth0 user profile
    await axios.patch(
      `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
      {
        picture: picture_url,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return reply.code(200).send({
      message: "Profile picture updated successfully",
      picture_url
    });
  } catch (error) {
    console.error("Error updating profile picture:", error);
    return reply.code(500).send({
      error: "Failed to update profile picture",
      details: error.response?.data || error.message,
    });
  }
};

// Update user
export const updateUser = async (request, reply) => {
  const { id } = request.params;
  const userData = request.body;

  await db
    .update(users)
    .set({ ...userData, updatedAt: new Date() })
    .where(eq(users.id, id));

  // Fetch the updated user
  const updatedUser = await db
    .select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1);

  if (!updatedUser || updatedUser.length === 0) {
    return reply.code(404).send({ error: "User not found" });
  }

  return updatedUser[0];
};

// Delete user
export const deleteUser = async (request, reply) => {
  const { id } = request.params;

  await db.delete(users).where(eq(users.id, id));

  return reply.code(204).send();
};

// Update user's Auth0 profile (name/email)
export const updateAuth0Profile = async (request, reply) => {
  try {
    const { id } = request.params;
    const { name, email } = request.body;

    // First, get the user from our database to get their Auth0 ID
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!user || !user.auth0Id) {
      return reply.code(404).send({ error: "User not found" });
    }

    // Get Management API token
    const token = await getManagementApiToken();

    // Prepare update payload
    const updateData = {};
    if (name) updateData.name = name;
    if (email) updateData.email = email;

    // Update Auth0 user profile
    await axios.patch(
      `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
      updateData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return reply.code(200).send({ message: "Profile updated successfully" });
  } catch (error) {
    console.error("Error updating profile:", error);
    return reply.code(500).send({
      error: "Failed to update profile",
      details: error.response?.data || error.message,
    });
  }
};

// Reset user's Auth0 password
export const resetPassword = async (request, reply) => {
  try {
    const { id } = request.params;

    // First, get the user from our database to get their Auth0 ID
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!user || !user.auth0Id) {
      return reply.code(404).send({ error: "User not found" });
    }

    // Get Management API token
    const token = await getManagementApiToken();

    // Log the user details we're working with
    console.log('Attempting password reset for:', {
      userId: id,
      auth0Id: user.auth0Id,
      email: user.email
    });

    // Trigger password reset email with connection specified
    const response = await axios.post(
      `https://${process.env.AUTH0_DOMAIN}/api/v2/tickets/password-change`,
      {
        email: user.email,
        result_url: process.env.AUTH0_PASSWORD_RESET_REDIRECT_URL,
        ttl_sec: 86400,
        mark_email_as_verified: true,
        connection_id: process.env.AUTH0_DB_CONNECTION_ID
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    // Log the response from Auth0
    console.log('Auth0 password reset response:', response.data);

    return reply.code(200).send({
      message: "Password reset email sent successfully",
      ticket: response.data
    });
  } catch (error) {
    console.error("Error initiating password reset:", error);
    return reply.code(500).send({
      error: "Failed to initiate password reset",
      details: error.response?.data || error.message,
    });
  }
};

// Get complete user profile with all relationships
export const getUserProfile = async (request, reply) => {
  const { id } = request.params;

  try {
    // Get user with basic info
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!user) {
      return reply.code(404).send({ error: "User not found" });
    }

    // Get user's account memberships with roles
    const accountMemberships = await db
      .select({
        accountId: schema.userAccounts.accountId,
        accountName: schema.accounts.name,
        role: schema.userAccounts.role,
        status: schema.userAccounts.status
      })
      .from(schema.userAccounts)
      .innerJoin(schema.accounts, eq(schema.userAccounts.accountId, schema.accounts.id))
      .where(eq(schema.userAccounts.userId, id));

    if (accountMemberships.length === 0) {
      return reply.code(404).send({ error: "User has no account memberships" });
    }

    const accountIds = accountMemberships.map((am) => am.accountId);

    // Get ALL brands in user's accounts (both assigned and unassigned)
    const allAccountBrands = await db
      .select({
        brandId: schema.brands.id,
        brandName: schema.brands.name,
        accountId: schema.brands.accountId,
        accountName: schema.accounts.name,
        primaryCredentialSetName: schema.credentialSets.name
      })
      .from(schema.brands)
      .innerJoin(schema.accounts, eq(schema.brands.accountId, schema.accounts.id))
      .leftJoin(schema.credentialSets, eq(schema.brands.primaryCredentialId, schema.credentialSets.id))
      .where(inArray(schema.brands.accountId, accountIds));

    // Get user's current brand assignments
    const userBrandAssignments = await db
      .select({
        brandId: schema.brandUsers.brandId,
        role: schema.brandUsers.role
      })
      .from(schema.brandUsers)
      .where(eq(schema.brandUsers.userId, id));

    const assignedBrandMap = new Map(
      userBrandAssignments.map((ba) => [ba.brandId, ba.role])
    );

    // Get all brand IDs to fetch retailers
    const allBrandIds = allAccountBrands.map((b) => b.brandId);

    // Get ALL retailers for all brands in user's accounts
    const allBrandRetailers = allBrandIds.length > 0 ? await db
      .select({
        brandId: schema.brandRetailers.brandId,
        retailerId: schema.brandRetailers.retailerId,
        retailerName: schema.retailers.name,
        retailerSlug: schema.retailers.slug,
        retailerLogoUrl: schema.retailers.logoUrl,
        retailerUrl: schema.retailers.url
      })
      .from(schema.brandRetailers)
      .innerJoin(schema.retailers, eq(schema.brandRetailers.retailerId, schema.retailers.id))
      .where(inArray(schema.brandRetailers.brandId, allBrandIds)) : [];

    // Get all available retailers (for unassigned retailers)
    const allRetailers = await db
      .select({
        id: schema.retailers.id,
        name: schema.retailers.name,
        slug: schema.retailers.slug,
        logoUrl: schema.retailers.logoUrl,
        url: schema.retailers.url
      })
      .from(schema.retailers);

    // Group assigned retailers by brand
    const assignedRetailersByBrand = allBrandRetailers.reduce((acc, br) => {
      if (!acc[br.brandId]) {
        acc[br.brandId] = new Set();
      }
      acc[br.brandId].add(br.retailerId);
      return acc;
    }, {});

    // Create comprehensive brand assignments with all brands and their retailers
    const brandAssignmentsWithStatus = allAccountBrands.map((brand) => {
      const isAssigned = assignedBrandMap.has(brand.brandId);
      const userRole = assignedBrandMap.get(brand.brandId) || null;
      const assignedRetailerIds = assignedRetailersByBrand[brand.brandId] || new Set();

      // Separate retailers into assigned and unassigned arrays
      const assignedRetailersData = [];
      const unassignedRetailersData = [];

      allRetailers.forEach((retailer) => {
        const retailerData = {
          id: retailer.id,
          name: retailer.name,
          slug: retailer.slug,
          logoUrl: retailer.logoUrl,
          url: retailer.url
        };

        if (assignedRetailerIds.has(retailer.id)) {
          assignedRetailersData.push(retailerData);
        } else {
          unassignedRetailersData.push(retailerData);
        }
      });

      return {
        brandId: brand.brandId,
        brandName: brand.brandName,
        accountId: brand.accountId,
        accountName: brand.accountName,
        primaryCredentialSetName: brand.primaryCredentialSetName,
        isAssigned,
        role: userRole,
        assignedRetailers: assignedRetailersData,
        unassignedRetailers: unassignedRetailersData
      };
    });

    // Separate brands into assigned and unassigned arrays
    const assignedBrands = brandAssignmentsWithStatus.filter(brand => brand.isAssigned);
    const unassignedBrands = brandAssignmentsWithStatus.filter(brand => !brand.isAssigned);

    // Remove the isAssigned field from the brand objects since it's now implicit
    const cleanAssignedBrands = assignedBrands.map(({ isAssigned, ...brand }) => brand);
    const cleanUnassignedBrands = unassignedBrands.map(({ isAssigned, ...brand }) => brand);

    // Get user's credential set associations
    const credentialSets = await db
      .select({
        credentialSetId: schema.userCredentialSets.credentialSetId,
        name: schema.credentialSets.name,
        type: schema.credentialSets.type,
        isActive: schema.credentialSets.isActive,
        credentials: schema.credentialSets.credentials
      })
      .from(schema.userCredentialSets)
      .innerJoin(schema.credentialSets, eq(schema.userCredentialSets.credentialSetId, schema.credentialSets.id))
      .where(eq(schema.userCredentialSets.userId, id));

    // Separate plugins from other credential sets
    const plugins = credentialSets
      .filter((cs) => cs.type === 'plugin')
      .map((cs) => ({
        name: cs.name,
        isActive: cs.isActive,
        credentials: cs.credentials,
        credentialSetId: cs.credentialSetId
      }));

    const nonPluginCredentialSets = credentialSets.filter((cs) => cs.type !== 'plugin');

    // Get user settings
    const [userSettingsData] = await db
      .select()
      .from(schema.userSettings)
      .where(eq(schema.userSettings.userId, id))
      .limit(1);

    // Try to get Auth0 user info for additional details (lastLogin, loginCount, etc.)
    let auth0User = null;
    if (user.auth0Id) {
      try {
        const token = await getManagementApiToken();
        const auth0Response = await axios.get(
          `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        auth0User = auth0Response.data;
      } catch (auth0Error) {
        console.error("Auth0 user not found, using database data only:", auth0Error.response?.status);
        // Continue without Auth0 data
      }
    }

    return {
      user: {
        ...user,
        lastLogin: auth0User?.last_login || null,
        loginCount: auth0User?.logins_count || 0,
        emailVerified: auth0User?.email_verified || false,
        picture: auth0User?.picture || null
      },
      accountMemberships,
      assignedBrands: cleanAssignedBrands,
      unassignedBrands: cleanUnassignedBrands,
      credentialSets: nonPluginCredentialSets,
      settings: userSettingsData?.settings || {},
      plugins
    };
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return reply.code(500).send({
      error: "Failed to fetch user profile",
      details: error.response?.data || error.message,
    });
  }
};

// Update basic profile info
export const updateProfileInfo = async (request, reply) => {
  const { id } = request.params;
  const { name, phone, language, timeZone, jobTitle } = request.body;

  try {
    // Update database user info
    const updateData = {};
    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (language) updateData.language = language;
    if (timeZone) updateData.timeZone = timeZone;
    if (jobTitle) updateData.jobTitle = jobTitle;

    if (Object.keys(updateData).length > 0) {
      await db
        .update(users)
        .set({ ...updateData, updatedAt: new Date() })
        .where(eq(users.id, id));
    }

    // Update Auth0 profile if name is changed
    if (name) {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (user?.auth0Id) {
        const token = await getManagementApiToken();
        await axios.patch(
          `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
          { name },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
      }
    }

    return reply.send({ message: "Profile updated successfully" });
  } catch (error) {
    console.error("Error updating profile info:", error);
    return reply.code(500).send({
      error: "Failed to update profile info",
      details: error.response?.data || error.message,
    });
  }
};

// Update user's brand assignments
export const updateBrandAssignments = async (request, reply) => {
  const { id } = request.params;
  const { brandAssignments } = request.body;

  try {
    for (const assignment of brandAssignments) {
      const { brandId, role, action } = assignment;

      switch (action) {
        case 'add':
          await db
            .insert(schema.brandUsers)
            .values({
              userId: id,
              brandId,
              role,
              createdAt: new Date(),
              updatedAt: new Date()
            })
            .onConflictDoUpdate({
              target: [schema.brandUsers.userId, schema.brandUsers.brandId],
              set: { role, updatedAt: new Date() }
            });
          break;

        case 'remove':
          await db
            .delete(schema.brandUsers)
            .where(
              and(
                eq(schema.brandUsers.userId, id),
                eq(schema.brandUsers.brandId, brandId)
              )
            );
          break;

        case 'update':
          await db
            .update(schema.brandUsers)
            .set({ role, updatedAt: new Date() })
            .where(
              and(
                eq(schema.brandUsers.userId, id),
                eq(schema.brandUsers.brandId, brandId)
              )
            );
          break;
      }
    }

    return reply.send({ message: "Brand assignments updated successfully" });
  } catch (error) {
    console.error("Error updating brand assignments:", error);
    return reply.code(500).send({
      error: "Failed to update brand assignments",
      details: error.message,
    });
  }
};

// Get all brands assigned to user
export const getUserBrands = async (request, reply) => {
  const { id } = request.params;

  try {
    const brandAssignments = await db
      .select({
        brandId: schema.brandUsers.brandId,
        brandName: schema.brands.name,
        role: schema.brandUsers.role,
        accountId: schema.brands.accountId,
        accountName: schema.accounts.name
      })
      .from(schema.brandUsers)
      .innerJoin(schema.brands, eq(schema.brandUsers.brandId, schema.brands.id))
      .innerJoin(schema.accounts, eq(schema.brands.accountId, schema.accounts.id))
      .where(eq(schema.brandUsers.userId, id));

    return reply.send(brandAssignments);
  } catch (error) {
    console.error("Error fetching user brands:", error);
    return reply.code(500).send({
      error: "Failed to fetch user brands",
      details: error.message,
    });
  }
};

// Update user's account role
export const updateUserRole = async (request, reply) => {
  const { id } = request.params;
  const { accountId, role } = request.body;

  try {
    const validRoles = ['owner', 'admin', 'member'];
    if (!validRoles.includes(role)) {
      return reply.code(400).send({ error: "Invalid role" });
    }

    await db
      .update(schema.userAccounts)
      .set({ role, updatedAt: new Date() })
      .where(
        and(
          eq(schema.userAccounts.userId, id),
          eq(schema.userAccounts.accountId, accountId)
        )
      );

    return reply.send({ message: "User role updated successfully" });
  } catch (error) {
    console.error("Error updating user role:", error);
    return reply.code(500).send({
      error: "Failed to update user role",
      details: error.message,
    });
  }
};

// Resend invitation email
export const resendInvite = async (request, reply) => {
  const { id } = request.params;

  try {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!user?.auth0Id) {
      return reply.code(404).send({ error: "User not found" });
    }

    // Trigger password reset email (same as invitation)
    const token = await getManagementApiToken();
    const response = await axios.post(
      `https://${process.env.AUTH0_DOMAIN}/api/v2/tickets/password-change`,
      {
        email: user.email,
        result_url: process.env.AUTH0_PASSWORD_RESET_REDIRECT_URL,
        ttl_sec: 86400,
        mark_email_as_verified: true,
        connection_id: process.env.AUTH0_DB_CONNECTION_ID
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return reply.send({
      message: "Invitation resent successfully",
      ticket: response.data
    });
  } catch (error) {
    console.error("Error resending invitation:", error);
    return reply.code(500).send({
      error: "Failed to resend invitation",
      details: error.response?.data || error.message,
    });
  }
};

// Get user's connected plugins
export const getUserPlugins = async (request, reply) => {
  const { id } = request.params;

  try {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!user) {
      return reply.code(404).send({ error: "User not found" });
    }

    // Get user's plugin credential sets
    const plugins = await db
      .select({
        credentialSetId: schema.userCredentialSets.credentialSetId,
        name: schema.credentialSets.name,
        type: schema.credentialSets.type,
        isActive: schema.credentialSets.isActive,
        credentials: schema.credentialSets.credentials,
        createdAt: schema.credentialSets.createdAt
      })
      .from(schema.userCredentialSets)
      .innerJoin(schema.credentialSets, eq(schema.userCredentialSets.credentialSetId, schema.credentialSets.id))
      .where(
        and(
          eq(schema.userCredentialSets.userId, id),
          eq(schema.credentialSets.type, 'plugin')
        )
      );

    return reply.send({ plugins });
  } catch (error) {
    console.error("Error fetching user plugins:", error);
    return reply.code(500).send({
      error: "Failed to fetch user plugins",
      details: error.message,
    });
  }
};

// Connect a plugin to user
export const connectPlugin = async (request, reply) => {
  const { id } = request.params;
  const { pluginName, pluginData } = request.body;

  try {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!user) {
      return reply.code(404).send({ error: "User not found" });
    }

    // Get user's account (assuming the user has at least one account membership)
    const [userAccount] = await db
      .select({ accountId: schema.userAccounts.accountId })
      .from(schema.userAccounts)
      .where(eq(schema.userAccounts.userId, id))
      .limit(1);

    if (!userAccount) {
      return reply.code(400).send({ error: "User must be associated with an account to add plugins" });
    }

    // Check if plugin already exists for this user
    const existingPlugin = await db
      .select()
      .from(schema.userCredentialSets)
      .innerJoin(schema.credentialSets, eq(schema.userCredentialSets.credentialSetId, schema.credentialSets.id))
      .where(
        and(
          eq(schema.userCredentialSets.userId, id),
          eq(schema.credentialSets.type, 'plugin'),
          eq(schema.credentialSets.name, pluginName)
        )
      )
      .limit(1);

    if (existingPlugin.length > 0) {
      // Update existing plugin
      await db
        .update(schema.credentialSets)
        .set({
          credentials: pluginData,
          isActive: true,
          updatedAt: new Date()
        })
        .where(eq(schema.credentialSets.id, existingPlugin[0].credential_sets.id));
    } else {
      // Create new credential set for the plugin
      const [newCredentialSet] = await db
        .insert(schema.credentialSets)
        .values({
          name: pluginName,
          type: 'plugin',
          credentials: pluginData,
          accountId: userAccount.accountId,
          adminId: id,
          isActive: true,
          isShared: false
        })
        .returning({ id: schema.credentialSets.id });

      // Associate the credential set with the user
      await db
        .insert(schema.userCredentialSets)
        .values({
          userId: id,
          credentialSetId: newCredentialSet.id
        });
    }

    return reply.send({ message: "Plugin connected successfully" });
  } catch (error) {
    console.error("Error connecting plugin:", error);
    return reply.code(500).send({
      error: "Failed to connect plugin",
      details: error.message,
    });
  }
};

// Disconnect plugin
export const disconnectPlugin = async (request, reply) => {
  const { id, pluginName } = request.params;

  try {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!user) {
      return reply.code(404).send({ error: "User not found" });
    }

    // Find the plugin credential set for this user
    const pluginCredentialSet = await db
      .select({ credentialSetId: schema.credentialSets.id })
      .from(schema.userCredentialSets)
      .innerJoin(schema.credentialSets, eq(schema.userCredentialSets.credentialSetId, schema.credentialSets.id))
      .where(
        and(
          eq(schema.userCredentialSets.userId, id),
          eq(schema.credentialSets.type, 'plugin'),
          eq(schema.credentialSets.name, pluginName)
        )
      )
      .limit(1);

    if (pluginCredentialSet.length === 0) {
      return reply.code(404).send({ error: "Plugin not found for this user" });
    }

    // Set the credential set as inactive instead of deleting
    await db
      .update(schema.credentialSets)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(schema.credentialSets.id, pluginCredentialSet[0].credentialSetId));

    return reply.send({ message: "Plugin disconnected successfully" });
  } catch (error) {
    console.error("Error disconnecting plugin:", error);
    return reply.code(500).send({
      error: "Failed to disconnect plugin",
      details: error.message,
    });
  }
};

// Get brands available for assignment to user
export const getAvailableBrands = async (request, reply) => {
  const { id } = request.params;

  try {
    // Get user's current account memberships
    const userAccountMemberships = await db
      .select({ accountId: schema.userAccounts.accountId })
      .from(schema.userAccounts)
      .where(eq(schema.userAccounts.userId, id));

    if (userAccountMemberships.length === 0) {
      return reply.send([]);
    }

    const accountIds = userAccountMemberships.map((ua) => ua.accountId);

    // Get all brands in accounts the user belongs to
    const allAccountBrands = await db
      .select({
        brandId: schema.brands.id,
        brandName: schema.brands.name,
        accountId: schema.brands.accountId,
        accountName: schema.accounts.name
      })
      .from(schema.brands)
      .innerJoin(schema.accounts, eq(schema.brands.accountId, schema.accounts.id))
      .where(inArray(schema.brands.accountId, accountIds));

    // Get brands the user is already assigned to
    const userBrandAssignments = await db
      .select({ brandId: schema.brandUsers.brandId })
      .from(schema.brandUsers)
      .where(eq(schema.brandUsers.userId, id));

    const assignedBrandIds = new Set(userBrandAssignments.map((ub) => ub.brandId));

    // Filter out brands already assigned to the user
    const availableBrands = allAccountBrands.filter(
      (brand) => !assignedBrandIds.has(brand.brandId)
    );

    return reply.send(availableBrands);
  } catch (error) {
    console.error("Error fetching available brands:", error);
    return reply.code(500).send({
      error: "Failed to fetch available brands",
      details: error.message,
    });
  }
};

//  BAF - 2: Comprehensive user profile update
// Get all retailers with assignment status for a specific brand
export const getAvailableRetailersForBrand = async (request, reply) => {
  const { id, brandId } = request.params;

  try {
    // Verify user has access to this brand
    const userAccountMemberships = await db
      .select({ accountId: schema.userAccounts.accountId })
      .from(schema.userAccounts)
      .where(eq(schema.userAccounts.userId, id));

    if (userAccountMemberships.length === 0) {
      return reply.code(403).send({ error: "User has no account memberships" });
    }

    const accountIds = userAccountMemberships.map((ua) => ua.accountId);

    // Verify the brand belongs to one of the user's accounts
    const [brand] = await db
      .select({
        id: schema.brands.id,
        name: schema.brands.name,
        accountId: schema.brands.accountId
      })
      .from(schema.brands)
      .where(
        and(
          eq(schema.brands.id, brandId),
          inArray(schema.brands.accountId, accountIds)
        )
      )
      .limit(1);

    if (!brand) {
      return reply.code(404).send({ error: "Brand not found or access denied" });
    }

    // Get all retailers
    const allRetailers = await db
      .select({
        id: schema.retailers.id,
        name: schema.retailers.name,
        slug: schema.retailers.slug,
        logoUrl: schema.retailers.logoUrl,
        url: schema.retailers.url
      })
      .from(schema.retailers);

    // Get retailers currently assigned to this brand
    const assignedRetailers = await db
      .select({ retailerId: schema.brandRetailers.retailerId })
      .from(schema.brandRetailers)
      .where(eq(schema.brandRetailers.brandId, brandId));

    const assignedRetailerIds = new Set(assignedRetailers.map((ar) => ar.retailerId));

    // Separate retailers into assigned and unassigned arrays
    const assignedRetailersData = [];
    const unassignedRetailersData = [];

    allRetailers.forEach((retailer) => {
      const retailerData = {
        id: retailer.id,
        name: retailer.name,
        slug: retailer.slug,
        logoUrl: retailer.logoUrl,
        url: retailer.url
      };

      if (assignedRetailerIds.has(retailer.id)) {
        assignedRetailersData.push(retailerData);
      } else {
        unassignedRetailersData.push(retailerData);
      }
    });

    return reply.send({
      brand: {
        id: brand.id,
        name: brand.name,
        accountId: brand.accountId
      },
      assignedRetailers: assignedRetailersData,
      unassignedRetailers: unassignedRetailersData
    });
  } catch (error) {
    console.error("Error fetching available retailers for brand:", error);
    return reply.code(500).send({
      error: "Failed to fetch available retailers for brand",
      details: error.message,
    });
  }
};

// Update brand-retailer assignments (add/remove retailers from brands)
export const updateBrandRetailerAssignments = async (request, reply) => {
  const { id, brandId } = request.params;
  const { retailerAssignments } = request.body;

  try {
    // Verify user has access to this brand
    const userAccountMemberships = await db
      .select({ accountId: schema.userAccounts.accountId })
      .from(schema.userAccounts)
      .where(eq(schema.userAccounts.userId, id));

    if (userAccountMemberships.length === 0) {
      return reply.code(403).send({ error: "User has no account memberships" });
    }

    const accountIds = userAccountMemberships.map((ua) => ua.accountId);

    // Verify the brand belongs to one of the user's accounts
    const [brand] = await db
      .select({
        id: schema.brands.id,
        name: schema.brands.name,
        accountId: schema.brands.accountId
      })
      .from(schema.brands)
      .where(
        and(
          eq(schema.brands.id, brandId),
          inArray(schema.brands.accountId, accountIds)
        )
      )
      .limit(1);

    if (!brand) {
      return reply.code(404).send({ error: "Brand not found or access denied" });
    }

    // Process retailer assignments
    for (const assignment of retailerAssignments) {
      const { retailerId, action } = assignment;

      switch (action) {
        case 'add':
          await db
            .insert(schema.brandRetailers)
            .values({
              brandId,
              retailerId,
              createdAt: new Date(),
              updatedAt: new Date()
            })
            .onConflictDoNothing(); // Ignore if already exists
          break;

        case 'remove':
          await db
            .delete(schema.brandRetailers)
            .where(
              and(
                eq(schema.brandRetailers.brandId, brandId),
                eq(schema.brandRetailers.retailerId, retailerId)
              )
            );
          break;

        default:
          return reply.code(400).send({
            error: `Invalid action: ${action}. Must be 'add' or 'remove'`
          });
      }
    }

    return reply.send({
      message: "Brand retailer assignments updated successfully",
      brandId,
      assignmentsProcessed: retailerAssignments.length
    });
  } catch (error) {
    console.error("Error updating brand retailer assignments:", error);
    return reply.code(500).send({
      error: "Failed to update brand retailer assignments",
      details: error.message,
    });
  }
};

// BAF - 2: Comprehensive user profile update
export const updateUserProfile = async (request, reply) => {
  try {
    const { id } = request.params;
    const {
      name,
      jobTitle,
      email,
      phone,
      brandRetailerAssignments,
      role,
      accountId,
      status,
      retailerCredentials
    } = request.body;

    // Verify user exists
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    if (!user) {
      return reply.code(404).send({ error: "User not found" });
    }

    // 1. Update basic user fields in local database
    const userUpdateData = {};
    if (name) userUpdateData.name = name;
    if (jobTitle) userUpdateData.jobTitle = jobTitle;
    if (email) userUpdateData.email = email;
    if (phone) userUpdateData.phone = phone;
    if (status) userUpdateData.status = status; // admin should control this

    if (Object.keys(userUpdateData).length > 0) {
      await db
        .update(users)
        .set({ ...userUpdateData, updatedAt: new Date() })
        .where(eq(users.id, id));
    }

    // 2. Update Auth0 profile if name or email changed
    if ((name || email) && user.auth0Id) {
      try {
        const token = await getManagementApiToken();
        const auth0Updates = {};
        if (name) auth0Updates.name = name;
        if (email) auth0Updates.email = email;

        await axios.patch(
          `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${user.auth0Id}`,
          auth0Updates,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
      } catch (auth0Error) {
        console.error("Auth0 update failed:", auth0Error);
        // Continue with other updates even if Auth0 fails
      }
    }

    // 3. Update user role (admin-only operation)
    if (role && accountId) {
      await db
        .update(schema.userAccounts)
        .set({ role, updatedAt: new Date() })
        .where(
          and(
            eq(schema.userAccounts.userId, id),
            eq(schema.userAccounts.accountId, accountId)
          )
        );
    }

    // 4. Handle brand retailer assignments
    if (brandRetailerAssignments && brandRetailerAssignments.length > 0) {
      for (const assignment of brandRetailerAssignments) {
        const { brandId, retailerId, action } = assignment;

        if (action === 'add') {
          await db
            .insert(schema.brandRetailers)
            .values({
              brandId,
              retailerId,
              createdAt: new Date(),
              updatedAt: new Date()
            })
            .onConflictDoNothing();
        } else if (action === 'remove') {
          await db
            .delete(schema.brandRetailers)
            .where(
              and(
                eq(schema.brandRetailers.brandId, brandId),
                eq(schema.brandRetailers.retailerId, retailerId)
              )
            );
        }
      }
    }

    // 5. Update retailer credentials (any retailer)
    if (retailerCredentials) {
      const { retailerName, label, advertisingId, status: credStatus } = retailerCredentials;
      const credentialSetName = retailerName ? retailerName.toLowerCase().replace(/\s+/g, '_') : 'retailer_connect';

      // Look for existing credential set for this retailer and user
      const existingCreds = await db
        .select({ id: schema.credentialSets.id })
        .from(schema.credentialSets)
        .innerJoin(schema.userCredentialSets, eq(schema.credentialSets.id, schema.userCredentialSets.credentialSetId))
        .where(
          and(
            eq(schema.userCredentialSets.userId, id),
            eq(schema.credentialSets.name, credentialSetName)
          )
        )
        .limit(1);

      const credentialsPayload = {
        retailerName: retailerName || null,
        label: label || null,
        advertisingId: advertisingId || null,
        status: credStatus || null
      };

      if (existingCreds.length > 0) {
        // Update existing credentials
        await db
          .update(schema.credentialSets)
          .set({
            credentials: credentialsPayload,
            isActive: true,
            updatedAt: new Date()
          })
          .where(eq(schema.credentialSets.id, existingCreds[0].id));
      } else {
        // Create new credential set
        const [userAccount] = await db
          .select({ accountId: schema.userAccounts.accountId })
          .from(schema.userAccounts)
          .where(eq(schema.userAccounts.userId, id))
          .limit(1);

        if (userAccount) {
          const [newCredSet] = await db
            .insert(schema.credentialSets)
            .values({
              name: credentialSetName,
              type: 'retailer',
              credentials: credentialsPayload,
              accountId: userAccount.accountId,
              adminId: id,
              isActive: true,
              isShared: false
            })
            .returning({ id: schema.credentialSets.id });

          // Link to user
          await db
            .insert(schema.userCredentialSets)
            .values({
              userId: id,
              credentialSetId: newCredSet.id
            });
        }
      }
    }

    return reply.send({
      message: "User profile updated successfully",
      updatedFields: Object.keys(request.body)
    });

  } catch (error) {
    console.error("Error updating user profile:", error);
    return reply.code(500).send({
      error: "Failed to update user profile",
      details: error.message,
    });
  }
};