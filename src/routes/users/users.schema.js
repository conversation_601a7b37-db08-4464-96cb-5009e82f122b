// User validation schemas

export const createUserSchema = {
  type: 'object',
  required: ['name', 'email'],
  properties: {
    name: { type: 'string', minLength: 1, maxLength: 255 },
    email: { type: 'string', format: 'email' },
    auth0Id: { type: 'string' },
    phone: { type: 'string' },
    language: { type: 'string' },
    timeZone: { type: 'string' },
    jobTitle: { type: 'string' },
    status: { type: 'string', enum: ['active', 'inactive', 'pending'] }
  }
};

export const updateUserSchema = {
  type: 'object',
  properties: {
    name: { type: 'string', minLength: 1, maxLength: 255 },
    email: { type: 'string', format: 'email' },
    phone: { type: 'string' },
    language: { type: 'string' },
    timeZone: { type: 'string' },
    jobTitle: { type: 'string' },
    status: { type: 'string', enum: ['active', 'inactive', 'pending'] }
  }
};

export const updateProfileInfoSchema = {
  type: 'object',
  properties: {
    name: { type: 'string', minLength: 1, maxLength: 255 },
    phone: { type: 'string' },
    language: { type: 'string' },
    timeZone: { type: 'string' },
    jobTitle: { type: 'string' }
  }
};

export const updateAuth0ProfileSchema = {
  type: 'object',
  properties: {
    name: { type: 'string', minLength: 1, maxLength: 255 },
    email: { type: 'string', format: 'email' }
  }
};

export const updateBrandAssignmentsSchema = {
  type: 'object',
  required: ['brandAssignments'],
  properties: {
    brandAssignments: {
      type: 'array',
      items: {
        type: 'object',
        required: ['brandId', 'role', 'action'],
        properties: {
          brandId: { type: 'string' },
          role: { type: 'string' },
          action: { type: 'string', enum: ['add', 'remove', 'update'] }
        }
      }
    }
  }
};

export const updateUserRoleSchema = {
  type: 'object',
  required: ['accountId', 'role'],
  properties: {
    accountId: { type: 'string' },
    role: { type: 'string', enum: ['owner', 'admin', 'member'] }
  }
};

export const connectPluginSchema = {
  type: 'object',
  required: ['pluginName', 'pluginData'],
  properties: {
    pluginName: { type: 'string', minLength: 1 },
    pluginData: { type: 'object' }
  }
};

export const updateUserProfileSchema = {
  type: 'object',
  properties: {
    name: { type: 'string', minLength: 1, maxLength: 255 },
    jobTitle: { type: 'string' },
    email: { type: 'string', format: 'email' },
    phone: { type: 'string' },
    brandRetailerAssignments: {
      type: 'array',
      items: {
        type: 'object',
        required: ['brandId', 'retailerId', 'action'],
        properties: {
          brandId: { type: 'string' },
          retailerId: { type: 'string' },
          action: { type: 'string', enum: ['add', 'remove'] }
        }
      }
    },
    role: { type: 'string', enum: ['owner', 'admin', 'member'] },
    accountId: { type: 'string' },
    status: { type: 'string', enum: ['active', 'inactive', 'pending'] },
    retailerCredentials: {
      type: 'object',
      properties: {
        retailerName: { type: 'string' },
        label: { type: 'string' },
        advertisingId: { type: 'string' },
        status: { type: 'string' }
      }
    }
  }
};

export const userParamsSchema = {
  type: 'object',
  required: ['id'],
  properties: {
    id: { type: 'string' }
  }
};

export const pluginParamsSchema = {
  type: 'object',
  required: ['id', 'pluginName'],
  properties: {
    id: { type: 'string' },
    pluginName: { type: 'string' }
  }
};

export const brandRetailerParamsSchema = {
  type: 'object',
  required: ['id', 'brandId'],
  properties: {
    id: { type: 'string' },
    brandId: { type: 'string' }
  }
};

export const updateBrandRetailerAssignmentsSchema = {
  type: 'object',
  required: ['retailerAssignments'],
  properties: {
    retailerAssignments: {
      type: 'array',
      items: {
        type: 'object',
        required: ['retailerId', 'action'],
        properties: {
          retailerId: { type: 'string' },
          action: { type: 'string', enum: ['add', 'remove'] }
        }
      }
    }
  }
};