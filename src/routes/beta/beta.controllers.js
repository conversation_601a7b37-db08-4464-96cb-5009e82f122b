import { BetaService } from '../../services/beta.service.js';
import { LeadsService } from '../../services/leads.service.js';
import { emailQueue } from '../../services/emailQueue.service.js';
import { AuthService } from '../../services/auth.service.ts';

/**
 * Creates a new lead using data from the request body, with conditional auto-approval and invitation email sending based on the lead source.
 *
 * If the lead source is 'adobe' or 'shoptalk', the lead is auto-approved and a beta invitation email is sent. <PERSON>les duplicate email and validation errors with appropriate HTTP responses.
 */
export async function createLead(request, reply) {
  try {
    // Extract lead data from request body - only the required fields
    const {
      fullName,
      email,
      organization,
      source = 'online' // Default to 'online' if not provided
    } = request.body;

    // Normalize email to lowercase
    const normalizedEmail = email.toLowerCase();

    // Log the incoming request for debugging
    console.log('Received lead creation request:', { fullName, email, organization, source });

    // Check if the source is adobe or shoptalk for auto-approval
    let autoApproved = false;
    let inviteStatus = 'pending'; // Default status

    if (source === 'adobe' || source === 'shoptalk') {
      autoApproved = true;
      inviteStatus = 'accepted'; // Set to accepted for auto-approved sources
    }

    // Create the lead with all the fields, including the inviteStatus
    const lead = await LeadsService.createLead({
      fullName,
      email: normalizedEmail,
      organization,
      source,
      inviteStatus // Include the inviteStatus when creating the lead
    });

    // Since betaCode is now the same as betaId, use betaId directly
    const betaCode = lead.betaId;

    // Construct the signup URL with the beta code (which is now the betaId)
    const signupUrl = `https://app.adfury.ai/signup?betaCode=${betaCode}`;

    // Send invitation email for auto-approved leads
    if (autoApproved) {
      // Auto-send beta invitation email
      try {
        await emailQueue.addBetaInvitationEmail({
          to: normalizedEmail,
          senderName: 'AdFury.ai Team',
          senderEmail: '<EMAIL>',
          betaCode: betaCode,
          personalMessage: `Thank you for your interest in AdFury.ai at ${source}! We're excited to have you join our beta program.`
        });

        console.log(`Auto-sent beta invitation to ${email} from ${source} source`);
      } catch (emailError) {
        console.error('Error sending auto-invitation email:', emailError);
        // Continue with lead creation even if email fails
      }
    }

    // Return the response with email, id, URL, and auto-approval status
    reply.code(201).send({
      success: true,
      message: `Lead created successfully for ${email}${autoApproved ? ' and auto-approved' : ''}`,
      data: {
        id: lead.id,
        email: lead.email,
        url: signupUrl,
        autoApproved
      }
    });
  } catch (error) {
    console.error('Error creating lead:', error);

    // Handle duplicate email error specifically
    if (error.message.includes('already exists')) {
      return reply.code(409).send({
        success: false,
        message: 'Lead creation failed - duplicate email',
        error: error.message
      });
    }

    // Handle validation errors
    if (error.message.includes('Missing required fields')) {
      return reply.code(400).send({
        success: false,
        message: 'Lead creation failed - validation error',
        error: error.message
      });
    }

    // Handle other errors
    reply.code(500).send({
      success: false,
      message: 'Lead creation failed - server error',
      error: 'An unexpected error occurred while creating the lead'
    });
  }
}

/**
 * Updates a lead's invite status and, if newly accepted, sends a beta invitation email.
 *
 * If the invite status is changed to 'accepted' and was not previously accepted, a beta invitation email is sent to the lead. Responds with the updated lead information and whether the invitation email was sent. Returns appropriate HTTP status codes for missing email, lead not found, or server errors.
 */
export async function updateLead(request, reply) {
  try {
    // Extract data from request body
    const {
      email,
      inviteStatus
    } = request.body;

    // Log the incoming request for debugging
    console.log('Received lead update request:', { email, inviteStatus });

    // Validate required fields
    if (!email) {
      return reply.code(400).send({
        success: false,
        message: 'Lead update failed - missing email',
        error: 'Email is required to identify the lead'
      });
    }

    // Get the lead by email
    const existingLead = await LeadsService.getLeadByEmail(email);

    if (!existingLead) {
      return reply.code(404).send({
        success: false,
        message: 'Lead update failed - lead not found',
        error: `No lead found with email: ${email}`
      });
    }

    console.log('Found existing lead:', existingLead);

    // Update the lead
    const updatedLead = await LeadsService.updateLead(email, {
      inviteStatus: inviteStatus || existingLead.inviteStatus
    });

    console.log('Lead updated successfully:', updatedLead);

    // Check if the lead was approved and send beta invitation email
    let emailSent = false;
    if (inviteStatus === 'accepted' && existingLead.inviteStatus !== 'accepted') {
      try {
        // Send beta invitation email
        await emailQueue.addBetaInvitationEmail({
          to: email,
          senderName: 'AdFury.ai Team',
          senderEmail: '<EMAIL>',
          betaCode: updatedLead.betaId,
          personalMessage: 'Congratulations! Your application to join the AdFury.ai beta program has been approved. We\'re excited to have you on board!'
        });

        console.log(`Sent beta invitation email to approved lead: ${email}`);
        emailSent = true;
      } catch (emailError) {
        console.error('Error sending beta invitation email:', emailError);
        // Continue with lead update even if email fails
      }
    }

    // Return simplified success response with inviteStatus included
    reply.code(200).send({
      success: true,
      message: `Lead updated successfully for ${email}${emailSent ? ' and invitation email sent' : ''}`,
      data: {
        id: updatedLead.id,
        email: updatedLead.email,
        inviteStatus: updatedLead.inviteStatus,
        emailSent
      }
    });
  } catch (error) {
    console.error('Error updating lead:', error);

    // Handle other errors
    reply.code(500).send({
      success: false,
      message: 'Lead update failed - server error',
      error: 'An unexpected error occurred while updating the lead'
    });
  }
}

/**
 * Sends a beta invitation email to a specified friend on behalf of the authenticated user.
 *
 * Requires a valid Bearer token for authentication. Validates the user's eligibility, ensures available beta keys, and prevents duplicate invitations to the same email. Tracks the sent beta key and returns the number of remaining invitations.
 * 
 * @returns {Promise<object>} An object indicating success, the recipient's email, and the count of remaining invitations.
 */
export async function sendBetaInvitation(request, reply) {
  try {
    // Get the auth token from the request header
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return reply.code(401).send({
        error: 'Authentication required',
        message: 'You must be logged in to send beta invitations'
      });
    }

    // Extract the token and get user info
    const token = authHeader.substring(7);
    const userInfo = await AuthService.getUserInfo(token);

    if (!userInfo || !userInfo.email) {
      return reply.code(401).send({
        error: 'Authentication required',
        message: 'You must be logged in to send beta invitations'
      });
    }

    // Get the request data and normalize friend's email
    const { friendEmail, personalMessage } = request.body || {};
    const normalizedFriendEmail = friendEmail.toLowerCase();

    if (!normalizedFriendEmail) {
      return reply.code(400).send({
        error: 'Missing required field',
        message: 'Friend\'s email is required'
      });
    }

    // Get the lead by the authenticated user's email
    const lead = await LeadsService.getLeadByEmail(userInfo.email);

    if (!lead) {
      return reply.code(404).send({
        error: 'Lead not found',
        message: 'Your account is not registered as a beta participant'
      });
    }

    // Initialize arrays if they don't exist
    const betaKeys = lead.betaKeys || [];
    const sentKeys = lead.sentKeys || [];

    // Get the keys that have already been sent
    const sentKeyValues = sentKeys.map(sk => sk.key);

    // Find available keys (keys in betaKeys but not in sentKeyValues)
    const availableKeys = betaKeys.filter(key => !sentKeyValues.includes(key));

    // Check if there are any available keys
    if (availableKeys.length === 0) {
      return reply.code(400).send({
        error: 'No beta keys available',
        message: 'You have already sent all your available beta invitations'
      });
    }

    // Check if an invitation has already been sent to this email
    const alreadySentToEmail = sentKeys.some(sk => sk.email === normalizedFriendEmail);

    if (alreadySentToEmail) {
      return reply.code(400).send({
        error: 'Duplicate invitation',
        message: `You have already sent an invitation to ${normalizedFriendEmail}`
      });
    }

    // Get the first available beta key
    const betaKey = availableKeys[0];

    // Send the beta invitation email
    await emailQueue.addBetaInvitationEmail({
      to: normalizedFriendEmail,
      senderName: userInfo.name || lead.fullName,
      senderEmail: userInfo.email,
      betaCode: betaKey,
      personalMessage
    });

    // Track that the beta key was sent
    await LeadsService.trackSentBetaKey(userInfo.email, betaKey, normalizedFriendEmail);

    // Return success response with the number of remaining available keys
    return reply.send({
      success: true,
      message: `Beta invitation sent to ${normalizedFriendEmail}`,
      remainingInvitations: availableKeys.length - 1
    });
  } catch (error) {
    console.error('Error sending beta invitation:', error);

    // Handle specific errors
    if (error.message === 'This beta key has already been sent') {
      return reply.code(400).send({
        error: 'Duplicate invitation',
        message: 'You have already sent an invitation using this beta key'
      });
    }

    return reply.code(500).send({
      error: 'Failed to send beta invitation',
      message: error.message
    });
  }
}

/**
 * Get a lead by email
 * This endpoint requires authentication
 */
export async function fetchLeadByEmail(request, reply) {
  try {
    // Extract email from request body
    const { email } = request.body;

    if (!email) {
      return reply.code(400).send({
        success: false,
        message: 'Email query parameter is required',
        error: 'Missing email parameter'
      });
    }

    // Get the lead by normalized email
    const lead = await LeadsService.getLeadByEmail(email);

    if (!lead) {
      return reply.code(404).send({
        success: false,
        message: 'Lead not found',
        error: `No lead found with email: ${email}`
      });
    }

    // Check if each sent key has been used
    const sentKeys = lead.sentKeys || [];
    const enhancedSentKeys = await Promise.all(
      sentKeys.map(async (sentKey) => {
        try {
          const isUsed = await BetaService.isBetaKeyUsed(sentKey.key);
          return {
            ...sentKey,
            isUsed
          };
        } catch (error) {
          console.error(`Error checking if key ${sentKey.key} is used:`, error);
          return {
            ...sentKey,
            isUsed: false
          };
        }
      })
    );

    // Return the lead data with beta keys and enhanced sent keys
    reply.code(200).send({
      success: true,
      message: 'Lead found',
      data: {
        id: lead.id,
        email: lead.email,
        fullName: lead.fullName,
        organization: lead.organization,
        inviteStatus: lead.inviteStatus,
        betaId: lead.betaId,
        betaKeys: lead.betaKeys || [],
        sentKeys: enhancedSentKeys,
        createdAt: lead.createdAt,
        updatedAt: lead.updatedAt
      }
    });
  } catch (error) {
    console.error('Error retrieving lead:', error);
    reply.code(500).send({
      success: false,
      message: 'Failed to retrieve lead',
      error: 'An unexpected error occurred while retrieving the lead'
    });
  }
}

// Similar check needed for getKeyByUser function if it requires authentication
export async function getKeyByUser(request, reply) {
  try {
    // Assuming verifyAuth is used for this route:
    const sessionUser = request.user;
    if (!sessionUser || !sessionUser.auth0Id) {
      return reply.code(401).send({ error: 'Unauthorized - Session invalid' });
    }

    // Find user in DB using session info
    const [dbUser] = await db
      .select()
      .from(users)
      .where(eq(users.auth0Id, sessionUser.auth0Id)) // Use sessionUser.auth0Id
      .limit(1);

    if (!dbUser) {
      return reply.code(404).send({ error: 'User not found' });
    }

    if (!dbUser.betaKeyId) {
      // ... existing code ...
    }
  } catch (error) {
    console.error('Error retrieving key:', error);
    reply.code(500).send({
      success: false,
      message: 'Failed to retrieve key',
      error: 'An unexpected error occurred while retrieving the key'
    });
  }
}