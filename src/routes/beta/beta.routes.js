import {
  createLead,
  updateLead,
  sendBetaInvitation,
  fetchLeadByEmail
} from './beta.controllers.js';

export default async function betaRoutes(fastify, options) {
  // Ensure auth decorator is available for protected routes
  if (!fastify.hasDecorator("verifyAuth")) {
    throw new Error("Auth plugin must be registered before routes");
  }

  // Fetch lead by email
  fastify.post("/leads/fetch", {
    onRequest: [fastify.verifyAuth],
    schema: {
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: { type: 'string', format: 'email' }
        }
      }
    },
    handler: fetchLeadByEmail
  });

  // Create lead from n8n workflow (public route)
  fastify.post("/leads", {
    schema: {
      body: {
        type: 'object',
        required: ['fullName', 'email', 'organization'],
        properties: {
          fullName: { type: 'string' },
          email: { type: 'string', format: 'email' },
          organization: { type: 'string' },
          source: {
            type: 'string',
            enum: ['adobe', 'shoptalk', 'online'],
            default: 'online'
          }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                url: { type: 'string' },
                autoApproved: { type: 'boolean' }
              }
            }
          }
        }
      }
    },
    handler: createLead
  });

  // Update lead from n8n workflow (public route)
  fastify.put("/leads", {
    schema: {
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: { type: 'string', format: 'email' },
          inviteStatus: {
            type: 'string',
            enum: ['pending', 'accepted', 'rejected'],
            default: 'pending'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                inviteStatus: { type: 'string' },
                emailSent: { type: 'boolean' }
              }
            }
          }
        }
      }
    },
    handler: updateLead
  });

  // Send beta invitation (protected route - requires authentication)
  fastify.post("/invitations", {
    onRequest: [fastify.verifyAuth],
    schema: {
      body: {
        type: 'object',
        required: ['friendEmail'],
        properties: {
          friendEmail: { type: 'string', format: 'email' },
          personalMessage: { type: 'string', maxLength: 500 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            remainingInvitations: { type: 'integer' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    },
    handler: sendBetaInvitation
  });


}