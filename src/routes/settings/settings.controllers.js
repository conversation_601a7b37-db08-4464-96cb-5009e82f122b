import { eq, inArray, sql, and, ne } from "drizzle-orm";
import { db } from "../../db/index.js";
import { users } from "../../db/schema/users.js";
import { brands } from "../../db/schema/brands.js";
import { products } from "../../db/schema/products.js";
import { credentialSets } from "../../db/schema/credentialSets.js";
import { userSettings } from "../../db/schema/userSettings.js";
import { brandUsers } from "../../db/schema/brandUsers.js";
import { userAccounts } from "../../db/schema/userAccounts.js";
import { userCredentialSets } from "../../db/schema/userCredentialSets.js";
import { retailers, brandRetailers } from "../../db/schema/retailers.js";
import { credentialSetRetailers } from "../../db/schema/credentialSetRetailers.js";
import axios from "axios";
import { logger } from "../../utils/logger.js";
import { fetchProductsForBrandsInBackground } from "../products/products.controllers.js";

/**
 * Validates a Walmart advertising ID by sending a request to the Walmart API.
 *
 * @param {string} wmAdvertiserId - The Walmart advertiser ID to validate.
 * @param {object} request - The HTTP request object containing authorization headers.
 * @return {object} An object indicating whether the ID is valid. If valid, includes associated brand data and total results; if invalid, includes an error message describing the failure reason.
 */
async function validateAdvertisingIdWithWalmart(wmAdvertiserId, request) {
  try {
    const response = await axios({
      method: 'post',
      url: `${process.env.WALMART_MANAGER_API_URL}/display/catalog/brands/list/`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.authorization,
        'x-account-id': request.headers['x-account-id'],
      },
      data: {
        advertiserId: wmAdvertiserId
      }
    });

    logger.info('Walmart API validation response received', { 
      advertiserId: wmAdvertiserId,
      responseCode: response.data.code,
      totalResults: response.data.totalResults
    });

    // Check if response indicates success
    if (response.status === 200 && response.data.code === 'success') {
      return {
        isValid: true,
        brands: response.data.data || [],
        totalResults: response.data.totalResults || 0
      };
    } else {
      return {
        isValid: false,
        error: 'Invalid advertising ID - not found in Walmart system'
      };
    }

  } catch (error) {
    logger.error('Error validating advertising ID with Walmart', error, {
      advertiserId: wmAdvertiserId
    });
    
    // Handle specific error cases
    if (error.response?.status === 401) {
      return {
        isValid: false,
        error: 'Authentication failed with Walmart API'
      };
    } else if (error.response?.status === 404) {
      return {
        isValid: false,
        error: 'Invalid advertising ID - not found in Walmart system'
      };
    } else if (error.response?.status >= 400 && error.response?.status < 500) {
      return {
        isValid: false,
        error: 'Invalid advertising ID - rejected by Walmart API'
      };
    } else {
      return {
        isValid: false,
        error: 'Walmart validation service unavailable - please try again later'
      };
    }
  }
}

/**
 * Inserts brand records from Walmart API data into the database for a specific account and credential set.
 * Also connects the specified user as an owner of each created brand.
 *
 * Attempts to store each brand in the provided array, associating them with the given account and credential set. 
 * Creates user-brand relationships with 'owner' role for each brand. Continues processing remaining brands even if some insertions fail.
 *
 * @param {Array<Object>} brandsData - Array of brand objects from the Walmart API.
 * @param {string|number} accountId - Account ID to associate with the brands.
 * @param {string|number} credentialSetId - Credential set ID to associate as the primary credential for each brand.
 * @param {string|number} userId - User ID to connect as owner of each brand.
 * @returns {Object} An object indicating success, the number of brands stored, the total received, and the stored brand records. On failure, returns an error message.
 */
async function storeBrandsFromWalmart(brandsData, accountId, credentialSetId, userId) {
  try {
    const storedBrands = [];
    
    for (const brandData of brandsData) {
      try {
        // Insert brand into database
        const [newBrand] = await db
          .insert(brands)
          .values({
            accountId: accountId,
            name: brandData.brandName,
            productCount: brandData.itemCount || 0,
            primaryCredentialId: credentialSetId,
            // All other fields use default values
          })
          .returning();

        storedBrands.push(newBrand);
        console.log(`Stored brand: ${brandData.brandName} with ${brandData.itemCount} items`);
        
        // Connect user to brand as owner
        try {
          await db
            .insert(brandUsers)
            .values({
              brandId: newBrand.id,
              userId: userId,
              role: 'owner',
              createdAt: new Date(),
              updatedAt: new Date()
            });
          console.log(`Connected user ${userId} as owner to brand ${brandData.brandName}`);
        } catch (userBrandError) {
          console.error(`Error connecting user to brand ${brandData.brandName}:`, userBrandError);
          // Continue with other brands even if user connection fails
          continue;
        }
        
      } catch (error) {
        console.error(`Error storing brand ${brandData.brandName}:`, error);
        // Continue with other brands even if one fails
        continue;
      }
    }

    return {
      success: true,
      storedCount: storedBrands.length,
      totalReceived: brandsData.length,
      brands: storedBrands
    };

  } catch (error) {
    logger.error('Error storing brands from Walmart', error);
    return {
      success: false,
      error: 'Failed to store brand data'
    };
  }
}

/**
 * Associates a list of brand IDs with the Walmart retailer in the database.
 *
 * Attempts to create brand-retailer relationships for each provided brand ID, skipping any that are already linked. Returns the number of successfully created links and retailer information.
 *
 * @param {Array} brandIds - List of brand IDs to associate with Walmart.
 * @returns {Object} Result object containing success status, count of linked brands, total brands processed, and retailer details.
 */
async function linkBrandsToWalmart(brandIds) {
  try {
    if (!brandIds || brandIds.length === 0) {
      return {
        success: true,
        linkedCount: 0,
        message: 'No brands to link'
      };
    }

    // Find Walmart retailer
    const [walmartRetailer] = await db
      .select()
      .from(retailers)
      .where(eq(retailers.slug, 'walmart'))
      .limit(1);

    if (!walmartRetailer) {
      return {
        success: false,
        error: 'Walmart retailer not found in database'
      };
    }

    const linkedRelationships = [];
    
    for (const brandId of brandIds) {
      try {
        // Check if relationship already exists
        const [existingRelation] = await db
          .select()
          .from(brandRetailers)
          .where(
            and(
              eq(brandRetailers.brandId, brandId),
              eq(brandRetailers.retailerId, walmartRetailer.id)
            )
          )
          .limit(1);

        if (!existingRelation) {
          // Create the relationship
          const [newRelation] = await db
            .insert(brandRetailers)
            .values({
              brandId: brandId,
              retailerId: walmartRetailer.id
            })
            .returning();

          linkedRelationships.push(newRelation);
          console.log(`Linked brand ${brandId} to Walmart`);
        }
        
      } catch (error) {
        console.error(`Error linking brand ${brandId} to Walmart:`, error);
        continue;
      }
    }

    return {
      success: true,
      linkedCount: linkedRelationships.length,
      totalBrands: brandIds.length,
      retailer: walmartRetailer
    };

  } catch (error) {
    console.error('Error linking brands to Walmart:', error);
    return {
      success: false,
      error: 'Failed to link brands to Walmart'
    };
  }
}

/**
 * Associates a credential set with the Walmart retailer in the database.
 *
 * @param {string} credentialSetId - The unique identifier of the credential set to link.
 * @returns {Object} Result object indicating success or failure, including retailer and relationship data if successful.
 */
async function linkCredentialSetToWalmart(credentialSetId) {
  try {
    // Find Walmart retailer
    const [walmartRetailer] = await db
      .select()
      .from(retailers)
      .where(eq(retailers.slug, 'walmart'))
      .limit(1);

    if (!walmartRetailer) {
      return {
        success: false,
        error: 'Walmart retailer not found in database'
      };
    }

    // Check if relationship already exists
    const [existingRelation] = await db
      .select()
      .from(credentialSetRetailers)
      .where(
        and(
          eq(credentialSetRetailers.credentialSetId, credentialSetId),
          eq(credentialSetRetailers.retailerId, walmartRetailer.id)
        )
      )
      .limit(1);

    if (existingRelation) {
      return {
        success: true,
        message: 'Credential set already linked to Walmart',
        retailer: walmartRetailer
      };
    }

    // Create the relationship
    const [newRelation] = await db
      .insert(credentialSetRetailers)
      .values({
        credentialSetId: credentialSetId,
        retailerId: walmartRetailer.id
      })
      .returning();

    console.log(`Linked credential set ${credentialSetId} to Walmart`);
    
    return {
      success: true,
      relationship: newRelation,
      retailer: walmartRetailer
    };

  } catch (error) {
    console.error('Error linking credential set to Walmart:', error);
    return {
      success: false,
      error: 'Failed to link credential set to Walmart'
    };
  }
}

/**
 * Associates a credential set with the Canva retailer in the database.
 */
async function linkCredentialSetToCanva(credentialSetId, canvaRetailerId) {
  try {
    // Check if relationship already exists
    const [existingRelation] = await db
      .select()
      .from(credentialSetRetailers)
      .where(
        and(
          eq(credentialSetRetailers.credentialSetId, credentialSetId),
          eq(credentialSetRetailers.retailerId, canvaRetailerId)
        )
      )
      .limit(1);

    if (existingRelation) {
      return {
        success: true,
        message: 'Credential set already linked to Canva',
        retailer: { id: canvaRetailerId, name: 'Canva', slug: 'canva' }
      };
    }

    // Create the relationship
    const [newRelation] = await db
      .insert(credentialSetRetailers)
      .values({
        credentialSetId: credentialSetId,
        retailerId: canvaRetailerId
      })
      .returning();

    console.log(`Linked credential set ${credentialSetId} to Canva`);
    
    return {
      success: true,
      relationship: newRelation,
      retailer: { id: canvaRetailerId, name: 'Canva', slug: 'canva' }
    };

  } catch (error) {
    console.error('Error linking credential set to Canva:', error);
    return {
      success: false,
      error: 'Failed to link credential set to Canva'
    };
  }
}

/**
 * Retrieves the authenticated user's profile, associated account IDs, credential sets, linked brands, and user settings.
 *
 * Combines session user data with database records to return a comprehensive settings object, including all credential sets linked to the user and their associated brands. If user settings do not exist, creates default settings with email notifications enabled.
 *
 * Returns a 401 error if the session is invalid, 404 if the user or accounts are not found, and 500 for internal errors.
 */
export async function getSettings(request, reply) {
  try {
    const sessionUser = request.user;
    console.log("sessionUser", sessionUser);
    if (!sessionUser || !sessionUser.auth0Id) {
      return reply.code(401).send({ error: 'Unauthorized - Session invalid' });
    }

    // Get user profile from database using session info
    const [dbUser] = await db
      .select()
      .from(users)
      .where(eq(users.auth0Id, sessionUser.auth0Id))
      .limit(1);

    console.log("dbUser", dbUser);

    if (!dbUser) {
      return reply.code(404).send({ error: "User not found" });
    }

    // Get all of the user's accounts
    const userAccountsResult = await db
      .select({
        accountId: userAccounts.accountId
      })
      .from(userAccounts)
      .where(eq(userAccounts.userId, dbUser.id));

    if (!userAccountsResult.length) {
      return reply.code(404).send({ error: "User has no associated accounts" });
    }

    // Extract all account IDs the user has access to
    const userAccountIds = userAccountsResult.map(ua => ua.accountId);

    // First get just the user's brand relationships
    const brandUsersResult = await db
      .select({
        brandId: brandUsers.brandId,
        role: brandUsers.role,
      })
      .from(brandUsers)
      .where(eq(brandUsers.userId, dbUser.id));

    // Create a set of brand IDs and role map
    const userBrandIds = new Set(brandUsersResult.map(bu => bu.brandId));
    const brandRolesMap = new Map(
      brandUsersResult.map(bu => [bu.brandId, bu.role])
    );

    // Now fetch credentialSets directly for this user
    const credentialSetsResult = await db
      .select({
        credentialSet: credentialSets,
      })
      .from(userCredentialSets)
      .innerJoin(
        credentialSets,
        eq(userCredentialSets.credentialSetId, credentialSets.id)
      )
      .where(
        and(
          eq(userCredentialSets.userId, dbUser.id),
          inArray(credentialSets.accountId, userAccountIds)
        )
      );

    const filteredCredentialSets = credentialSetsResult;

    console.log("filteredCredentialSets", filteredCredentialSets);

    // Get all linked brands for this user
    const linkedBrands = await db
      .select()
      .from(brandUsers)
      .where(eq(brandUsers.userId, dbUser.id));

    console.log("linkedBrands", linkedBrands);

    // Enhance credentials - no longer linked to specific brands
    const enhancedCredentialSets = filteredCredentialSets.map((cred) => ({
      ...cred.credentialSet,
      linkedBrands: linkedBrands,
    }));

    console.log("enhancedCredentialSets", enhancedCredentialSets);

    // Get or create user settings
    let [userSettingsData] = await db
      .select()
      .from(userSettings)
      .where(eq(userSettings.userId, dbUser.id))
      .limit(1);

    if (!userSettingsData) {
      [userSettingsData] = await db
        .insert(userSettings)
        .values({
          userId: dbUser.id,
          settings: {
            emailNotifications: true
          }
        })
        .returning();
    }

    // Combine auth0 profile (from session) with database user data
    const userProfile = {
      ...sessionUser,
      id: dbUser.id,
      account_ids: userAccountIds, // Now returning all account IDs
    };

    console.log("userSettings", userSettingsData);
    const response = {
      profile: userProfile,
      credentials: enhancedCredentialSets,
      settings: userSettingsData.settings,
    };
    console.log("Full response:", response);
    return reply.send(response);
  } catch (error) {
    logger.error('Error fetching settings', error, {
      userId: sessionUser?.id
    });
    return reply.code(500).send({ error: "Internal server error" });
  }
}

/**
 * Updates the authenticated user's settings by merging new settings data with existing settings or creating default settings if none exist.
 * 
 * If the user does not have existing settings, a new settings record is created. Otherwise, the provided settings are merged with the current settings and the record is updated.
 * 
 * @returns {object} The updated or newly created user settings.
 */
export async function updateSettings(request, reply) {
  try {
    const sessionUser = request.user;
    if (!sessionUser || !sessionUser.auth0Id) {
      return reply.code(401).send({ error: 'Unauthorized - Session invalid' });
    }

    const settingsData = request.body;

    // Fetch user from DB to get their internal ID
    const [dbUser] = await db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.auth0Id, sessionUser.auth0Id))
      .limit(1);

    if (!dbUser) {
      return reply.code(404).send({ error: "User not found" });
    }

    // First get existing settings to merge with new settings
    const [existingSettings] = await db
      .select()
      .from(userSettings)
      .where(eq(userSettings.userId, dbUser.id))
      .limit(1);

    // Create default settings if they don't exist
    if (!existingSettings) {
      const [newSettings] = await db
        .insert(userSettings)
        .values({
          userId: dbUser.id,
          settings: settingsData
        })
        .returning();

      return reply.send(newSettings);
    }

    // Merge existing settings with new settings
    const mergedSettings = {
      ...existingSettings.settings,
      ...settingsData
    };

    // Update settings
    const [updatedSettings] = await db
      .update(userSettings)
      .set({
        settings: mergedSettings,
        updatedAt: new Date()
      })
      .where(eq(userSettings.userId, dbUser.id))
      .returning();

    return reply.send(updatedSettings);
  } catch (error) {
    logger.error('Error updating settings', error, {
      userId: sessionUser?.id
    });
    return reply.code(500).send({ error: "Internal server error" });
  }
}

/**
 * Retrieves the Walmart secret associated with a given partner ID.
 *
 * Extracts the partner ID from the request, locates the corresponding credential set, and returns the appropriate secret based on the client type (SELLER or SUPPLIER). Responds with relevant error codes if the partner ID is missing, credentials are not found, client type is invalid, or the secret is unavailable.
 */
export async function getSecretByPartnerId(request, reply) {
  try {
    // Extract partnerId from request parameters
    const partnerId = request.params.partnerId;

    if (!partnerId) {
      return reply.code(400).send({ error: "Partner ID is required" });
    }

    // Get credential sets for the specific partnerId
    const credentialSetsResult = await db
      .select()
      .from(credentialSets)
      .where(
        eq(sql`(${credentialSets.credentials}->>'partnerId')`, partnerId)
      );

    // Check if credential sets exist
    if (!credentialSetsResult || credentialSetsResult.length === 0) {
      return reply
        .code(404)
        .send({ error: "No credentials found for this partner ID" });
    }

    const credentialSet = credentialSetsResult[0];
    const credentials = credentialSet.credentials;

    // Return the appropriate secret based on clientType
    let secret = null;
    if (credentials.clientType === "SELLER") {
      secret = credentials.wmtMarketplaceSecret;
    } else if (credentials.clientType === "SUPPLIER") {
      secret = credentials.supplierSecret;
    } else {
      return reply.code(400).send({ error: "Invalid client type" });
    }

    if (!secret) {
      return reply
        .code(404)
        .send({ error: "Secret not found for this client type" });
    }

    return reply.send({ secret });
  } catch (error) {
    console.error("Error fetching secret by partnerId:", error);
    return reply.code(500).send({ error: "Internal server error" });
  }
}

export async function deleteCredentialSetFields(request, reply) {
  try {
    const credentialSetId = request.params.credentialSetId;

    if (!credentialSetId) {
      return reply.code(400).send({ error: "Credential Set ID is required" });
    }

    // Get the current credential set
    const [credentialSet] = await db
      .select()
      .from(credentialSets)
      .where(eq(credentialSets.id, credentialSetId))
      .limit(1);

    if (!credentialSet) {
      return reply.code(404).send({ error: "Credential set not found" });
    }

    // Create a new credentials object with specific fields removed
    const updatedCredentials = { ...credentialSet.credentials };
    delete updatedCredentials.partnerId;
    delete updatedCredentials.refreshToken;
    delete updatedCredentials.clientType;

    // Update the credential set
    const [updatedCredentialSet] = await db
      .update(credentialSets)
      .set({
        credentials: updatedCredentials,
        updatedAt: new Date()
      })
      .where(eq(credentialSets.id, credentialSetId))
      .returning();

    return reply.send(updatedCredentialSet);
  } catch (error) {
    console.error("Error deleting credential set fields:", error);
    return reply.code(500).send({ error: "Internal server error" });
  }
}

/**
 * Updates an existing credential set by merging new credential data into the current credentials.
 *
 * Responds with the updated credential set, or an error if the credential set is not found or the request is invalid.
 */
export async function updateCredentialSet(request, reply) {
  try {
    const credentialSetId = request.params.credentialSetId;
    const updateData = request.body;

    if (!credentialSetId) {
      return reply.code(400).send({ error: "Credential Set ID is required" });
    }

    // Get current credential set
    const [currentCredentialSet] = await db
      .select()
      .from(credentialSets)
      .where(eq(credentialSets.id, credentialSetId))
      .limit(1);

    if (!currentCredentialSet) {
      return reply.code(404).send({ error: "Credential set not found" });
    }

    // Merge new credentials with existing ones
    const updatedCredentials = {
      ...currentCredentialSet.credentials,
      ...updateData
    };

    // Update the credential set
    const [updatedCredentialSet] = await db
      .update(credentialSets)
      .set({
        credentials: updatedCredentials,
        updatedAt: new Date()
      })
      .where(eq(credentialSets.id, credentialSetId))
      .returning();

    return reply.send(updatedCredentialSet);
  } catch (error) {
    console.error("Error updating credential set:", error);
    return reply.code(500).send({ error: "Internal server error" });
  }
}


// BAF - 35 CRUD for Credential Sets
/**
 * Retrieves all active credential sets for a given account, optionally filtered by retailer type.
 *
 * Returns a list of credential sets with metadata, connection status, and sanitized credential details, including token presence indicators.
 */

export async function getAccountCredentials(request, reply) {
  try {
    const { accountId } = request.params;
    const { retailerId } = request.query;
    const sessionUser = request.user;

    // Account access already verified by middleware

    let whereConditions = [
      eq(credentialSets.accountId, accountId),
    ];

    // Add retailer filter if provided (?retailerId=walmart)
    if (retailerId === 'walmart') {
      whereConditions.push(
        inArray(credentialSets.type, ['wm_display', 'wm_supplier', 'wm_marketplace', 'wm_sponsored'])
      );
    }

    // Get credential sets for the account with retailer information
    const credentialSetsResult = await db
      .select({
        id: credentialSets.id,
        name: credentialSets.name,
        type: credentialSets.type,
        isActive: credentialSets.isActive,
        deletedAt: credentialSets.deletedAt,
        credentials: credentialSets.credentials,
        createdAt: credentialSets.createdAt,
        updatedAt: credentialSets.updatedAt,
        retailerId: retailers.id,
        retailerName: retailers.name
      })
      .from(credentialSets)
      .leftJoin(credentialSetRetailers, eq(credentialSets.id, credentialSetRetailers.credentialSetId))
      .leftJoin(retailers, eq(credentialSetRetailers.retailerId, retailers.id))
      .where(and(...whereConditions));

    const processedCredentials = credentialSetsResult.map(cred => {
      const credentials = cred.credentials || {};
      
      // Determine if connected based on type
      let isConnected = false;
      if (cred.type === 'wm_display' || cred.type === 'wm_sponsored') {
        isConnected = !!(credentials.wmAdvertiserId && credentials.wmAdvertiserId.trim() !== '');
      } else if (cred.type === 'wm_supplier' || cred.type === 'wm_marketplace') {
        isConnected = !!(credentials.wmPartnerId && credentials.refreshToken);
      }

      return {
        id: cred.id,
        name: cred.name,
        type: cred.type,
        isActive: cred.isActive,
        isConnected,
        deletedAt: cred.deletedAt,
        createdAt: cred.createdAt,
        updatedAt: cred.updatedAt,
        retailerId: cred.retailerId,
        retailerName: cred.retailerName,
        credentials: {
          wmAdvertiserId: credentials.wmAdvertiserId || null,
          environment: credentials.environment || 'production',
          apiVersion: credentials.apiVersion || 'v1',
          clientType: credentials.clientType || 'WMT_DISPLAY',
          // For OAuth types, show if they have tokens but don't expose them
          hasRefreshToken: !!credentials.refreshToken,
          hasAccessToken: !!credentials.accessToken
        }
      };
    });

    return reply.send({ credentialSets: processedCredentials });
  } catch (error) {
    console.error("Error fetching account credentials:", error);
    return reply.code(500).send({ error: "Internal server error" });
  }
}

/**
 * Creates one or more credential sets for an account, with distinct handling for display/sponsored vs supplier/marketplace types.
 *
 * For each entry in the `accountEntries` array, this function:
 * - Validates required fields and credential type.
 * - Checks for duplicate credential set names and advertising IDs within the account.
 * - For display and sponsored types, validates the advertising ID with Walmart and stores associated brand data.
 * - Creates the credential set, links it to the user, and associates related brands and the credential set with the Walmart retailer.
 * 
 * Returns a batch result containing the status and metadata for each entry, along with a summary of successes and failures.
 *
 * @returns {object} Batch processing results for all account entries, including per-entry status and a summary.
 */
export async function createAccountCredential(request, reply) {
  try {
    const { accountId } = request.params;
    const { type, accountEntries } = request.body;
    const sessionUser = request.user;

    if (!type) {
      return reply.code(400).send({ error: 'Type is required' });
    }

    const validTypes = ['wm_display', 'wm_supplier', 'wm_marketplace', 'wm_sponsored', 'canva'];
    if (!validTypes.includes(type)) {
      return reply.code(400).send({ error: 'Invalid credential type' });
    }

    if (!accountEntries || !Array.isArray(accountEntries)) {
      return reply.code(400).send({ error: 'accountEntries array is required' });
    }

    if (accountEntries.length === 0) {
      return reply.code(400).send({ error: 'At least one account entry is required' });
    }

    const MAX_BATCH_SIZE = 100;
    if (accountEntries.length > MAX_BATCH_SIZE) {
      return reply.code(400).send({ error: `Batch size exceeds maximum limit of ${MAX_BATCH_SIZE}` });
    }

    // Route to appropriate handler based on type
    if (type === 'wm_display' || type === 'wm_sponsored') {
      return await createDisplayCredentials(accountId, type, accountEntries, sessionUser, request, reply);
    } else if (type === 'wm_supplier' || type === 'wm_marketplace') {
      return await createSupplierCredentials(accountId, type, accountEntries, sessionUser, reply);
    } else if (type === 'canva') {
      return await createCanvaCredentials(accountId, type, accountEntries, sessionUser, reply);
    }

    const results = [];
    let successfulCount = 0;
    let failedCount = 0;

    // Process each account entry individually
    for (const entry of accountEntries) {
      const { label, wmAdvertiserId } = entry;
      
      try {
        // Check for duplicate name in the same account
        const [existingCredential] = await db
          .select()
          .from(credentialSets)
          .where(
            and(
              eq(credentialSets.accountId, accountId),
              eq(credentialSets.name, label),
              eq(credentialSets.isActive, true)
            )
          )
          .limit(1);

        if (existingCredential) {
          results.push({
            label,
            wmAdvertiserId: String(wmAdvertiserId),
            success: false,
            error: 'A credential set with this name already exists in this account',
            code: 409
          });
          failedCount++;
          continue;
        }

        // Check for duplicate wmAdvertiserId in the same account (for display and sponsored types)
        if ((type === 'wm_display' || type === 'wm_sponsored') && wmAdvertiserId) {
          const wmAdvertiserIdString = String(wmAdvertiserId);
          const [existingAdvertiserId] = await db
            .select()
            .from(credentialSets)
            .where(
              and(
                eq(credentialSets.accountId, accountId),
                eq(sql`(${credentialSets.credentials}->>'wmAdvertiserId')`, wmAdvertiserIdString),
                eq(credentialSets.isActive, true)
              )
            )
            .limit(1);

          if (existingAdvertiserId) {
            results.push({
              label,
              wmAdvertiserId: wmAdvertiserIdString,
              success: false,
              error: 'A credential set with this advertising ID already exists in this account',
              code: 409
            });
            failedCount++;
            continue;
          }
        }

        // Step 1: Validate advertising ID with Walmart API (only for display and sponsored types)
        let validationResult = { isValid: true, brands: [], totalResults: 0 };
        let wmAdvertiserIdString = '';
        
        if (type === 'wm_display' || type === 'wm_sponsored') {
          wmAdvertiserIdString = String(wmAdvertiserId);
          console.log(`Validating advertising ID ${wmAdvertiserIdString} with Walmart...`);
          validationResult = await validateAdvertisingIdWithWalmart(wmAdvertiserIdString, request);
          
          if (!validationResult.isValid) {
            results.push({
              label,
              wmAdvertiserId: wmAdvertiserIdString,
              success: false,
              error: validationResult.error,
              code: 400
            });
            failedCount++;
            continue;
          }

          console.log(`✅ Advertising ID ${wmAdvertiserIdString} is valid. Found ${validationResult.totalResults} brands.`);
        } else {
          // For non-display types, use the provided wmAdvertiserId if available, otherwise empty string
          wmAdvertiserIdString = wmAdvertiserId ? String(wmAdvertiserId) : '';
        }

        let credentialData = {
          nonce: null,
          state: null,
          apiVersion:  'v1',
          clientType:  'WMT_DISPLAY',
          accessToken: '',
          environment: 'production',
          permissions: [],
          refreshToken: '',
          wmPartnerId: '',
          tokenExpiresAt: '',
          wmAdvertiserId: wmAdvertiserIdString || ''
        };

        // Create credential set with custom label
        const [newCredentialSet] = await db
          .insert(credentialSets)
          .values({
            name: label,
            type,
            credentials: credentialData,
            accountId,
            adminId: sessionUser.id,
            isShared: false,
            isActive: true
          })
          .returning();

        // Create user-credential relationship
        await db
          .insert(userCredentialSets)
          .values({
            userId: sessionUser.id,
            credentialSetId: newCredentialSet.id
          });

        // Step 2: Store brands from Walmart API response (only for display and sponsored types)
        let brandStorageResult = { storedCount: 0 };
        if (type === 'wm_display' || type === 'wm_sponsored') {
          console.log(`Storing ${validationResult.brands.length} brands for advertising ID ${wmAdvertiserIdString}...`);
          brandStorageResult = await storeBrandsFromWalmart(
            validationResult.brands, 
            accountId, 
            newCredentialSet.id
          );
        }

        // Step 3: Link brands to Walmart retailer
        let linkBrandsResult = { linkedCount: 0 };
        if ((type === 'wm_display' || type === 'wm_sponsored') && brandStorageResult.success && brandStorageResult.brands) {
          console.log(`Linking ${brandStorageResult.storedCount} brands to Walmart...`);
          linkBrandsResult = await linkBrandsToWalmart(brandStorageResult.brands.map(b => b.id));
        }

        // Step 4: Link credential set to Walmart retailer
        let linkCredentialSetResult = { success: true, retailer: null };
        if (type === 'wm_display' || type === 'wm_sponsored' || type === 'wm_supplier' || type === 'wm_marketplace') {
          console.log(`Linking credential set ${newCredentialSet.id} to Walmart...`);
          linkCredentialSetResult = await linkCredentialSetToWalmart(newCredentialSet.id);
        }

        // Determine if connected based on type
        let isConnected = false;
        if (type === 'wm_display' || type === 'wm_sponsored') {
          isConnected = !!(wmAdvertiserIdString && wmAdvertiserIdString.trim() !== '');
        } else if (type === 'wm_supplier' || type === 'wm_marketplace') {
          isConnected = !!(credentialData.wmPartnerId && credentialData.refreshToken);
        }

        results.push({
          label,
          wmAdvertiserId: wmAdvertiserIdString,
          success: true,
          data: {
            id: newCredentialSet.id,
            name: newCredentialSet.name,
            type: newCredentialSet.type,
            isActive: newCredentialSet.isActive,
            isConnected,
            wmAdvertiserId: wmAdvertiserIdString,
            retailer: linkCredentialSetResult.retailer ? {
              id: linkCredentialSetResult.retailer.id,
              name: linkCredentialSetResult.retailer.name,
              slug: linkCredentialSetResult.retailer.slug
            } : null,
            totalBrands: validationResult.totalResults,
            brandsStored: brandStorageResult.storedCount,
            brandsLinkedToRetailer: linkBrandsResult.linkedCount,
            createdAt: newCredentialSet.createdAt,
            updatedAt: newCredentialSet.updatedAt
          }
        });
        successfulCount++;

      } catch (error) {
        console.error(`Error creating credential set for entry ${label}:`, error);
        results.push({
          label,
          wmAdvertiserId: String(wmAdvertiserId || ''),
          success: false,
          error: 'Internal server error while creating credential set',
          code: 500
        });
        failedCount++;
      }
    }

    // Return batch processing results
    return reply.code(201).send({
      results,
      summary: {
        total: accountEntries.length,
        successful: successfulCount,
        failed: failedCount
      },
      message: `Processed ${accountEntries.length} account entries: ${successfulCount} successful, ${failedCount} failed`
    });

  } catch (error) {
    console.error("Error creating account credentials:", error);
    return reply.code(500).send({ error: "Internal server error" });
  }
}

/**
 * Handles creation of display/sponsored credential sets with Walmart API validation and brand management.
 */
async function createDisplayCredentials(accountId, type, accountEntries, sessionUser, request, reply) {
  // Validate each entry has required fields for display/sponsored
  for (const entry of accountEntries) {
    if (!entry.label || typeof entry.label !== 'string' || !entry.label.trim()) {
      return reply.code(400).send({ error: 'Each account entry must have a valid label' });
    }
    if (!entry.wmAdvertiserId || !String(entry.wmAdvertiserId).trim()) {
      return reply.code(400).send({ error: 'Each account entry must have a valid advertising ID' });
    }
  }

  const results = [];
  let successfulCount = 0;
  let failedCount = 0;

  for (const entry of accountEntries) {
    const { label, wmAdvertiserId } = entry;
    const wmAdvertiserIdString = String(wmAdvertiserId);
    
    try {
      // Check for duplicate name
      const [existingCredential] = await db
        .select()
        .from(credentialSets)
        .where(
          and(
            eq(credentialSets.accountId, accountId),
            eq(credentialSets.name, label),
            eq(credentialSets.isActive, true)
          )
        )
        .limit(1);

      if (existingCredential) {
        results.push({
          label,
          wmAdvertiserId: wmAdvertiserIdString,
          success: false,
          error: 'A credential set with this name already exists in this account',
          code: 409
        });
        failedCount++;
        continue;
      }

      // Check for duplicate wmAdvertiserId
      const [existingAdvertiserId] = await db
        .select()
        .from(credentialSets)
        .where(
          and(
            eq(credentialSets.accountId, accountId),
            eq(sql`(${credentialSets.credentials}->>'wmAdvertiserId')`, wmAdvertiserIdString),
            eq(credentialSets.isActive, true)
          )
        )
        .limit(1);

      if (existingAdvertiserId) {
        results.push({
          label,
          wmAdvertiserId: wmAdvertiserIdString,
          success: false,
          error: 'A credential set with this advertising ID already exists in this account',
          code: 409
        });
        failedCount++;
        continue;
      }

      // Validate advertising ID with Walmart API
      console.log(`Validating advertising ID ${wmAdvertiserIdString} with Walmart...`);
      const validationResult = await validateAdvertisingIdWithWalmart(wmAdvertiserIdString, request);
      
      if (!validationResult.isValid) {
        results.push({
          label,
          wmAdvertiserId: wmAdvertiserIdString,
          success: false,
          error: validationResult.error,
          code: 400
        });
        failedCount++;
        continue;
      }

      console.log(`Advertising ID ${wmAdvertiserIdString} is valid. Found ${validationResult.totalResults} brands.`);

      // Create credential data for display/sponsored
      const credentialData = {
        nonce: null,
        state: null,
        apiVersion: 'v1',
        clientType: 'WMT_DISPLAY',
        accessToken: '',
        environment: 'production',
        permissions: [],
        refreshToken: '',
        wmPartnerId: '',
        tokenExpiresAt: '',
        wmAdvertiserId: wmAdvertiserIdString
      };

      // Create credential set
      const [newCredentialSet] = await db
        .insert(credentialSets)
        .values({
          name: label,
          type,
          credentials: credentialData,
          accountId,
          adminId: sessionUser.id,
          isShared: false,
          isActive: true
        })
        .returning();

      // Create user-credential relationship
      await db
        .insert(userCredentialSets)
        .values({
          userId: sessionUser.id,
          credentialSetId: newCredentialSet.id
        });

      // Store brands from Walmart API response
      console.log(`Storing ${validationResult.brands.length} brands for advertising ID ${wmAdvertiserIdString}...`);
      const brandStorageResult = await storeBrandsFromWalmart(
        validationResult.brands, 
        accountId, 
        newCredentialSet.id,
        sessionUser.id
      );

      // Link brands to Walmart retailer
      let linkBrandsResult = { linkedCount: 0 };
      if (brandStorageResult.success && brandStorageResult.brands) {
        console.log(`Linking ${brandStorageResult.storedCount} brands to Walmart...`);
        linkBrandsResult = await linkBrandsToWalmart(brandStorageResult.brands.map(b => b.id));
      }

      // Link credential set to Walmart retailer
      console.log(`Linking credential set ${newCredentialSet.id} to Walmart...`);
      const linkCredentialSetResult = await linkCredentialSetToWalmart(newCredentialSet.id);

      const isConnected = !!(wmAdvertiserIdString && wmAdvertiserIdString.trim() !== '');

      results.push({
        label,
        wmAdvertiserId: wmAdvertiserIdString,
        success: true,
        data: {
          id: newCredentialSet.id,
          name: newCredentialSet.name,
          type: newCredentialSet.type,
          isActive: newCredentialSet.isActive,
          isConnected,
          wmAdvertiserId: wmAdvertiserIdString,
          retailer: linkCredentialSetResult.retailer ? {
            id: linkCredentialSetResult.retailer.id,
            name: linkCredentialSetResult.retailer.name,
            slug: linkCredentialSetResult.retailer.slug
          } : null,
          totalBrands: validationResult.totalResults,
          brandsStored: brandStorageResult.storedCount,
          brandsLinkedToRetailer: linkBrandsResult.linkedCount,
          createdAt: newCredentialSet.createdAt,
          updatedAt: newCredentialSet.updatedAt
        }
      });
      successfulCount++;

    } catch (error) {
      console.error(`Error creating display credential set for entry ${label}:`, error);
      results.push({
        label,
        wmAdvertiserId: wmAdvertiserIdString,
        success: false,
        error: 'Internal server error while creating credential set',
        code: 500
      });
      failedCount++;
    }
  }

  // Step 4: Return 200 response immediately after brands are stored
  const responseData = {
    results,
    summary: {
      total: accountEntries.length,
      successful: successfulCount,
      failed: failedCount
    },
    message: `Processed ${accountEntries.length} display/sponsored entries: ${successfulCount} successful, ${failedCount} failed`
  };

  // Background processing should be done after response is sent
  if (successfulCount > 0) {
    // Use setImmediate to ensure this runs after the response is sent
    setImmediate(async () => {
      try {
        console.log('Starting background product fetching for successfully created credential sets');
        
        // Collect all stored brands from successful credential set creations
        const allStoredBrands = [];
        for (const result of results) {
          if (result.success && result.data) {
            // Get brands that were stored for this credential set
            try {
              const storedBrands = await db
                .select()
                .from(brands)
                .where(eq(brands.primaryCredentialId, result.data.id));
              
              allStoredBrands.push(...storedBrands);
            } catch (error) {
              console.error(`Error fetching stored brands for credential set ${result.data.id}:`, error);
            }
          }
        }

        if (allStoredBrands.length > 0) {
          // Trigger background product fetching (fire and forget)
          await fetchProductsForBrandsInBackground(allStoredBrands, accountId, request);
        }
      } catch (error) {
        console.error('Background product fetching failed:', error);
      }
    });
  }

  // Send response and return it to signal completion
  return reply.code(201).send(responseData);
}

/**
 * Handles creation of supplier/marketplace credential sets with partner ID and refresh token management.
 */
async function createSupplierCredentials(accountId, type, accountEntries, sessionUser, reply) {
  // Validate each entry has required fields for supplier/marketplace
  for (const entry of accountEntries) {
    if (!entry.partnerId || !String(entry.partnerId).trim()) {
      return reply.code(400).send({ error: 'Each account entry must have a valid partnerId' });
    }
    if (!entry.refreshToken || !String(entry.refreshToken).trim()) {
      return reply.code(400).send({ error: 'Each account entry must have a valid refreshToken' });
    }
  }

  const results = [];
  let successfulCount = 0;
  let failedCount = 0;

  for (const entry of accountEntries) {
    const { partnerId, refreshToken } = entry;
    const partnerIdString = String(partnerId);
    const refreshTokenString = String(refreshToken);
    
    try {
      // Check for duplicate partnerId - if exists, update the refresh token
      const [existingPartnerId] = await db
        .select()
        .from(credentialSets)
        .where(
          and(
            eq(credentialSets.accountId, accountId),
            eq(sql`(${credentialSets.credentials}->>'wmPartnerId')`, partnerIdString),
            eq(credentialSets.isActive, true)
          )
        )
        .limit(1);

      if (existingPartnerId) {
        // Update existing credential set with new refresh token
        try {
          const currentCredentials = existingPartnerId.credentials || {};
          const updatedCredentials = {
            ...currentCredentials,
            refreshToken: refreshTokenString,
            wmPartnerId: partnerIdString
          };

          const [updatedCredentialSet] = await db
            .update(credentialSets)
            .set({
              credentials: updatedCredentials,
              updatedAt: new Date()
            })
            .where(eq(credentialSets.id, existingPartnerId.id))
            .returning();

          const isConnected = !!(updatedCredentials.wmPartnerId && updatedCredentials.refreshToken);

          results.push({
            partnerId: partnerIdString,
            success: true,
            updated: true,
            data: {
              id: updatedCredentialSet.id,
              name: updatedCredentialSet.name,
              type: updatedCredentialSet.type,
              isActive: updatedCredentialSet.isActive,
              isConnected,
              wmPartnerId: updatedCredentials.wmPartnerId,
              createdAt: updatedCredentialSet.createdAt,
              updatedAt: updatedCredentialSet.updatedAt
            }
          });
          successfulCount++;
          continue;

        } catch (updateError) {
          console.error(`Error updating existing credential set for partnerId ${partnerIdString}:`, updateError);
          results.push({
            partnerId: partnerIdString,
            success: false,
            error: 'Failed to update existing credential set with new refresh token',
            code: 500
          });
          failedCount++;
          continue;
        }
      }

      // Create credential data for supplier/marketplace
      const credentialData = {
        nonce: null,
        state: null,
        apiVersion: 'v1',
        clientType: type === 'wm_supplier' ? 'SUPPLIER' : 'SELLER',
        accessToken: '',
        environment: 'production',
        permissions: [],
        refreshToken: refreshTokenString,
        wmPartnerId: partnerIdString,
        tokenExpiresAt: '',
        wmAdvertiserId: ''
      };

      // Generate a name based on partner ID
      const generatedName = `${type} - ${partnerIdString}`;

      // Create credential set
      const [newCredentialSet] = await db
        .insert(credentialSets)
        .values({
          name: generatedName,
          type,
          credentials: credentialData,
          accountId,
          adminId: sessionUser.id,
          isShared: false,
          isActive: true
        })
        .returning();

      // Create user-credential relationship
      await db
        .insert(userCredentialSets)
        .values({
          userId: sessionUser.id,
          credentialSetId: newCredentialSet.id
        });

      // Link credential set to Walmart retailer
      const linkCredentialSetResult = await linkCredentialSetToWalmart(newCredentialSet.id);

      // For supplier/marketplace, connect user to any existing brands in this account
      // that don't already have an owner
      try {
        const existingBrands = await db
          .select()
          .from(brands)
          .where(eq(brands.accountId, accountId));

        for (const brand of existingBrands) {
          // Check if user is already connected to this brand
          const [existingUserBrand] = await db
            .select()
            .from(brandUsers)
            .where(
              and(
                eq(brandUsers.brandId, brand.id),
                eq(brandUsers.userId, sessionUser.id)
              )
            )
            .limit(1);

          if (!existingUserBrand) {
            // Connect user to brand as owner
            await db
              .insert(brandUsers)
              .values({
                brandId: brand.id,
                userId: sessionUser.id,
                role: 'owner',
                createdAt: new Date(),
                updatedAt: new Date()
              });
            console.log(`Connected user ${sessionUser.id} as owner to existing brand ${brand.name}`);
          }
        }
      } catch (brandUserError) {
        console.error('Error connecting user to existing brands:', brandUserError);
        // Continue even if brand user connection fails
      }

      const isConnected = !!(credentialData.wmPartnerId && credentialData.refreshToken);

      results.push({
        partnerId: partnerIdString,
        success: true,
        data: {
          id: newCredentialSet.id,
          name: newCredentialSet.name,
          type: newCredentialSet.type,
          isActive: newCredentialSet.isActive,
          isConnected,
          wmPartnerId: credentialData.wmPartnerId,
          retailer: linkCredentialSetResult.retailer ? {
            id: linkCredentialSetResult.retailer.id,
            name: linkCredentialSetResult.retailer.name,
            slug: linkCredentialSetResult.retailer.slug
          } : null,
          createdAt: newCredentialSet.createdAt,
          updatedAt: newCredentialSet.updatedAt
        }
      });
      successfulCount++;

    } catch (error) {
      console.error(`Error creating supplier/marketplace credential set for partnerId ${partnerIdString}:`, error);
      results.push({
        partnerId: partnerIdString,
        success: false,
        error: 'Internal server error while creating credential set',
        code: 500
      });
      failedCount++;
    }
  }

  return reply.code(201).send({
    results,
    summary: {
      total: accountEntries.length,
      successful: successfulCount,
      failed: failedCount
    },
    message: `Processed ${accountEntries.length} supplier/marketplace entries: ${successfulCount} successful, ${failedCount} failed`
  });
}

/**
 * Handles creation of Canva credential sets with OAuth integration.
 */
async function createCanvaCredentials(accountId, type, accountEntries, sessionUser, reply) {
  // Validate each entry has required fields for Canva
  for (const entry of accountEntries) {
    if (!entry.label || typeof entry.label !== 'string' || !entry.label.trim()) {
      return reply.code(400).send({ error: 'Each account entry must have a valid label' });
    }
  }

  const results = [];
  let successfulCount = 0;
  let failedCount = 0;

  for (const entry of accountEntries) {
    const { label } = entry;
    
    try {
      // Check for duplicate name in the same account
      const [existingCredential] = await db
        .select()
        .from(credentialSets)
        .where(
          and(
            eq(credentialSets.accountId, accountId),
            eq(credentialSets.name, label),
            eq(credentialSets.isActive, true)
          )
        )
        .limit(1);

      if (existingCredential) {
        results.push({
          label,
          success: false,
          error: 'A credential set with this name already exists in this account',
          code: 409
        });
        failedCount++;
        continue;
      }

      // Create credential data for Canva
      const credentialData = {
        apiVersion: 'v1',
        clientType: 'CANVA',
        accessToken: '',
        environment: 'production',
        permissions: [],
        refreshToken: '',
        tokenExpiresAt: '',
        // Canva-specific fields
        canvaClientId: process.env.CANVA_CLIENT_ID || '',
        canvaRedirectUri: process.env.CANVA_REDIRECT_URI || 'http://127.0.0.1:3000/creative-builder/canva-callback',
        connectedAt: null,
        scopes: [
          'design:content:read',
          'design:content:write', 
          'design:meta:read',
          'asset:read',
          'asset:write',
          'brandtemplate:meta:read',
          'brandtemplate:content:read',
          'profile:read'
        ]
      };

      // Create credential set
      const [newCredentialSet] = await db
        .insert(credentialSets)
        .values({
          name: label,
          type,
          credentials: credentialData,
          accountId,
          adminId: sessionUser.id,
          isShared: false,
          isActive: true
        })
        .returning();

      // Create user-credential relationship
      await db
        .insert(userCredentialSets)
        .values({
          userId: sessionUser.id,
          credentialSetId: newCredentialSet.id
        });

      // Link credential set to Canva retailer (if Canva retailer exists)
      let linkCredentialSetResult = { success: true, retailer: null };
      try {
        // Check if Canva retailer exists
        const [canvaRetailer] = await db
          .select()
          .from(retailers)
          .where(eq(retailers.slug, 'canva'))
          .limit(1);

        if (canvaRetailer) {
          console.log(`Linking credential set ${newCredentialSet.id} to Canva...`);
          linkCredentialSetResult = await linkCredentialSetToCanva(newCredentialSet.id, canvaRetailer.id);
        }
      } catch (error) {
        console.error('Error linking credential set to Canva retailer:', error);
        // Continue even if retailer linking fails
      }

      const isConnected = false; // Canva requires OAuth to be connected

      results.push({
        label,
        success: true,
        data: {
          id: newCredentialSet.id,
          name: newCredentialSet.name,
          type: newCredentialSet.type,
          isActive: newCredentialSet.isActive,
          isConnected,
          retailer: linkCredentialSetResult.retailer ? {
            id: linkCredentialSetResult.retailer.id,
            name: linkCredentialSetResult.retailer.name,
            slug: linkCredentialSetResult.retailer.slug
          } : null,
          createdAt: newCredentialSet.createdAt,
          updatedAt: newCredentialSet.updatedAt
        }
      });
      successfulCount++;

    } catch (error) {
      console.error(`Error creating Canva credential set for entry ${label}:`, error);
      results.push({
        label,
        success: false,
        error: 'Internal server error while creating credential set',
        code: 500
      });
      failedCount++;
    }
  }

  return reply.code(201).send({
    results,
    summary: {
      total: accountEntries.length,
      successful: successfulCount,
      failed: failedCount
    },
    message: `Processed ${accountEntries.length} Canva entries: ${successfulCount} successful, ${failedCount} failed`
  });
}

/**
 * Updates the name and/or credentials of an account credential set if the user has access.
 *
 * Validates user ownership, ensures the new name is unique within the account, and merges new credential data with existing credentials. Returns updated credential set metadata or an error response if validation fails or access is denied.
 */
export async function updateAccountCredential(request, reply) {
  try {
    const { accountId, credentialId } = request.params;
    const { credentials = {} } = request.body;
    const sessionUser = request.user;
    const name = credentials.name || request.body.name;
    const isActive = credentials.isActive;

    // Verify credential set exists and belongs to this account
    const [credentialSet] = await db
      .select()
      .from(credentialSets)
      .innerJoin(
        userCredentialSets,
        eq(credentialSets.id, userCredentialSets.credentialSetId)
      )
      .where(
        and(
          eq(credentialSets.id, credentialId),
          eq(credentialSets.accountId, accountId),
        )
      )
      .limit(1);

    if (!credentialSet) {
      return reply.code(404).send({ error: 'Credential set not found or access denied' });
    }
    if (name && name.trim() === '') {
      return reply.code(400).send({ error: 'Name cannot be empty' });
    }

    if (name && name !== credentialSet.credential_sets.name) {
      const [existingCredential] = await db
        .select()
        .from(credentialSets)
        .where(
          and(
            eq(credentialSets.accountId, accountId),
            eq(credentialSets.name, name),
            ne(credentialSets.id, credentialId)
          )
        )
        .limit(1);

      if (existingCredential) {
        return reply.code(409).send({ error: 'A credential set with this name already exists in this account' });
      }
    }

    const updateData = {
      updatedAt: new Date()
    };

    if (name) {
      updateData.name = name;
    }

    if (isActive !== undefined) {
      updateData.isActive = isActive;
    }

    // Filter out isActive and name from credentials before merging
    const { isActive: _, name: __, ...credentialsToUpdate } = credentials;
    if (Object.keys(credentialsToUpdate).length > 0) {
      const currentCredentials = credentialSet.credential_sets.credentials || {};
      updateData.credentials = {
        ...currentCredentials,
        ...credentialsToUpdate
      };
    }

    const [updatedCredentialSet] = await db
      .update(credentialSets)
      .set(updateData)
      .where(eq(credentialSets.id, credentialId))
      .returning();

    return reply.send({
      id: updatedCredentialSet.id,
      name: updatedCredentialSet.name,
      type: updatedCredentialSet.type,
      isActive: updatedCredentialSet.isActive,
      message: 'Credential set updated successfully'
    });
  } catch (error) {
    console.error("Error updating account credential:", error);
    return reply.code(500).send({ error: "Internal server error" });
  }
}

/**
 * Soft deletes a credential set for an account if the user has access.
 *
 * Marks the specified credential set as inactive and records the deletion timestamp. Returns a success message with the credential ID and deletion time, or a 404 error if the credential set is not found or access is denied.
 */
export async function deleteAccountCredential(request, reply) {
  try {
    const { accountId, credentialId } = request.params;
    const sessionUser = request.user;

    const [credentialSet] = await db
      .select()
      .from(credentialSets)
      .innerJoin(
        userCredentialSets,
        eq(credentialSets.id, userCredentialSets.credentialSetId)
      )
      .where(
        and(
          eq(credentialSets.id, credentialId),
          eq(credentialSets.accountId, accountId),
          eq(userCredentialSets.userId, sessionUser.id),
          eq(credentialSets.isActive, true)
        )
      )
      .limit(1);

    if (!credentialSet) {
      return reply.code(404).send({ error: 'Credential set not found or access denied' });
    }

    // Soft delete - set isActive to false and deletedAt timestamp
    const deletedAt = new Date();
    await db
      .update(credentialSets)
      .set({
        isActive: false,
        deletedAt: deletedAt,
        updatedAt: deletedAt
      })
      .where(eq(credentialSets.id, credentialId));

    return reply.send({
      message: 'Credential set deleted successfully',
      id: credentialId,
      deletedAt: deletedAt.toISOString()
    });
  } catch (error) {
    console.error("Error deleting account credential:", error);
    return reply.code(500).send({ error: "Internal server error" });
  }
}