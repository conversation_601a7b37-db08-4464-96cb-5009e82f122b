export const getSettingsSchema = {
  response: {
    200: {
      type: 'object',
      properties: {
        profile: {
          type: 'object',
          properties: {
            sub: { type: 'string' },
            email: { type: 'string' },
            email_verified: { type: 'boolean' },
            name: { type: 'string' },
            nickname: { type: 'string' },
            picture: { type: 'string' },
            updated_at: { type: 'string' },
            locale: { type: ['string', 'null'] },
            given_name: { type: ['string', 'null'] },
            family_name: { type: ['string', 'null'] },
            permission_level: { type: 'string' },
            id: { type: 'string' },
            account_id: { type: 'string' }
          }
        },
        credentials: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              type: { type: 'string' },
              accountId: { type: 'string' },
              credentials: {
                type: 'object',
                properties: {
                  advertiserId: { type: ['string', 'null'] },
                  clientType: { type: ['string', 'null'] },
                  partnerId: { type: ['string', 'null'] },
                  accessToken: { type: ['string', 'null'] },
                  refreshToken: { type: ['string', 'null'] },
                  expiresAt: { type: ['string', 'null'] },
                  nonce: { type: ['string', 'null'] },
                  state: { type: ['string', 'null'] },
                  wmtMarketplaceId: { type: ['string', 'null'] },
                  wmtMarketplaceSecret: { type: ['string', 'null'] },
                  supplierId: { type: ['string', 'null'] },
                  supplierSecret: { type: ['string', 'null'] }
                }
              },
              createdAt: { type: 'string', format: 'date-time' },
              updatedAt: { type: 'string', format: 'date-time' },
              // User's linked brands (all brands, not specific to this credential set)
              linkedBrands: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    brandId: { type: 'string' },
                    userId: { type: 'string' },
                    role: { type: 'string' },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' }
                  }
                }
              }
            }
          }
        },
        settings: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            emailNotifications: { type: 'boolean' }
          }
        }
      }
    },
    404: {
      type: 'object',
      properties: {
        error: { type: 'string' }
      }
    },
    500: {
      type: 'object',
      properties: {
        error: { type: 'string' }
      }
    }
  }
};


