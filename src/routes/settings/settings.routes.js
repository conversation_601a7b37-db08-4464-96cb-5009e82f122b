import {
  getSettings,
  updateSettings,
  getSecretByPartnerId,
  deleteCredentialSetFields,
  updateCredentialSet,
  getAccountCredentials,
  createAccountCredential,
  updateAccountCredential,
  deleteAccountCredential
} from './settings.controllers.js'
import { eq, and, inArray } from 'drizzle-orm'
import { db } from '../../db/index.js'
import { userSettings } from '../../db/schema/userSettings.ts'
import { userAccounts } from '../../db/schema/userAccounts.ts'
import { credentialSets } from '../../db/schema/credentialSets.ts'
import { userCredentialSets } from '../../db/schema/userCredentialSets.ts'
import { authService } from '../../services/authorization.service.js'

/**
 * Determines whether the session user has an 'admin' or 'owner' role on any active account associated with the target user.
 *
 * @param {string} sessionUserId - The ID of the user whose access is being checked.
 * @param {string} targetUserId - The ID of the user whose accounts are being evaluated.
 * @returns {Promise<boolean>} True if the session user has admin or owner access on any of the target user's active accounts; otherwise, false.
 */
async function hasAdminAccessToUser(sessionUserId, targetUserId) {
  // Get all accounts where the target user is a member
  const targetUserAccounts = await db
    .select({ accountId: userAccounts.accountId })
    .from(userAccounts)
    .where(and(
      eq(userAccounts.userId, targetUserId),
      eq(userAccounts.status, 'active')
    ));

  if (targetUserAccounts.length === 0) {
    return false;
  }

  // Check if session user has admin or owner role on any of those accounts
  const adminAccess = await db
    .select({ role: userAccounts.role })
    .from(userAccounts)
    .where(and(
      eq(userAccounts.userId, sessionUserId),
      eq(userAccounts.status, 'active'),
      inArray(userAccounts.accountId, targetUserAccounts.map(acc => acc.accountId))
    ));

  return adminAccess.some(access => access.role === 'admin' || access.role === 'owner');
}

/**
 * Middleware that verifies the authenticated user's access to a specific account based on their role.
 *
 * Responds with 401 if no session user is found, 403 if the user lacks access to the account, or 500 on internal errors. Attaches the user's role on the account to the request object for downstream handlers.
 */
async function verifyAccountAccess(request, reply) {
  try {
    const { accountId } = request.params;
    const sessionUser = request.user;

    if (!sessionUser) {
      return reply.code(401).send({ error: 'Unauthorized - No session found' });
    }

    // Use AuthorizationService to check account access
    const userRole = await authService.getUserAccountRole(sessionUser.id, accountId);

    if (!userRole) {
      return reply.code(403).send({ error: 'Access denied to this account' });
    }

    // Add role to request for use in controllers
    request.userAccountRole = userRole;
  } catch (error) {
    console.error('Error verifying account access:', error);
    return reply.code(500).send({ error: 'Internal server error' });
  }
}

/**
 * Registers API routes for user settings, credential sets, secrets, and account credentials with authentication and role-based authorization.
 *
 * Sets up endpoints for managing credential sets, retrieving secrets, and performing CRUD operations on user and account credentials. Enforces authentication and authorization for each route, supporting both self-service and admin/owner access to user settings. Integrates with the database for persistent storage and returns appropriate HTTP status codes for authorization and error conditions.
 *
 * @throws {Error} If the authentication plugin is not registered on the Fastify instance.
 */
export default async function settingsRoutes(fastify, options) {
  // Ensure auth decorator is available
  if (!fastify.hasDecorator("verifyAuth")) {
    throw new Error("Auth plugin must be registered before routes");
  }

  // Update credentialSet by ID
  fastify.put("/credentialSets/:credentialSetId", {
    onRequest: fastify.verifyAuth,
    handler: updateCredentialSet
  });

  // Delete credential set fields by credentialSetId
  fastify.delete("/credentialSets/:credentialSetId/fields", {
    onRequest: fastify.verifyAuth,
    handler: deleteCredentialSetFields
  });

  // Get secret by partnerId
  fastify.get("/secret/:partnerId", {
    onRequest: fastify.verifyAuth,
    handler: getSecretByPartnerId
  });

  // Protected settings route
  fastify.get("/", {
    onRequest: fastify.verifyAuth,
    handler: getSettings
  });

  // Update settings
  fastify.put("/", {
    onRequest: fastify.verifyAuth,
    handler: updateSettings
  });

  // GET /api/settings/users/:id - Get settings for specific user (admin only)
  fastify.get("/users/:id", {
    onRequest: fastify.verifyAuth,
    handler: async (request, reply) => {
      try {
        const { id } = request.params;
        const sessionUser = request.user;

        if (!sessionUser) {
          return reply.code(401).send({ error: 'Unauthorized - Session invalid' });
        }

        // Check if user has permission to view other user's settings
        // Allow if it's their own settings OR if they have admin/owner role on target user's account
        if (sessionUser.id !== id) {
          const hasAccess = await hasAdminAccessToUser(sessionUser.id, id);
          if (!hasAccess) {
            return reply.code(403).send({ error: 'Forbidden - Cannot view other user settings' });
          }
        }

        // Get user settings
        const [userSettingsData] = await db
          .select()
          .from(userSettings)
          .where(eq(userSettings.userId, id))
          .limit(1);

        if (!userSettingsData) {
          // Create default settings if they don't exist
          const [newSettings] = await db
            .insert(userSettings)
            .values({
              userId: id,
              settings: {
                emailNotifications: true
              }
            })
            .returning();

          return reply.send(newSettings);
        }

        return reply.send(userSettingsData);
      } catch (error) {
        console.error("Error fetching user settings:", error);
        return reply.code(500).send({ error: "Internal server error" });
      }
    }
  });

  // PUT /api/settings/users/:id - Update settings for specific user (admin only)
  fastify.put("/users/:id", {
    onRequest: fastify.verifyAuth,
    handler: async (request, reply) => {
      try {
        const { id } = request.params;
        const settingsData = request.body;
        const sessionUser = request.user;

        if (!sessionUser) {
          return reply.code(401).send({ error: 'Unauthorized - Session invalid' });
        }

        // Check if user has permission to update other user's settings
        // Allow if it's their own settings OR if they have admin/owner role on target user's account
        if (sessionUser.id !== id) {
          const hasAccess = await hasAdminAccessToUser(sessionUser.id, id);
          if (!hasAccess) {
            return reply.code(403).send({ error: 'Forbidden - Cannot update other user settings' });
          }
        }

        // Get existing settings
        const [existingSettings] = await db
          .select()
          .from(userSettings)
          .where(eq(userSettings.userId, id))
          .limit(1);

        if (!existingSettings) {
          // Create new settings
          const [newSettings] = await db
            .insert(userSettings)
            .values({
              userId: id,
              settings: settingsData
            })
            .returning();

          return reply.send(newSettings);
        }

        // Merge existing settings with new settings
        const mergedSettings = {
          ...existingSettings.settings,
          ...settingsData
        };

        // Update settings
        const [updatedSettings] = await db
          .update(userSettings)
          .set({
            settings: mergedSettings,
            updatedAt: new Date()
          })
          .where(eq(userSettings.userId, id))
          .returning();

        return reply.send(updatedSettings);
      } catch (error) {
        console.error("Error updating user settings:", error);
        return reply.code(500).send({ error: "Internal server error" });
      }
    }
  });

  // Credential Management Routes for Account (4 CRUD operations)

  // GET /api/settings/accounts/:accountId/credentials
  // GET /api/settings/accounts/:accountId/credentials?retailerId=walmart
  fastify.get("/accounts/:accountId/credentials", {
    onRequest: [fastify.verifyAuth, verifyAccountAccess],
    handler: getAccountCredentials
  });

  // POST /api/settings/accounts/:accountId/credentials
  fastify.post("/accounts/:accountId/credentials", {
    onRequest: [fastify.verifyAuth, verifyAccountAccess],
    handler: createAccountCredential
  });

  // PUT /api/settings/accounts/:accountId/credentials/:credentialId
  fastify.put("/accounts/:accountId/credentials/:credentialId", {
    onRequest: [fastify.verifyAuth, verifyAccountAccess],
    handler: updateAccountCredential
  });

  // DELETE /api/settings/accounts/:accountId/credentials/:credentialId (soft delete)
  fastify.delete("/accounts/:accountId/credentials/:credentialId", {
    onRequest: [fastify.verifyAuth, verifyAccountAccess],
    handler: deleteAccountCredential
  });

  // Canva OAuth Token Management Routes

  // POST /api/settings/accounts/:accountId/users/:userId/plugins/canva
  // Create new Canva credential set and store OAuth tokens
  fastify.post("/accounts/:accountId/users/:userId/plugins/canva", {
    onRequest: [fastify.verifyAuth, verifyAccountAccess],
    handler: async (request, reply) => {
      try {
        const { accountId, userId } = request.params;
        const {
          credentialData
           } = request.body;
        const sessionUser = request.user;

        console.log('Request body:', request.body);
        console.log('accountId', accountId);
        console.log('userId', userId);
        console.log('sessionUser', sessionUser);
        

        // Create new credential set with OAuth tokens
        const credentials = {
          accessToken: credentialData.access_token || '',
          refreshToken: credentialData.refresh_token || '',
          tokenType: credentialData.token_type || 'Bearer',
          connectedAt: new Date().toISOString(),
          isConnected: true
        };
        console.log('credentials', credentials);
        // Insert new credential set
        const [newCredentialSet] = await db
          .insert(credentialSets)
          .values({
            name: 'Canva',
            type: 'canva',
            credentials: credentials,
            accountId: accountId,
            adminId: sessionUser.id,
            isShared: true,
            isActive: true
          })
          .returning();

        // Link the credential set to the user
        await db
          .insert(userCredentialSets)
          .values({
            userId: sessionUser.id,
            credentialSetId: newCredentialSet.id
          });

        return reply.send({
          success: true,
          message: 'Canva credential set created successfully',
          data: {
            id: newCredentialSet.id,
            name: newCredentialSet.name,
            type: newCredentialSet.type,
            isConnected: true,
            connectedAt: credentials.connectedAt,
            tokenExpiresAt: credentials.tokenExpiresAt
          }
        });

      } catch (error) {
        console.error('Error creating Canva credential set:', error);
        return reply.code(500).send({ error: 'Internal server error' });
      }
    }
  });

  // GET /api/settings/accounts/:accountId/credentials/:credentialId/canva/status
  // Get Canva connection status and token info
  fastify.get("/accounts/:accountId/credentials/:credentialId/canva/status", {
    onRequest: [fastify.verifyAuth, verifyAccountAccess],
    handler: async (request, reply) => {
      try {
        const { accountId, credentialId } = request.params;
        const sessionUser = request.user;

        // Verify the credential belongs to this account and user has access
        const [credentialSet] = await db
          .select()
          .from(credentialSets)
          .innerJoin(
            userCredentialSets,
            eq(credentialSets.id, userCredentialSets.credentialSetId)
          )
          .where(
            and(
              eq(credentialSets.id, credentialId),
              eq(credentialSets.accountId, accountId),
              eq(userCredentialSets.userId, sessionUser.id),
              eq(credentialSets.type, 'canva'),
              eq(credentialSets.isActive, true)
            )
          )
          .limit(1);

        if (!credentialSet) {
          return reply.code(404).send({ error: 'Canva credential set not found or access denied' });
        }

        const credentials = credentialSet.credential_sets.credentials || {};
        const isConnected = !!(credentials.accessToken && credentials.accessToken.trim() !== '');
        const isExpired = credentials.tokenExpiresAt ? new Date(credentials.tokenExpiresAt) < new Date() : false;

        return reply.send({
          success: true,
          data: {
            id: credentialSet.credential_sets.id,
            name: credentialSet.credential_sets.name,
            type: credentialSet.credential_sets.type,
            isConnected,
            isExpired,
            connectedAt: credentials.connectedAt,
            tokenExpiresAt: credentials.tokenExpiresAt,
            hasRefreshToken: !!(credentials.refreshToken && credentials.refreshToken.trim() !== ''),
            scopes: credentials.scopes || []
          }
        });

      } catch (error) {
        console.error('Error getting Canva connection status:', error);
        return reply.code(500).send({ error: 'Internal server error' });
      }
    }
  });

  // DELETE /api/settings/accounts/:accountId/credentials/:credentialId/canva/disconnect
  // Disconnect Canva by clearing OAuth tokens
  fastify.delete("/accounts/:accountId/credentials/:credentialId/canva/disconnect", {
    onRequest: [fastify.verifyAuth, verifyAccountAccess],
    handler: async (request, reply) => {
      try {
        const { accountId, credentialId } = request.params;
        const sessionUser = request.user;

        // Verify the credential belongs to this account and user has access
        const [credentialSet] = await db
          .select()
          .from(credentialSets)
          .innerJoin(
            userCredentialSets,
            eq(credentialSets.id, userCredentialSets.credentialSetId)
          )
          .where(
            and(
              eq(credentialSets.id, credentialId),
              eq(credentialSets.accountId, accountId),
              eq(userCredentialSets.userId, sessionUser.id),
              eq(credentialSets.type, 'canva'),
              eq(credentialSets.isActive, true)
            )
          )
          .limit(1);

        if (!credentialSet) {
          return reply.code(404).send({ error: 'Canva credential set not found or access denied' });
        }

        // Clear OAuth tokens
        const updatedCredentials = {
          ...credentialSet.credential_sets.credentials,
          accessToken: '',
          refreshToken: '',
          tokenExpiresAt: '',
          connectedAt: null,
          isConnected: false
        };

        const [updatedCredentialSet] = await db
          .update(credentialSets)
          .set({
            credentials: updatedCredentials,
            updatedAt: new Date()
          })
          .where(eq(credentialSets.id, credentialId))
          .returning();

        return reply.send({
          success: true,
          message: 'Canva disconnected successfully',
          data: {
            id: updatedCredentialSet.id,
            name: updatedCredentialSet.name,
            type: updatedCredentialSet.type,
            isConnected: false
          }
        });

      } catch (error) {
        console.error('Error disconnecting Canva:', error);
        return reply.code(500).send({ error: 'Internal server error' });
      }
    }
  });

}
