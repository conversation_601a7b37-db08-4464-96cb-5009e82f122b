{"id": "8253b4c0-675a-4a0a-b8a7-c74dc53417db", "prevId": "f2e32dcf-f5c4-4623-b83d-073aee1f2616", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "default": "'default'"}, "is_trial": {"name": "is_trial", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "account_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "valid_subscription": {"name": "valid_subscription", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "has_payment_method": {"name": "has_payment_method", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "onboarding_step": {"name": "onboarding_step", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_owner_id_users_id_fk": {"name": "accounts_owner_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.account_settings": {"name": "account_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"account_settings_account_id_accounts_id_fk": {"name": "account_settings_account_id_accounts_id_fk", "tableFrom": "account_settings", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"account_settings_account_id_unique": {"name": "account_settings_account_id_unique", "nullsNotDistinct": false, "columns": ["account_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assets": {"name": "assets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"assets_account_id_accounts_id_fk": {"name": "assets_account_id_accounts_id_fk", "tableFrom": "assets", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brand_guidelines": {"name": "brand_guidelines", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "text", "primaryKey": false, "notNull": false}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true, "default": "'guidelines'"}, "asset_url": {"name": "asset_url", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "status": {"name": "status", "type": "brand_guideline_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_guidelines_brand_id_brands_id_fk": {"name": "brand_guidelines_brand_id_brands_id_fk", "tableFrom": "brand_guidelines", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "brand_guidelines_uploaded_by_users_id_fk": {"name": "brand_guidelines_uploaded_by_users_id_fk", "tableFrom": "brand_guidelines", "tableTo": "users", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brand_categories": {"name": "brand_categories", "schema": "", "columns": {"brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_categories_brand_id_brands_id_fk": {"name": "brand_categories_brand_id_brands_id_fk", "tableFrom": "brand_categories", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "brand_categories_category_id_categories_id_fk": {"name": "brand_categories_category_id_categories_id_fk", "tableFrom": "brand_categories", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"brand_categories_brand_id_category_id_pk": {"name": "brand_categories_brand_id_category_id_pk", "columns": ["brand_id", "category_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brands": {"name": "brands", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "colors": {"name": "colors", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "logo_asset_url": {"name": "logo_asset_url", "type": "text", "primaryKey": false, "notNull": false}, "primary_credential_id": {"name": "primary_credential_id", "type": "uuid", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "product_count": {"name": "product_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brands_account_id_accounts_id_fk": {"name": "brands_account_id_accounts_id_fk", "tableFrom": "brands", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "brands_primary_credential_id_credential_sets_id_fk": {"name": "brands_primary_credential_id_credential_sets_id_fk", "tableFrom": "brands", "tableTo": "credential_sets", "columnsFrom": ["primary_credential_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brand_users": {"name": "brand_users", "schema": "", "columns": {"brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'member'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_users_brand_id_brands_id_fk": {"name": "brand_users_brand_id_brands_id_fk", "tableFrom": "brand_users", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "brand_users_user_id_users_id_fk": {"name": "brand_users_user_id_users_id_fk", "tableFrom": "brand_users", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"brand_users_brand_id_user_id_pk": {"name": "brand_users_brand_id_user_id_pk", "columns": ["brand_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"categories_account_id_accounts_id_fk": {"name": "categories_account_id_accounts_id_fk", "tableFrom": "categories", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.creatives": {"name": "creatives", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "wm_creative_id": {"name": "wm_creative_id", "type": "text", "primaryKey": false, "notNull": false}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "generation_id": {"name": "generation_id", "type": "uuid", "primaryKey": false, "notNull": false}, "preview_urls": {"name": "preview_urls", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "preview_status": {"name": "preview_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'PENDING'"}, "folder_id": {"name": "folder_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'DRAFT'"}, "keyword": {"name": "keyword", "type": "text", "primaryKey": false, "notNull": false}, "ad_units": {"name": "ad_units", "type": "jsonb", "primaryKey": false, "notNull": true}, "review_comments": {"name": "review_comments", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"creatives_project_id_projects_id_fk": {"name": "creatives_project_id_projects_id_fk", "tableFrom": "creatives", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "creatives_brand_id_brands_id_fk": {"name": "creatives_brand_id_brands_id_fk", "tableFrom": "creatives", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "creatives_user_id_users_id_fk": {"name": "creatives_user_id_users_id_fk", "tableFrom": "creatives", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "creatives_generation_id_generations_id_fk": {"name": "creatives_generation_id_generations_id_fk", "tableFrom": "creatives", "tableTo": "generations", "columnsFrom": ["generation_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credential_set_retailers": {"name": "credential_set_retailers", "schema": "", "columns": {"credential_set_id": {"name": "credential_set_id", "type": "uuid", "primaryKey": false, "notNull": true}, "retailer_id": {"name": "retailer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"credential_set_retailers_credential_set_id_credential_sets_id_fk": {"name": "credential_set_retailers_credential_set_id_credential_sets_id_fk", "tableFrom": "credential_set_retailers", "tableTo": "credential_sets", "columnsFrom": ["credential_set_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "credential_set_retailers_retailer_id_retailers_id_fk": {"name": "credential_set_retailers_retailer_id_retailers_id_fk", "tableFrom": "credential_set_retailers", "tableTo": "retailers", "columnsFrom": ["retailer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"credential_set_retailers_credential_set_id_retailer_id_pk": {"name": "credential_set_retailers_credential_set_id_retailer_id_pk", "columns": ["credential_set_id", "retailer_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credential_sets": {"name": "credential_sets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "default": "'Default Credential Set'"}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "default": "'default'"}, "credentials": {"name": "credentials", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "admin_id": {"name": "admin_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_shared": {"name": "is_shared", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"credential_sets_account_id_accounts_id_fk": {"name": "credential_sets_account_id_accounts_id_fk", "tableFrom": "credential_sets", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "credential_sets_admin_id_users_id_fk": {"name": "credential_sets_admin_id_users_id_fk", "tableFrom": "credential_sets", "tableTo": "users", "columnsFrom": ["admin_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.external_reviewers": {"name": "external_reviewers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "requested_by_user_id": {"name": "requested_by_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "retailer_id": {"name": "retailer_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"external_reviewers_account_id_accounts_id_fk": {"name": "external_reviewers_account_id_accounts_id_fk", "tableFrom": "external_reviewers", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "external_reviewers_requested_by_user_id_users_id_fk": {"name": "external_reviewers_requested_by_user_id_users_id_fk", "tableFrom": "external_reviewers", "tableTo": "users", "columnsFrom": ["requested_by_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "external_reviewers_retailer_id_retailers_id_fk": {"name": "external_reviewers_retailer_id_retailers_id_fk", "tableFrom": "external_reviewers", "tableTo": "retailers", "columnsFrom": ["retailer_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.generations": {"name": "generations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "keyword_id": {"name": "keyword_id", "type": "uuid", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "generation_version_id": {"name": "generation_version_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"generations_product_id_products_id_fk": {"name": "generations_product_id_products_id_fk", "tableFrom": "generations", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "generations_keyword_id_keywords_id_fk": {"name": "generations_keyword_id_keywords_id_fk", "tableFrom": "generations", "tableTo": "keywords", "columnsFrom": ["keyword_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "generations_project_id_projects_id_fk": {"name": "generations_project_id_projects_id_fk", "tableFrom": "generations", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "generations_brand_id_brands_id_fk": {"name": "generations_brand_id_brands_id_fk", "tableFrom": "generations", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "generations_generation_version_id_generation_versions_id_fk": {"name": "generations_generation_version_id_generation_versions_id_fk", "tableFrom": "generations", "tableTo": "generation_versions", "columnsFrom": ["generation_version_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.generation_versions": {"name": "generation_versions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "generation_id": {"name": "generation_id", "type": "uuid", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "unit_fields": {"name": "unit_fields", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'"}, "has_completed": {"name": "has_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "ad_previews": {"name": "ad_previews", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "preview_image_positions": {"name": "preview_image_positions", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'"}, "apply_to_all": {"name": "apply_to_all", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"generation_versions_generation_id_generations_id_fk": {"name": "generation_versions_generation_id_generations_id_fk", "tableFrom": "generation_versions", "tableTo": "generations", "columnsFrom": ["generation_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "generation_versions_created_by_users_id_fk": {"name": "generation_versions_created_by_users_id_fk", "tableFrom": "generation_versions", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.image_metadata": {"name": "image_metadata", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "image_text": {"name": "image_text", "type": "text", "primaryKey": false, "notNull": false}, "classification": {"name": "classification", "type": "image_classification", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"image_metadata_product_id_products_product_id_fk": {"name": "image_metadata_product_id_products_product_id_fk", "tableFrom": "image_metadata", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["product_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"image_metadata_product_id_image_url_unique": {"name": "image_metadata_product_id_image_url_unique", "nullsNotDistinct": false, "columns": ["product_id", "image_url"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "auth0_id": {"name": "auth0_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false, "default": "'en-US'"}, "time_zone": {"name": "time_zone", "type": "text", "primaryKey": false, "notNull": false, "default": "'UTC'"}, "is_beta_user": {"name": "is_beta_user", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "beta_key_id": {"name": "beta_key_id", "type": "uuid", "primaryKey": false, "notNull": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_auth0_id_unique": {"name": "users_auth0_id_unique", "nullsNotDistinct": false, "columns": ["auth0_id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "brand_ids": {"name": "brand_ids", "type": "uuid[]", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "invitation_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'member'"}, "status": {"name": "status", "type": "invitation_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"invitations_account_id_accounts_id_fk": {"name": "invitations_account_id_accounts_id_fk", "tableFrom": "invitations", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.keywords": {"name": "keywords", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "keyword": {"name": "keyword", "type": "text", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "include": {"name": "include", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\n    \"tone\": null,\n    \"word\": null,\n    \"consideration\": null\n  }'"}, "exclude": {"name": "exclude", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\n    \"tone\": null,\n    \"word\": null,\n    \"consideration\": null\n  }'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"keywords_account_id_accounts_id_fk": {"name": "keywords_account_id_accounts_id_fk", "tableFrom": "keywords", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "product_title": {"name": "product_title", "type": "text", "primaryKey": false, "notNull": true}, "product_type": {"name": "product_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "short_description": {"name": "short_description", "type": "text", "primaryKey": false, "notNull": false}, "long_description": {"name": "long_description", "type": "text", "primaryKey": false, "notNull": false}, "gen_ai_description": {"name": "gen_ai_description", "type": "text", "primaryKey": false, "notNull": false}, "specifications": {"name": "specifications", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "product_highlights": {"name": "product_highlights", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "class_type": {"name": "class_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "upc": {"name": "upc", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "images": {"name": "images", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "custom_images": {"name": "custom_images", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": false}, "pdp_summary": {"name": "pdp_summary", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"products_account_id_accounts_id_fk": {"name": "products_account_id_accounts_id_fk", "tableFrom": "products", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "products_brand_id_brands_id_fk": {"name": "products_brand_id_brands_id_fk", "tableFrom": "products", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"products_product_id_unique": {"name": "products_product_id_unique", "nullsNotDistinct": false, "columns": ["product_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permissions": {"name": "permissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "permission_data": {"name": "permission_data", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_settings_user_id_unique": {"name": "user_settings_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brand_retailers": {"name": "brand_retailers", "schema": "", "columns": {"brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "retailer_id": {"name": "retailer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_retailers_brand_id_brands_id_fk": {"name": "brand_retailers_brand_id_brands_id_fk", "tableFrom": "brand_retailers", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "brand_retailers_retailer_id_retailers_id_fk": {"name": "brand_retailers_retailer_id_retailers_id_fk", "tableFrom": "brand_retailers", "tableTo": "retailers", "columnsFrom": ["retailer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"brand_retailers_brand_id_retailer_id_pk": {"name": "brand_retailers_brand_id_retailer_id_pk", "columns": ["brand_id", "retailer_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.retailer_products": {"name": "retailer_products", "schema": "", "columns": {"retailer_id": {"name": "retailer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"retailer_products_retailer_id_retailers_id_fk": {"name": "retailer_products_retailer_id_retailers_id_fk", "tableFrom": "retailer_products", "tableTo": "retailers", "columnsFrom": ["retailer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "retailer_products_product_id_products_product_id_fk": {"name": "retailer_products_product_id_products_product_id_fk", "tableFrom": "retailer_products", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["product_id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"retailer_products_retailer_id_product_id_pk": {"name": "retailer_products_retailer_id_product_id_pk", "columns": ["retailer_id", "product_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.retailer_specs": {"name": "retailer_specs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "asset_url": {"name": "asset_url", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "retailer_id": {"name": "retailer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"retailer_specs_retailer_id_retailers_id_fk": {"name": "retailer_specs_retailer_id_retailers_id_fk", "tableFrom": "retailer_specs", "tableTo": "retailers", "columnsFrom": ["retailer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.retailers": {"name": "retailers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"retailers_slug_unique": {"name": "retailers_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "message": {"name": "message", "type": "jsonb", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'unread'"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "notifications_account_id_accounts_id_fk": {"name": "notifications_account_id_accounts_id_fk", "tableFrom": "notifications", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.leads": {"name": "leads", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "beta_id": {"name": "beta_id", "type": "uuid", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "organization": {"name": "organization", "type": "text", "primaryKey": false, "notNull": true}, "dss_spend": {"name": "dss_spend", "type": "integer", "primaryKey": false, "notNull": false}, "org_size": {"name": "org_size", "type": "integer", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "invite_status": {"name": "invite_status", "type": "invite_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "beta_keys": {"name": "beta_keys", "type": "uuid[]", "primaryKey": false, "notNull": false}, "sent_keys": {"name": "sent_keys", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "wm_campaigns": {"name": "wm_campaigns", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "retailer_id": {"name": "retailer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "current_step": {"name": "current_step", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "project_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'in_progress'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"projects_brand_id_brands_id_fk": {"name": "projects_brand_id_brands_id_fk", "tableFrom": "projects", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "projects_retailer_id_retailers_id_fk": {"name": "projects_retailer_id_retailers_id_fk", "tableFrom": "projects", "tableTo": "retailers", "columnsFrom": ["retailer_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "projects_created_by_users_id_fk": {"name": "projects_created_by_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_credential_sets": {"name": "user_credential_sets", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "credential_set_id": {"name": "credential_set_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_credential_sets_user_id_users_id_fk": {"name": "user_credential_sets_user_id_users_id_fk", "tableFrom": "user_credential_sets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_credential_sets_credential_set_id_credential_sets_id_fk": {"name": "user_credential_sets_credential_set_id_credential_sets_id_fk", "tableFrom": "user_credential_sets", "tableTo": "credential_sets", "columnsFrom": ["credential_set_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_credential_sets_user_id_credential_set_id_pk": {"name": "user_credential_sets_user_id_credential_set_id_pk", "columns": ["user_id", "credential_set_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_accounts": {"name": "user_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'member'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_accounts_user_id_users_id_fk": {"name": "user_accounts_user_id_users_id_fk", "tableFrom": "user_accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "user_accounts_account_id_accounts_id_fk": {"name": "user_accounts_account_id_accounts_id_fk", "tableFrom": "user_accounts", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.proofs": {"name": "proofs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "generation_version_id": {"name": "generation_version_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "email_description": {"name": "email_description", "type": "text", "primaryKey": false, "notNull": false}, "ad_unit_ids": {"name": "ad_unit_ids", "type": "text[]", "primaryKey": false, "notNull": false}, "reviewers": {"name": "reviewers", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{ \"internal\": [], \"external\": [] }'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"proofs_generation_version_id_generation_versions_id_fk": {"name": "proofs_generation_version_id_generation_versions_id_fk", "tableFrom": "proofs", "tableTo": "generation_versions", "columnsFrom": ["generation_version_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.proof_comments": {"name": "proof_comments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "proof_id": {"name": "proof_id", "type": "uuid", "primaryKey": false, "notNull": true}, "reviewer_id": {"name": "reviewer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ad_unit_id": {"name": "ad_unit_id", "type": "text", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}, "reply_to_id": {"name": "reply_to_id", "type": "uuid", "primaryKey": false, "notNull": false}, "x": {"name": "x", "type": "integer", "primaryKey": false, "notNull": false}, "y": {"name": "y", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"proof_comments_proof_id_proofs_id_fk": {"name": "proof_comments_proof_id_proofs_id_fk", "tableFrom": "proof_comments", "tableTo": "proofs", "columnsFrom": ["proof_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "proof_comments_reviewer_id_reviewers_id_fk": {"name": "proof_comments_reviewer_id_reviewers_id_fk", "tableFrom": "proof_comments", "tableTo": "reviewers", "columnsFrom": ["reviewer_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "proof_comments_reply_to_id_proof_comments_id_fk": {"name": "proof_comments_reply_to_id_proof_comments_id_fk", "tableFrom": "proof_comments", "tableTo": "proof_comments", "columnsFrom": ["reply_to_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reviewers": {"name": "reviewers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "proof_id": {"name": "proof_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_external": {"name": "is_external", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "requested_by": {"name": "requested_by", "type": "uuid", "primaryKey": false, "notNull": true}, "last_requested_at": {"name": "last_requested_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"reviewers_proof_id_proofs_id_fk": {"name": "reviewers_proof_id_proofs_id_fk", "tableFrom": "reviewers", "tableTo": "proofs", "columnsFrom": ["proof_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "reviewers_user_id_users_id_fk": {"name": "reviewers_user_id_users_id_fk", "tableFrom": "reviewers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "reviewers_requested_by_users_id_fk": {"name": "reviewers_requested_by_users_id_fk", "tableFrom": "reviewers", "tableTo": "users", "columnsFrom": ["requested_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.account_status": {"name": "account_status", "schema": "public", "values": ["active", "inactive", "deleted"]}, "public.brand_guideline_status": {"name": "brand_guideline_status", "schema": "public", "values": ["active", "archived"]}, "public.image_classification": {"name": "image_classification", "schema": "public", "values": ["product", "lifestyle", "other", "invalid"]}, "public.beta_status": {"name": "beta_status", "schema": "public", "values": ["pending", "accepted", "rejected"]}, "public.invitation_status": {"name": "invitation_status", "schema": "public", "values": ["pending", "accepted", "rejected"]}, "public.invitation_type": {"name": "invitation_type", "schema": "public", "values": ["request", "invite"]}, "public.invite_status": {"name": "invite_status", "schema": "public", "values": ["pending", "accepted", "rejected"]}, "public.project_status": {"name": "project_status", "schema": "public", "values": ["in_progress", "in_review", "published", "inactive", "archived"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}