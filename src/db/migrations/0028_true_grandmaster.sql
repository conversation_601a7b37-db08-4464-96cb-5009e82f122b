ALTER TABLE "creatives" ADD COLUMN "project_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "creatives" ADD CONSTRAINT "creatives_project_id_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "creatives" DROP COLUMN "wm_ad_group_ids";--> statement-breakpoint
ALTER TABLE "creatives" DROP COLUMN "wm_campaign_ids";