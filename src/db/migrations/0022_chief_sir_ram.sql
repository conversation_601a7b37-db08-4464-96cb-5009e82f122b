-- Drop foreign key constraints first
ALTER TABLE "retailer_products" DROP CONSTRAINT IF EXISTS "retailer_products_product_id_products_product_id_fk";--> statement-breakpoint
ALTER TABLE "image_metadata" DROP CONSTRAINT IF EXISTS "image_metadata_product_id_products_product_id_fk";--> statement-breakpoint

-- Now drop the unique constraint
ALTER TABLE "products" DROP CONSTRAINT IF EXISTS "products_product_id_unique";--> statement-breakpoint

-- Make image_metadata.product_id nullable
ALTER TABLE "image_metadata" ALTER COLUMN "product_id" DROP NOT NULL;--> statement-breakpoint

-- Add new column
ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "transparent_thumbnail_url" text;