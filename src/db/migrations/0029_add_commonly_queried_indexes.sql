-- Migration: Add indexes for commonly queried fields
-- Linear Issue: BAF-52 - Index commonly queried fields

-- accountId indexes (for tables that don't already have them)
CREATE INDEX IF NOT EXISTS idx_assets_account_id ON assets(account_id);
CREATE INDEX IF NOT EXISTS idx_credential_sets_account_id ON credential_sets(account_id);
CREATE INDEX IF NOT EXISTS idx_account_settings_account_id ON account_settings(account_id);

-- userId indexes (for tables that don't already have them)
CREATE INDEX IF NOT EXISTS idx_users_id ON users(id);
CREATE INDEX IF NOT EXISTS idx_generation_versions_created_by ON generation_versions(created_by);
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON projects(created_by);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_credential_sets_user_id ON user_credential_sets(user_id);
CREATE INDEX IF NOT EXISTS idx_reviewers_user_id ON reviewers(user_id);

-- projectId indexes (for tables that don't already have them)
CREATE INDEX IF NOT EXISTS idx_projects_id ON projects(id);

-- brandId indexes (for tables that don't already have them)
CREATE INDEX IF NOT EXISTS idx_brands_id ON brands(id);
CREATE INDEX IF NOT EXISTS idx_brand_guidelines_brand_id ON brand_guidelines(brand_id);
CREATE INDEX IF NOT EXISTS idx_brand_categories_brand_id ON brand_categories(brand_id);
CREATE INDEX IF NOT EXISTS idx_brand_retailers_brand_id ON brand_retailers(brand_id);

-- generationId indexes (for tables that reference generations)
CREATE INDEX IF NOT EXISTS idx_generations_id ON generations(id);
CREATE INDEX IF NOT EXISTS idx_generation_versions_generation_id ON generation_versions(generation_id);

-- Additional commonly queried foreign key indexes
CREATE INDEX IF NOT EXISTS idx_proofs_generation_version_id ON proofs(generation_version_id);
CREATE INDEX IF NOT EXISTS idx_proof_comments_proof_id ON proof_comments(proof_id);
CREATE INDEX IF NOT EXISTS idx_proof_comments_reviewer_id ON proof_comments(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_image_metadata_product_id ON image_metadata(product_id);
CREATE INDEX IF NOT EXISTS idx_credential_set_retailers_credential_set_id ON credential_set_retailers(credential_set_id);
CREATE INDEX IF NOT EXISTS idx_credential_set_retailers_retailer_id ON credential_set_retailers(retailer_id);
CREATE INDEX IF NOT EXISTS idx_user_credential_sets_credential_set_id ON user_credential_sets(credential_set_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_account_id ON notifications(account_id);
CREATE INDEX IF NOT EXISTS idx_external_reviewers_user_id ON external_reviewers(user_id);
CREATE INDEX IF NOT EXISTS idx_external_reviewers_account_id ON external_reviewers(account_id);
CREATE INDEX IF NOT EXISTS idx_external_reviewers_brand_id ON external_reviewers(brand_id);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_generation_versions_generation_id_version ON generation_versions(generation_id, version);
CREATE INDEX IF NOT EXISTS idx_proof_comments_proof_id_created_at ON proof_comments(proof_id, created_at);
CREATE INDEX IF NOT EXISTS idx_assets_account_id_created_at ON assets(account_id, created_at);

-- Partial indexes for active/status filtering
CREATE INDEX IF NOT EXISTS idx_user_credential_sets_active ON user_credential_sets(user_id, credential_set_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_notifications_unread ON notifications(user_id, created_at) WHERE is_read = false;

-- Performance indexes for timestamp-based queries
CREATE INDEX IF NOT EXISTS idx_generation_versions_created_at ON generation_versions(created_at);
CREATE INDEX IF NOT EXISTS idx_proofs_created_at ON proofs(created_at);
CREATE INDEX IF NOT EXISTS idx_proof_comments_created_at ON proof_comments(created_at);
CREATE INDEX IF NOT EXISTS idx_assets_created_at ON assets(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);