import { pgTable, uuid, text, timestamp, pgEnum, integer, jsonb } from 'drizzle-orm/pg-core';

export const inviteStatusEnum = pgEnum('invite_status', ['pending', 'accepted', 'rejected']);

export const leads = pgTable('leads', {
  id: uuid('id').defaultRandom().primaryKey(),
  betaId: uuid('beta_id').notNull(),
  fullName: text('full_name').notNull(),
  email: text('email').notNull(),
  organization: text('organization').notNull(), // are we keeping this?
  // brand field?
  dssSpend: integer('dss_spend'),
  orgSize: integer('org_size'),
  role: text('role'),
  inviteStatus: inviteStatusEnum('invite_status').default('pending').notNull(),
  betaKeys: uuid('beta_keys').array(),
  sentKeys: jsonb('sent_keys').default('[]'), // includes email of who they sent the key to (key field should always exist {key: 'uuid', email: null})
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});