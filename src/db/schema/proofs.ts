import { pgTable, uuid, text, timestamp, jsonb } from 'drizzle-orm/pg-core';
import { generationVersions } from './generationVersions';

export const proofs = pgTable('proofs', {
  id: uuid('id').defaultRandom().primaryKey(),
  generationVersionId: uuid('generation_version_id')
    .references(() => generationVersions.id, { onDelete: 'set null' })
    .notNull(),
  title: text('title').notNull(),
  emailDescription: text('email_description'),
  adUnitIds: text('ad_unit_ids').array(),
  reviewers: jsonb('reviewers').default('{ "internal": [], "external": [] }'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});