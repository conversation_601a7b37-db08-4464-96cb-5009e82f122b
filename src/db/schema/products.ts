import { pgTable, uuid, text, varchar, jsonb } from 'drizzle-orm/pg-core';
import { accounts } from './accounts';
import { brands } from './brands';

export const products = pgTable('products', {
  id: uuid('id').defaultRandom().primaryKey(),
  productId: varchar('product_id').notNull(),
  productTitle: text('product_title').notNull(),
  productType: varchar('product_type'),
  category: text('category'),
  thumbnailUrl: text('thumbnail_url'),
  transparentThumbnailUrl: text('transparent_thumbnail_url'),
  shortDescription: text('short_description'),
  longDescription: text('long_description'),
  genAiDescription: text('gen_ai_description'),
  specifications: jsonb('specifications').default('[]'),
  productHighlights: jsonb('product_highlights').default('[]'),
  classType: varchar('class_type'),
  upc: varchar('upc'),
  gtin: varchar('gtin'),
  images: jsonb('images').default('[]'),
  customImages: jsonb('custom_images').default('[]'),
  accountId: uuid('account_id')
    .references(() => accounts.id, { onDelete: 'set null' }),
  brandId: uuid('brand_id')
    .references(() => brands.id, { onDelete: 'set null' }),
  pdpSummary: text('pdp_summary'),
});
