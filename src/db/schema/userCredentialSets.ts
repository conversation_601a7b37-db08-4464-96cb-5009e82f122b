import { pgTable, uuid, timestamp, primaryKey, text } from 'drizzle-orm/pg-core';
import { users } from './users.ts';
import { credentialSets } from './credentialSets.ts';

export const userCredentialSets = pgTable('user_credential_sets', {
  userId: uuid('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull(),
  credentialSetId: uuid('credential_set_id')
    .references(() => credentialSets.id, { onDelete: 'cascade' })
    .notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.userId, table.credentialSetId] })
  };
});