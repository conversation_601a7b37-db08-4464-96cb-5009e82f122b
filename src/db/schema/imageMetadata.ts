import { pgTable, uuid, text, varchar, pgEnum, timestamp, unique } from 'drizzle-orm/pg-core';
import { products } from './products';

export const imageClassification = pgEnum('image_classification', ['product', 'lifestyle', 'other', 'invalid']);
export const imageMetadata = pgTable('image_metadata', {
  id: uuid('id').defaultRandom().primaryKey(),
  productId: varchar('product_id'),
  imageUrl: text('image_url').notNull(),
  imageText: text('image_text'),
  classification: imageClassification('classification').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
}, (table) => ({
  uniqueProductImage: unique().on(table.productId, table.imageUrl),
}));
