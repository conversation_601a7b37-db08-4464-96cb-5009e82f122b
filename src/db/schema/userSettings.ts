import { pgTable, uuid, timestamp, jsonb, boolean } from 'drizzle-orm/pg-core';
import { users } from './users.ts';

export const userSettings = pgTable('user_settings', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull()
    .unique(),
  // emailNotifications: boolean('email_notifications').default(true).notNull(),
  // do email notifications reside in the settings object?
  settings: jsonb('settings').default({
    // phone
    // lanauge default en-us
    // timezone default UTC

  }).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});
