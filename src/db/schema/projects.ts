import { pgTable, uuid, text, timestamp, jsonb, integer, pgEnum } from 'drizzle-orm/pg-core';
import { brands } from './brands.ts';
import { users } from './users.ts';
import { retailers } from './retailers.ts';

export const projectStatus = pgEnum('project_status', ['in_progress', 'in_review', 'published', 'inactive', 'archived']);

export const projects = pgTable('projects', {
  id: uuid('id').defaultRandom().primaryKey(),
  brandId: uuid('brand_id')
    .references(() => brands.id, { onDelete: 'set null' }),
  name: text('name').notNull(),
  wmCampaigns: jsonb('wm_campaigns').default({}).notNull(),
  // Structure
  // {"9BzIWUWf8i":{"name":"Total zero defect standardization","adGroups":{"6Wq0r2xk":"Health","CRFqOU7w":"Electronics"}}}
  // Structure: { [campaignId]: { name: 'Campaign Name', adGroups: { [adGroupId]: 'Ad Group Name' } } }
  retailerId: uuid('retailer_id')
    .references(() => retailers.id, { onDelete: 'set null' }).notNull(),
  createdBy: uuid('created_by')
    .references(() => users.id, { onDelete: 'set null' }),
  currentStep: integer('current_step').notNull().default(0),
  status: projectStatus('status').default('in_progress').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});
