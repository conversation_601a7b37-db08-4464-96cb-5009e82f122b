import { pgTable, uuid, text, timestamp, pgEnum } from 'drizzle-orm/pg-core';
import { brands } from './brands';
import { users } from './users';

export const brandGuidelineStatus = pgEnum('brand_guideline_status', ['active', 'archived']);

export const brandGuidelines = pgTable('brand_guidelines', {
  id: uuid('id').defaultRandom().primaryKey(),
  brandId: uuid('brand_id')
    .references(() => brands.id, { onDelete: 'cascade', onUpdate: 'cascade' })
    .notNull(),
  label: text('label'),
  fileName: text('file_name').notNull().default('guidelines'),
  assetUrl: text('asset_url').notNull().default(''),
  status: brandGuidelineStatus('status').default('active').notNull(), // active, archived
  uploadedBy: uuid('uploaded_by')
    .references(() => users.id, { onDelete: 'set null', onUpdate: 'cascade' }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});