import { pgTable, uuid, text, timestamp, jsonb } from "drizzle-orm/pg-core";
import { accounts } from "./accounts.ts";

export const keywords = pgTable("keywords", {
  id: uuid("id").defaultRandom().primaryKey(),
  keyword: text("keyword").notNull(),
  accountId: uuid("account_id")
    .references(() => accounts.id, { onDelete: "set null" }),
  include: jsonb('include').default(`{
    "tone": null,
    "word": null,
    "consideration": null
  }`),
  exclude: jsonb('exclude').default(`{
    "tone": null,
    "word": null,
    "consideration": null
  }`),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});