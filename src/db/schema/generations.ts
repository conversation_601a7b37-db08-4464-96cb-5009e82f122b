import { pgTable, uuid, varchar, timestamp, PgTableWithColumns } from 'drizzle-orm/pg-core';
import { products } from './products';
import { brands } from './brands';
import { projects } from './projects';
import { keywords } from './keywords';
import { generationVersions } from './generationVersions';

export const generations: PgTableWithColumns<any> = pgTable('generations', {
  id: uuid('id').defaultRandom().primaryKey(),
  productId: uuid('product_id')
    .references(() => products.id)
    .notNull(),
  keywordId: uuid('keyword_id')
    .references(() => keywords.id),
  projectId: uuid('project_id')
    .references(() => projects.id)
    .notNull(),
  brandId: uuid('brand_id')
    .references(() => brands.id)
    .notNull(),
  generationVersionId: uuid('generation_version_id')
    .references(() => generationVersions.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});
