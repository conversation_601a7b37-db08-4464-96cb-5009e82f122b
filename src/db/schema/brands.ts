import { pgTable, uuid, text, timestamp, jsonb, primaryKey, integer } from 'drizzle-orm/pg-core';
import { categories } from './categories';
import { accounts } from './accounts.ts';
import { credentialSets } from './credentialSets.ts';

export const brands = pgTable('brands', {
  id: uuid('id').defaultRandom().primaryKey(),
  accountId: uuid('account_id')
    .references(() => accounts.id, { onDelete: 'set null' }),
  name: text('name').notNull(),
  description: text('description'),
  colors: jsonb('colors').default([]),
  logoAssetUrl: text('logo_asset_url'),
  primaryCredentialId: uuid('primary_credential_id')
    .references(() => credentialSets.id, { onDelete: 'set null' }),
  notes: text('notes'),
  productCount: integer('product_count').default(0), // (from walmart display list brands)
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Many-to-many relationship between brands and categories
export const brandCategories = pgTable('brand_categories', {
  brandId: uuid('brand_id')
    .references(() => brands.id, { onDelete: 'cascade' })
    .notNull(),
  categoryId: uuid('category_id')
    .references(() => categories.id, { onDelete: 'cascade' })
    .notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.brandId, table.categoryId] })
  };
});
