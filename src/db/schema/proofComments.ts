import { pgTable, uuid, text, timestamp, integer } from 'drizzle-orm/pg-core';
import { proofs } from './proofs';
import { reviewers } from './reviewers';
import type { InferSelectModel } from 'drizzle-orm';

export const proofComments = pgTable('proof_comments', {
  id: uuid('id').defaultRandom().primaryKey(),
  proofId: uuid('proof_id')
    .references(() => proofs.id, { onDelete: 'set null' })
    .notNull(),
  reviewerId: uuid('reviewer_id')
    .references(() => reviewers.id, { onDelete: 'set null' })
    .notNull(),
  adUnitId: text('ad_unit_id'),
  comment: text('comment').notNull(),
  replyToId: uuid('reply_to_id')
    .references((): any => proofComments.id, { onDelete: 'set null' }),
  x: integer('x'),
  y: integer('y'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

export type ProofComment = InferSelectModel<typeof proofComments>;