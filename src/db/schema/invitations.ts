 import { pgTable, uuid, text, timestamp, pgEnum } from 'drizzle-orm/pg-core';
import { accounts } from './accounts';
import { brands } from './brands';
export const invitationType = pgEnum('invitation_type', ['request', 'invite']);
export const invitationStatus = pgEnum('invitation_status', ['pending', 'accepted', 'rejected']);

export const invitations = pgTable('invitations', {
  id: uuid('id').defaultRandom().primaryKey(),
  accountId: uuid('account_id')
    .references(() => accounts.id, { onDelete: 'cascade' })
    .notNull(),
  brandIds: uuid('brand_ids').array(),
  email: text('email').notNull(),
  type: invitationType('type').notNull(),
  role: text('role').default('member').notNull(),
  status: invitationStatus('status').default('pending').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});