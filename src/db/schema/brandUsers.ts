import { pgTable, uuid, timestamp, text, primaryKey } from 'drizzle-orm/pg-core';
import { brands } from './brands.ts';
import { users } from './users.ts';

// Many-to-many relationship between brands and users
export const brandUsers = pgTable('brand_users', {
  brandId: uuid('brand_id')
    .references(() => brands.id, { onDelete: 'cascade' })
    .notNull(),
  userId: uuid('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull(),
  role: text('role').default('member').notNull(), // owner, admin, member
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.brandId, table.userId] })
  };
});