import { pgTable, uuid, jsonb, text, timestamp } from 'drizzle-orm/pg-core'
import { users } from './users'
import { accounts } from './accounts'

export const notifications = pgTable('notifications', {
  id: uuid('id').primaryKey().defaultRandom().notNull(),
  message: jsonb('message').notNull(),
  status: text('status').default('unread').notNull(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'set null' }),
  accountId: uuid('account_id').references(() => accounts.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
})