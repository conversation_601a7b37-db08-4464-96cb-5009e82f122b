import { pgTable, uuid, text, timestamp, pgEnum, boolean, integer, AnyPgColumn } from 'drizzle-orm/pg-core';
import { users } from './users';

export const accountStatus = pgEnum('account_status', ['active', 'inactive', 'deleted']);

export const accounts = pgTable('accounts', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: text('name').notNull(),
  type: text('type').default('default').notNull(),
  isTrial: boolean('is_trial').default(false).notNull(),
  seats: integer('seats').default(1).notNull(),
  ownerId: uuid('owner_id')
    .references((): AnyPgColumn => users.id, { onDelete: 'set null', onUpdate: 'cascade' })
    .notNull(),
  status: accountStatus('status').default('active').notNull(),
  stripeCustomerId: text('stripe_customer_id'),
  validSubscription: boolean('valid_subscription').notNull().default(false),
  hasPaymentMethod: boolean('has_payment_method').notNull().default(false),
  onboardingStep: integer('onboarding_step').default(0).notNull(),
  logoUrl: text('logo_url'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at')
});

// Account Types
// brand, agency, broker, marketplace, other