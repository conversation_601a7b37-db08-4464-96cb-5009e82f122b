import { pgTable, uuid, timestamp, jsonb } from 'drizzle-orm/pg-core';
import { accounts } from './accounts.ts';

export const accountSettings = pgTable('account_settings', {
  id: uuid('id').defaultRandom().primaryKey(),
  accountId: uuid('account_id')
    .references(() => accounts.id, { onDelete: 'cascade' })
    .notNull()
    .unique(),
  settings: jsonb('settings').default({
    // notifications
  }).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});
