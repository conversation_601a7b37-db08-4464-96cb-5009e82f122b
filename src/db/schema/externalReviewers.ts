import { pgTable, uuid, text, timestamp } from 'drizzle-orm/pg-core';
import { accounts } from './accounts';
import { retailers } from './retailers';
import { users } from './users';

export const externalReviewers = pgTable('external_reviewers', {
  id: uuid('id').defaultRandom().primaryKey(),
  accountId: uuid('account_id')
    .references(() => accounts.id, { onDelete: 'set null' })
    .notNull(),
  jobTitle: text('job_title').notNull(),
  name: text('name').notNull(),
  email: text('email').notNull(),
  requestedByUserId: uuid('requested_by_user_id')
    .references(() => users.id, { onDelete: 'set null' })
    .notNull(),
  retailerId: uuid('retailer_id')
    .references(() => retailers.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

