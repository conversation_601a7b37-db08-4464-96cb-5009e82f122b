import { pgTable, uuid, text, timestamp, jsonb, boolean } from 'drizzle-orm/pg-core';
import { accounts } from './accounts.ts';
import { users } from './users.ts';

export const credentialSets = pgTable('credential_sets', {
    id: uuid('id').defaultRandom().primaryKey(),
    name: text('name').default('Default Credential Set').notNull(),
    type: text('type').default('default').notNull(), // wm_display, wm_supplier, wm_marketplace, wm_sponsored, canva
    credentials: jsonb('credentials').default({
        // advertiserId: null,
        // nonce: null,
        // state: null,
        // partnerId: null,
        // accessToken: null,
        // refreshToken: null,
        // expiresAt: null
    }),
    accountId: uuid('account_id')
        .references(() => accounts.id, { onDelete: 'cascade' })
        .notNull(),
    adminId: uuid('admin_id')
        .references(() => users.id, { onDelete: 'set null' }),
    isShared: boolean('is_shared').default(true).notNull(),
    isActive: boolean('is_active').default(false).notNull(),
    deletedAt: timestamp('deleted_at'), // null = active, timestamp = soft deleted
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull()
});

