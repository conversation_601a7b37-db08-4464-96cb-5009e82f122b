import { pgTable, uuid, text, timestamp, boolean } from 'drizzle-orm/pg-core';
import { proofs } from './proofs';
import { users } from './users';

export const reviewers = pgTable('reviewers', {
  id: uuid('id').defaultRandom().primaryKey(),
  proofId: uuid('proof_id')
    .references(() => proofs.id, { onDelete: 'set null' }),
  name: text('name').notNull(),
  email: text('email').notNull(),
  userId: uuid('user_id')
    .references(() => users.id, { onDelete: 'set null' }),
  isExternal: boolean('is_external').default(false),
  status: text('status').default('pending'),
  requestedBy: uuid('requested_by')
    .references(() => users.id, { onDelete: 'set null' })
    .notNull(),
  lastRequestedAt: timestamp('last_requested_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});