import { pgTable, uuid, timestamp, text } from 'drizzle-orm/pg-core';
import { users } from './users.ts';
import { accounts } from './accounts.ts';

export const userAccounts = pgTable('user_accounts', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id')
    .references(() => users.id, { onDelete: 'set null' }),
  accountId: uuid('account_id')
    .references(() => accounts.id, { onDelete: 'set null' }),
  role: text('role').default('member').notNull(), // owner, admin, member,
  status: text('status').default('active').notNull(), // active, inactive
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at')
});