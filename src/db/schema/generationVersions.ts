import { pgTable, uuid, text, timestamp, jsonb, integer, boolean, PgTableWithColumns } from 'drizzle-orm/pg-core';
import { generations } from './generations';
import { users } from './users';

export const generationVersions: PgTableWithColumns<any> = pgTable('generation_versions', {
  id: uuid('id').defaultRandom().primaryKey(),
  generationId: uuid('generation_id')
    .references(() => generations.id)
    .notNull(),
  sessionId: text('session_id'),
  unitFields: jsonb('unit_fields').default('{}'),
  hasCompleted: boolean('has_completed').default(false),
  createdBy: uuid('created_by')
    .references(() => users.id)
    .notNull(),
  version: integer('version').default(1),
  adPreviews: jsonb('ad_previews').default('[]'),
  previewImagePositions: jsonb('preview_image_positions').default('{}'),
  applyToAll: boolean('apply_to_all').default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});