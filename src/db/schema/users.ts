import {
  pgTable,
  uuid,
  text,
  timestamp,
  integer,
  boolean,
  pgEnum,
  primaryKey,
} from "drizzle-orm/pg-core";

export const betaStatusEnum = pgEnum("beta_status", [
  "pending",
  "accepted",
  "rejected",
]);

export const users = pgTable("users", {
  id: uuid("id").defaultRandom().primaryKey(),
  auth0Id: text("auth0_id").notNull().unique(),
  email: text("email").notNull().unique(),
  isVerified: boolean("is_verified").notNull().default(false),
  name: text("name").notNull(),
  jobTitle: text("job_title"),
  phone: text("phone"),
  status: text("status").default("active"), // active, inactive, pending, deleted
  language: text("language").default("en-US"),
  timeZone: text("time_zone").default("UTC"),
  isBetaUser: boolean("is_beta_user").notNull().default(false),
  betaKeyId: uuid("beta_key_id"),
  lastLoginAt: timestamp("last_login_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
