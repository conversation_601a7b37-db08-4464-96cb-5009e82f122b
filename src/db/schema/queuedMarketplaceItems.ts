import { pgTable, uuid, text, timestamp } from 'drizzle-orm/pg-core';
import { products } from './products';
import { credentialSets } from './credentialSets';

export const queuedMarketplaceItems = pgTable('queued_marketplace_items', {
  id: uuid('id').defaultRandom().primaryKey(),
  productId: uuid('product_id').references(() => products.id, { onDelete: 'set null' }).notNull(),
  credentialSetId: uuid('credential_set_id')
    .references(() => credentialSets.id, { onDelete: 'set null' }),
  status: text('status').notNull().default('queued'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});