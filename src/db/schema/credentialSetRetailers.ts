import { pgTable, uuid, timestamp, primaryKey } from 'drizzle-orm/pg-core';
import { credentialSets } from './credentialSets.ts';
import { retailers } from './retailers.ts';

// Many-to-many relationship between credential sets and retailers
export const credentialSetRetailers = pgTable('credential_set_retailers', {
  credentialSetId: uuid('credential_set_id')
    .references(() => credentialSets.id, { onDelete: 'cascade' })
    .notNull(),
  retailerId: uuid('retailer_id')
    .references(() => retailers.id, { onDelete: 'cascade' })
    .notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.credentialSetId, table.retailerId] })
  };
});