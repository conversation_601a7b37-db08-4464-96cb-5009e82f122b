import { pgTable, uuid, timestamp, jsonb } from 'drizzle-orm/pg-core';

const permissionMap = {
  'can_create_brand': true,
  'can_create_creative': true,
}

export const permissions = pgTable('permissions', {
  id: uuid('id').defaultRandom().primaryKey(),
  permissionData: jsonb('permission_data').default({}).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});