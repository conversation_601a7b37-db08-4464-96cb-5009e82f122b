import { pgTable, uuid, text, timestamp, primaryKey, varchar } from 'drizzle-orm/pg-core';
import { brands } from './brands';
import { products } from './products';

export const retailers = pgTable('retailers', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: text('name').notNull(),
  logoUrl: text('logo_url'),
  url: text('url'),
  slug: text('slug').notNull().unique(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Many-to-many relationship between brands and retailers
export const brandRetailers = pgTable('brand_retailers', {
  brandId: uuid('brand_id')
    .references(() => brands.id, { onDelete: 'cascade' })
    .notNull(),
  retailerId: uuid('retailer_id')
    .references(() => retailers.id, { onDelete: 'cascade' })
    .notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.brandId, table.retailerId] })
  };
});

// Many-to-many relationship between retailers and products
export const retailerProducts = pgTable('retailer_products', {
  retailerId: uuid('retailer_id')
    .references(() => retailers.id, { onDelete: 'cascade' })
    .notNull(),
  productId: varchar('product_id')
    .notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.retailerId, table.productId] })
  };
});

// Retailer specifications table
export const retailerSpecs = pgTable('retailer_specs', {
  id: uuid('id').defaultRandom().primaryKey(),
  assetUrl: text('asset_url').notNull(),
  status: text('status').notNull(),
  retailerId: uuid('retailer_id')
    .references(() => retailers.id, { onDelete: 'cascade' })
    .notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});
