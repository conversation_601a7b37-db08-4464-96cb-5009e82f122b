import { pgTable, uuid, text, timestamp, jsonb, integer } from 'drizzle-orm/pg-core';
import { brands } from './brands.ts';
import { users } from './users.ts';
import { generations } from './generations.ts';
import { projects } from './projects.ts';

export const creatives = pgTable('creatives', {
  id: uuid('id').defaultRandom().primaryKey(),
  // wmAdGroupIds: text('wm_ad_group_ids').array(),
  // wmCampaignIds: text('wm_campaign_ids').array(), // from walmart
  projectId: uuid('project_id')
    .references(() => projects.id)
    .notNull(),
  wmCreativeId: text('wm_creative_id'), // walmart creative id
  brandId: uuid('brand_id')
    .references(() => brands.id)
    .notNull(),
  userId: uuid('user_id')
    .references(() => users.id)
    .notNull(),
  generationId: uuid('generation_id')
    .references(() => generations.id),
  previewUrls: jsonb('preview_urls').default([]),
  previewStatus: text('preview_status').default('PENDING'), // from walmart
  folderId: uuid('folder_id'),
  status: text('status').default('DRAFT').notNull(), // from walmart
  keyword: text('keyword'), // will this now reference the keywords table?
  adUnits: jsonb('ad_units').notNull(), // from walmart
  reviewComments: jsonb('review_comments').default([]),
  // version: integer('version').default(1),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});
