# Parameterized Database Seeding

The parameterized database seeder allows you to create specific amounts of data for targeted users, accounts, or across the entire system. This is perfect for testing scenarios or creating custom demo environments.

## Quick Start

```bash
# Get help and see all available options
npm run db:seed:custom:help

# Run a dry run to see what would be created
npm run db:seed:custom:dry

# Create 5 brands with 10 users each for a specific super user
npm run db:seed:custom -- -u YOUR_USER_ID -b 5 -uc 10

# Add brands to a specific account without clearing the database
npm run db:seed:custom -- -a YOUR_ACCOUNT_ID -b 3 --skip-clearing
```

## Available NPM Scripts

- `db:seed:custom` - Main parameterized seeding command
- `db:seed:custom:help` - Show help and usage information
- `db:seed:custom:dry` - Run in dry mode to preview changes
- `db:seed:user` - Quick template for user-specific seeding
- `db:seed:brands` - Quick template for brand creation
- `db:seed:users` - Quick template for user creation

## Command Line Options

### Target Selection
- `-u, --super-user-id <uuid>` - Target a specific super user
- `-a, --account-id <uuid>` - Target a specific account

### Content Creation
- `-b, --brand-count <number>` - Number of brands to create (1-50)
- `-uc, --user-count <number>` - Number of users to create (1-100)
- `-p, --product-count <number>` - Number of products to create (1-200)
- `-pr, --project-count <number>` - Number of projects to create (1-50)

### Database Management
- `--skip-clearing` - Don't clear the database before seeding
- `--force-clearing` - Force database clearing (overrides SKIP_CLEARING env var)
- `--dry-run` - Preview what would be done without executing

## Usage Examples

### Super User Scenarios

```bash
# Create a complete ecosystem for a super user
npm run db:seed:custom -- -u ************************************ -b 5 -uc 15 -p 25 -pr 8

# Add more brands to an existing super user's account
npm run db:seed:custom -- -u ************************************ -b 3 --skip-clearing

# Create demo users for a super user's brands
npm run db:seed:custom -- -u ************************************ -uc 20 --skip-clearing
```

### Account-Specific Scenarios

```bash
# Add brands to a specific account
npm run db:seed:custom -- -a ************************************ -b 4 --skip-clearing

# Add users to a specific account
npm run db:seed:custom -- -a ************************************ -uc 10 --skip-clearing
```

### Global/Distributed Scenarios

```bash
# Create brands distributed across all super user accounts
npm run db:seed:custom -- -b 20

# Create users distributed across all accounts
npm run db:seed:custom -- -uc 50

# Create products distributed across all brands
npm run db:seed:custom -- -p 100 --skip-clearing
```

### Testing & Development

```bash
# Preview what would be created (dry run)
npm run db:seed:custom -- -b 10 -uc 25 -p 50 --dry-run

# Quick test data setup
npm run db:seed:custom -- -b 3 -uc 5 -p 10 -pr 2

# Force clean and rebuild with specific data
npm run db:seed:custom -- -b 8 -uc 20 --force-clearing
```

## Data Distribution Logic

### Super User Targeting (`-u`)
When targeting a specific super user:
- Brands are created in their owned account
- Users are added to their account and distributed across their brands
- Content (products/projects) is created for their brands

### Account Targeting (`-a`)
When targeting a specific account:
- Brands are created directly in that account
- Users are added to that account and distributed across its brands

### Global Distribution
When no specific target is provided:
- Brands are distributed evenly across all super user accounts
- Users are distributed evenly across all accounts
- Content is distributed across all existing brands

## Validation & Safety

- All UUIDs are validated before processing
- Counts are validated against reasonable limits
- Dry run mode lets you preview changes safely
- Database clearing can be controlled or skipped
- Comprehensive error handling and logging

## Super User IDs

Get super user IDs from the `superusers.json` file or query the database:

```sql
SELECT id, name, email FROM users WHERE email LIKE '%@advid.ai';
```

## Account IDs

Get account IDs from the database:

```sql
SELECT id, name, "ownerId" FROM accounts;
```

## Integration with Existing Seeders

The parameterized seeder works alongside existing seeders:
- Uses the same modular architecture
- Shares utility functions and validation
- Can be run after base seeding for additional data
- Compatible with all existing npm seed scripts