import 'dotenv/config';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import * as schema from '../schema';

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql, { schema });

console.log('🔍 Validating seeded data structure...');

const users = await db.select().from(schema.users);
const accounts = await db.select().from(schema.accounts);
const userAccounts = await db.select().from(schema.userAccounts);
const brands = await db.select().from(schema.brands);
const brandUsers = await db.select().from(schema.brandUsers);
const products = await db.select().from(schema.products);

console.log(`📊 Data validation:`);
console.log(`  Users: ${users.length} (4 super users + ${users.length - 4} demo users)`);
console.log(`  Accounts: ${accounts.length} (one per super user)`);
console.log(`  User-Account relationships: ${userAccounts.length}`);
console.log(`  Brands: ${brands.length} (${brands.length / accounts.length} per account)`);
console.log(`  Brand-User relationships: ${brandUsers.length}`);
console.log(`  Products: ${products.length}`);

// Check super user requirements
const superUsers = users.slice(0, 4);
console.log(`\n🔍 Super User Account Ownership (should own exactly 1 account each):`);
for (const superUser of superUsers) {
  const ownedAccounts = accounts.filter(acc => acc.ownerId === superUser.id);
  const userAccountRels = userAccounts.filter(ua => ua.userId === superUser.id);
  console.log(`  ${superUser.name}: owns ${ownedAccounts.length} account(s), member of ${userAccountRels.length} total`);
}

// Check account membership (should have 5+ demo users each)
console.log(`\n🔍 Account Membership (should have 6 total: 1 owner + 5 demo users):`);
for (const account of accounts) {
  const memberCount = userAccounts.filter(ua => ua.accountId === account.id).length;
  const owner = users.find(u => u.id === account.ownerId);
  console.log(`  ${account.name} (owner: ${owner?.name}): ${memberCount} total members`);
}

// Check super user brand access (should have 3+ brands with different roles)
console.log(`\n🔍 Super User Brand Access (should have 3+ brands with owner/admin/member roles):`);
for (const superUser of superUsers) {
  const userBrandRels = brandUsers.filter(bu => bu.userId === superUser.id);
  const roles = userBrandRels.map(bu => bu.role);
  const roleCount = roles.reduce((acc, role) => {
    acc[role] = (acc[role] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  console.log(`  ${superUser.name}: ${userBrandRels.length} brands`);
  console.log(`    - owner: ${roleCount.owner || 0}, admin: ${roleCount.admin || 0}, member: ${roleCount.member || 0}`);
}

// Check brand membership (should have 5+ demo users each)
console.log(`\n🔍 Brand Membership (should have 5+ demo users + super users):`);
const brandMembershipCounts = brands.map(brand => {
  const memberCount = brandUsers.filter(bu => bu.brandId === brand.id).length;
  return { name: brand.name.substring(0, 30) + '...', count: memberCount };
}).sort((a, b) => a.count - b.count);

for (const brand of brandMembershipCounts) {
  console.log(`  ${brand.name}: ${brand.count} members`);
}

console.log('✅ Validation complete!');