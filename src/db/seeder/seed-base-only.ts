import 'dotenv/config';
import { clearDatabase } from './seeders/core';
import { ensureSuperUsers, ensureAccounts, ensureAccountSettings, ensureUserSettings, ensurePermissions } from './seeders/base';
import { ensureUserAccounts } from './seeders/relationships';
import { logger } from './seeders/utils';
import { ENV_CONFIG } from './seedConfig';

console.log('🌱 Starting base entities seeding only...');

// Validate environment
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

try {
  logger.startPhase('Database Preparation');

  if (!ENV_CONFIG.SKIP_CLEARING) {
    console.log('🧹 Clearing database...');
    await clearDatabase();
  }

  logger.endPhase();

  logger.startPhase('Base Entities');

  // Seed base entities and capture counts
  const users = await ensureSuperUsers();
  const accounts = await ensureAccounts(users);
  const userAccountsCount = await ensureUserAccounts(users, accounts);
  const accountSettingsCount = await ensureAccountSettings(accounts);
  const userSettingsCount = await ensureUserSettings(users);
  const permissionsCount = await ensurePermissions();

  logger.endPhase();

  const totalRecords = users.length + accounts.length + userAccountsCount + accountSettingsCount + userSettingsCount + permissionsCount;
  logger.logSummary(totalRecords);

} catch (error) {
  logger.logError('Base entities seeding failed', error instanceof Error ? error : new Error(String(error)));
  process.exit(1);
}

console.log('✅ Base entities seeding completed successfully!');