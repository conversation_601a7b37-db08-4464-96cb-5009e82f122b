# Database Seeding Guide

## Overview

This directory contains database seeding scripts that properly handle super users from configuration files and ensure a clean, consistent database state for development and demo purposes.

## Files

- **`seed.ts`** - ✅ **Main seeder (improved, idempotent for super users)**
- **`idempotent-seed.ts`** - Alternative idempotent seeder (preserves existing data)
- **`realistic-demo-seeder.ts`** - Enhances existing data with realistic scenarios
- **`superUsers.json`** - Configuration file containing specific users to ensure exist
- **`config.ts`** - Database configuration
- **`jsonb-examples.ts`** - Example JSONB data structures

## Key Improvements

### 🧹 Always Clean Start
- **Always clears database** before seeding (unless `SKIP_CLEARING=true`)
- **Handles circular dependencies** properly (generations ↔ generationVersions)
- **Includes all tables** in clearing process (including retailerSpecs)
- **Safe re-runnable** - can be executed multiple times without errors

### 👥 Super User Management
- **Loads specific users** from `superUsers.json`
- **Ensures they exist** in the database with exact data
- **Creates proper relationships** for super users (accounts, brands, credentials, etc.)
- **Adds realistic additional users** if needed for demo scenarios (minimum 8 users)

### 🔧 Schema Compatibility
- **Fixed schema mismatches**: Uses correct field names (`isVerified` not `emailVerified`)
- **Removed invalid fields**: No longer tries to set non-existent fields like `isActive` on brandUsers
- **Proper notifications structure**: Uses correct JSONB structure with `message` and `status` fields
- **Handles FK constraints**: Properly manages foreign key dependencies during clearing

## Usage

### Recommended: Main Seeder
```bash
# Clean database and seed with fresh data (recommended)
npm run db:seed
# or
npx tsx src/db/seeder/seed.ts

# Skip clearing existing data (preserves current data)
SKIP_CLEARING=true npm run db:seed
```

### Alternative: Idempotent Seeder
```bash
# Safe, preserves existing data while adding missing pieces
npx tsx src/db/seeder/idempotent-seed.ts

# Force clear non-user data first (use with caution)
FORCE_CLEAR=true npx tsx src/db/seeder/idempotent-seed.ts
```

### Realistic Demo Enhancement
```bash
# Run after either seeder to add realistic demo scenarios
npx tsx src/db/seeder/realistic-demo-seeder.ts
```

## Super Users Configuration

The `superUsers.json` file contains 4 specific users that must exist in the system:

```json
[
  {
    "id": "681d85e5-3294-4c70-82f3-57c2b4265e27",
    "auth0_id": "auth0|67a6205660d9881ab36c6b59",
    "email": "<EMAIL>",
    "name": "Geoffrey Hinton",
    "job_title": "Demos Dancer",
    "phone": "911",
    "language": "en-US",
    "time_zone": "America/Chicago",
    "status": "active",
    "is_verified": false
  }
  // ... 3 more users (Bryce, Aayushii, Max)
]
```

### What Happens to Super Users

1. **Existence Check**: Seeder checks if each super user exists by `auth0_id`
2. **Safe Insert**: Missing super users are inserted with their exact data
3. **Relationship Creation**: Super users get proper access to:
   - Primary demo account (as admin/owner roles)
   - Demo brands (as admin/editor roles)
   - Credential sets (full access to demo Walmart Connect credentials)
   - User settings (with proper configurations)
4. **Demo-Ready**: Super users can immediately log in and demonstrate all app features

## Main vs. Idempotent Seeder

| Feature | Main `seed.ts` | Idempotent `idempotent-seed.ts` |
|---------|----------------|--------------------------------|
| **Approach** | 🧹 Clean slate | 🛡️ Preserves existing |
| **Super Users** | ✅ Loads from JSON | ✅ Loads from JSON |
| **Database Clearing** | ✅ Always (unless skipped) | ❌ Optional with FORCE_CLEAR |
| **Schema Compliance** | ✅ Matches current schema | ✅ Matches current schema |
| **Re-runnable** | ✅ Always consistent state | ✅ Truly idempotent |
| **Data Volume** | 🎲 Rich, varied data | 🎯 Minimal, focused data |
| **Demo Ready** | ✅ Full featured demos | ✅ Basic functionality |

## What Gets Created

### Core Entities
- ✅ **Users**: 4 super users from config + 4+ additional faker users for realistic scenarios
- ✅ **Accounts**: 3 demo accounts with realistic settings and proper ownership
- ✅ **Settings**: Account & user settings with proper JSONB configurations

### Business Data
- ✅ **Categories**: 12 categories (Electronics, Fashion, Home & Garden, etc.)
- ✅ **Keywords**: 20 keywords with include/exclude tone and word rules
- ✅ **Retailers**: Multiple retailers (Walmart, Amazon, etc.) with proper specs
- ✅ **Credential Sets**: 6 credential sets including demo Walmart Connect credentials
- ✅ **Brands**: 8 brands with realistic branding, colors, and product counts

### Relationships & Access
- ✅ **User ↔ Account**: Super users get admin/owner access to primary account
- ✅ **Brand ↔ User**: Super users get admin/editor roles on demo brands (1-4 users per brand)
- ✅ **User ↔ Credential Set**: Super users get access to 1-2 credential sets each
- ✅ **Brand ↔ Category**: Brands properly categorized
- ✅ **Credential Set ↔ Retailer**: Credential sets connected to 2-5 retailers each
- ✅ **Brand ↔ Retailer**: Brands connected to relevant retailers

### Content & Workflow
- ✅ **Assets**: Demo assets with proper metadata
- ✅ **Products**: Realistic products (5-150 per brand) with proper categorization
- ✅ **Projects**: Demo campaigns with realistic settings and retailer connections
- ✅ **Generations**: Sample ad generations with proper product/keyword associations
- ✅ **Creatives**: Ad creatives with review workflows
- ✅ **Proofs & Reviews**: Complete review workflow with comments and external reviewers
- ✅ **Notifications**: Realistic notification data with proper JSONB structure

## Environment Variables

The seeder respects these environment variables:

- **`SKIP_CLEARING=true`** - Skip database clearing (preserves existing data)
- **`DATABASE_URL`** - Required for database connection

## Logging Output

The main seeder provides comprehensive logging:

```
🌱 Starting idempotent database seeding...
🧹 Clearing database before seeding...
✅ Database cleared successfully
📋 Loaded 4 super users from config
👥 Ensuring super users exist...
Found 0 existing users
🔧 Inserting 4 missing super users...
✅ Inserted 4 super users
✅ Added 4 additional faker users
👥 Total users available: 8
🏢 Ensuring accounts exist...
✅ Created 3 new accounts
🔗 Seeding user accounts...
✅ Created 18 user account relationships
⚙️ Seeding account settings...
✅ Created 3 account settings
👤 Seeding user settings...
✅ Created 8 user settings
📂 Seeding categories...
✅ Created 12 categories
🔍 Seeding keywords...
✅ Created 20 keywords
🏷️ Seeding brands...
✅ Created 8 brands
🔐 Seeding credential sets...
✅ Created 6 credential sets
🛒 Seeding retailers...
✅ Created 10 retailers
🔗 Linking brands with credential sets...
✅ Updated 5 brands with primary credential sets
```

## Recent Improvements

### Schema Fixes
- ✅ **Fixed user fields**: Changed `emailVerified` to `isVerified` to match schema
- ✅ **Removed invalid fields**: No longer sets non-existent `isActive` fields
- ✅ **Notification structure**: Uses proper `message` (JSONB) and `status` fields
- ✅ **Complete table coverage**: Added missing `retailerSpecs` to clearing process

### Dependency Management
- ✅ **Circular dependency fix**: Handles generations ↔ generationVersions circular FK properly
- ✅ **Safe clearing order**: Respects all foreign key constraints during database clearing
- ✅ **No orphaned data**: Prevents foreign key constraint violations

### Reliability
- ✅ **Always clean start**: Database clearing is now the default behavior
- ✅ **Consistent results**: Same seed value (42) ensures reproducible fake data
- ✅ **Error-free execution**: Can be run multiple times without constraint errors

## Best Practices

1. **Use the main seeder** (`seed.ts`) for development and demo environments
2. **Run regularly** to ensure fresh, consistent demo data
3. **Update superUsers.json** when adding new required users
4. **Use SKIP_CLEARING=true** only when you want to preserve existing data
5. **Check logs** to verify expected data creation

## Migration from Previous Versions

If you were using an older version of the seeder:

1. **Database structure**: Current seeder matches the latest schema exactly
2. **Clean execution**: No more constraint errors or orphaned data issues
3. **Super user preservation**: Your configured super users will be properly created
4. **Rich demo data**: More comprehensive data for better demos

## Troubleshooting

### Constraint Errors
If you see foreign key constraint errors:
- The current seeder handles all FK dependencies properly
- Make sure you're using the latest version of `seed.ts`
- Check that no other processes are modifying the database during seeding

### Missing Super Users
If super users aren't being created:
- Check `superUsers.json` file exists and is valid JSON
- Verify the `auth0_id` fields are unique and properly formatted
- Check the seeder logs for any insertion errors

### Schema Mismatches
If you see field-related errors:
- The current seeder matches the latest schema
- Ensure your database migrations are up to date
- Check that field names in the seeder match your schema definitions

### Performance
The main seeder is optimized for clean, comprehensive data:
- Always starts with a clean database for consistency
- Creates substantial demo data for realistic scenarios
- Typically completes in under 30 seconds for full seeding