import 'dotenv/config';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { eq, inArray } from 'drizzle-orm';
import { faker } from '@faker-js/faker';
import { readFileSync } from 'fs';
import { join } from 'path';
import * as schema from '../schema';
import * as relations from '../relations';

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is required');
}
const sql = neon(process.env.DATABASE_URL);
const db = drizzle(sql, { schema: { ...schema, ...relations } });

// Set faker seed for consistent results
faker.seed(100);

console.log('🦸‍♂️ Starting enhanced super user seeding...');

await enhanceSuperUsers();

console.log('✅ Enhanced super user seeding completed!');

/**
 * Loads and parses super user data from a local JSON file.
 *
 * Returns an array of super user objects, or an empty array if the file cannot be read or parsed.
 */
function loadSuperUsers() {
  try {
    const superUsersPath = join(process.cwd(), 'src/db/seeder/superUsers.json');
    const data = readFileSync(superUsersPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.log('⚠️  Could not load superUsers.json, using empty array');
    return [];
  }
}

/**
 * Populates the database with fully connected relational data for all super users.
 *
 * For each super user, creates three accounts with distinct roles (owner, admin, member), assigns brands to each account, links brands to retailers, generates products and projects, and ensures credential sets are assigned. All related entities are created and associated to reflect realistic user, account, and brand relationships.
 *
 * @throws If no super users are found or if any step in the enhancement process fails.
 */
async function enhanceSuperUsers() {
  try {
    const superUsers = loadSuperUsers();

    if (superUsers.length === 0) {
      throw new Error('No super users found in superUsers.json');
    }

    console.log(`Found ${superUsers.length} super users to enhance`);

    // Ensure super users exist in the database
    await ensureSuperUsersExist(superUsers);

    // Get existing data
    const users = await db.select().from(schema.users).where(
      inArray(schema.users.id, superUsers.map((su: any) => su.id))
    );
    const retailers = await db.select().from(schema.retailers);
    const categories = await db.select().from(schema.categories);

    console.log(`Working with ${users.length} users, ${retailers.length} retailers`);

    // Create dedicated accounts, brands, and relationships for each super user
    for (const [index, user] of users.entries()) {
      console.log(`\n👤 Setting up relationships for user: ${user.name} (${user.email})`);

      // Create 3 accounts for this user
      const userAccounts = await createAccountsForUser(user, index);

      // Assign user to accounts with specific roles (owner, admin, member)
      await assignUserToAccounts(user, userAccounts);

      // Create 1 brand for each account (so 3 brands total)
      const userBrands = await createBrandsForAccounts(userAccounts, user);

      // Assign user to the brands (as admin/member since they're the account owner/admin/member)
      await assignUserToBrands(user, userBrands, userAccounts);

      // Associate brands with retailers
      await associateBrandsWithRetailers(userBrands, retailers);

      // Create products for the brands
      const products = await createProductsForBrands(userBrands, userAccounts);

      // Create projects for the brands
      await createProjectsForBrands(userBrands, user, retailers);

      // Ensure user has credential sets
      await ensureUserCredentials(user, userAccounts);

      console.log(`✅ Completed setup for ${user.name}`);
    }

    console.log('\n🎯 Enhanced super user seeding completed successfully!');

  } catch (error) {
    console.error('❌ Enhanced super user seeding failed:', error);
    throw error;
  }
}

/**
 * Inserts any super users from the provided list that do not already exist in the database.
 *
 * @param superUsers - An array of super user objects to ensure are present in the database
 */
async function ensureSuperUsersExist(superUsers: any[]) {
  console.log('👥 Ensuring super users exist in database...');

  const existingUsers = await db.select().from(schema.users);
  const existingAuth0Ids = new Set(existingUsers.map(u => u.auth0Id));

  const missingUsers = superUsers.filter(su => !existingAuth0Ids.has(su.auth0_id));

  if (missingUsers.length > 0) {
    console.log(`🔧 Inserting ${missingUsers.length} missing super users...`);

    const superUserData = missingUsers.map(su => ({
      id: su.id,
      auth0Id: su.auth0_id,
      email: su.email,
      name: su.name,
      isVerified: su.is_verified,
      jobTitle: su.job_title,
      phone: su.phone,
      status: su.status as 'active' | 'inactive' | 'pending',
      language: su.language,
      timeZone: su.time_zone,
      isBetaUser: su.is_beta_user,
      betaKeyId: su.beta_key_id,
      createdAt: new Date(su.created_at),
      updatedAt: new Date(su.updated_at)
    }));

    await db.insert(schema.users).values(superUserData);
    console.log(`✅ Inserted ${missingUsers.length} super users`);
  }
}

/**
 * Creates three distinct accounts for a given user, each with specific attributes and roles.
 *
 * The accounts include one agency, one third-party, and one first-party account, with randomized seat counts and onboarding steps. The last account is marked as a trial, while the first two have active subscriptions and payment methods.
 *
 * @param user - The user object for whom the accounts are created
 * @param userIndex - The index of the user in the processing sequence
 * @returns An array of the created account records
 */
async function createAccountsForUser(user: any, userIndex: number) {
  console.log(`🏢 Creating 3 accounts for ${user.name}...`);

  const accountTemplates = [
    {
      name: `${user.name.split(' ')[0]}'s Premium Agency`,
      type: 'agency' as const,
      ownerId: user.id, // User will be owner of this account
    },
    {
      name: `${user.name.split(' ')[0]}'s Tech Solutions Inc`,
      type: '3p' as const,
      ownerId: user.id, // User will be admin of this account (set below)
    },
    {
      name: `${user.name.split(' ')[0]}'s Lifestyle Co`,
      type: '1p' as const,
      ownerId: user.id, // User will be member of this account (set below)
    }
  ];

  const accountsData = accountTemplates.map((template, index) => ({
    ...template,
    isTrial: index === 2, // Last account is trial
    seats: faker.number.int({ min: 5, max: 50 }),
    status: 'active' as const,
    stripeCustomerId: index < 2 ? `cus_${faker.string.alphanumeric(14)}` : null,
    validSubscription: index < 2,
    hasPaymentMethod: index < 2,
    onboardingStep: faker.number.int({ min: 3, max: 5 }),
  }));

  const accounts = await db.insert(schema.accounts).values(accountsData).returning();
  console.log(`✅ Created ${accounts.length} accounts for ${user.name}`);
  return accounts;
}

/**
 * Assigns a user to three accounts with the roles of owner, admin, and member.
 *
 * Each account receives a distinct role assignment for the user, establishing their permissions and status within those accounts.
 */
async function assignUserToAccounts(user: any, accounts: any[]) {
  console.log(`🔗 Assigning ${user.name} to accounts with specific roles...`);

  const roles = ['owner', 'admin', 'member'];

  const userAccountData = accounts.map((account, index) => ({
    userId: user.id,
    accountId: account.id,
    role: roles[index],
    status: 'active' as const
  }));

  await db.insert(schema.userAccounts).values(userAccountData);
  console.log(`✅ Assigned ${user.name} to ${accounts.length} accounts with roles: ${roles.join(', ')}`);
}

/**
 * Creates one brand for each provided account using predefined templates and associates them with the user.
 *
 * Each brand is assigned a unique name, description, color scheme, logo, and notes referencing the user and account. The number of products for each brand is randomized.
 *
 * @param accounts - The list of accounts to create brands for
 * @param user - The user to associate with the created brands
 * @returns An array of the created brand records
 */
async function createBrandsForAccounts(accounts: any[], user: any) {
  console.log(`🏷️ Creating 1 brand for each of ${accounts.length} accounts...`);

  const brandTemplates = [
    {
      name: `${user.name.split(' ')[0]}'s Premium Brand`,
      description: `Premium brand managed by ${user.name}`,
      colors: ['#10B981', '#059669', '#6EE7B7'],
      logoAssetUrl: 'https://images.unsplash.com/photo-*************-64674bd600d8?w=200',
    },
    {
      name: `${user.name.split(' ')[0]}'s Tech Solutions`,
      description: `Technology-focused brand by ${user.name}`,
      colors: ['#3B82F6', '#1E40AF', '#93C5FD'],
      logoAssetUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200',
    },
    {
      name: `${user.name.split(' ')[0]}'s Lifestyle Co`,
      description: `Lifestyle brand curated by ${user.name}`,
      colors: ['#F59E0B', '#D97706', '#FCD34D'],
      logoAssetUrl: 'https://images.unsplash.com/photo-*************-27b2c045efd7?w=200',
    }
  ];

  const brandsData = accounts.map((account, index) => ({
    ...brandTemplates[index],
    accountId: account.id,
    notes: `Brand for ${user.name}'s ${account.name}`,
    productCount: faker.number.int({ min: 5, max: 20 })
  }));

  const brands = await db.insert(schema.brands).values(brandsData).returning();
  console.log(`✅ Created ${brands.length} brands (1 per account)`);
  return brands;
}

/**
 * Assigns a user to each of their brands with roles corresponding to their account roles.
 *
 * Each brand receives a user assignment with the role (owner, admin, or member) matching the user's role in the associated account.
 */
async function assignUserToBrands(user: any, brands: any[], accounts: any[]) {
  console.log(`🔗 Assigning ${user.name} to brands with appropriate roles...`);

  // Map account roles to brand roles
  const accountRoleToBrandRole = {
    'owner': 'owner',
    'admin': 'admin',
    'member': 'member'
  };

  const roles = ['owner', 'admin', 'member']; // Same order as accounts

  const brandUserData = brands.map((brand, index) => ({
    brandId: brand.id,
    userId: user.id,
    role: roles[index]
  }));

  await db.insert(schema.brandUsers).values(brandUserData);
  console.log(`✅ Assigned ${user.name} to ${brands.length} brands with roles: ${roles.join(', ')}`);
}

/**
 * Creates associations between each brand and a random selection of 2 to 4 retailers.
 *
 * For every brand in the input array, randomly selects 2 to 4 retailers and inserts association records into the database.
 */
async function associateBrandsWithRetailers(brands: any[], retailers: any[]) {
  console.log(`🛒 Associating ${brands.length} brands with retailers...`);

  const brandRetailerData = [];

  for (const brand of brands) {
    // Each brand gets associated with 2-4 retailers
    const numRetailers = faker.number.int({ min: 2, max: Math.min(4, retailers.length) });
    const selectedRetailers = faker.helpers.arrayElements(retailers, numRetailers);

    for (const retailer of selectedRetailers) {
      brandRetailerData.push({
        brandId: brand.id,
        retailerId: retailer.id
      });
    }
  }

  if (brandRetailerData.length > 0) {
    await db.insert(schema.brandRetailers).values(brandRetailerData);
    console.log(`✅ Created ${brandRetailerData.length} brand-retailer associations`);
  }
}

/**
 * Generates and inserts a randomized set of products for each provided brand.
 *
 * For each brand, creates between 3 and 8 products with randomized attributes such as product ID, title, type, category, descriptions, specifications, highlights, UPC, images, and summaries. Inserts all generated products into the database and returns the created product records.
 *
 * @param brands - The list of brand objects for which products will be created
 * @param accounts - The list of account objects associated with the brands
 * @returns An array of the created product records
 */
async function createProductsForBrands(brands: any[], accounts: any[]) {
  console.log(`📦 Creating products for ${brands.length} brands...`);

  const productsData: any[] = [];

  brands.forEach((brand, brandIndex) => {
    // Create 3-8 products per brand
    const numProducts = faker.number.int({ min: 3, max: 8 });

    for (let i = 0; i < numProducts; i++) {
      productsData.push({
        productId: `${brand.name.replace(/\s+/g, '').toUpperCase()}_${faker.string.alphanumeric(6)}`,
        productTitle: `${brand.name} - ${faker.commerce.productName()}`,
        productType: faker.helpers.arrayElement(['physical', 'digital']),
        category: faker.commerce.department(),
        thumbnailUrl: faker.image.urlLoremFlickr({ category: 'product' }),
        shortDescription: faker.commerce.productDescription(),
        longDescription: faker.lorem.paragraphs(2),
        genAiDescription: faker.lorem.paragraph(),
        specifications: faker.helpers.arrayElements([
          'waterproof', 'wireless', 'rechargeable', 'portable', 'eco-friendly'
        ], { min: 1, max: 3 }),
        productHighlights: faker.helpers.arrayElements([
          'Best seller', 'Editor\'s choice', 'Customer favorite', 'New arrival'
        ], { min: 1, max: 2 }),
        classType: faker.helpers.arrayElement(['A', 'B', 'C']),
        upc: faker.string.numeric(12),
        images: Array.from({ length: faker.number.int({ min: 1, max: 4 }) }, () =>
          faker.image.urlLoremFlickr({ category: 'product' })
        ),
        customImages: [],
        accountId: brand.accountId, // Use the brand's account
        brandId: brand.id,
        pdpSummary: faker.lorem.sentence()
      });
    }
  });

  if (productsData.length > 0) {
    const products = await db.insert(schema.products).values(productsData).returning();
    console.log(`✅ Created ${products.length} products`);
    return products;
  }

  return [];
}

/**
 * Generates and inserts 1–3 projects for each brand, each with randomized campaign and ad group data.
 *
 * Each project is associated with a brand, a retailer, and the user as creator. Campaigns and ad groups are randomly generated for each project.
 *
 * @param brands - The list of brands to create projects for
 * @param user - The user who will be set as the creator of each project
 * @param retailers - The list of retailers to associate with projects
 * @returns The array of created project records
 */
async function createProjectsForBrands(brands: any[], user: any, retailers: any[]) {
  console.log(`📊 Creating projects for ${brands.length} brands...`);

  const projectsData = [];

  for (const brand of brands) {
    // Create 1-3 projects per brand
    const numProjects = faker.number.int({ min: 1, max: 3 });

    for (let i = 0; i < numProjects; i++) {
      projectsData.push({
        name: `${brand.name} - ${faker.company.buzzPhrase()}`,
        brandId: brand.id,
        retailerId: faker.helpers.arrayElement(retailers).id,
        createdBy: user.id,
        currentStep: faker.number.int({ min: 1, max: 5 }),
        wmCampaigns: (() => {
          const campaigns: Record<string, { name: string; adGroups: Record<string, string> }> = {};
          const campaignNames = ['awareness', 'consideration', 'conversion', 'retention'];
          const selectedCampaigns = faker.helpers.arrayElements(campaignNames, { min: 1, max: 2 });

          selectedCampaigns.forEach(campaignName => {
            const campaignId = faker.string.alphanumeric(10);
            const numAdGroups = faker.number.int({ min: 1, max: 3 });
            const adGroups: Record<string, string> = {};

            for (let j = 0; j < numAdGroups; j++) {
              const adGroupId = faker.string.alphanumeric(8);
              const adGroupName = faker.commerce.department();
              adGroups[adGroupId] = adGroupName;
            }

            campaigns[campaignId] = {
              name: `${faker.company.buzzPhrase()} (${campaignName})`,
              adGroups
            };
          });

          return campaigns;
        })()
      });
    }
  }

  if (projectsData.length > 0) {
    const projects = await db.insert(schema.projects).values(projectsData).returning();
    console.log(`✅ Created ${projects.length} projects`);
    return projects;
  }

  return [];
}

/**
 * Assigns the user to one or more existing credential sets if they do not already have any, enabling access to retailer APIs.
 *
 * If the user has no credential sets, randomly assigns them to up to two available credential sets in the database.
 */
async function ensureUserCredentials(user: any, accounts: any[]) {
  console.log(`🔐 Setting up credentials for ${user.name}...`);

  // Check if user already has credential sets
  const existingUserCreds = await db.select()
    .from(schema.userCredentialSets)
    .where(eq(schema.userCredentialSets.userId, user.id));

  if (existingUserCreds.length === 0) {
    // Get some existing credential sets or create one
    const credentialSets = await db.select().from(schema.credentialSets);

    if (credentialSets.length > 0) {
      // Assign user to 1-2 credential sets
      const numCredSets = Math.min(2, credentialSets.length);
      const selectedCredSets = faker.helpers.arrayElements(credentialSets, numCredSets);

      const userCredData = selectedCredSets.map(credSet => ({
        userId: user.id,
        credentialSetId: credSet.id
      }));

      await db.insert(schema.userCredentialSets).values(userCredData);
      console.log(`✅ Assigned ${user.name} to ${userCredData.length} credential sets`);
    }
  } else {
    console.log(`✅ ${user.name} already has ${existingUserCreds.length} credential sets`);
  }
}