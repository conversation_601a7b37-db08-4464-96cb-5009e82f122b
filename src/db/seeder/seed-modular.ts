import 'dotenv/config';
import { clearDatabase } from './seeders/core';
import { ensureSuperUsers, ensureAccounts, ensureAccountSettings, ensureUserSettings, ensurePermissions } from './seeders/base';
import {
  ensureBrands,
  ensureCategories,
  ensureKeywords,
  ensureCredentialSets,
  ensureRetailers,
  ensureRetailerSpecs,
  updateBrandsWithCredentials,
  ensureBrandGuidelines
} from './seeders/business';
import {
  ensureUserAccounts,
  ensureBrandCategories,
  ensureBrandUsers,
  ensureBrandRetailers,
  ensureUserCredentialSets,
  ensureCredentialSetRetailers
} from './seeders/relationships';
import {
  ensureAssets,
  ensureProducts,
  ensureRetailerProducts,
  ensureImageMetadata,
  ensureProjects
} from './seeders/content';
import {
  ensureGenerations,
  ensureCreatives,
  ensureGenerationVersions,
  ensureProofs,
  ensureReviewers,
  ensureExternalReviewers,
  ensureProofComments,
  ensureNotifications,
  ensureInvitations,
  ensureLeads
} from './seeders/workflow';
import { logger } from './seeders/utils';
import { ENV_CONFIG } from './seedConfig';

console.log('🌱 Starting modular database seeding...');

// Validate environment
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

// Configuration
const FORCE_CLEAR = ENV_CONFIG.FORCE_CLEAR;
const SKIP_CLEARING = ENV_CONFIG.SKIP_CLEARING;

console.log(`🔧 Configuration: FORCE_CLEAR=${FORCE_CLEAR}, SKIP_CLEARING=${SKIP_CLEARING}`);

/**
 * Orchestrates the complete seeding of the database with all required entities and their relationships in a modular, idempotent manner.
 *
 * Executes a multi-phase process to ensure that core, business, relationship, content, workflow, and auxiliary entities are present and properly interconnected, without creating duplicates. The process conditionally clears the database based on configuration and logs progress and summary information. Terminates the process on critical errors.
 */
async function seedDatabase() {
  try {
    logger.startPhase('Database Preparation');

    // Always clear database before seeding unless explicitly skipped
    if (!SKIP_CLEARING) {
      console.log('🧹 Clearing database before seeding...');
      await clearDatabase();
    }

    logger.endPhase();

    logger.startPhase('Base Entities');

    // Phase 1: Ensure super users exist and get all users
    const users = await ensureSuperUsers();

    // Phase 2: Core entities (idempotent)
    const accounts = await ensureAccounts(users);
    await ensureUserAccounts(users, accounts);
    await ensureAccountSettings(accounts);
    await ensureUserSettings(users);
    await ensurePermissions();

    logger.endPhase();

    logger.startPhase('Business Logic');

    // Phase 3: Business entities (idempotent)
    const categories = await ensureCategories(accounts);
    const keywords = await ensureKeywords(accounts);
    const brands = await ensureBrands(accounts);
    const credentialSets = await ensureCredentialSets(accounts, users);
    const retailers = await ensureRetailers();
    await ensureRetailerSpecs(retailers);

    // Update some brands with primaryCredentialId
    await updateBrandsWithCredentials(brands, credentialSets);

    logger.endPhase();

    logger.startPhase('Relationships');

    // Phase 4: Relationships (idempotent)
    await ensureBrandCategories(brands, categories);
    await ensureBrandUsers(brands, users);
    await ensureUserCredentialSets(users, credentialSets);
    await ensureCredentialSetRetailers(credentialSets, retailers);
    await ensureBrandRetailers(brands, retailers);

    logger.endPhase();

    logger.startPhase('Content & Assets');

    // Phase 5: Content & workflow (idempotent)
    const assets = await ensureAssets(accounts);
    const products = await ensureProducts(accounts, brands);
    await ensureRetailerProducts(retailers, products);
    await ensureImageMetadata(products);
    const projects = await ensureProjects(brands, users, retailers);

    logger.endPhase();

    logger.startPhase('Workflow Entities');

    const generations = await ensureGenerations(products, keywords, projects, brands);
    const creatives = await ensureCreatives(brands, users, generations, projects);

    // Phase 6: Review workflow (idempotent)
    const generationVersions = await ensureGenerationVersions(generations, users);
    const proofs = await ensureProofs(generationVersions);
    const reviewers = await ensureReviewers(proofs, users);
    const externalReviewers = await ensureExternalReviewers(accounts, users, retailers);
    await ensureProofComments(proofs, reviewers);

    logger.endPhase();

    logger.startPhase('Final Entities');

    // Phase 7: Final entities (idempotent)
    await ensureNotifications(users, accounts);
    await ensureInvitations(accounts, brands);
    await ensureBrandGuidelines(brands, users);
    await ensureLeads();

    logger.endPhase();

    // Calculate total records for summary
    const totalRecords = (
      users.length +
      accounts.length +
      categories.length +
      keywords.length +
      brands.length +
      credentialSets.length +
      retailers.length +
      assets.length +
      products.length +
      projects.length +
      generations.length +
      creatives.length +
      generationVersions.length +
      proofs.length +
      reviewers.length +
      externalReviewers.length
    );

    logger.logSummary(totalRecords);

  } catch (error) {
    logger.logError('Database seeding failed', error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  }
}

// Run the seeding
await seedDatabase();

console.log('✅ Modular database seeding completed successfully!');