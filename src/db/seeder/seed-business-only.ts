import 'dotenv/config';
import { 
  ensureBrands, 
  ensureCategories, 
  ensureKeywords, 
  ensureCredentialSets, 
  ensureRetailers,
  ensureRetailerSpecs,
  updateBrandsWithCredentials
} from './seeders/business';
import { ensureSuperUsers, ensureAccounts } from './seeders/base';
import { logger } from './seeders/utils';

console.log('🌱 Starting business entities seeding only...');

// Validate environment
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

try {
  logger.startPhase('Prerequisites');
  
  // Need users and accounts for business entities
  const users = await ensureSuperUsers();
  const accounts = await ensureAccounts(users);
  
  logger.endPhase();
  
  logger.startPhase('Business Entities');
  
  // Seed business entities
  const categories = await ensureCategories(accounts);
  const keywords = await ensureKeywords(accounts);
  const brands = await ensureBrands(accounts);
  const credentialSets = await ensureCredentialSets(accounts, users);
  const retailers = await ensureRetailers();
  await ensureRetailerSpecs(retailers);
  await updateBrandsWithCredentials(brands, credentialSets);
  
  logger.endPhase();
  
  const totalRecords = categories.length + keywords.length + brands.length + credentialSets.length + retailers.length + 2;
  logger.logSummary(totalRecords);

} catch (error) {
  logger.logError('Business entities seeding failed', error instanceof Error ? error : new Error(String(error)));
  process.exit(1);
}

console.log('✅ Business entities seeding completed successfully!');