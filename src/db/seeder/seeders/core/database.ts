import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import * as schema from '../../../schema';
import * as relations from '../../../relations';

const sql = neon(process.env.DATABASE_URL!);
export const db = drizzle(sql, { schema: { ...schema, ...relations } });

/**
 * Removes all records from every database table in a dependency-aware order, including users, to fully reset the database.
 *
 * This function ensures that all tables are emptied without violating foreign key constraints, handling circular dependencies as needed to maintain relational integrity. Intended for use before reseeding the database.
 */
export async function clearDatabase() {
  console.log('🧹 Clearing existing data...');

  // Clear in reverse dependency order to respect foreign key constraints
  await db.delete(schema.proofComments);
  await db.delete(schema.reviewers);
  await db.delete(schema.externalReviewers);
  await db.delete(schema.proofs);

  // Handle circular dependency between generations and generationVersions
  // First, set generationVersionId in generations table to null
  await db.update(schema.generations).set({ generationVersionId: null });

  // Now we can safely delete in order
  await db.delete(schema.creatives);
  await db.delete(schema.generationVersions);
  await db.delete(schema.generations);

  await db.delete(schema.projects);
  await db.delete(schema.retailerProducts);
  await db.delete(schema.brandRetailers);
  await db.delete(schema.retailerSpecs);
  await db.delete(schema.credentialSetRetailers);
  await db.delete(schema.imageMetadata);
  await db.delete(schema.products);
  await db.delete(schema.brandGuidelines);
  await db.delete(schema.brandCategories);
  await db.delete(schema.brandUsers);
  await db.delete(schema.userCredentialSets);
  await db.delete(schema.credentialSets);
  await db.delete(schema.brands);
  await db.delete(schema.categories);
  await db.delete(schema.keywords);
  await db.delete(schema.retailers);
  await db.delete(schema.assets);
  await db.delete(schema.notifications);
  await db.delete(schema.invitations);
  await db.delete(schema.userAccounts);
  await db.delete(schema.userSettings);
  await db.delete(schema.accountSettings);
  await db.delete(schema.accounts);
  await db.delete(schema.leads);
  await db.delete(schema.permissions);
  // Also clear users table now since we'll re-seed them
  await db.delete(schema.users);

  console.log('✅ Database cleared successfully');
}