import { faker } from '@faker-js/faker';

// Set faker seed for consistent results
faker.seed(42);

// Predefined logo assets for brands
const BRAND_LOGO_ASSETS = [
  'https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_10_26%20AM.png',
  'https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_00_52%20AM.png',
  'https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_07_39%20AM.png',
  'https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_02_09%20AM.png'
];

// Brand-Product mapping with specific assets
const BRAND_PRODUCT_MAPPING = [
  {
    brandLogo: 'https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_10_26%20AM.png',
    productImages: [
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_1_product_1.png',
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_1_product_2.png',
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_1_product_3.png'
    ]
  },
  {
    brandLogo: 'https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_00_52%20AM.png',
    productImages: [
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_2_product_1.png',
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_2_product_2.png',
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_2_product_3.png'
    ]
  },
  {
    brandLogo: 'https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_07_39%20AM.png',
    productImages: [
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_3_product_1.png',
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_3_product_2.png',
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_3_product_3.png'
    ]
  },
  {
    brandLogo: 'https://storage.googleapis.com/adfury_demo_assets/ChatGPT%20Image%20Jul%208%2C%202025%2C%2009_02_09%20AM.png',
    productImages: [
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_4_product_1.png',
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_4_product_2.png',
      'https://storage.googleapis.com/adfury_demo_assets/seed_images/brand_4_product_3.png'
    ]
  }
];

// Default images for fallback
const DEFAULT_BRAND_IMAGE = 'https://storage.googleapis.com/adfury_demo_assets/seed_images/default_brand.png';
const DEFAULT_PRODUCT_IMAGE = 'https://storage.googleapis.com/adfury_demo_assets/seed_images/default_product.png';

/**
 * Generates an array of three random RGB color strings.
 *
 * @returns An array containing three RGB color values in string format.
 */
export function generateBrandColors() {
  return [
    faker.color.rgb(),
    faker.color.rgb(),
    faker.color.rgb()
  ];
}

// Global counter for brand generation
let brandCounter = 0;

/**
 * Generates a realistic mock brand object with a unique name, description, color palette, logo URL, notes, and product count.
 *
 * The brand's name, description, and colors are selected from predefined templates and palettes. For the first four brands generated, a specific logo asset is assigned; subsequent brands use a default logo image.
 *
 * @returns An object containing brand name, description, color palette, logo asset URL, notes, and a randomized product count.
 */
export function generateRealisticBrand() {
  const brandTypes = [
    { prefix: 'Eco', suffix: 'Essentials', desc: 'Sustainable living products for conscious consumers' },
    { prefix: 'Tech', suffix: 'Pro', desc: 'Professional-grade electronics and accessories' },
    { prefix: 'Home', suffix: 'Living', desc: 'Curated home decor and furniture collection' },
    { prefix: 'Fit', suffix: 'Athletics', desc: 'Performance sportswear and fitness equipment' },
    { prefix: 'Urban', suffix: 'Style', desc: 'Modern urban fashion and accessories' },
    { prefix: 'Pure', suffix: 'Wellness', desc: 'Natural health and wellness products' },
    { prefix: 'Smart', suffix: 'Solutions', desc: 'Innovative technology for modern living' },
    { prefix: 'Craft', suffix: 'Co', desc: 'Handmade artisanal products and gifts' }
  ];
  
  const brandType = faker.helpers.arrayElement(brandTypes);
  const colorPalettes = [
    ['#10B981', '#059669', '#6EE7B7'], // Green
    ['#3B82F6', '#1E40AF', '#93C5FD'], // Blue
    ['#F59E0B', '#D97706', '#FCD34D'], // Yellow
    ['#EF4444', '#DC2626', '#FCA5A5'], // Red
    ['#8B5CF6', '#7C3AED', '#C4B5FD'], // Purple
    ['#06B6D4', '#0891B2', '#67E8F9'], // Cyan
    ['#F97316', '#EA580C', '#FDBA74'], // Orange
    ['#84CC16', '#65A30D', '#BEF264']  // Lime
  ];
  
  // Use specific brand logo for first 4 brands, then use default
  let logoAssetUrl;
  if (brandCounter < BRAND_PRODUCT_MAPPING.length) {
    logoAssetUrl = BRAND_PRODUCT_MAPPING[brandCounter].brandLogo;
  } else {
    logoAssetUrl = DEFAULT_BRAND_IMAGE;
  }
  
  brandCounter++;
  
  return {
    name: `${brandType.prefix}${faker.helpers.arrayElement(['Life', 'Flow', 'Style', 'Wave', 'Core', 'Pro', 'Max', 'Plus'])} ${brandType.suffix}`,
    description: brandType.desc,
    colors: faker.helpers.arrayElement(colorPalettes),
    logoAssetUrl,
    notes: generateBrandNotes(),
    productCount: faker.number.int({ min: 5, max: 150 })
  };
}

/**
 * Returns a randomly selected brand note template with realistic marketing messaging and target audience focus.
 *
 * The note provides guidance on brand positioning, visual style, and intended demographic.
 * @returns A brand note string describing messaging, style, and target audience.
 */
export function generateBrandNotes() {
  const noteTemplates = [
    'Focus on sustainability messaging, earth tones, lifestyle imagery. Target eco-conscious millennials and Gen-Z.',
    'Modern, sleek design. Highlight premium quality and innovation. Appeal to tech professionals and early adopters.',
    'Cozy, inviting imagery. Family-focused messaging. Emphasize comfort and style for modern homes.',
    'High-energy, motivational messaging. Target fitness enthusiasts and athletes. Emphasize performance and results.',
    'Urban, trendy aesthetic. Bold colors and dynamic layouts. Appeal to young professionals and creatives.',
    'Natural, organic feel. Soft colors and wellness-focused messaging. Target health-conscious consumers.',
    'Clean, minimalist design. Focus on innovation and efficiency. Appeal to tech-savvy professionals.',
    'Handcrafted, artisanal feel. Warm colors and personal touch. Target gift-buyers and craft enthusiasts.'
  ];
  
  return faker.helpers.arrayElement(noteTemplates);
}

// Global counter for product generation
let productCounter = 0;

/**
 * Generates a realistic mock product object with category-specific details, randomized specifications, highlights, and images.
 *
 * The first 12 products are assigned brand-specific images; subsequent products use a default product image.
 * Product fields include a unique ID, title, category, descriptions, specifications, highlights, UPC, class type, thumbnail URL, and image array.
 *
 * @returns An object representing a mock product with realistic attributes and images.
 */
export function generateRealisticProduct() {
  const productCategories: {
    [key: string]: {
      titles: string[];
      descriptions: string[];
      specs: string[];
    };
  } = {
    'Home & Kitchen': {
      titles: ['Bamboo Fiber Dinnerware Set', 'Organic Cotton Reusable Bags', 'Sustainable Cutting Board', 'Eco-Friendly Storage Containers'],
      descriptions: ['Complete eco-friendly dining set', 'Durable reusable shopping bags', 'Sustainable bamboo cutting surface', 'Airtight food storage solution'],
      specs: ['biodegradable', 'dishwasher-safe', 'microwave-safe', 'antimicrobial']
    },
    'Electronics': {
      titles: ['Wireless Earbuds with ANC', 'Smart Home Hub', 'Portable Power Bank', 'Bluetooth Speaker'],
      descriptions: ['Premium wireless earbuds with noise cancellation', 'Central control for smart devices', 'High-capacity portable charging', 'Waterproof wireless speaker'],
      specs: ['wireless', 'noise-cancelling', 'waterproof', 'long-battery']
    },
    'Sports & Fitness': {
      titles: ['Yoga Mat Premium', 'Resistance Bands Set', 'Smart Fitness Tracker', 'Protein Shaker Bottle'],
      descriptions: ['Non-slip premium yoga mat', 'Complete resistance training set', 'Advanced fitness monitoring', 'Leak-proof protein mixer'],
      specs: ['non-slip', 'portable', 'durable', 'easy-clean']
    }
  };
  
  const category = faker.helpers.arrayElement(Object.keys(productCategories));
  const categoryData = productCategories[category];
  const title = faker.helpers.arrayElement(categoryData.titles);
  
  // Determine which brand this product belongs to and get specific image
  let thumbnailUrl;
  let imageArray;
  
  // First 12 products (3 per brand) use specific brand images
  if (productCounter < 12) {
    const brandIndex = Math.floor(productCounter / 3);
    const productIndex = productCounter % 3;
    
    if (brandIndex < BRAND_PRODUCT_MAPPING.length) {
      const brandMapping = BRAND_PRODUCT_MAPPING[brandIndex];
      thumbnailUrl = brandMapping.productImages[productIndex];
      imageArray = [brandMapping.productImages[productIndex]];
    } else {
      thumbnailUrl = DEFAULT_PRODUCT_IMAGE;
      imageArray = [DEFAULT_PRODUCT_IMAGE];
    }
  } else {
    // Use default product image for additional products
    thumbnailUrl = DEFAULT_PRODUCT_IMAGE;
    imageArray = [DEFAULT_PRODUCT_IMAGE];
  }
  
  productCounter++;
  
  return {
    productId: faker.string.alphanumeric(8).toUpperCase(),
    productTitle: `${title} - ${faker.helpers.arrayElement(['Premium', 'Pro', 'Elite', 'Standard', 'Deluxe'])}`,
    category,
    shortDescription: faker.helpers.arrayElement(categoryData.descriptions),
    longDescription: `${faker.helpers.arrayElement(categoryData.descriptions)}. ${faker.lorem.sentences(2)}`,
    specifications: faker.helpers.arrayElements(categoryData.specs, { min: 2, max: 4 }),
    productHighlights: faker.helpers.arrayElements(['Eco-friendly', 'Premium quality', 'Durable', 'Easy to use', 'Great value'], { min: 3, max: 4 }),
    upc: faker.string.numeric(12),
    classType: faker.helpers.arrayElement(['A', 'B', 'C']),
    thumbnailUrl,
    imageArray
  };
}

/**
 * Generates a realistic mock project object with a campaign structure, current step, and status.
 *
 * @returns An object containing the project name, campaign details with ad groups, current workflow step, and status.
 */
export function generateRealisticProject() {
  const projectTemplates = [
    { name: 'Holiday Gift Guide', campaign: 'HOLIDAY_GIFTS', theme: 'seasonal' },
    { name: 'Product Launch', campaign: 'PRODUCT_LAUNCH', theme: 'launch' },
    { name: 'Brand Awareness', campaign: 'BRAND_AWARENESS', theme: 'branding' },
    { name: 'Performance Campaign', campaign: 'PERFORMANCE', theme: 'conversion' },
    { name: 'Retargeting Campaign', campaign: 'RETARGETING', theme: 'retention' }
  ];
  
  const template = faker.helpers.arrayElement(projectTemplates);
  const year = new Date().getFullYear();
  
  return {
    name: `${template.name} ${year}`,
    wmCampaigns: {
      [`${template.campaign}_${year}`]: {
        name: template.name,
        adGroups: {
          'PRIMARY_AG': 'Primary Ad Group',
          'SECONDARY_AG': 'Secondary Ad Group',
          'RETARGETING_AG': 'Retargeting Ad Group'
        }
      }
    },
    currentStep: faker.number.int({ min: 1, max: 5 }),
    status: faker.helpers.arrayElement(['in_progress', 'in_review', 'published', 'inactive', 'archived'])
  };
}

/**
 * Generates a random user settings object with realistic preferences.
 *
 * The returned object includes randomized values for email notifications, language, timezone, theme, and date format.
 * @returns An object representing user settings.
 */
export function generateUserSettings() {
  return {
    emailNotifications: faker.datatype.boolean(),
    language: faker.helpers.arrayElement(['en-US', 'es-ES', 'fr-FR', 'de-DE']),
    timezone: faker.location.timeZone(),
    theme: faker.helpers.arrayElement(['light', 'dark', 'auto']),
    dateFormat: faker.helpers.arrayElement(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'])
  };
}

/**
 * Generates randomized account settings including notification preferences, integrations, and usage limits.
 *
 * @returns An object containing notification settings, enabled integrations, and account limits.
 */
export function generateAccountSettings() {
  return {
    notifications: {
      email: faker.datatype.boolean(),
      slack: faker.datatype.boolean(),
      webhook: faker.datatype.boolean() ? faker.internet.url() : null
    },
    integrations: {
      stripe: faker.datatype.boolean(),
      auth0: faker.datatype.boolean(),
      analytics: faker.datatype.boolean()
    },
    limits: {
      maxUsers: faker.number.int({ min: 5, max: 100 }),
      maxBrands: faker.number.int({ min: 1, max: 20 }),
      maxProjects: faker.number.int({ min: 10, max: 500 })
    }
  };
}

/**
 * Generates a notification data object with randomized type, action, entity ID, and metadata for use in seeding or testing.
 *
 * @returns An object containing a notification type, action, unique entity ID, and metadata with a user name and recent timestamp.
 */
export function generateNotificationData() {
  const types = ['system', 'project', 'brand', 'user', 'billing'];
  return {
    type: faker.helpers.arrayElement(types),
    action: faker.helpers.arrayElement(['created', 'updated', 'deleted', 'approved', 'rejected']),
    entityId: faker.string.uuid(),
    metadata: {
      userName: faker.person.fullName(),
      timestamp: faker.date.recent().toISOString()
    }
  };
}

/**
 * Generates a realistic notification object with a title, message, data payload, read status, and optional read timestamp.
 *
 * @returns An object representing a notification, including title, message, data (type, action, entity ID, metadata), isRead flag, and readAt date if read.
 */
export function generateRealisticNotification() {
  const notificationTypes = [
    {
      title: 'Creative Approved ✅',
      message: `${faker.person.fullName()} approved your creative`,
      type: 'creative_approval'
    },
    {
      title: 'Review Requested 📝',
      message: 'New creative review requested',
      type: 'review_request'
    },
    {
      title: 'Project Updated 📋',
      message: 'Project moved to next phase',
      type: 'project_update'
    },
    {
      title: 'Brand Guidelines Updated 📖',
      message: 'New brand guidelines uploaded',
      type: 'brand_update'
    },
    {
      title: 'Campaign Performance 📈',
      message: 'Campaign performing above target',
      type: 'performance_update'
    }
  ];
  
  const notification = faker.helpers.arrayElement(notificationTypes);
  
  return {
    title: notification.title,
    message: notification.message,
    data: {
      type: notification.type,
      action: faker.helpers.arrayElement(['created', 'updated', 'approved', 'requested']),
      entityId: faker.string.uuid(),
      metadata: {
        userName: faker.person.fullName(),
        timestamp: faker.date.recent().toISOString()
      }
    },
    isRead: faker.datatype.boolean(0.3), // 30% chance of being read
    readAt: faker.datatype.boolean(0.3) ? faker.date.recent() : null
  };
}

/**
 * Resets the internal brand and product counters to zero for deterministic data generation.
 */
export function resetCounters() {
  brandCounter = 0;
  productCounter = 0;
}

// Export brand logo assets for use in other modules
export { BRAND_LOGO_ASSETS };

export { faker };