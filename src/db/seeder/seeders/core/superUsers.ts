import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * Loads and parses super user data from the `superUsers.json` file in the seeder directory.
 *
 * @returns An array of super user objects, or an empty array if the file is missing or contains invalid JSON.
 */
export function loadSuperUsers(): any[] {
  try {
    const superUsersPath = join(process.cwd(), 'src/db/seeder/superUsers.json');
    const data = readFileSync(superUsersPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.log('⚠️  Could not load superUsers.json, using empty array');
    return [];
  }
}