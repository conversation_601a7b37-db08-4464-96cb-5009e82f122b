import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Creates user-account relationship records by assigning each super user as the owner of an account and adding five demo users to each account with randomized roles and statuses.
 *
 * For each account, the function establishes an "owner" relationship with a super user and assigns five demo users as either "admin" or "member" with varied statuses. All relationships are inserted into the database in bulk.
 *
 * @param users - List of user objects, with the first four treated as super users and the rest as demo users
 * @param accounts - List of account objects to associate with users
 * @returns The total number of user-account relationship records created
 */
export async function ensureUserAccounts(users: any[], accounts: any[]): Promise<number> {
  console.log('🔗 Seeding user accounts...');

  const userAccountData: any[] = [];
  const superUsers = users.slice(0, 4); // First 4 are super users
  const demoUsers = users.slice(4); // Remaining are demo users

  // Ensure each super user is the owner of their account
  for (const account of accounts) {
    userAccountData.push({
      userId: account.ownerId,
      accountId: account.id,
      role: 'owner',
      status: 'active'
    });
  }

  // Add 5 demo users to each account with varied roles
  for (const account of accounts) {
    const availableDemoUsers = demoUsers.filter((user: any) =>
      !userAccountData.some((ua: any) => ua.userId === user.id && ua.accountId === account.id)
    );

    const selectedUsers = faker.helpers.arrayElements(availableDemoUsers, 5);

    for (const user of selectedUsers) {
      userAccountData.push({
        userId: user.id,
        accountId: account.id,
        role: faker.helpers.arrayElement(['admin', 'member']), // Demo users are admin or member
        status: faker.helpers.arrayElement(['active', 'pending', 'inactive'])
      });
    }
  }

  await db.insert(schema.userAccounts).values(userAccountData);
  console.log(`✅ Created ${userAccountData.length} user account relationships`);
  console.log(`   - ${accounts.length} super user owners`);
  console.log(`   - ${userAccountData.length - accounts.length} demo user members`);
  return userAccountData.length;
}