import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Associates each user with one or two randomly selected credential sets and inserts these relationships into the database.
 *
 * @param users - The list of users to be linked to credential sets
 * @param credentialSets - The available credential sets for association
 */
export async function ensureUserCredentialSets(users: any[], credentialSets: any[]) {
  console.log('🔐 Seeding user credential sets...');

  const userCredentialData = [];

  // Each user gets access to 1-2 credential sets
  for (const user of users) {
    const numCredSets = faker.number.int({ min: 1, max: 2 });
    const selectedCredSets = faker.helpers.arrayElements(credentialSets, numCredSets);

    for (const credSet of selectedCredSets) {
      userCredentialData.push({
        userId: user.id,
        credentialSetId: credSet.id
      });
    }
  }

  await db.insert(schema.userCredentialSets).values(userCredentialData);
  console.log(`✅ Created ${userCredentialData.length} user credential set relationships`);
}

/**
 * Links each credential set to 2 to 5 randomly selected retailers by creating relationship records in the database.
 *
 * For each credential set, selects a random subset of retailers and inserts credential set-retailer associations.
 */
export async function ensureCredentialSetRetailers(credentialSets: any[], retailers: any[]) {
  console.log('🛒 Seeding credential set retailers...');

  const credRetailerData = [];

  // Each credential set gets access to 2-5 retailers
  for (const credSet of credentialSets) {
    const numRetailers = faker.number.int({ min: 2, max: 5 });
    const selectedRetailers = faker.helpers.arrayElements(retailers, numRetailers);

    for (const retailer of selectedRetailers) {
      credRetailerData.push({
        credentialSetId: credSet.id,
        retailerId: retailer.id
      });
    }
  }

  await db.insert(schema.credentialSetRetailers).values(credRetailerData);
  console.log(`✅ Created ${credRetailerData.length} credential set retailer relationships`);
}