import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Creates brand-category relationships by associating each brand with 1 to 3 randomly selected categories.
 *
 * Inserts new associations into the database without checking for existing duplicates.
 */
export async function ensureBrandCategories(brands: any[], categories: any[]) {
  console.log('🔗 Seeding brand categories...');

  const brandCategoryData = [];

  // Each brand gets 1-3 categories
  for (const brand of brands) {
    const numCategories = faker.number.int({ min: 1, max: 3 });
    const selectedCategories = faker.helpers.arrayElements(categories, numCategories);

    for (const category of selectedCategories) {
      brandCategoryData.push({
        brandId: brand.id,
        categoryId: category.id
      });
    }
  }

  await db.insert(schema.brandCategories).values(brandCategoryData);
  console.log(`✅ Created ${brandCategoryData.length} brand category relationships`);
}

/**
 * Seeds the database with brand-user relationships, ensuring super users are assigned to at least three brands per account with distinct roles, and each brand has up to five demo users with varied roles.
 *
 * Super users are assigned "owner", "admin", and "member" roles across their brands, while demo users are randomly assigned "admin" or "member" roles to brands they are not already associated with.
 */
export async function ensureBrandUsers(brands: any[], users: any[]) {
  console.log('👥 Seeding brand users...');

  const brandUserData: any[] = [];
  const superUsers = users.slice(0, 4); // First 4 are super users
  const demoUsers = users.slice(4); // Remaining are demo users

  // Group brands by account for super user assignment
  const brandsByAccount = brands.reduce((acc: any, brand) => {
    if (!acc[brand.accountId]) acc[brand.accountId] = [];
    acc[brand.accountId].push(brand);
    return acc;
  }, {});

  // Assign super users to their account's brands with specific roles
  for (let i = 0; i < superUsers.length; i++) {
    const superUser = superUsers[i];
    const userBrands = brandsByAccount[Object.keys(brandsByAccount)[i]] || [];
    
    // Ensure each super user has at least 3 brands with different roles
    const roles = ['owner', 'admin', 'member'];
    for (let j = 0; j < Math.min(userBrands.length, 3); j++) {
      brandUserData.push({
        brandId: userBrands[j].id,
        userId: superUser.id,
        role: roles[j]
      });
    }
    
    // If there are more brands, assign random roles
    for (let j = 3; j < userBrands.length; j++) {
      brandUserData.push({
        brandId: userBrands[j].id,
        userId: superUser.id,
        role: faker.helpers.arrayElement(['admin', 'member'])
      });
    }
  }

  // Add 5 demo users to each brand with varied roles
  for (const brand of brands) {
    const availableDemoUsers = demoUsers.filter(user => 
      !brandUserData.some(bu => bu.userId === user.id && bu.brandId === brand.id)
    );
    
    const selectedUsers = faker.helpers.arrayElements(availableDemoUsers, Math.min(5, availableDemoUsers.length));
    
    for (const user of selectedUsers) {
      brandUserData.push({
        brandId: brand.id,
        userId: user.id,
        role: faker.helpers.arrayElement(['admin', 'member']) // Demo users are admin or member
      });
    }
  }

  await db.insert(schema.brandUsers).values(brandUserData);
  console.log(`✅ Created ${brandUserData.length} brand user relationships`);
  
  // Log super user brand assignments
  for (const superUser of superUsers) {
    const userBrandCount = brandUserData.filter(bu => bu.userId === superUser.id).length;
    console.log(`   - ${superUser.name}: ${userBrandCount} brands`);
  }
}

/**
 * Creates and inserts brand-retailer relationships by associating each brand with 2 to 6 randomly selected retailers.
 *
 * @param brands - List of brand objects to associate with retailers
 * @param retailers - List of retailer objects to be linked with brands
 */
export async function ensureBrandRetailers(brands: any[], retailers: any[]) {
  console.log('🛒 Seeding brand retailers...');

  const brandRetailerData = [];

  // Each brand is available on 2-6 retailers
  for (const brand of brands) {
    const numRetailers = faker.number.int({ min: 2, max: 6 });
    const selectedRetailers = faker.helpers.arrayElements(retailers, numRetailers);

    for (const retailer of selectedRetailers) {
      brandRetailerData.push({
        brandId: brand.id,
        retailerId: retailer.id
      });
    }
  }

  await db.insert(schema.brandRetailers).values(brandRetailerData);
  console.log(`✅ Created ${brandRetailerData.length} brand retailer relationships`);
}