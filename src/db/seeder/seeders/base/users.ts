import { db } from '../core';
import { faker } from '../core';
import { loadSuperUsers } from '../core';
import * as schema from '../../../schema';

/**
 * Ensures all configured super users are present in the database and adds randomly generated demo users if needed to reach a minimum of 25 users.
 *
 * Inserts any missing super users based on configuration and supplements with fake users to guarantee a baseline user count. Returns the complete list of users after all insertions.
 *
 * @returns An array of all user records currently in the database
 */
export async function ensureSuperUsers() {
  console.log('👥 Ensuring super users exist...');

  const superUsers = loadSuperUsers();
  const existingUsers = await db.select().from(schema.users);
  const existingAuth0Ids = new Set(existingUsers.map(u => u.auth0Id));

  console.log(`Found ${existingUsers.length} existing users`);

  // Insert missing super users
  const missingUsers = superUsers.filter((su: any) => !existingAuth0Ids.has(su.auth0_id));

  if (missingUsers.length > 0) {
    console.log(`🔧 Inserting ${missingUsers.length} missing super users...`);

    const superUserData = missingUsers.map((su: any) => ({
      id: su.id,
      auth0Id: su.auth0_id,
      email: su.email,
      name: su.name,
      isVerified: su.is_verified,
      jobTitle: su.job_title,
      phone: su.phone,
      status: su.status as 'active' | 'inactive' | 'pending',
      language: su.language,
      timeZone: su.time_zone,
      isBetaUser: su.is_beta_user,
      betaKeyId: su.beta_key_id,
      createdAt: new Date(su.created_at),
      updatedAt: new Date(su.updated_at)
    }));

    await db.insert(schema.users).values(superUserData);
    console.log(`✅ Inserted ${missingUsers.length} super users`);
  }

  // Add demo users for accounts and brands (need at least 21 for 4 accounts * 5 users + 1 buffer)
  const allUsers = await db.select().from(schema.users);
  if (allUsers.length < 25) {
    const additionalUsers = Array.from({ length: 25 - allUsers.length }, () => ({
      auth0Id: `auth0|${faker.string.alphanumeric(24)}`,
      email: faker.internet.email(),
      name: faker.person.fullName(),
      isVerified: faker.datatype.boolean({ probability: 0.8 }),
      jobTitle: faker.person.jobTitle(),
      phone: faker.datatype.boolean({ probability: 0.7 }) ? faker.phone.number() : null,
      status: faker.helpers.arrayElement(['active', 'inactive', 'pending'] as const),
      language: faker.helpers.arrayElement(['en-US', 'es-ES', 'fr-FR']),
      timeZone: faker.location.timeZone(),
      isBetaUser: faker.datatype.boolean({ probability: 0.1 }), // Lower chance for demo users
      betaKeyId: faker.datatype.boolean({ probability: 0.1 }) ? faker.string.uuid() : null,
    }));

    await db.insert(schema.users).values(additionalUsers);
    console.log(`✅ Added ${additionalUsers.length} demo users for accounts and brands`);
  }

  const finalUsers = await db.select().from(schema.users);
  console.log(`👥 Total users available: ${finalUsers.length}`);
  return finalUsers;
}