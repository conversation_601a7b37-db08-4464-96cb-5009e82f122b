import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Ensures that each of the first four users (super users) has an account, creating new accounts with randomized attributes if none exist.
 *
 * If no accounts are present in the database, generates one account per super user with varied but realistic data and inserts them. Returns all existing and newly created accounts.
 *
 * @param users - List of user objects, with the first four considered super users
 * @returns Array of all existing and newly created account records
 */
export async function ensureAccounts(users: any[]) {
  console.log('🏢 Ensuring accounts exist...');

  const existingAccounts = await db.select().from(schema.accounts);
  console.log(`Found ${existingAccounts.length} existing accounts`);

  if (existingAccounts.length === 0) {
    // Get first 4 users (super users from config)
    const superUsers = users.slice(0, 4);
    
    const accountData = superUsers.map(superUser => ({
      name: `${superUser.name}'s ${faker.company.buzzNoun()} ${faker.company.buzzNoun()}`,
      type: faker.helpers.arrayElement(['agency', '3p', '1p'] as const),
      isTrial: faker.datatype.boolean({ probability: 0.3 }),
      seats: faker.number.int({ min: 10, max: 100 }), // Super users get more seats
      ownerId: superUser.id, // Each super user owns their account
      status: 'active' as const, // Super user accounts are always active
      stripeCustomerId: faker.datatype.boolean({ probability: 0.8 }) ? `cus_${faker.string.alphanumeric(14)}` : null,
      validSubscription: faker.datatype.boolean({ probability: 0.9 }), // Higher chance for super users
      hasPaymentMethod: faker.datatype.boolean({ probability: 0.8 }), // Higher chance for super users
      onboardingStep: faker.number.int({ min: 3, max: 5 }), // Super users are further along
    }));

    const newAccounts = await db.insert(schema.accounts).values(accountData).returning();
    console.log(`✅ Created ${newAccounts.length} accounts (one per super user)`);
    return [...existingAccounts, ...newAccounts];
  }

  return existingAccounts;
}