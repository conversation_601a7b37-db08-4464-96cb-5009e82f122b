import { db, faker } from '../core';
import { eq } from 'drizzle-orm';
import * as schema from '../../../schema';
import { SeedingParams } from '../utils';

/**
 * Creates a specified number of demo users for a given account, including user records, user-account relationships, and user settings.
 *
 * If `params.dryRun` is true, simulates the creation process without modifying the database.
 *
 * @param accountId - The ID of the account to associate the new users with
 * @param count - The number of demo users to create
 * @param params - Optional seeding parameters, including dry-run mode
 * @returns An array of the created user records
 */
export async function createUsersForAccount(accountId: string, count: number, params: SeedingParams = {}) {
  console.log(`👥 Creating ${count} demo users for account ${accountId}...`);

  if (params.dryRun) {
    console.log(`   [DRY RUN] Would create ${count} demo users for account ${accountId}`);
    return [];
  }

  // Create demo users
  const userData = Array.from({ length: count }, () => ({
    auth0Id: `auth0|${faker.string.alphanumeric(24)}`,
    email: faker.internet.email(),
    name: faker.person.fullName(),
    isVerified: faker.datatype.boolean({ probability: 0.8 }),
    jobTitle: faker.person.jobTitle(),
    phone: faker.datatype.boolean({ probability: 0.7 }) ? faker.phone.number() : null,
    status: faker.helpers.arrayElement(['active', 'inactive', 'pending'] as const),
    language: faker.helpers.arrayElement(['en-US', 'es-ES', 'fr-FR']),
    timeZone: faker.location.timeZone(),
    isBetaUser: faker.datatype.boolean({ probability: 0.1 }),
    betaKeyId: faker.datatype.boolean({ probability: 0.1 }) ? faker.string.uuid() : null,
  }));

  const users = await db.insert(schema.users).values(userData).returning();
  console.log(`✅ Created ${users.length} demo users`);

  // Create user-account relationships
  const userAccountData = users.map(user => ({
    userId: user.id,
    accountId,
    role: faker.helpers.arrayElement(['admin', 'member'] as const),
    status: faker.helpers.arrayElement(['active', 'pending', 'inactive'] as const)
  }));

  await db.insert(schema.userAccounts).values(userAccountData);
  console.log(`✅ Created ${userAccountData.length} user-account relationships`);

  // Create user settings
  const userSettingsData = users.map(user => ({
    userId: user.id,
    settings: {
      emailNotifications: faker.datatype.boolean(),
      language: faker.helpers.arrayElement(['en-US', 'es-ES', 'fr-FR', 'de-DE']),
      timezone: faker.location.timeZone(),
      theme: faker.helpers.arrayElement(['light', 'dark', 'auto']),
      dateFormat: faker.helpers.arrayElement(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'])
    }
  }));

  await db.insert(schema.userSettings).values(userSettingsData);
  console.log(`✅ Created ${userSettingsData.length} user settings`);

  return users;
}

/**
 * Creates demo users for a specified brand and establishes brand-user relationships.
 *
 * Retrieves the brand's associated account, generates the requested number of users for that account, and links each user to the brand with a randomly assigned role. Supports dry-run mode to simulate actions without modifying the database.
 *
 * @param brandId - The unique identifier of the brand for which users are created
 * @param count - The number of demo users to generate
 * @returns An array of the created user records
 * @throws If the specified brand does not exist
 */
export async function createUsersForBrand(brandId: string, count: number, params: SeedingParams = {}) {
  console.log(`👥 Creating ${count} demo users for brand ${brandId}...`);

  if (params.dryRun) {
    console.log(`   [DRY RUN] Would create ${count} demo users for brand ${brandId}`);
    return [];
  }

  // Get the brand to find its account
  const brand = await db.select().from(schema.brands).where(
    eq(schema.brands.id, brandId)
  ).limit(1);

  if (brand.length === 0) {
    throw new Error(`Brand ${brandId} not found`);
  }

  // Create users for the brand's account first
  const users = await createUsersForAccount(brand[0].accountId!, count, params);

  // Create brand-user relationships
  const brandUserData = users.map(user => ({
    brandId,
    userId: user.id,
    role: faker.helpers.arrayElement(['admin', 'member'] as const)
  }));

  await db.insert(schema.brandUsers).values(brandUserData);
  console.log(`✅ Created ${brandUserData.length} brand-user relationships`);

  return users;
}

/**
 * Creates demo users for the account owned by a specified super user and assigns each user to one or more brands under that account.
 *
 * If no account is found for the given super user ID, an error is thrown. Each created user is linked to 1–3 randomly selected brands associated with the account. Supports dry-run mode to simulate actions without modifying the database.
 *
 * @param superUserId - The ID of the super user whose account will receive the new users
 * @param count - The number of demo users to create
 * @param params - Optional seeding parameters, including dry-run mode
 * @returns An array of the created user records
 */
export async function createUsersForSuperUser(superUserId: string, count: number, params: SeedingParams = {}) {
  console.log(`👥 Creating ${count} demo users for super user ${superUserId}...`);

  if (params.dryRun) {
    console.log(`   [DRY RUN] Would create ${count} demo users for super user ${superUserId}`);
    return [];
  }

  // Find the account owned by this super user
  const account = await db.select().from(schema.accounts).where(
    eq(schema.accounts.ownerId, superUserId)
  ).limit(1);

  if (account.length === 0) {
    throw new Error(`No account found for super user ${superUserId}`);
  }

  // Create users for the account
  const users = await createUsersForAccount(account[0].id, count, params);

  // Get all brands for this account
  const brands = await db.select().from(schema.brands).where(
    eq(schema.brands.accountId, account[0].id)
  );

  if (brands.length > 0) {
    // Distribute users across brands
    const brandUserData = [];
    
    for (const user of users) {
      // Each user gets access to 1-3 random brands
      const numBrands = faker.number.int({ min: 1, max: Math.min(3, brands.length) });
      const selectedBrands = faker.helpers.arrayElements(brands, numBrands);
      
      for (const brand of selectedBrands) {
        brandUserData.push({
          brandId: brand.id,
          userId: user.id,
          role: faker.helpers.arrayElement(['admin', 'member'] as const)
        });
      }
    }

    await db.insert(schema.brandUsers).values(brandUserData);
    console.log(`✅ Created ${brandUserData.length} brand-user relationships across ${brands.length} brands`);
  }

  return users;
}