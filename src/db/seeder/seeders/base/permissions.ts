import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Seeds the database with a set of randomly generated permission records for user permission management.
 *
 * @returns The number of permission records created
 */
export async function ensurePermissions(): Promise<number> {
  console.log('🔐 Seeding permissions...');

  const permissionsData = Array.from({ length: 5 }, () => ({
    permissionData: {
      can_create_brand: faker.datatype.boolean({ probability: 0.8 }),
      can_create_creative: faker.datatype.boolean({ probability: 0.9 }),
      can_edit_brand: faker.datatype.boolean({ probability: 0.7 }),
      can_delete_brand: faker.datatype.boolean({ probability: 0.3 }),
      can_manage_users: faker.datatype.boolean({ probability: 0.4 }),
      can_view_analytics: faker.datatype.boolean({ probability: 0.8 }),
      can_export_data: faker.datatype.boolean({ probability: 0.6 }),
      can_manage_billing: faker.datatype.boolean({ probability: 0.2 })
    }
  }));

  await db.insert(schema.permissions).values(permissionsData);
  console.log(`✅ Created ${permissionsData.length} permission sets`);
  return permissionsData.length;
}