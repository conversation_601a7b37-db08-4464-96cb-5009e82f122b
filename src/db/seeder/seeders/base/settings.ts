import { db, generateAccountSettings, generateUserSettings } from '../core';
import * as schema from '../../../schema';

/**
 * Creates and inserts settings records for each account in the provided list.
 *
 * @param accounts - Array of account objects for which to generate and store settings
 * @returns The total number of account settings records inserted
 */
export async function ensureAccountSettings(accounts: any[]): Promise<number> {
  console.log('⚙️ Seeding account settings...');

  const settingsData = accounts.map(account => ({
    accountId: account.id,
    settings: generateAccountSettings()
  }));

  await db.insert(schema.accountSettings).values(settingsData);
  console.log(`✅ Created ${settingsData.length} account settings`);
  return settingsData.length;
}

/**
 * Creates and inserts user settings for each user in the provided array.
 *
 * Generates random settings for each user and stores them in the user settings table.
 *
 * @param users - Array of user objects for which to generate and insert settings
 * @returns The number of user settings records inserted
 */
export async function ensureUserSettings(users: any[]): Promise<number> {
  console.log('👤 Seeding user settings...');

  const settingsData = users.map(user => ({
    userId: user.id,
    settings: generateUserSettings()
  }));

  await db.insert(schema.userSettings).values(settingsData);
  console.log(`✅ Created ${settingsData.length} user settings`);
  return settingsData.length;
}