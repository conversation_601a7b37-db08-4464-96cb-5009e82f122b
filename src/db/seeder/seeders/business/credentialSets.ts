import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Seeds the database with credential sets, ensuring each account has at least one and adding additional sets for variety.
 *
 * Generates credential sets with randomized properties and assigns them to accounts and users. Returns the array of created credential set records.
 *
 * @returns The array of created credential set records.
 */
export async function ensureCredentialSets(accounts: any[], users: any[]) {
  console.log('🔐 Seeding credential sets...');

  const credentialData = [];
  
  // First, create at least one credential set per account
  for (const account of accounts) {
    credentialData.push({
      accountId: account.id,
      name: `${faker.company.name()} Credentials`,
      type: faker.helpers.arrayElement(['wm_display', 'wm_supplier', 'wm_marketplace', 'wm_sponsored', 'default']),
      credentials: {
        advertiserId: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(12) : null,
        nonce: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(24) : null,
        state: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(16) : null,
        partnerId: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(10) : null,
        accessToken: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(32) : null,
        refreshToken: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(32) : null,
        expiresAt: faker.datatype.boolean({ probability: 0.5 }) ? faker.date.future().toISOString() : null
      },
      adminId: faker.helpers.arrayElement(users).id,
      isShared: faker.datatype.boolean({ probability: 0.7 }),
      isActive: faker.datatype.boolean({ probability: 0.8 })
    });
  }
  
  // Add additional credential sets for variety (2 more)
  for (let i = 0; i < 2; i++) {
    credentialData.push({
      accountId: faker.helpers.arrayElement(accounts).id,
      name: `${faker.company.name()} Credentials`,
      type: faker.helpers.arrayElement(['wm_display', 'wm_supplier', 'wm_marketplace', 'wm_sponsored', 'default']),
      credentials: {
        advertiserId: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(12) : null,
        nonce: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(24) : null,
        state: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(16) : null,
        partnerId: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(10) : null,
        accessToken: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(32) : null,
        refreshToken: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(32) : null,
        expiresAt: faker.datatype.boolean({ probability: 0.5 }) ? faker.date.future().toISOString() : null
      },
      adminId: faker.helpers.arrayElement(users).id,
      isShared: faker.datatype.boolean({ probability: 0.7 }),
      isActive: faker.datatype.boolean({ probability: 0.8 })
    });
  }

  const credentialSets = await db.insert(schema.credentialSets).values(credentialData).returning();
  console.log(`✅ Created ${credentialSets.length} credential sets`);
  return credentialSets;
}