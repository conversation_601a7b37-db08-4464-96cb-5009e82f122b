import { db, faker, generateBrandColors, generateRealisticBrand } from '../core';
import { eq } from 'drizzle-orm';
import * as schema from '../../../schema';

/**
 * Creates and inserts four randomized brand records for each provided account if they do not already exist.
 *
 * Each brand is initialized with realistic data and a null `primaryCredentialId`. Returns the array of inserted brand records.
 *
 * @param accounts - The list of account objects to associate brands with
 * @returns The array of created brand records
 */
export async function ensureBrands(accounts: any[]) {
  console.log('🏷️ Seeding brands...');

  const brandData = [];
  
  // Create 4 brands per account (12 for super users + extra for demo users)
  for (const account of accounts) {
    for (let i = 0; i < 4; i++) {
      const realisticBrand = generateRealisticBrand();
      brandData.push({
        accountId: account.id,
        name: realisticBrand.name,
        description: realisticBrand.description,
        colors: realisticBrand.colors,
        logoAssetUrl: realisticBrand.logoAssetUrl,
        primaryCredentialId: null, // Will be set after creating credential sets
        notes: realisticBrand.notes,
        productCount: realisticBrand.productCount
      });
    }
  }

  const brands = await db.insert(schema.brands).values(brandData).returning();
  console.log(`✅ Created ${brands.length} brands (${brands.length / accounts.length} per account)`);
  return brands;
}

/**
 * Assigns a primary credential set to each brand from available credential sets belonging to the same account.
 *
 * For each brand, selects a credential set associated with the brand's account and updates the brand's `primaryCredentialId` field in the database.
 */
export async function updateBrandsWithCredentials(brands: any[], credentialSets: any[]) {
  console.log('🔗 Linking brands with credential sets...');

  // Update ALL brands with a primary credential set
  const brandsToUpdate = brands;
  let updatedCount = 0;

  for (const brand of brandsToUpdate) {
    // Find credential sets from the same account
    const accountCredSets = credentialSets.filter(cs => cs.accountId === brand.accountId);

    if (accountCredSets.length > 0) {
      const selectedCredSet = faker.helpers.arrayElement(accountCredSets);
      await db.update(schema.brands)
        .set({ primaryCredentialId: selectedCredSet.id })
        .where(eq(schema.brands.id, brand.id));
      updatedCount++;
    }
  }

  console.log(`✅ Updated ${updatedCount} brands with primary credential sets`);
}

/**
 * Creates one or two randomized brand guideline records for each brand and inserts them into the database.
 *
 * Each guideline includes a random label, file name, asset URL, status, and an uploader selected from the provided users.
 */
export async function ensureBrandGuidelines(brands: any[], users: any[]) {
  console.log('📖 Seeding brand guidelines...');

  const guidelineData = [];

  // Each brand gets 1-2 guideline files
  for (const brand of brands) {
    const numGuidelines = faker.number.int({ min: 1, max: 2 });

    for (let i = 0; i < numGuidelines; i++) {
      guidelineData.push({
        brandId: brand.id,
        label: faker.helpers.arrayElement([
          'Brand Guidelines', 'Logo Usage', 'Color Palette', 'Typography Guide'
        ]),
        fileName: faker.system.fileName(),
        assetUrl: faker.internet.url(),
        status: faker.helpers.arrayElement(['active', 'archived'] as const),
        uploadedBy: faker.helpers.arrayElement(users).id
      });
    }
  }

  await db.insert(schema.brandGuidelines).values(guidelineData);
  console.log(`✅ Created ${guidelineData.length} brand guidelines`);
}