import { db, faker, generateBrandColors } from '../core';
import { eq } from 'drizzle-orm';
import * as schema from '../../../schema';
import { SeedingParams } from '../utils';

/**
 * Creates a specified number of brand records for a given account, generating randomized brand data.
 *
 * If `params.dryRun` is true, simulates the creation without modifying the database and returns an empty array.
 *
 * @param accountId - The ID of the account to associate the new brands with
 * @param count - The number of brands to create
 * @returns An array of the created brand records, or an empty array if in dry run mode
 */
export async function createBrandsForAccount(accountId: string, count: number, params: SeedingParams = {}) {
  console.log(`🏷️ Creating ${count} brands for account ${accountId}...`);

  if (params.dryRun) {
    console.log(`   [DRY RUN] Would create ${count} brands for account ${accountId}`);
    return [];
  }

  const brandData = Array.from({ length: count }, (_, index) => ({
    accountId,
    name: `${faker.company.name()} ${faker.company.buzzNoun()}`,
    description: faker.company.catchPhrase(),
    colors: generateBrandColors(),
    logoAssetUrl: faker.image.urlLoremFlickr({ category: 'business' }),
    primaryCredentialId: null,
    notes: faker.lorem.sentence(),
    productCount: faker.number.int({ min: 5, max: 150 })
  }));

  const brands = await db.insert(schema.brands).values(brandData).returning();
  console.log(`✅ Created ${brands.length} brands for account`);
  return brands;
}

/**
 * Creates a specified total number of brand records, distributing them evenly across all super user accounts.
 *
 * If the total number of brands does not divide evenly, the last account receives the remainder. Throws an error if no accounts exist. Supports dry run mode, which simulates creation without modifying the database.
 *
 * @param totalBrands - The total number of brands to create across all super user accounts
 * @returns An array of the created brand records, or an empty array if in dry run mode
 */
export async function createBrandsForSuperUsers(totalBrands: number, params: SeedingParams = {}) {
  console.log(`🏷️ Creating ${totalBrands} brands distributed across super user accounts...`);

  if (params.dryRun) {
    console.log(`   [DRY RUN] Would create ${totalBrands} brands across super user accounts`);
    return [];
  }

  // Get all accounts (super user accounts)
  const accounts = await db.select().from(schema.accounts);
  
  if (accounts.length === 0) {
    throw new Error('No accounts found. Please run base seeding first.');
  }

  const brandsPerAccount = Math.ceil(totalBrands / accounts.length);
  const allBrands = [];

  for (let i = 0; i < accounts.length; i++) {
    const account = accounts[i];
    const brandsForThisAccount = i === accounts.length - 1 
      ? totalBrands - (i * brandsPerAccount) // Last account gets remaining brands
      : brandsPerAccount;

    if (brandsForThisAccount > 0) {
      const brands = await createBrandsForAccount(account.id, brandsForThisAccount, params);
      allBrands.push(...brands);
    }
  }

  console.log(`✅ Created ${allBrands.length} brands across ${accounts.length} accounts`);
  return allBrands;
}

/**
 * Creates a specified number of brand records for the account owned by the given super user.
 *
 * Throws an error if no account is found for the provided super user ID.
 *
 * @param superUserId - The unique identifier of the super user
 * @param count - The number of brands to create
 * @returns An array of the created brand records, or an empty array if in dry run mode
 */
export async function createBrandsForSuperUser(superUserId: string, count: number, params: SeedingParams = {}) {
  console.log(`🏷️ Creating ${count} brands for super user ${superUserId}...`);

  if (params.dryRun) {
    console.log(`   [DRY RUN] Would create ${count} brands for super user ${superUserId}`);
    return [];
  }

  // Find the account owned by this super user
  const account = await db.select().from(schema.accounts).where(
    eq(schema.accounts.ownerId, superUserId)
  ).limit(1);

  if (account.length === 0) {
    throw new Error(`No account found for super user ${superUserId}`);
  }

  return await createBrandsForAccount(account[0].id, count, params);
}