import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Seeds the database with a predefined list of product categories, each associated with a randomly selected account.
 *
 * @param accounts - Array of account objects used to assign an account to each category
 * @returns An array of the created category records
 */
export async function ensureCategories(accounts: any[]) {
  console.log('📂 Seeding categories...');

  const categoryNames = [
    'Electronics', 'Fashion', 'Home & Garden', 'Sports & Outdoors',
    'Beauty & Personal Care', 'Books & Media', 'Automotive', 'Food & Beverage',
    'Health & Wellness', 'Toys & Games', 'Pet Supplies', 'Office Supplies'
  ];

  const categoryData = categoryNames.map(name => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    name
  }));

  const categories = await db.insert(schema.categories).values(categoryData).returning();
  console.log(`✅ Created ${categories.length} categories`);
  return categories;
}