import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Inserts a single retailer record for Walmart into the database and returns the created record.
 *
 * @returns An array containing the created Walmart retailer record.
 */
export async function ensureRetailers() {
  console.log('🛒 Seeding retailers...');

  const retailerData = [
    {
      name: 'Walmart',
      url: 'https://walmart.com',
      slug: 'walmart',
      logoUrl: faker.image.urlLoremFlickr({ category: 'business' })
    }
  ];

  const retailers = await db.insert(schema.retailers).values(retailerData).returning();
  console.log(`✅ Created ${retailers.length} retailers (Walmart only)`);
  return retailers;
}

/**
 * Creates and inserts multiple retailer specification records for each retailer in the provided list.
 *
 * For each retailer, generates between two and three specifications with random asset URLs and statuses, then inserts them into the database.
 *
 * @param retailers - The list of retailer objects for which specifications will be generated
 */
export async function ensureRetailerSpecs(retailers: any[]) {
  console.log('📋 Seeding retailer specs...');

  const retailerSpecsData = [];
  
  for (const retailer of retailers) {
    // Create 2-3 specs per retailer
    const numSpecs = faker.number.int({ min: 2, max: 3 });
    
    for (let i = 0; i < numSpecs; i++) {
      retailerSpecsData.push({
        assetUrl: faker.internet.url(),
        status: faker.helpers.arrayElement(['active', 'inactive', 'pending']),
        retailerId: retailer.id
      });
    }
  }

  await db.insert(schema.retailerSpecs).values(retailerSpecsData);
  console.log(`✅ Created ${retailerSpecsData.length} retailer specs`);
}