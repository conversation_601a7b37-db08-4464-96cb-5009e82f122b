import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Generates and inserts 20 predefined keyword records into the database, each associated with a randomly selected account from the provided list.
 *
 * Each keyword includes randomized inclusion and exclusion metadata, such as tone, word, and consideration fields.
 *
 * @param accounts - The list of account objects to associate keywords with
 * @returns The array of inserted keyword records
 */
export async function ensureKeywords(accounts: any[]) {
  console.log('🔍 Seeding keywords...');

  const keywordTerms = [
    'summer sale', 'new arrival', 'best seller', 'limited edition', 'premium quality',
    'eco friendly', 'organic', 'handmade', 'vintage', 'modern design',
    'affordable', 'luxury', 'trending', 'seasonal', 'exclusive',
    'fast shipping', 'free returns', 'customer favorite', 'top rated', 'bestselling'
  ];

  const keywordData = keywordTerms.map(term => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    keyword: term,
    include: {
      tone: faker.helpers.arrayElement(['casual', 'professional', 'friendly', 'urgent']),
      word: faker.lorem.word(),
      consideration: faker.lorem.sentence()
    },
    exclude: {
      tone: faker.helpers.arrayElement(['aggressive', 'boring', 'confusing']),
      word: faker.lorem.word(),
      consideration: faker.lorem.sentence()
    }
  }));

  const keywords = await db.insert(schema.keywords).values(keywordData).returning();
  console.log(`✅ Created ${keywords.length} keywords`);
  return keywords;
}