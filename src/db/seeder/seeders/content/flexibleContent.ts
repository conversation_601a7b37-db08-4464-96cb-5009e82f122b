import { db, faker } from '../core';
import { eq, inArray } from 'drizzle-orm';
import * as schema from '../../../schema';
import { SeedingParams } from '../utils';

/**
 * Generates and inserts a specified number of products for given brand IDs, assigning each product to the appropriate account based on brand associations.
 *
 * Throws an error if any brand ID lacks a corresponding account mapping. Supports a dry-run mode that simulates product creation without modifying the database.
 *
 * @param brandIds - Array of brand IDs for which products will be created
 * @param count - Number of products to generate
 * @param params - Optional seeding parameters, including dry-run mode
 * @returns An array of the created product records
 */
export async function createProductsForBrands(brandIds: string[], count: number, params: SeedingParams = {}) {
  console.log(`📦 Creating ${count} products for ${brandIds.length} brands...`);

  if (params.dryRun) {
    console.log(`   [DRY RUN] Would create ${count} products for ${brandIds.length} brands`);
    return [];
  }

  const productData = Array.from({ length: count }, () => {
    const selectedBrand = faker.helpers.arrayElement(brandIds);
    return {
      productId: faker.string.alphanumeric(10),
      productTitle: faker.commerce.productName(),
      productType: faker.helpers.arrayElement(['physical', 'digital', 'service'] as const),
      category: faker.commerce.department(),
      thumbnailUrl: faker.image.urlLoremFlickr({ category: 'product' }),
      shortDescription: faker.commerce.productDescription(),
      longDescription: faker.lorem.paragraphs(2),
      genAiDescription: faker.lorem.paragraph(),
      specifications: faker.helpers.arrayElements([
        'waterproof', 'wireless', 'rechargeable', 'portable', 'eco-friendly'
      ], { min: 1, max: 3 }),
      productHighlights: faker.helpers.arrayElements([
        'Best seller', 'Editor\'s choice', 'Customer favorite', 'New arrival'
      ], { min: 1, max: 2 }),
      classType: faker.helpers.arrayElement(['A', 'B', 'C'] as const),
      upc: faker.string.numeric(12),
      images: Array.from({ length: faker.number.int({ min: 1, max: 4 }) }, () =>
        faker.image.urlLoremFlickr({ category: 'product' })
      ),
      customImages: [],
      accountId: null, // Will be set based on brand
      brandId: selectedBrand,
      pdpSummary: faker.lorem.sentence()
    };
  });

  // Get brand account information
  const brands = await db.select().from(schema.brands).where(
    inArray(schema.brands.id, brandIds)
  );

  const brandAccountMap = brands.reduce((acc, brand) => {
    acc[brand.id] = brand.accountId!;
    return acc;
  }, {} as Record<string, string>);

  // Validate that all brand IDs have corresponding account mappings
  const missingBrands = brandIds.filter(brandId => !(brandId in brandAccountMap));
  if (missingBrands.length > 0) {
    throw new Error(`Cannot create products: Missing account mappings for brand IDs: ${missingBrands.join(', ')}`);
  }

  // Set account IDs based on brand with validation
  const finalProductData = productData.map(product => {
    const accountId = brandAccountMap[product.brandId];
    if (!accountId) {
      throw new Error(`No account ID found for brand ${product.brandId}`);
    }
    return {
      ...product,
      accountId
    };
  });

  const products = await db.insert(schema.products).values(finalProductData).returning();
  console.log(`✅ Created ${products.length} products`);

  return products;
}

/**
 * Creates a specified number of projects for the given brand IDs, assigning each project to random users and retailers.
 *
 * Throws an error if no users or retailers exist in the database. Supports dry-run mode to simulate project creation without database changes.
 *
 * @param brandIds - Array of brand IDs to associate with the new projects
 * @param count - Number of projects to create
 * @returns An array of the created project records
 */
export async function createProjectsForBrands(brandIds: string[], count: number, params: SeedingParams = {}) {
  console.log(`📋 Creating ${count} projects for ${brandIds.length} brands...`);

  if (params.dryRun) {
    console.log(`   [DRY RUN] Would create ${count} projects for ${brandIds.length} brands`);
    return [];
  }

  // Get users and retailers for project creation
  const users = await db.select().from(schema.users);
  const retailers = await db.select().from(schema.retailers);

  if (users.length === 0 || retailers.length === 0) {
    throw new Error('Users and retailers must exist before creating projects');
  }

  const projectData = Array.from({ length: count }, () => {
    const selectedBrand = faker.helpers.arrayElement(brandIds);
    return {
      brandId: selectedBrand,
      name: faker.company.buzzPhrase(),
      wmCampaigns: {
        [faker.string.alphanumeric(10)]: {
          name: faker.company.catchPhrase(),
          adGroups: {
            [faker.string.alphanumeric(8)]: faker.commerce.department(),
            [faker.string.alphanumeric(8)]: faker.commerce.department()
          }
        }
      },
      retailerId: faker.helpers.arrayElement(retailers).id,
      createdBy: faker.helpers.arrayElement(users).id,
      currentStep: faker.number.int({ min: 0, max: 5 }),
      status: faker.helpers.arrayElement(['in_progress', 'in_review', 'published', 'inactive', 'archived'] as const),
    };
  });

  const projects = await db.insert(schema.projects).values(projectData).returning();
  console.log(`✅ Created ${projects.length} projects`);

  return projects;
}

/**
 * Generates and inserts a set of products, projects, and assets associated with the account and brands of a specified super user.
 *
 * Throws an error if the super user does not have an associated account or brands. Supports dry-run mode to simulate actions without modifying the database.
 *
 * @param superUserId - The ID of the super user for whom to create content
 * @returns An object containing arrays of the created products, projects, and assets
 */
export async function createContentForSuperUser(superUserId: string, params: SeedingParams = {}) {
  console.log(`🎨 Creating content ecosystem for super user ${superUserId}...`);

  if (params.dryRun) {
    console.log(`   [DRY RUN] Would create content ecosystem for super user ${superUserId}`);
    return { products: [], projects: [], assets: [] };
  }

  // Find the account and brands for this super user
  const account = await db.select().from(schema.accounts).where(
    eq(schema.accounts.ownerId, superUserId)
  ).limit(1);

  if (account.length === 0) {
    throw new Error(`No account found for super user ${superUserId}`);
  }

  const brands = await db.select().from(schema.brands).where(
    eq(schema.brands.accountId, account[0].id)
  );

  if (brands.length === 0) {
    throw new Error(`No brands found for super user ${superUserId}`);
  }

  const brandIds = brands.map(b => b.id);

  // Create content with user-specified quantities or defaults
  const productCount = params.productCount || 10;
  const projectCount = params.projectCount || 5;

  const products = await createProductsForBrands(brandIds, productCount, params);
  const projects = await createProjectsForBrands(brandIds, projectCount, params);

  // Create assets for the account
  const assetData = Array.from({ length: 10 }, () => ({
    accountId: account[0].id,
    url: faker.image.url()
  }));

  const assets = await db.insert(schema.assets).values(assetData).returning();
  console.log(`✅ Created ${assets.length} assets`);

  return { products, projects, assets };
}