import { db, faker, generateRealisticProject } from '../core';
import * as schema from '../../../schema';

/**
 * Populates the database with 15 new projects using randomized, realistic data linked to provided brands, users, and retailers.
 *
 * Each created project includes campaign details, status, and timestamps within recent ranges.
 *
 * @returns An array of the newly created project records.
 */
export async function ensureProjects(brands: any[], users: any[], retailers: any[]) {
  console.log('📋 Seeding projects...');

  const projectData = Array.from({ length: 15 }, () => {
    const realisticProject = generateRealisticProject();
    const now = new Date();
    const daysAgo = faker.number.int({ min: 1, max: 30 });
    const hoursAgo = faker.number.int({ min: 1, max: 24 });
    
    return {
      brandId: faker.helpers.arrayElement(brands).id,
      name: realisticProject.name,
      wmCampaigns: realisticProject.wmCampaigns,
      retailerId: faker.helpers.arrayElement(retailers).id,
      createdBy: faker.helpers.arrayElement(users).id,
      currentStep: realisticProject.currentStep,
      status: realisticProject.status as any,
      createdAt: new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000),
      updatedAt: new Date(now.getTime() - hoursAgo * 60 * 60 * 1000)
    };
  });

  const projects = await db.insert(schema.projects).values(projectData).returning();
  console.log(`✅ Created ${projects.length} projects`);
  return projects;
}