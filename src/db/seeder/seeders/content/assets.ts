import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Creates 25 asset records with random image URLs, each linked to a randomly selected account from the provided list.
 *
 * @param accounts - Array of account objects to associate with the new assets
 * @returns An array of the inserted asset records
 */
export async function ensureAssets(accounts: any[]) {
  console.log('🖼️ Seeding assets...');

  const assetData = Array.from({ length: 25 }, () => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    url: faker.image.url()
  }));

  const assets = await db.insert(schema.assets).values(assetData).returning();
  console.log(`✅ Created ${assets.length} assets`);
  return assets;
}