import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Generates and inserts randomized image metadata records for each product in the provided list.
 *
 * For each product, creates between 1 and 4 image metadata entries with randomized image URLs, descriptive text, and classification, then inserts all records into the database.
 *
 * @param products - The list of products for which to generate and associate image metadata
 */
export async function ensureImageMetadata(products: any[]) {
  console.log('🖼️ Seeding image metadata...');

  const imageData = [];

  // Each product gets 1-4 images
  for (const product of products) {
    const numImages = faker.number.int({ min: 1, max: 4 });

    for (let i = 0; i < numImages; i++) {
      imageData.push({
        productId: product.productId,
        imageUrl: faker.image.urlLoremFlickr({ category: 'product' }),
        imageText: faker.lorem.sentence(),
        classification: faker.helpers.arrayElement(['product', 'lifestyle', 'other'] as const)
      });
    }
  }

  await db.insert(schema.imageMetadata).values(imageData);
  console.log(`✅ Created ${imageData.length} image metadata records`);
}