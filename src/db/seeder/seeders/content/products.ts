import { db, faker, generateRealisticProduct, resetCounters } from '../core';
import * as schema from '../../../schema';

/**
 * Inserts 30 products into the database, ensuring each brand in the provided list has at least one associated product.
 *
 * Each product is generated with realistic, randomized attributes and linked to a brand and its corresponding account. After guaranteeing one product per brand, the remaining products are assigned to random brands from the list.
 *
 * @param accounts - List of account objects used to associate with products
 * @param brands - List of brand objects to ensure product coverage and association
 * @returns An array of the created product records
 */
export async function ensureProducts(accounts: any[], brands: any[]) {
  console.log('📦 Seeding products...');
  
  // Reset counters for consistent brand-product mapping
  resetCounters();

  const productData = [];
  const totalProducts = 30;
  
  // First, ensure every brand gets at least one product
  console.log(`📊 Ensuring all ${brands.length} brands have at least one product...`);
  
  for (let i = 0; i < brands.length; i++) {
    const brand = brands[i];
    const realisticProduct = generateRealisticProduct();
    
    productData.push({
      productId: realisticProduct.productId,
      productTitle: realisticProduct.productTitle,
      productType: 'physical', // Most products are physical
      category: realisticProduct.category,
      thumbnailUrl: realisticProduct.thumbnailUrl,
      shortDescription: realisticProduct.shortDescription,
      longDescription: realisticProduct.longDescription,
      genAiDescription: `${realisticProduct.shortDescription} Perfect for modern consumers who value quality and functionality.`,
      specifications: realisticProduct.specifications,
      productHighlights: realisticProduct.productHighlights,
      classType: realisticProduct.classType,
      upc: realisticProduct.upc,
      images: realisticProduct.imageArray,
      customImages: [],
      accountId: brand.accountId, // Use the brand's account to maintain consistency
      brandId: brand.id,
      pdpSummary: realisticProduct.shortDescription
    });
  }
  
  // Then create remaining products with random brand assignment
  const remainingProducts = totalProducts - brands.length;
  console.log(`📊 Creating ${remainingProducts} additional products with random brand assignment...`);
  
  for (let i = 0; i < remainingProducts; i++) {
    const realisticProduct = generateRealisticProduct();
    const selectedBrand = faker.helpers.arrayElement(brands);
    
    productData.push({
      productId: realisticProduct.productId,
      productTitle: realisticProduct.productTitle,
      productType: 'physical', // Most products are physical
      category: realisticProduct.category,
      thumbnailUrl: realisticProduct.thumbnailUrl,
      shortDescription: realisticProduct.shortDescription,
      longDescription: realisticProduct.longDescription,
      genAiDescription: `${realisticProduct.shortDescription} Perfect for modern consumers who value quality and functionality.`,
      specifications: realisticProduct.specifications,
      productHighlights: realisticProduct.productHighlights,
      classType: realisticProduct.classType,
      upc: realisticProduct.upc,
      images: realisticProduct.imageArray,
      customImages: [],
      accountId: selectedBrand.accountId, // Use the brand's account to maintain consistency
      brandId: selectedBrand.id,
      pdpSummary: realisticProduct.shortDescription
    });
  }

  const products = await db.insert(schema.products).values(productData).returning();
  console.log(`✅ Created ${products.length} products (${brands.length} brands guaranteed, ${remainingProducts} additional)`);
  return products;
}

/**
 * Creates retailer-product relationships by linking each product to 2 to 4 randomly selected retailers.
 *
 * For each product in the provided list, associates it with a random subset of retailers and inserts these relationships into the database.
 */
export async function ensureRetailerProducts(retailers: any[], products: any[]) {
  console.log('🛍️ Seeding retailer products...');

  const retailerProductData = [];

  // Each product is available on 2-4 retailers
  for (const product of products) {
    const numRetailers = faker.number.int({ min: 2, max: 4 });
    const selectedRetailers = faker.helpers.arrayElements(retailers, numRetailers);

    for (const retailer of selectedRetailers) {
      retailerProductData.push({
        retailerId: retailer.id,
        productId: product.productId
      });
    }
  }

  await db.insert(schema.retailerProducts).values(retailerProductData);
  console.log(`✅ Created ${retailerProductData.length} retailer product relationships`);
}