import { db, faker, generateNotificationData, generateRealisticNotification } from '../core';
import * as schema from '../../../schema';

/**
 * Creates and inserts a proof record for each provided generation version with randomized titles, descriptions, ad unit IDs, and reviewer lists.
 *
 * @param generationVersions - The generation version objects for which proofs should be created
 * @returns An array of the created proof records
 */
export async function ensureProofs(generationVersions: any[]) {
  console.log('📋 Seeding proofs...');

  if (!Array.isArray(generationVersions) || generationVersions.length === 0) {
    console.log('⚠️ No generation versions provided, skipping proof creation');
    return [];
  }

  const proofData = generationVersions.map(version => ({
    generationVersionId: version.id,
    title: faker.company.buzzPhrase(),
    emailDescription: faker.lorem.paragraph(),
    adUnitIds: faker.helpers.arrayElements([
      faker.string.alphanumeric(8),
      faker.string.alphanumeric(8),
      faker.string.alphanumeric(8)
    ], { min: 1, max: 3 }),
    reviewers: {
      internal: Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => ({
        id: faker.string.uuid(),
        name: faker.person.fullName(),
        email: faker.internet.email()
      })),
      external: Array.from({ length: faker.number.int({ min: 0, max: 2 }) }, () => ({
        id: faker.string.uuid(),
        name: faker.person.fullName(),
        email: faker.internet.email()
      }))
    }
  }));

  const proofs = await db.insert(schema.proofs).values(proofData).returning();
  console.log(`✅ Created ${Array.isArray(proofs) ? proofs.length : 'unknown'} proofs`);
  return Array.isArray(proofs) ? proofs : [];
}

/**
 * Creates 1 to 3 reviewer records for each proof, assigning random users as reviewers and requesters with varied statuses.
 *
 * @param proofs - The proofs to which reviewers will be assigned
 * @param users - The users from which reviewers and requesters are randomly selected
 * @returns An array of the created reviewer records
 */
export async function ensureReviewers(proofs: any[], users: any[]) {
  console.log('👁️ Seeding reviewers...');

  const reviewerData = [];

  // Each proof gets 1-3 reviewers
  for (const proof of proofs) {
    const numReviewers = faker.number.int({ min: 1, max: 3 });

    for (let i = 0; i < numReviewers; i++) {
      const reviewer = faker.helpers.arrayElement(users);
      const requester = faker.helpers.arrayElement(users);

      reviewerData.push({
        proofId: proof.id,
        name: reviewer.name,
        email: reviewer.email,
        userId: reviewer.id,
        isExternal: faker.datatype.boolean({ probability: 0.2 }),
        status: faker.helpers.arrayElement(['pending', 'approved', 'rejected', 'needs_changes']),
        requestedBy: requester.id,
        lastRequestedAt: faker.date.recent()
      });
    }
  }

  const reviewers = await db.insert(schema.reviewers).values(reviewerData).returning();
  console.log(`✅ Created ${reviewers.length} reviewers`);
  return reviewers;
}

/**
 * Creates 12 external reviewer records with randomized data, each linked to a random account, user (as requester), and retailer.
 *
 * @returns The array of inserted external reviewer records.
 */
export async function ensureExternalReviewers(accounts: any[], users: any[], retailers: any[]) {
  console.log('🔗 Seeding external reviewers...');

  const externalReviewerData = Array.from({ length: 12 }, () => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    jobTitle: faker.person.jobTitle(),
    name: faker.person.fullName(),
    email: faker.internet.email(),
    requestedByUserId: faker.helpers.arrayElement(users).id,
    retailerId: faker.helpers.arrayElement(retailers).id
  }));

  const externalReviewers = await db.insert(schema.externalReviewers).values(externalReviewerData).returning();
  console.log(`✅ Created ${externalReviewers.length} external reviewers`);
  return externalReviewers;
}

/**
 * Seeds each proof with parent and reply comments to simulate review discussions.
 *
 * For each proof, creates 1 to 5 parent comments from its reviewers, then adds reply comments to approximately 30% of those parents. Ensures comments are linked to valid reviewers and avoids duplicate seeding.
 */
export async function ensureProofComments(proofs: any[], reviewers: any[]) {
  console.log('💬 Seeding proof comments...');

  const commentData = [];

  // Each proof gets 1-5 comments
  for (const proof of proofs) {
    const proofReviewers = reviewers.filter(r => r.proofId === proof.id);
    if (proofReviewers.length === 0) continue;

    const numComments = faker.number.int({ min: 1, max: 5 });

    for (let i = 0; i < numComments; i++) {
      const comment = {
        proofId: proof.id,
        reviewerId: faker.helpers.arrayElement(proofReviewers).id,
        comment: faker.lorem.paragraph(),
        replyToId: null as string | null
      };

      commentData.push(comment);
    }
  }

  // Insert parent comments first
  const insertedComments = await db.insert(schema.proofComments).values(commentData).returning();
  console.log(`✅ Created ${commentData.length} parent proof comments`);

  // Now add some reply comments using actual comment IDs
  const replyCommentData = [];
  const parentComments = faker.helpers.arrayElements(insertedComments, Math.floor(insertedComments.length * 0.3));

  for (const parentComment of parentComments) {
    const proofReviewers = reviewers.filter(r => r.proofId === parentComment.proofId);
    if (proofReviewers.length === 0) continue;

    replyCommentData.push({
      proofId: parentComment.proofId,
      reviewerId: faker.helpers.arrayElement(proofReviewers).id,
      comment: faker.lorem.sentence(),
      replyToId: parentComment.id
    });
  }

  if (replyCommentData.length > 0) {
    await db.insert(schema.proofComments).values(replyCommentData);
    console.log(`✅ Created ${replyCommentData.length} reply proof comments`);
  }
}

/**
 * Seeds the notifications table with 45 randomized notifications for the given users and accounts.
 *
 * Each notification includes a realistic title, message, status, and timestamps, and is associated with a randomly selected user and account.
 */
export async function ensureNotifications(users: any[], accounts: any[]) {
  console.log('🔔 Seeding notifications...');

  const notificationData = Array.from({ length: 45 }, () => {
    const realisticNotification = generateRealisticNotification();
    return {
      userId: faker.helpers.arrayElement(users).id,
      accountId: faker.helpers.arrayElement(accounts).id,
      title: realisticNotification.title,
      message: realisticNotification.message,
      data: realisticNotification.data,
      isRead: realisticNotification.isRead,
      readAt: realisticNotification.readAt,
      createdAt: faker.date.recent({ days: 7 })
    };
  });

  await db.insert(schema.notifications).values(notificationData);
  console.log(`✅ Created ${notificationData.length} notifications`);
}

/**
 * Inserts eight invitation records with randomized data for the specified accounts and brands.
 *
 * Each invitation is linked to a random account, includes up to three random brand IDs (if provided), and is assigned a random email, type, and status.
 */
export async function ensureInvitations(accounts: any[], brands: any[]) {
  console.log('✉️ Seeding invitations...');

  // If no brands are provided, fall back to empty brandIds array
  const availableBrands = brands && brands.length > 0 ? brands : [];

  const invitationData = Array.from({ length: 8 }, () => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    brandIds: availableBrands.length > 0
      ? faker.helpers.arrayElements(
          availableBrands.map(brand => brand.id),
          { min: 0, max: Math.min(3, availableBrands.length) }
        )
      : [],
    email: faker.internet.email(),
    type: faker.helpers.arrayElement(['request', 'invite'] as const),
    status: faker.helpers.arrayElement(['pending', 'accepted', 'rejected'] as const)
  }));

  await db.insert(schema.invitations).values(invitationData);
  console.log(`✅ Created ${invitationData.length} invitations`);
}