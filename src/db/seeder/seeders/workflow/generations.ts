import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Seeds the database with 35 generation records, each linking a randomly selected product, keyword, project, and brand from the provided arrays.
 *
 * @param products - Array of product objects to select from
 * @param keywords - Array of keyword objects to select from
 * @param projects - Array of project objects to select from
 * @param brands - Array of brand objects to select from
 * @returns An array of the inserted generation records, or an empty array if insertion fails
 */
export async function ensureGenerations(products: any[], keywords: any[], projects: any[], brands: any[]) {
  console.log('⚡ Seeding generations...');

  const generationData = Array.from({ length: 35 }, () => ({
    productId: faker.helpers.arrayElement(products).id,
    keywordId: faker.helpers.arrayElement(keywords).id,
    projectId: faker.helpers.arrayElement(projects).id,
    brandId: faker.helpers.arrayElement(brands).id
  }));

  const generations = await db.insert(schema.generations).values(generationData).returning();
  console.log(`✅ Created ${Array.isArray(generations) ? generations.length : 'unknown'} generations`);
  return Array.isArray(generations) ? generations : [];
}