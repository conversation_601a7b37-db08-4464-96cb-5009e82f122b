import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Populates the database with 15 demo leads for the beta program, generating realistic test data for each lead.
 *
 * Each lead includes a unique beta ID, contact and organization details, DSS spend, organization size, job role, invite status, a selection of beta keys, and a list of sent keys with optional emails.
 */
export async function ensureLeads() {
  console.log('🎯 Seeding leads...');

  const leadsData = Array.from({ length: 15 }, () => ({
    betaId: faker.string.uuid(),
    fullName: faker.person.fullName(),
    email: faker.internet.email(),
    organization: faker.company.name(),
    dssSpend: faker.number.int({ min: 1000, max: 50000 }),
    orgSize: faker.number.int({ min: 5, max: 500 }),
    role: faker.person.jobTitle(),
    inviteStatus: faker.helpers.arrayElement(['pending', 'accepted', 'rejected'] as const),
    betaKeys: faker.helpers.arrayElements(
      Array.from({ length: 5 }, () => faker.string.uuid()),
      { min: 0, max: 3 }
    ),
    sentKeys: Array.from({ length: faker.number.int({ min: 0, max: 2 }) }, () => ({
      key: faker.string.uuid(),
      email: faker.datatype.boolean({ probability: 0.7 }) ? faker.internet.email() : null
    }))
  }));

  await db.insert(schema.leads).values(leadsData);
  console.log(`✅ Created ${leadsData.length} leads`);
}