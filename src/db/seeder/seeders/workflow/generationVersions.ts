import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Generates and inserts randomized generation version records for each provided generation entity.
 *
 * For every generation in the input array, creates between one and three version records with randomized unit fields, ad previews, preview image positions, and randomly assigned creators. Returns all successfully inserted generation version records, or an empty array if insertion fails.
 *
 * @param generations - List of generation entities to generate versions for
 * @param users - List of user entities to randomly assign as creators of the versions
 * @returns Array of newly created generation version records
 */
export async function ensureGenerationVersions(generations: any[], users: any[]) {
  console.log('🔄 Seeding generation versions...');

  const versionData = [];

  // Each generation gets 1-3 versions
  for (const generation of generations) {
    const numVersions = faker.number.int({ min: 1, max: 3 });

    for (let i = 0; i < numVersions; i++) {
      versionData.push({
        generationId: generation.id,
        sessionId: faker.string.uuid(),
        unitFields: {
          headline: faker.company.catchPhrase(),
          description: faker.lorem.sentence(),
          cta: faker.helpers.arrayElement(['Shop Now', 'Learn More', 'Buy Now', 'Discover'])
        },
        hasCompleted: faker.datatype.boolean({ probability: 0.7 }),
        createdBy: faker.helpers.arrayElement(users).id,
        version: i + 1,
        adPreviews: Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => ({
          id: faker.string.uuid(),
          url: faker.image.url(),
          size: faker.helpers.arrayElement(['300x250', '728x90', '320x50'])
        })),
        previewImagePositions: {
          primary: { x: 0, y: 0 },
          secondary: { x: 100, y: 100 }
        },
        applyToAll: faker.datatype.boolean({ probability: 0.3 })
      });
    }
  }

  const versions = await db.insert(schema.generationVersions).values(versionData).returning();
  console.log(`✅ Created ${Array.isArray(versions) ? versions.length : 'unknown'} generation versions`);
  return Array.isArray(versions) ? versions : [];
}