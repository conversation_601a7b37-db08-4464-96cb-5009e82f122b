import { db, faker } from '../core';
import * as schema from '../../../schema';

/**
 * Generates and inserts 50 creative records with randomized, realistic data linked to the provided brands, users, generations, and projects.
 *
 * Each creative includes randomized campaign IDs, keywords, ad unit specifications, preview URLs, status fields, and review comments. Creation and update timestamps are set to recent dates and times. The function inserts all generated creatives into the database and returns the inserted records.
 *
 * @param brands - Array of brand objects to associate with each creative
 * @param users - Array of user objects to associate with each creative
 * @param generations - Array of generation objects to associate with each creative
 * @param projects - Array of project objects to associate with each creative
 * @returns An array of the inserted creative records
 */
export async function ensureCreatives(brands: any[], users: any[], generations: any[], projects: any[]) {
  console.log('🎨 Seeding creatives...');

  const creativeData = Array.from({ length: 50 }, () => {
    const now = new Date();
    const daysAgo = faker.number.int({ min: 1, max: 15 });
    const hoursAgo = faker.number.int({ min: 1, max: 12 });

    // Realistic campaign IDs
    const campaignTemplates = ['HOLIDAY_CAMPAIGN', 'PRODUCT_LAUNCH', 'BRAND_AWARENESS', 'PERFORMANCE_DRIVE'];

    const campaignId = faker.helpers.arrayElement(campaignTemplates) + '_' + faker.string.alphanumeric(6);

    // Realistic keywords
    const realisticKeywords = [
      'sustainable gifts', 'wireless earbuds', 'home decor', 'fitness equipment',
      'tech accessories', 'organic products', 'premium quality', 'eco-friendly',
      'smart home', 'workout gear', 'modern furniture', 'healthy living'
    ];

    // Realistic review comments
    const reviewComments = [];
    const numComments = faker.number.int({ min: 0, max: 3 });
    const commentTemplates = [
      'Great concept! Can we make the CTA more prominent?',
      'Love the color scheme, but the text might be too small for mobile.',
      'This really captures our brand voice. Ready to approve!',
      'Could we A/B test with a different headline?',
      'The product image looks great, but let\'s add more lifestyle context.',
      'Perfect! This aligns well with our brand guidelines.'
    ];

    for (let i = 0; i < numComments; i++) {
      reviewComments.push({
        comment: faker.helpers.arrayElement(commentTemplates),
        reviewer: `${faker.person.firstName()} ${faker.person.lastName()} - ${faker.helpers.arrayElement(['Brand Manager', 'Creative Director', 'Marketing Lead', 'Designer'])}`,
        timestamp: new Date(now.getTime() - (i + 1) * 24 * 60 * 60 * 1000).toISOString()
      });
    }

    return {
      projectId: faker.helpers.arrayElement(projects).id,
      wmCreativeId: `${campaignId}_CREATIVE_${faker.string.alphanumeric(6)}`,
      brandId: faker.helpers.arrayElement(brands).id,
      userId: faker.helpers.arrayElement(users).id,
      generationId: faker.helpers.arrayElement(generations).id,
      previewUrls: {
        thumbnail: faker.image.urlLoremFlickr({ category: 'business', width: 200, height: 200 }),
        fullSize: faker.image.urlLoremFlickr({ category: 'business', width: 800, height: 600 })
      },
      previewStatus: faker.helpers.arrayElement(['PENDING', 'COMPLETED', 'FAILED']),
      folderId: faker.datatype.boolean({ probability: 0.7 }) ? faker.string.uuid() : null,
      status: faker.helpers.arrayElement(['DRAFT', 'REVIEW', 'APPROVED', 'REJECTED']),
      keyword: faker.helpers.arrayElement(realisticKeywords),
      adUnits: {
        banner: {
          width: faker.helpers.arrayElement([728, 300, 320, 970, 160]),
          height: faker.helpers.arrayElement([90, 250, 50, 250, 600]),
          format: faker.helpers.arrayElement(['jpeg', 'png'])
        },
        video: {
          duration: faker.helpers.arrayElement([15, 30, 60]),
          format: 'mp4'
        }
      },
      reviewComments,
      createdAt: new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000),
      updatedAt: new Date(now.getTime() - hoursAgo * 60 * 60 * 1000)
    };
  });

  const creatives = await db.insert(schema.creatives).values(creativeData).returning();
  console.log(`✅ Created ${creatives.length} creatives`);
  return creatives;
}