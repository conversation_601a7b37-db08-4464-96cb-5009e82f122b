import { SeederConfig } from './types';

// Default configuration for seeding quantities
export const DEFAULT_SEEDER_CONFIG: SeederConfig = {
  userCount: 8, // Minimum users (including super users)
  accountCount: 3, // Each super user gets multiple accounts
  brandCount: 8,
  productCount: 30,
  projectCount: 15,
  generationCount: 35,
  creativeCount: 50,
  assetCount: 25,
  notificationCount: 45,
  invitationCount: 8,
  externalReviewerCount: 12
};

// Predefined category names
export const CATEGORY_NAMES = [
  'Electronics', 'Fashion', 'Home & Garden', 'Sports & Outdoors',
  'Beauty & Personal Care', 'Books & Media', 'Automotive', 'Food & Beverage',
  'Health & Wellness', 'Toys & Games', 'Pet Supplies', 'Office Supplies'
];

// Predefined keyword terms
export const KEYWORD_TERMS = [
  'summer sale', 'new arrival', 'best seller', 'limited edition', 'premium quality',
  'eco friendly', 'organic', 'handmade', 'vintage', 'modern design',
  'affordable', 'luxury', 'trending', 'seasonal', 'exclusive',
  'fast shipping', 'free returns', 'customer favorite', 'top rated', 'bestselling'
];

// Brand guideline labels
export const BRAND_GUIDELINE_LABELS = [
  'Brand Guidelines', 'Logo Usage', 'Color Palette', 'Typography Guide'
];

// User account roles and their weights
export const USER_ACCOUNT_ROLES = {
  'owner': 0.1,   // 10% chance
  'admin': 0.3,   // 30% chance
  'member': 0.5,  // 50% chance
  'viewer': 0.1   // 10% chance
};

// Creative ad unit sizes
export const AD_UNIT_SIZES = ['300x250', '728x90', '320x50', '160x600', '468x60'];

// Environment configuration
export const ENV_CONFIG = {
  FORCE_CLEAR: process.env.FORCE_CLEAR === 'true',
  SKIP_CLEARING: process.env.SKIP_CLEARING === 'true'
};