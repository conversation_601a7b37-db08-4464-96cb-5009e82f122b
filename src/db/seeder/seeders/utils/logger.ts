// Enhanced seeder logger with structured logging
import { logger as mainLogger } from '../../../../utils/logger.js';

export class SeederLogger {
  private startTime: number;
  private phase: string = '';

  constructor() {
    this.startTime = Date.now();
  }

  startPhase(phase: string) {
    this.phase = phase;
    mainLogger.info(`🚀 Starting Phase: ${phase}`, { phase, seederPhase: true });
  }

  endPhase() {
    const duration = Date.now() - this.startTime;
    mainLogger.info(`✅ Completed Phase: ${this.phase}`, { 
      phase: this.phase, 
      duration_ms: duration,
      seederPhase: true 
    });
  }

  logProgress(message: string, count?: number) {
    mainLogger.info(`📊 ${message}`, { 
      count, 
      phase: this.phase,
      seederProgress: true 
    });
  }

  logSuccess(message: string, count?: number) {
    mainLogger.info(`✅ ${message}`, { 
      count, 
      phase: this.phase,
      seederSuccess: true 
    });
  }

  logWarning(message: string) {
    mainLogger.warn(`⚠️  ${message}`, { 
      phase: this.phase,
      seederWarning: true 
    });
  }

  logError(message: string, error?: Error) {
    mainLogger.error(`❌ ${message}`, error, { 
      phase: this.phase,
      seederError: true 
    });
  }

  logSummary(totalRecords: number) {
    const duration = ((Date.now() - this.startTime) / 1000).toFixed(2);
    mainLogger.info(`🎉 Seeding completed successfully!`, {
      totalRecords,
      duration_seconds: parseFloat(duration),
      duration_ms: Date.now() - this.startTime,
      seederSummary: true
    });
  }
}

export const logger = new SeederLogger();