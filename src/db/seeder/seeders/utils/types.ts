// Type definitions for seeded data
export interface SeededUser {
  id: string;
  auth0Id: string;
  email: string;
  name: string;
  isVerified: boolean;
  jobTitle?: string | null;
  phone?: string | null;
  status: 'active' | 'inactive' | 'pending';
  language: string;
  timeZone: string;
  isBetaUser: boolean;
  betaKeyId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SeededAccount {
  id: string;
  name: string;
  type: 'agency' | '3p' | '1p';
  isTrial: boolean;
  seats: number;
  ownerId: string;
  status: 'active' | 'inactive';
  stripeCustomerId?: string | null;
  validSubscription: boolean;
  hasPaymentMethod: boolean;
  onboardingStep: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface SeededBrand {
  id: string;
  accountId: string;
  name: string;
  description: string;
  colors: string[]; // Array format: [primary, secondary, accent?]
  logoAssetUrl: string;
  primaryCredentialId?: string | null;
  notes: string;
  productCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface SeededProduct {
  id: string;
  productId: string;
  productTitle: string;
  productType: 'physical' | 'digital' | 'service';
  category: string;
  thumbnailUrl: string;
  shortDescription: string;
  longDescription: string;
  genAiDescription: string;
  specifications: string[];
  productHighlights: string[];
  classType: 'A' | 'B' | 'C';
  upc: string;
  images: string[];
  customImages: string[];
  accountId: string;
  brandId: string;
  pdpSummary: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SeederConfig {
  userCount: number;
  accountCount: number;
  brandCount: number;
  productCount: number;
  projectCount: number;
  generationCount: number;
  creativeCount: number;
  assetCount: number;
  notificationCount: number;
  invitationCount: number;
  externalReviewerCount: number;
}