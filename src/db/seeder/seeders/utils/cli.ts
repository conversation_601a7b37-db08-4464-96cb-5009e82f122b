/**
 * CLI argument parsing utilities for parameterized seeding
 */

export interface SeedingParams {
  superUserId?: string;
  accountId?: string;
  brandCount?: number;
  userCount?: number;
  productCount?: number;
  projectCount?: number;
  skipClearing?: boolean;
  forceClearing?: boolean;
  dryRun?: boolean;
}

/**
 * Parses command-line arguments into a `SeedingParams` object for database seeding.
 *
 * Recognizes both long and short flag forms for user IDs, account IDs, entity counts, and boolean options.
 * Flags expecting values will consume the next argument if present and not another flag.
 *
 * @param args - The array of command-line arguments to parse. Defaults to `process.argv`.
 * @returns An object containing the extracted seeding parameters.
 */
export function parseArgs(args: string[] = process.argv): SeedingParams {
  const params: SeedingParams = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    const nextArg = args[i + 1];

    switch (arg) {
      case '--super-user-id':
      case '-u':
        if (nextArg && !nextArg.startsWith('-')) {
          params.superUserId = nextArg;
          i++;
        }
        break;

      case '--account-id':
      case '-a':
        if (nextArg && !nextArg.startsWith('-')) {
          params.accountId = nextArg;
          i++;
        }
        break;

      case '--brand-count':
      case '-b':
        if (nextArg && !nextArg.startsWith('-')) {
          params.brandCount = parseInt(nextArg, 10);
          i++;
        }
        break;

      case '--user-count':
      case '-uc':
        if (nextArg && !nextArg.startsWith('-')) {
          params.userCount = parseInt(nextArg, 10);
          i++;
        }
        break;

      case '--product-count':
      case '-p':
        if (nextArg && !nextArg.startsWith('-')) {
          params.productCount = parseInt(nextArg, 10);
          i++;
        }
        break;

      case '--project-count':
      case '-pr':
        if (nextArg && !nextArg.startsWith('-')) {
          params.projectCount = parseInt(nextArg, 10);
          i++;
        }
        break;

      case '--skip-clearing':
        params.skipClearing = true;
        break;

      case '--force-clearing':
        params.forceClearing = true;
        break;

      case '--dry-run':
        params.dryRun = true;
        break;
    }
  }

  return params;
}

/**
 * Validates seeding parameters for correct value ranges and formats.
 *
 * Checks that count parameters are within allowed limits and that IDs are valid UUIDs.
 *
 * @returns An object containing a `valid` flag and an array of error messages for any invalid parameters.
 */
export function validateParams(params: SeedingParams): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (params.brandCount !== undefined && (params.brandCount < 1 || params.brandCount > 50)) {
    errors.push('Brand count must be between 1 and 50');
  }

  if (params.userCount !== undefined && (params.userCount < 1 || params.userCount > 100)) {
    errors.push('User count must be between 1 and 100');
  }

  if (params.productCount !== undefined && (params.productCount < 1 || params.productCount > 200)) {
    errors.push('Product count must be between 1 and 200');
  }

  if (params.projectCount !== undefined && (params.projectCount < 1 || params.projectCount > 50)) {
    errors.push('Project count must be between 1 and 50');
  }

  if (params.superUserId && !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(params.superUserId)) {
    errors.push('Super user ID must be a valid UUID');
  }

  if (params.accountId && !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(params.accountId)) {
    errors.push('Account ID must be a valid UUID');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Prints detailed CLI usage instructions, available options, and example commands for the parameterized database seeding tool.
 */
export function printHelp() {
  console.log(`
📋 Parameterized Database Seeder

Usage: npm run db:seed:custom -- [options]

Options:
  -u, --super-user-id <uuid>    Target super user ID
  -a, --account-id <uuid>       Target account ID
  -b, --brand-count <number>    Number of brands to create (1-50)
  -uc, --user-count <number>    Number of users to create (1-100)
  -p, --product-count <number>  Number of products to create (1-200)
  -pr, --project-count <number> Number of projects to create (1-50)
  --skip-clearing              Skip database clearing
  --force-clearing             Force database clearing
  --dry-run                    Show what would be done without executing

Examples:
  # Create 5 brands with 10 users each for a specific super user
  npm run db:seed:custom -- -u ************************************ -b 5 -uc 10
  
  # Add 3 brands to a specific account without clearing
  npm run db:seed:custom -- -a ************************************ -b 3 --skip-clearing
  
  # Create 20 products and 5 projects for testing
  npm run db:seed:custom -- -p 20 -pr 5
  
  # Dry run to see what would be created
  npm run db:seed:custom -- -b 5 -uc 15 --dry-run
`);
}

/**
 * Prints a formatted summary of the provided seeding parameters to the console.
 *
 * Each parameter is displayed with its value or state if it is defined or enabled.
 */
export function printParams(params: SeedingParams) {
  console.log('\n🔧 Seeding Parameters:');
  
  if (params.superUserId) console.log(`   Super User ID: ${params.superUserId}`);
  if (params.accountId) console.log(`   Account ID: ${params.accountId}`);
  if (params.brandCount) console.log(`   Brand Count: ${params.brandCount}`);
  if (params.userCount) console.log(`   User Count: ${params.userCount}`);
  if (params.productCount) console.log(`   Product Count: ${params.productCount}`);
  if (params.projectCount) console.log(`   Project Count: ${params.projectCount}`);
  if (params.skipClearing) console.log(`   Skip Clearing: true`);
  if (params.forceClearing) console.log(`   Force Clearing: true`);
  if (params.dryRun) console.log(`   Dry Run: true`);
  
  console.log('');
}