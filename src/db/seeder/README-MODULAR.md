# Modular Database Seeder

This directory contains a modular refactoring of the original monolithic seeder, breaking it into manageable, focused modules for better maintainability and reusability.

## Directory Structure

```
src/db/seeder/
├── seed-modular.ts          # Main orchestrator (use this instead of seed.ts)
├── seedConfig.ts            # Configuration for seeding quantities
├── seeders/                 # Modular seeder components
│   ├── core/               # Core functionality
│   │   ├── database.ts     # Database connection & clearing logic
│   │   ├── superUsers.ts   # SuperUser loading
│   │   ├── faker-helpers.ts # Faker.js helper functions
│   │   └── index.ts
│   ├── base/               # Base entities
│   │   ├── users.ts        # User seeding (super users + faker users)
│   │   ├── accounts.ts     # Account seeding
│   │   ├── settings.ts     # Account and user settings
│   │   └── index.ts
│   ├── business/           # Business logic
│   │   ├── brands.ts       # Brand seeding with guidelines
│   │   ├── categories.ts   # Category seeding
│   │   ├── keywords.ts     # Keyword seeding
│   │   ├── credentialSets.ts # Credential set seeding
│   │   ├── retailers.ts    # Retailer seeding
│   │   └── index.ts
│   ├── relationships/      # Entity relationships
│   │   ├── userAccounts.ts # User-account role relationships
│   │   ├── brandRelationships.ts # Brand-category, brand-user, etc.
│   │   ├── credentialRelationships.ts # User-credential relationships
│   │   └── index.ts
│   ├── content/           # Content entities
│   │   ├── assets.ts      # Asset seeding
│   │   ├── products.ts    # Product seeding with retailer relationships
│   │   ├── imageMetadata.ts # Image metadata for products
│   │   ├── projects.ts    # Project seeding
│   │   └── index.ts
│   ├── workflow/          # Workflow entities
│   │   ├── generations.ts # Generation seeding
│   │   ├── creatives.ts   # Creative seeding
│   │   ├── generationVersions.ts # Generation version seeding
│   │   ├── reviews.ts     # Proof, reviewer, and comment seeding
│   │   └── index.ts
│   ├── utils/             # Utilities
│   │   ├── types.ts       # TypeScript interfaces
│   │   ├── constants.ts   # Configuration constants
│   │   ├── logger.ts      # Seeding progress logging
│   │   └── index.ts
│   └── index.ts           # Main export file
└── seed.ts                # Legacy seeder (marked for deprecation)
```

## Usage

### Running the Modular Seeder

```bash
# Use the new modular seeder
bun run src/db/seeder/seed-modular.ts

# Or if you have a package.json script
npm run seed:modular
```

### Environment Variables

```bash
FORCE_CLEAR=true     # Force clear database even if not empty
SKIP_CLEARING=true   # Skip database clearing (for testing)
DATABASE_URL=...     # Your database connection string
```

## Key Features

### ✅ Superuser Integration
- Each superuser owns exactly ONE account (role: owner)
- Each superuser has access to at least 3 brands with different roles (owner, admin, member)
- Loads from `superUsers.json` configuration file
- Supplements with demo users to ensure rich relational data

### ✅ Account Relationships  
- Each account has exactly 6 members: 1 super user owner + 5 demo users
- Super users only own one account each, no cross-account membership
- Demo users have varied roles (admin, member) and statuses

### ✅ Rich Demo Data
- All junction tables populated with relationships
- Each brand has at least 6 users: 1 super user + 5 demo users with varied roles
- 4 brands per account ensuring super users have 3+ brands with owner/admin/member roles
- Products associated with multiple retailers
- Complete review workflow with proofs and comments

### ✅ Idempotent Seeding
- Safe to run multiple times
- Checks for existing data before creating
- Respects foreign key constraints during clearing

### ✅ Foreign Key Constraint Handling
- Proper deletion order during database clearing
- Handles circular dependencies (generations ↔ generationVersions)
- No foreign key violations

## Configuration

Edit `seedConfig.ts` to adjust seeding quantities:

```typescript
export const SEED_CONFIG: SeederConfig = {
  userCount: 25,          // 4 super users + 21 demo users
  accountCount: 4,        // One account per super user
  brandCount: 16,         // 4 brands per account
  productCount: 30,
  projectCount: 15,
  generationCount: 35,
  creativeCount: 50,
  // ... other configurations
};
```

## Benefits Over Monolithic Seeder

1. **Maintainability** - Each module handles specific domain area
2. **Testability** - Individual modules can be tested independently  
3. **Reusability** - Modules can be reused across different scenarios
4. **Scalability** - Easy to add new entity types or modify existing ones
5. **Dependency Management** - Clear dependencies between modules
6. **Configuration** - Centralized configuration for quantities and relationships

## Module Dependencies

```
Core → Base → Business → Relationships → Content → Workflow
  ↓      ↓        ↓           ↓            ↓         ↓
Database Users  Brands   UserAccounts  Products  Generations
Helpers  Accounts Categories BrandUsers  Assets    Creatives
SuperUsers Settings Keywords  BrandRetail Projects  Reviews
```

## Migration from Legacy Seeder

The original `seed.ts` has been marked as legacy. The modular version:
- Produces identical data structure
- Maintains all existing relationships
- Preserves idempotent behavior
- Uses same configuration files (`superUsers.json`)

## Adding New Entities

1. Create new module in appropriate directory (`business/`, `content/`, etc.)
2. Export seeding function following naming convention (`ensure*`)
3. Add to module's `index.ts`
4. Import and call in `seed-modular.ts` orchestrator
5. Update `seedConfig.ts` if needed