# Database Seeder Commands

This document describes all available seeding commands and their use cases.

## Main Commands

### `npm run db:seed` or `bun run db:seed`
**Default seeder** - Runs the full modular seeder with all entities and relationships.
- Clears database by default
- Seeds all 30 database tables
- 572 records created in ~4.5 seconds
- **Use this for: Production seeding, full demo data**

### `npm run db:seed:validate`
**Validation utility** - Validates the seeded data structure without making changes.
- Checks super user requirements
- Validates account and brand relationships
- Reports membership counts
- **Use this for: Verifying seeding results, debugging data issues**

## Database Control Commands

### `npm run db:seed:clear`
**Force clear and seed** - Explicitly clears database before seeding.
- Sets `SKIP_CLEARING=false` (default behavior)
- Useful for emphasizing the clearing step
- **Use this for: Ensuring clean slate before seeding**

### `npm run db:seed:no-clear`
**Seed without clearing** - Seeds data without clearing existing data.
- Sets `SKIP_CLEARING=true`
- Attempts idempotent seeding
- **Use this for: Adding data to existing database, testing incremental seeding**

### `npm run db:seed:force`
**Force clear mode** - Forces clearing even if database has data.
- Sets `FORCE_CLEAR=true`
- Overrides safety checks
- **Use this for: Production resets, cleaning corrupted data**

## Modular Seeding Commands

### `npm run db:seed:base`
**Base entities only** - Seeds users, accounts, settings, and permissions.
- 83 records created
- No business logic or workflow data
- **Use this for: User management testing, basic setup**

### `npm run db:seed:business`
**Business entities only** - Seeds brands, categories, keywords, credentials, retailers.
- Requires base entities to exist
- ~70 records created
- **Use this for: Brand management testing, business logic development**

### `npm run db:seed:no-workflow`
**Everything except workflow** - Seeds base, business, relationships, and content.
- Excludes generations, creatives, reviews, proofs
- ~400 records created
- **Use this for: UI development, non-workflow features**

## Legacy Commands

### `npm run db:seed:legacy`
**Legacy monolithic seeder** - Runs the original 1200-line seeder.
- Marked for deprecation
- Use only for comparison or fallback
- **Use this for: Comparing with modular version, emergency fallback**

### `npm run db:seed:modular`
**Explicit modular seeder** - Same as `db:seed`, but explicit.
- Identical to main `db:seed` command
- **Use this for: Explicit modular seeding, scripts**

## Development Commands

### `npm run db:seed:drizzle`
**Drizzle-based seeder** - Alternative seeder using Drizzle utilities.
- Different implementation approach
- **Use this for: Drizzle-specific testing**

### `npm run db:seed:demo`
**Demo data seeder** - Creates realistic demo data.
- Alternative seeding approach
- **Use this for: Demo environments, realistic data**

### `npm run db:seed:superusers`
**Super users only** - Seeds only super users from configuration.
- Minimal seeding approach
- **Use this for: User authentication testing**

## Environment Variables

All commands support these environment variables:

- `DATABASE_URL` - **Required** database connection string
- `SKIP_CLEARING=true` - Skip database clearing step
- `FORCE_CLEAR=true` - Force clear database even if it has data

## Common Workflows

### Full Development Setup
```bash
npm run db:seed                    # Full seeding
npm run db:seed:validate          # Verify results
```

### Iterative Development
```bash
npm run db:seed:base              # Base entities
npm run db:seed:business          # Add business logic
npm run db:seed:no-workflow       # Add content without workflow
npm run db:seed                   # Complete with workflow
```

### Production Deployment
```bash
npm run db:seed:force             # Force clean and seed
npm run db:seed:validate          # Validate production data
```

### Testing Scenarios
```bash
npm run db:seed:no-clear          # Test idempotent seeding
npm run db:seed:base              # Test user management
npm run db:seed:business          # Test business logic
```

## Performance Benchmarks

| Command | Records | Time | Use Case |
|---------|---------|------|----------|
| `db:seed` | 572 | ~4.5s | Full production seeding |
| `db:seed:base` | 83 | ~1.2s | User management testing |
| `db:seed:business` | 70 | ~1.0s | Business logic development |
| `db:seed:no-workflow` | 400 | ~3.0s | UI development |
| `db:seed:validate` | 0 | ~0.5s | Data validation |

## Error Handling

All commands include:
- Environment validation
- Database connection checks
- Comprehensive error logging
- Graceful failure handling
- Exit codes for CI/CD integration