import 'dotenv/config';
import { clearDatabase, db } from './seeders/core';
import { ensureSuperUsers, ensureAccounts, ensureAccountSettings, ensureUserSettings, ensurePermissions } from './seeders/base';
import { createUsersForSuperUser, createUsersForAccount, createUsersForBrand } from './seeders/base/flexibleUsers';
import { createBrandsForSuperUser, createBrandsForAccount, createBrandsForSuperUsers } from './seeders/business/flexibleBrands';
import { createContentForSuperUser, createProductsForBrands, createProjectsForBrands } from './seeders/content/flexibleContent';
import { ensureRetailers, ensureCategories, ensureKeywords } from './seeders/business';
import { parseArgs, validateParams, printHelp, printParams, SeedingParams } from './seeders/utils';
import * as schema from '../schema';

console.log('🌱 Starting parameterized database seeding...');

// Parse command line arguments
const params = parseArgs();

// Check for help flag
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  printHelp();
  process.exit(0);
}

// Validate environment
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

// Validate parameters
const validation = validateParams(params);
if (!validation.valid) {
  console.error('❌ Invalid parameters:');
  validation.errors.forEach(error => console.error(`   - ${error}`));
  console.log('\nUse --help for usage information');
  process.exit(1);
}

// Print parameters
printParams(params);

/**
 * Executes a parameterized database seeding process based on provided command line arguments and environment variables.
 *
 * The function supports dry-run mode, optional database clearing, and flexible seeding scenarios targeting super users, accounts, or global distribution. It ensures the existence of required base and business entities, and seeds brands, users, and content as specified by parameters. Logs progress and errors, and exits the process on failure.
 */
async function seedWithParameters() {
  try {
    // Handle database clearing
    if (params.forceClearing || (!params.skipClearing && !process.env.SKIP_CLEARING)) {
      console.log('🧹 Clearing database...');
      if (!params.dryRun) {
        await clearDatabase();
      } else {
        console.log('   [DRY RUN] Would clear database');
      }
    }

    // Ensure base entities exist
    console.log('📋 Ensuring base entities exist...');
    let users, accounts;
    
    if (!params.dryRun) {
      users = await ensureSuperUsers();
      accounts = await ensureAccounts(users);
      
      // Only create these if they don't exist
      try {
        await ensureAccountSettings(accounts);
        await ensureUserSettings(users);
        await ensurePermissions();
      } catch (error) {
        console.log('   Base entities already exist, continuing...');
      }
    } else {
      console.log('   [DRY RUN] Would ensure base entities exist');
    }

    // Ensure basic business entities exist
    console.log('📋 Ensuring basic business entities exist...');
    if (!params.dryRun) {
      await ensureRetailers();
      await ensureCategories(accounts || []);
      await ensureKeywords(accounts || []);
    } else {
      console.log('   [DRY RUN] Would ensure basic business entities exist');
    }

    // Handle specific seeding scenarios
    if (params.superUserId) {
      console.log(`🎯 Seeding for super user: ${params.superUserId}`);
      
      // Create brands for super user
      if (params.brandCount) {
        await createBrandsForSuperUser(params.superUserId, params.brandCount, params);
      }
      
      // Create users for super user
      if (params.userCount) {
        await createUsersForSuperUser(params.superUserId, params.userCount, params);
      }
      
      // Create content for super user
      if (params.productCount || params.projectCount) {
        await createContentForSuperUser(params.superUserId, params);
      }
      
    } else if (params.accountId) {
      console.log(`🎯 Seeding for account: ${params.accountId}`);
      
      // Create brands for account
      if (params.brandCount) {
        await createBrandsForAccount(params.accountId, params.brandCount, params);
      }
      
      // Create users for account
      if (params.userCount) {
        await createUsersForAccount(params.accountId, params.userCount, params);
      }
      
    } else {
      console.log('🎯 Seeding with global parameters');
      
      // Create brands distributed across super users
      if (params.brandCount) {
        await createBrandsForSuperUsers(params.brandCount, params);
      }
      
      // Create users distributed across accounts
      if (params.userCount && !params.dryRun) {
        const accounts = await db.select().from(schema.accounts);
        const usersPerAccount = Math.ceil(params.userCount / accounts.length);
        
        for (const account of accounts) {
          await createUsersForAccount(account.id, usersPerAccount, params);
        }
      }
    }

    if (params.dryRun) {
      console.log('\n✅ Dry run completed successfully!');
      console.log('   Use the same command without --dry-run to execute');
    } else {
      console.log('\n✅ Parameterized seeding completed successfully!');
    }

  } catch (error) {
    console.error('❌ Parameterized seeding failed:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Run the seeding
await seedWithParameters();