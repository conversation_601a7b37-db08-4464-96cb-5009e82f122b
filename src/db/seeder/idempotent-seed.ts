import 'dotenv/config';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { eq, inArray, not, and } from 'drizzle-orm';
import { faker } from '@faker-js/faker';
import { readFileSync } from 'fs';
import { join } from 'path';
import * as schema from '../schema';
import * as relations from '../relations';

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is required');
}
const sql = neon(process.env.DATABASE_URL);
const db = drizzle(sql, { schema: { ...schema, ...relations } });

// Set faker seed for consistent results
faker.seed(42);

console.log('🌱 Starting idempotent database seeding...');

// Configuration
const FORCE_CLEAR = process.env.FORCE_CLEAR === 'true';
const SKIP_CLEARING = process.env.SKIP_CLEARING === 'true';

// Load superUsers from JSON
const superUsers = loadSuperUsers();
console.log(`📋 Loaded ${superUsers.length} super users from config`);

if (FORCE_CLEAR && !SKIP_CLEARING) {
  console.log('⚠️  FORCE_CLEAR=true - Clearing all data except users...');
  await clearDatabase();
}

// Run idempotent seeding
await seedDatabase();

console.log('✅ Idempotent database seeding completed successfully!');

/**
 * Loads super user data from a JSON file, returning an array of user objects or an empty array if loading fails.
 *
 * The file is expected at `src/db/seeder/superUsers.json` relative to the current working directory.
 *
 * @returns An array of super user objects, or an empty array if the file cannot be read or parsed.
 */
function loadSuperUsers() {
  try {
    const superUsersPath = join(process.cwd(), 'src/db/seeder/superUsers.json');
    const data = readFileSync(superUsersPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.warn('⚠️  Could not load superUsers.json, using empty array');
    return [];
  }
}

/**
 * Generates an array of three random brand colors in RGB format.
 *
 * @returns An array containing three RGB color strings.
 */
function generateBrandColors() {
  return [
    faker.color.rgb(),
    faker.color.rgb(),
    faker.color.rgb()
  ];
}

/**
 * Generates a random user settings object with preferences for notifications, language, timezone, theme, and date format.
 *
 * @returns An object representing randomized user settings.
 */
function generateUserSettings() {
  return {
    emailNotifications: faker.datatype.boolean(),
    language: faker.helpers.arrayElement(['en-US', 'es-ES', 'fr-FR', 'de-DE']),
    timezone: faker.location.timeZone(),
    theme: faker.helpers.arrayElement(['light', 'dark', 'auto']),
    dateFormat: faker.helpers.arrayElement(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'])
  };
}

/**
 * Generates default account settings including notification preferences, enabled integrations, and usage limits.
 *
 * @returns An object containing default values for notifications, integrations, and account limits.
 */
function generateAccountSettings() {
  return {
    notifications: { email: true, slack: false, webhook: null },
    integrations: { stripe: true, auth0: true, analytics: true },
    limits: { maxUsers: 50, maxBrands: 10, maxProjects: 100 }
  };
}

/**
 * Deletes all data from the database except for users, preserving user records.
 *
 * Removes all entries from a predefined list of tables to reset the database state while retaining user data.
 */
async function clearDatabase() {
  console.log('🧹 Clearing existing data...');

  const tables = [
    schema.proofComments, schema.reviewers, schema.externalReviewers, schema.proofs,
    schema.generationVersions, schema.creatives, schema.generations, schema.projects,
    schema.retailerProducts, schema.brandRetailers, schema.credentialSetRetailers,
    schema.imageMetadata, schema.products, schema.brandGuidelines, schema.brandCategories,
    schema.brandUsers, schema.userCredentialSets, schema.credentialSets, schema.brands,
    schema.categories, schema.keywords, schema.retailers, schema.assets, schema.notifications,
    schema.invitations, schema.userAccounts, schema.userSettings, schema.accountSettings,
    schema.accounts
  ];

  for (const table of tables) {
    await db.delete(table);
  }
  console.log('✅ Database cleared successfully (preserving users)');
}

/**
 * Orchestrates the idempotent seeding of the database, ensuring all core entities, relationships, and demo content are present without duplication.
 *
 * Executes the seeding process in phases: users, accounts, user-account relationships, settings, business entities, relationships, and content. Each phase checks for existing data and inserts missing records as needed.
 */
async function seedDatabase() {
  console.log('🌱 Running idempotent seeding...');

  // Phase 1: Ensure users
  const users = await ensureUsers();

  // Phase 2: Core entities
  const accounts = await ensureAccounts(users);
  await ensureUserAccounts(users, accounts);
  await ensureSettings(users, accounts);

  // Phase 3: Business data
  const { categories, keywords, retailers, credentialSets, brands } = await ensureBusinessData(accounts, users);

  // Phase 4: Relationships
  await ensureRelationships(brands, categories, users, credentialSets, retailers);

  // Phase 5: Content
  await ensureContent(accounts, brands, users, retailers, categories, keywords);

  console.log('✅ Idempotent seeding completed');
}

/**
 * Ensures that all required users exist in the database, including super users from configuration and a minimum number of demo users.
 *
 * Inserts any missing super users based on the loaded configuration and generates additional fake users if the total user count is less than eight. Returns the complete list of users after seeding.
 *
 * @returns An array of all user records currently in the database.
 */
async function ensureUsers() {
  console.log('👥 Ensuring users exist...');

  const existingUsers = await db.select().from(schema.users);
  const existingAuth0Ids = new Set(existingUsers.map(u => u.auth0Id));

  console.log(`Found ${existingUsers.length} existing users`);

  // Insert missing super users
  const missingUsers = superUsers.filter((su: any) => !existingAuth0Ids.has(su.auth0_id));

  if (missingUsers.length > 0) {
    console.log(`🔧 Inserting ${missingUsers.length} missing super users...`);

    const superUserData = missingUsers.map((su: any) => ({
      id: su.id,
      auth0Id: su.auth0_id,
      email: su.email,
      name: su.name,
      isVerified: su.is_verified || false,
      jobTitle: su.job_title,
      phone: su.phone,
      status: (su.status as 'active' | 'inactive' | 'pending') || 'active',
      language: su.language || 'en-US',
      timeZone: su.time_zone || 'UTC',
      isBetaUser: su.is_beta_user || false,
      betaKeyId: su.beta_key_id,
      createdAt: new Date(su.created_at),
      updatedAt: new Date(su.updated_at)
    }));

    await db.insert(schema.users).values(superUserData);
    console.log(`✅ Inserted ${missingUsers.length} super users`);
  }

  // Ensure we have enough users for realistic scenarios
  const allUsers = await db.select().from(schema.users);
  if (allUsers.length < 8) {
    const additionalCount = 8 - allUsers.length;
    const additionalUsers = Array.from({ length: additionalCount }, () => ({
      auth0Id: `auth0|${faker.string.alphanumeric(24)}`,
      email: faker.internet.email(),
      name: faker.person.fullName(),
      emailVerified: faker.datatype.boolean({ probability: 0.8 }),
      jobTitle: faker.person.jobTitle(),
      phone: faker.phone.number(),
      status: faker.helpers.arrayElement(['active', 'inactive', 'pending'] as const),
      language: faker.helpers.arrayElement(['en-US', 'es-ES', 'fr-FR']),
      timeZone: faker.location.timeZone(),
      isBetaUser: faker.datatype.boolean({ probability: 0.3 }),
      betaKeyId: faker.datatype.boolean({ probability: 0.2 }) ? faker.string.uuid() : null,
    }));

    await db.insert(schema.users).values(additionalUsers);
    console.log(`✅ Added ${additionalCount} additional users`);
  }

  return await db.select().from(schema.users);
}

/**
 * Ensures that at least one account exists in the database, creating a demo account owned by the first user if none are found.
 *
 * @param users - The list of user records, where the first user will be assigned as the owner of the demo account if created.
 * @returns The list of existing or newly created account records.
 */
async function ensureAccounts(users: any[]) {
  console.log('🏢 Ensuring accounts exist...');

  const existing = await db.select().from(schema.accounts);
  console.log(`Found ${existing.length} existing accounts`);

  if (existing.length === 0) {
    const accountData = [
      {
        name: "AdFury Demo Account",
        type: 'agency' as const,
        isTrial: false,
        seats: 25,
        ownerId: users[0].id, // First user becomes owner
        status: 'active' as const,
        stripeCustomerId: `cus_${faker.string.alphanumeric(14)}`,
        validSubscription: true,
        hasPaymentMethod: true,
        onboardingStep: 5,
      }
    ];

    const newAccounts = await db.insert(schema.accounts).values(accountData).returning();
    console.log(`✅ Created ${newAccounts.length} accounts`);
    return newAccounts;
  }

  return existing;
}

/**
 * Ensures that required user-account relationships exist, including linking account owners and assigning admin roles to initial users for the primary account.
 *
 * Creates missing relationships between users and accounts as owners or admins, avoiding duplicates.
 */
async function ensureUserAccounts(users: any[], accounts: any[]) {
  console.log('🔗 Ensuring user-account relationships...');

  const existing = await db.select().from(schema.userAccounts);
  const existingKeys = new Set(existing.map(ua => `${ua.userId}-${ua.accountId}`));

  const newRelationships = [];

  // Ensure account owners are linked
  for (const account of accounts) {
    const key = `${account.ownerId}-${account.id}`;
    if (!existingKeys.has(key)) {
      newRelationships.push({
        userId: account.ownerId,
        accountId: account.id,
        role: 'owner',
        status: 'active'
      });
    }
  }

  // Add all superUsers to first account
  const primaryAccount = accounts[0];
  for (const user of users.slice(0, Math.min(users.length, 4))) {
    const key = `${user.id}-${primaryAccount.id}`;
    if (!existingKeys.has(key) && user.id !== primaryAccount.ownerId) {
      newRelationships.push({
        userId: user.id,
        accountId: primaryAccount.id,
        role: 'admin',
        status: 'active'
      });
    }
  }

  if (newRelationships.length > 0) {
    await db.insert(schema.userAccounts).values(newRelationships);
    console.log(`✅ Created ${newRelationships.length} user-account relationships`);
  }
}

/**
 * Ensures that each user and account has corresponding settings in the database, inserting default settings for any missing entries.
 *
 * @param users - The list of user records to check and seed settings for if missing
 * @param accounts - The list of account records to check and seed settings for if missing
 */
async function ensureSettings(users: any[], accounts: any[]) {
  console.log('⚙️ Ensuring settings exist...');

  // Account settings
  const existingAccountSettings = await db.select().from(schema.accountSettings);
  const existingAccountIds = new Set(existingAccountSettings.map(s => s.accountId));

  const newAccountSettings = accounts
    .filter(account => !existingAccountIds.has(account.id))
    .map(account => ({
      accountId: account.id,
      settings: {
        notifications: { email: true, slack: false },
        integrations: { stripe: true, auth0: true },
        limits: { maxUsers: 50, maxBrands: 10 }
      }
    }));

  if (newAccountSettings.length > 0) {
    await db.insert(schema.accountSettings).values(newAccountSettings);
    console.log(`✅ Created ${newAccountSettings.length} account settings`);
  }

  // User settings
  const existingUserSettings = await db.select().from(schema.userSettings);
  const existingUserIds = new Set(existingUserSettings.map(s => s.userId));

  const newUserSettings = users
    .filter(user => !existingUserIds.has(user.id))
    .map(user => ({
      userId: user.id,
      settings: {
        emailNotifications: true,
        language: user.language || 'en-US',
        timezone: user.timeZone || 'UTC',
        theme: 'light'
      }
    }));

  if (newUserSettings.length > 0) {
    await db.insert(schema.userSettings).values(newUserSettings);
    console.log(`✅ Created ${newUserSettings.length} user settings`);
  }
}

/**
 * Ensures that core business entities—categories, keywords, retailers, credential sets, and brands—exist in the database, creating default demo records if any are missing.
 *
 * @param accounts - List of account records used for associating new entities
 * @param users - List of user records used for associating new entities
 * @returns An object containing arrays of the ensured categories, keywords, retailers, credential sets, and brands
 */
async function ensureBusinessData(accounts: any[], users: any[]) {
  console.log('🏪 Ensuring business entities exist...');

  // Categories
  let categories = await db.select().from(schema.categories);
  if (categories.length === 0) {
    const categoryData = [
      'Electronics', 'Fashion', 'Home & Garden', 'Sports & Outdoors'
    ].map(name => ({
      accountId: accounts[0].id,
      name
    }));

    categories = await db.insert(schema.categories).values(categoryData).returning();
    console.log(`✅ Created ${categories.length} categories`);
  }

  // Keywords
  let keywords = await db.select().from(schema.keywords);
  if (keywords.length === 0) {
    const keywordData = [
      'summer sale', 'new arrival', 'best seller', 'premium quality'
    ].map(term => ({
      accountId: accounts[0].id,
      keyword: term,
      include: { tone: 'professional', word: term.split(' ')[0] },
      exclude: { tone: 'aggressive', word: 'cheap' }
    }));

    keywords = await db.insert(schema.keywords).values(keywordData).returning();
    console.log(`✅ Created ${keywords.length} keywords`);
  }

  // Retailers
  let retailers = await db.select().from(schema.retailers);
  if (retailers.length === 0) {
    const retailerData = [
      { name: 'Walmart', url: 'https://walmart.com', slug: 'walmart' },
      { name: 'Amazon', url: 'https://amazon.com', slug: 'amazon' }
    ].map(retailer => ({
      ...retailer,
      logoUrl: faker.image.urlLoremFlickr({ category: 'business' })
    }));

    retailers = await db.insert(schema.retailers).values(retailerData).returning();
    console.log(`✅ Created ${retailers.length} retailers`);
  }

  // Credential sets
  let credentialSets = await db.select().from(schema.credentialSets);
  if (credentialSets.length === 0) {
    const credData = [{
      accountId: accounts[0].id,
      name: 'Demo Walmart Connect',
      type: 'wm_display' as const,
      credentials: {
        advertiserId: 'DEMO_ADV_001',
        partnerId: 'DEMO_PARTNER',
        accessToken: faker.string.alphanumeric(32)
      },
      adminId: users[0].id,
      isShared: true,
      isActive: true
    }];

    credentialSets = await db.insert(schema.credentialSets).values(credData).returning();
    console.log(`✅ Created ${credentialSets.length} credential sets`);
  }

  // Brands
  let brands = await db.select().from(schema.brands);
  if (brands.length === 0) {
    const brandData = [{
      accountId: accounts[0].id,
      name: "Demo Brand",
      description: "A demonstration brand for AdFury",
      colors: ['#2563eb', '#1e40af', '#93c5fd'],
      logoAssetUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200',
      primaryCredentialId: credentialSets[0]?.id || null,
      notes: "Demo brand with realistic data",
      productCount: 10
    }];

    brands = await db.insert(schema.brands).values(brandData).returning();
    console.log(`✅ Created ${brands.length} brands`);
  }

  return { categories, keywords, retailers, credentialSets, brands };
}

/**
 * Ensures that key relationships among brands, categories, users, credential sets, and retailers exist in the database.
 *
 * Creates missing associations for brand-category, brand-user (with roles), user-credential set, credential set-retailer, and brand-retailer if they do not already exist.
 */
async function ensureRelationships(brands: any[], categories: any[], users: any[], credentialSets: any[], retailers: any[]) {
  console.log('🔗 Ensuring relationships exist...');

  // Brand categories
  const existingBrandCats = await db.select().from(schema.brandCategories);
  if (existingBrandCats.length === 0 && brands.length > 0 && categories.length > 0) {
    await db.insert(schema.brandCategories).values([{
      brandId: brands[0].id,
      categoryId: categories[0].id
    }]);
    console.log(`✅ Created brand-category relationships`);
  }

  // Brand users
  const existingBrandUsers = await db.select().from(schema.brandUsers);
  if (existingBrandUsers.length === 0 && brands.length > 0) {
    const brandUserData = users.slice(0, 4).map((user, index) => ({
      brandId: brands[0].id,
      userId: user.id,
      role: index === 0 ? 'admin' : 'editor',
      isActive: true
    }));

    await db.insert(schema.brandUsers).values(brandUserData);
    console.log(`✅ Created ${brandUserData.length} brand-user relationships`);
  }

  // User credential sets
  const existingUserCreds = await db.select().from(schema.userCredentialSets);
  if (existingUserCreds.length === 0 && credentialSets.length > 0) {
    const userCredData = users.slice(0, 4).map(user => ({
      userId: user.id,
      credentialSetId: credentialSets[0].id
    }));

    await db.insert(schema.userCredentialSets).values(userCredData);
    console.log(`✅ Created user-credential relationships`);
  }

  // Other relationships...
  const existingCredRetailers = await db.select().from(schema.credentialSetRetailers);
  if (existingCredRetailers.length === 0 && credentialSets.length > 0 && retailers.length > 0) {
    const credRetailerData = retailers.map(retailer => ({
      credentialSetId: credentialSets[0].id,
      retailerId: retailer.id,
      isActive: true
    }));

    await db.insert(schema.credentialSetRetailers).values(credRetailerData);
  }

  const existingBrandRetailers = await db.select().from(schema.brandRetailers);
  if (existingBrandRetailers.length === 0 && brands.length > 0 && retailers.length > 0) {
    const brandRetailerData = retailers.map(retailer => ({
      brandId: brands[0].id,
      retailerId: retailer.id
    }));

    await db.insert(schema.brandRetailers).values(brandRetailerData);
  }
}

/**
 * Ensures that demo content entities such as products, projects, generations, creatives, and notifications exist in the database, creating them if necessary to meet minimum requirements.
 *
 * This function checks for the presence of core content records and inserts demo data for products, projects, generations, creatives, and notifications if they are missing or below the required count. It establishes relationships among these entities using the provided accounts, brands, users, retailers, categories, and keywords.
 */
async function ensureContent(accounts: any[], brands: any[], users: any[], retailers: any[], categories: any[], keywords: any[]) {
  console.log('📦 Ensuring content exists...');

  // Products
  let products = await db.select().from(schema.products);
  if (products.length < 3 && brands.length > 0) {
    const productData = Array.from({ length: 3 }, (_, i) => ({
      productId: `DEMO_PROD_${i + 1}`,
      productTitle: faker.commerce.productName(),
      productType: 'physical' as const,
      category: categories[0]?.name || 'Electronics',
      thumbnailUrl: faker.image.urlLoremFlickr({ category: 'product' }),
      shortDescription: faker.commerce.productDescription(),
      longDescription: faker.lorem.paragraphs(2),
      genAiDescription: faker.lorem.paragraph(),
      specifications: ['premium', 'durable'],
      productHighlights: ['Best seller'],
      classType: 'A' as const,
      upc: faker.string.numeric(12),
      images: [faker.image.urlLoremFlickr({ category: 'product' })],
      customImages: [],
      accountId: accounts[0].id,
      brandId: brands[0].id,
      pdpSummary: faker.lorem.sentence()
    }));

    const newProducts = await db.insert(schema.products).values(productData).returning();
    products = [...products, ...newProducts];
    console.log(`✅ Created ${newProducts.length} products`);
  }

  // Projects
  let projects = await db.select().from(schema.projects);
  if (projects.length === 0 && brands.length > 0) {
    const projectData = [{
      brandId: brands[0].id,
      name: "Demo Campaign 2024",
      wmCampaigns: {
        "DEMO_2024": {
          name: "Demo Campaign",
          adGroups: { "DEMO_AG": "Demo Ad Group" }
        }
      },
      retailerId: retailers[0]?.id || null,
      createdBy: users[0].id,
      currentStep: 3,
      status: 'in_progress' as const,
    }];

    projects = await db.insert(schema.projects).values(projectData).returning();
    console.log(`✅ Created ${projects.length} projects`);
  }

  // Generations and creatives
  if (products.length > 0 && keywords.length > 0 && projects.length > 0) {
    let generations = await db.select().from(schema.generations);
    if (generations.length === 0) {
      const generationData = products.slice(0, 2).map(product => ({
        productId: product.id,
        keywordId: keywords[0].id,
        projectId: projects[0].id,
        brandId: brands[0].id
      }));

      generations = await db.insert(schema.generations).values(generationData).returning() as any[];
      console.log(`✅ Created ${generations.length} generations`);
    }

    // Creatives
    const existingCreatives = await db.select().from(schema.creatives);
    if (existingCreatives.length === 0 && generations.length > 0) {
      const creativeData = generations.map((gen, i) => ({
        projectId: projects[0].id,
        wmCreativeId: `DEMO_CREATIVE_${i + 1}`,
        brandId: brands[0].id,
        userId: users[0].id,
        generationId: gen.id,
        previewUrls: {
          thumbnail: faker.image.url(),
          fullSize: faker.image.url()
        },
        previewStatus: "COMPLETED",
        folderId: null,
        status: "APPROVED",
        keyword: "demo",
        adUnits: {
          banner: { width: 728, height: 90, format: "png" }
        },
        reviewComments: []
      }));

      await db.insert(schema.creatives).values(creativeData);
      console.log(`✅ Created ${creativeData.length} creatives`);
    }
  }

  // Notifications
  const existingNotifications = await db.select().from(schema.notifications);
  if (existingNotifications.length < 3) {
    const notificationData = Array.from({ length: 3 }, () => ({
      userId: faker.helpers.arrayElement(users).id,
      accountId: accounts[0].id,
      title: faker.lorem.sentence(),
      message: faker.lorem.paragraph(),
      data: {
        type: 'system',
        action: 'created',
        entityId: faker.string.uuid()
      },
      isRead: faker.datatype.boolean({ probability: 0.3 }),
      readAt: faker.datatype.boolean({ probability: 0.3 }) ? faker.date.recent() : null
    }));

    await db.insert(schema.notifications).values(notificationData);
    console.log(`✅ Created ${notificationData.length} notifications`);
  }
}