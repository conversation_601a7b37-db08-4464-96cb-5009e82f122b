import 'dotenv/config';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { eq, inArray, not } from 'drizzle-orm';
import { faker } from '@faker-js/faker';
import { readFileSync } from 'fs';
import { join } from 'path';
import * as schema from '../schema';
import * as relations from '../relations';
import { logger } from '../../utils/logger.js';

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql, { schema: { ...schema, ...relations } });

// Set faker seed for consistent results
faker.seed(42);

logger.info('🌱 Starting idempotent database seeding...', { seederStart: true });

// Configuration
const FORCE_CLEAR = process.env.FORCE_CLEAR === 'true';
const SKIP_CLEARING = process.env.SKIP_CLEARING === 'true';

// Always clear database before seeding unless explicitly skipped
if (!SKIP_CLEARING) {
  logger.info('🧹 Clearing database before seeding...', { databaseClear: true });
  await clearDatabase();
}

// Load superUsers
const superUsers = loadSuperUsers();
logger.info(`📋 Loaded ${superUsers.length} super users from config`, { superUserCount: superUsers.length });

// Seed data idempotently
await seedDatabase();

logger.info('✅ Database seeding completed successfully!', { seederComplete: true });

/**
 * Loads super user records from the `superUsers.json` file in the seeder directory.
 *
 * Returns an array of super user objects, or an empty array if the file is missing or invalid.
 */
function loadSuperUsers(): any[] {
  try {
    const superUsersPath = join(process.cwd(), 'src/db/seeder/superUsers.json');
    const data = readFileSync(superUsersPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    logger.warn('⚠️  Could not load superUsers.json, using empty array', { configFile: 'superUsers.json' });
    return [];
  }
}

/**
 * Deletes all data from every database table in an order that maintains referential integrity, including handling circular dependencies.
 *
 * Prepares the database for reseeding by ensuring all tables, including users and leads, are emptied without violating foreign key constraints.
 */
async function clearDatabase() {
  logger.info('🧹 Clearing existing data...', { operation: 'clearDatabase' });

  // Clear in reverse dependency order to respect foreign key constraints
  await db.delete(schema.proofComments);
  await db.delete(schema.reviewers);
  await db.delete(schema.externalReviewers);
  await db.delete(schema.proofs);

  // Handle circular dependency between generations and generationVersions
  // First, set generationVersionId in generations table to null
  await db.update(schema.generations).set({ generationVersionId: null });

  // Now we can safely delete in order
  await db.delete(schema.creatives);
  await db.delete(schema.generationVersions);
  await db.delete(schema.generations);

  await db.delete(schema.projects);
  await db.delete(schema.retailerProducts);
  await db.delete(schema.brandRetailers);
  await db.delete(schema.retailerSpecs);
  await db.delete(schema.credentialSetRetailers);
  await db.delete(schema.imageMetadata);
  await db.delete(schema.products);
  await db.delete(schema.brandGuidelines);
  await db.delete(schema.brandCategories);
  await db.delete(schema.brandUsers);
  await db.delete(schema.userCredentialSets);
  await db.delete(schema.credentialSets);
  await db.delete(schema.brands);
  await db.delete(schema.categories);
  await db.delete(schema.keywords);
  await db.delete(schema.retailers);
  await db.delete(schema.assets);
  await db.delete(schema.notifications);
  await db.delete(schema.invitations);
  await db.delete(schema.userAccounts);
  await db.delete(schema.userSettings);
  await db.delete(schema.accountSettings);
  await db.delete(schema.accounts);
  await db.delete(schema.leads);
  // Also clear users table now since we'll re-seed them
  await db.delete(schema.users);

  logger.info('✅ Database cleared successfully', { operation: 'clearDatabase', status: 'complete' });
}

/**
 * Generates an array of three random RGB color strings.
 *
 * @returns An array containing three RGB color values in string format.
 */
function generateBrandColors() {
  return [
    faker.color.rgb(),
    faker.color.rgb(),
    faker.color.rgb()
  ];
}

/**
 * Generates a randomized user settings object with preferences for notifications, language, timezone, theme, and date format.
 *
 * @returns An object containing randomized user settings.
 */
function generateUserSettings() {
  return {
    emailNotifications: faker.datatype.boolean(),
    language: faker.helpers.arrayElement(['en-US', 'es-ES', 'fr-FR', 'de-DE']),
    timezone: faker.location.timeZone(),
    theme: faker.helpers.arrayElement(['light', 'dark', 'auto']),
    dateFormat: faker.helpers.arrayElement(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'])
  };
}

function generateAccountSettings() {
  return {
    notifications: {
      email: faker.datatype.boolean(),
      slack: faker.datatype.boolean(),
      webhook: faker.datatype.boolean() ? faker.internet.url() : null
    },
    integrations: {
      stripe: faker.datatype.boolean(),
      auth0: faker.datatype.boolean(),
      analytics: faker.datatype.boolean()
    },
    limits: {
      maxUsers: faker.number.int({ min: 5, max: 100 }),
      maxBrands: faker.number.int({ min: 1, max: 20 }),
      maxProjects: faker.number.int({ min: 10, max: 500 })
    }
  };
}

/**
 * Creates a randomized notification data object with type, action, entity ID, and metadata for seeding or testing purposes.
 *
 * The object includes a notification type, action, a unique entity identifier, and metadata with a user name and recent timestamp.
 *
 * @returns A notification data object with randomized type, action, entityId, and metadata fields.
 */
function generateNotificationData() {
  const types = ['system', 'project', 'brand', 'user', 'billing'];
  return {
    type: faker.helpers.arrayElement(types),
    action: faker.helpers.arrayElement(['created', 'updated', 'deleted', 'approved', 'rejected']),
    entityId: faker.string.uuid(),
    metadata: {
      userName: faker.person.fullName(),
      timestamp: faker.date.recent().toISOString()
    }
  };
}

/**
 * Populates the database with all required entities and relationships in an idempotent manner.
 *
 * Ensures that users, accounts, business entities, relationships, content, workflow, and final entities are created and interconnected without duplicating existing data. This function can be safely run multiple times to maintain consistent and realistic test data for development or testing environments.
 */
async function seedDatabase() {
  logger.info('🌱 Seeding database with idempotent approach...', { operation: 'seedDatabase' });

  // Phase 1: Ensure super users exist and get all users
  const users = await ensureSuperUsers();

  // Phase 2: Core entities (idempotent)
  const accounts = await ensureAccounts(users);
  await ensureUserAccounts(users, accounts);
  await ensureAccountSettings(accounts);
  await ensureUserSettings(users);

  // Phase 3: Business entities (idempotent)
  const categories = await ensureCategories(accounts);
  const keywords = await ensureKeywords(accounts);
  const brands = await ensureBrands(accounts);
  const credentialSets = await ensureCredentialSets(accounts, users);
  const retailers = await ensureRetailers();

  // Update some brands with primaryCredentialId
  await updateBrandsWithCredentials(brands, credentialSets);

  // Phase 4: Relationships (idempotent)
  await ensureBrandCategories(brands, categories);
  await ensureBrandUsers(brands, users);
  await ensureUserCredentialSets(users, credentialSets);
  await ensureCredentialSetRetailers(credentialSets, retailers);
  await ensureBrandRetailers(brands, retailers);

  // Phase 5: Content & workflow (idempotent)
  const assets = await ensureAssets(accounts);
  const products = await ensureProducts(accounts, brands);
  await ensureRetailerProducts(retailers, products);
  await ensureImageMetadata(products);
  const projects = await ensureProjects(brands, users, retailers);
  const generations = await ensureGenerations(products, keywords, projects, brands);
  const creatives = await ensureCreatives(brands, users, generations, projects);

  // Phase 6: Review workflow (idempotent)
  const generationVersions = await ensureGenerationVersions(generations, users);
  const proofs = await ensureProofs(generationVersions);
  const reviewers = await ensureReviewers(proofs, users);
  const externalReviewers = await ensureExternalReviewers(accounts, users, retailers);
  await ensureProofComments(proofs, reviewers);

  // Phase 7: Final entities (idempotent)
  await ensureNotifications(users, accounts);
  await ensureInvitations(accounts, brands);
  await ensureBrandGuidelines(brands, users);
  await ensureLeads();

  logger.info('✅ All data seeded successfully with idempotent approach', { operation: 'seedDatabase', status: 'complete' });
}

/**
 * Ensures all configured super users exist in the database and adds additional fake users if needed to reach a minimum user count.
 *
 * Inserts any missing super users from configuration and supplements with randomly generated users to guarantee at least 8 users in the database.
 *
 * @returns An array of all user records currently in the database
 */
async function ensureSuperUsers() {
  console.log('👥 Ensuring super users exist...');

  const existingUsers = await db.select().from(schema.users);
  const existingAuth0Ids = new Set(existingUsers.map(u => u.auth0Id));

  console.log(`Found ${existingUsers.length} existing users`);

  // Insert missing super users
  const missingUsers = superUsers.filter((su: any) => !existingAuth0Ids.has(su.auth0_id));

  if (missingUsers.length > 0) {
    console.log(`🔧 Inserting ${missingUsers.length} missing super users...`);

    const superUserData = missingUsers.map((su: any) => ({
      id: su.id,
      auth0Id: su.auth0_id,
      email: su.email,
      name: su.name,
      isVerified: su.is_verified,
      jobTitle: su.job_title,
      phone: su.phone,
      status: su.status as 'active' | 'inactive' | 'pending',
      language: su.language,
      timeZone: su.time_zone,
      isBetaUser: su.is_beta_user,
      betaKeyId: su.beta_key_id,
      createdAt: new Date(su.created_at),
      updatedAt: new Date(su.updated_at)
    }));

    await db.insert(schema.users).values(superUserData);
    console.log(`✅ Inserted ${missingUsers.length} super users`);
  }

  // Add some additional faker users if we don't have enough for realistic scenarios
  const allUsers = await db.select().from(schema.users);
  if (allUsers.length < 8) {
    const additionalUsers = Array.from({ length: 8 - allUsers.length }, () => ({
      auth0Id: `auth0|${faker.string.alphanumeric(24)}`,
      email: faker.internet.email(),
      name: faker.person.fullName(),
      isVerified: faker.datatype.boolean({ probability: 0.8 }),
      jobTitle: faker.person.jobTitle(),
      phone: faker.phone.number(),
      status: faker.helpers.arrayElement(['active', 'inactive', 'pending'] as const),
      language: faker.helpers.arrayElement(['en-US', 'es-ES', 'fr-FR']),
      timeZone: faker.location.timeZone(),
      isBetaUser: faker.datatype.boolean({ probability: 0.3 }),
      betaKeyId: faker.datatype.boolean({ probability: 0.2 }) ? faker.string.uuid() : null,
    }));

    await db.insert(schema.users).values(additionalUsers);
    console.log(`✅ Added ${additionalUsers.length} additional faker users`);
  }

  const finalUsers = await db.select().from(schema.users);
  console.log(`👥 Total users available: ${finalUsers.length}`);
  return finalUsers;
}

/**
 * Ensures that at least three accounts exist in the database, creating new accounts with randomized data if none are present.
 *
 * @param users - The list of user objects to assign as account owners
 * @returns An array of all existing and newly created account records
 */
async function ensureAccounts(users: any[]) {
  console.log('🏢 Ensuring accounts exist...');

  const existingAccounts = await db.select().from(schema.accounts);
  console.log(`Found ${existingAccounts.length} existing accounts`);

  if (existingAccounts.length === 0) {
    const accountData = Array.from({ length: 3 }, () => ({
      name: faker.company.name(),
      type: faker.helpers.arrayElement(['agency', '3p', '1p'] as const),
      isTrial: faker.datatype.boolean({ probability: 0.3 }),
      seats: faker.number.int({ min: 1, max: 50 }),
      ownerId: faker.helpers.arrayElement(users).id,
      status: faker.helpers.arrayElement(['active', 'inactive'] as const),
      stripeCustomerId: faker.datatype.boolean({ probability: 0.7 }) ? `cus_${faker.string.alphanumeric(14)}` : null,
      validSubscription: faker.datatype.boolean({ probability: 0.8 }),
      hasPaymentMethod: faker.datatype.boolean({ probability: 0.7 }),
      onboardingStep: faker.number.int({ min: 0, max: 5 }),
    }));

    const newAccounts = await db.insert(schema.accounts).values(accountData).returning();
    console.log(`✅ Created ${newAccounts.length} new accounts`);
    return [...existingAccounts, ...newAccounts];
  }

  return existingAccounts;
}

/**
 * Creates user-account relationships to ensure every account has at least one user (the owner), grants all superusers access to all accounts, and adds additional unique user-account associations with varied roles and statuses.
 *
 * Guarantees that superusers (first four users) have access to all accounts except those they own, and that no duplicate relationships are created.
 */
async function ensureUserAccounts(users: any[], accounts: any[]) {
  console.log('🔗 Seeding user accounts...');

  const userAccountData = [];

  // Ensure each account has at least one user (the owner)
  for (const account of accounts) {
    userAccountData.push({
      userId: account.ownerId,
      accountId: account.id,
      role: 'owner',
      status: 'active'
    });
  }

  // Ensure all superusers (first 4 users) get access to accounts
  const superUsers = users.slice(0, 4); // First 4 are superusers from config
  for (const superUser of superUsers) {
    for (const account of accounts) {
      // Skip if this user is already the owner of this account
      if (account.ownerId === superUser.id) continue;

      // Check if relationship already exists
      if (!userAccountData.some(ua => ua.userId === superUser.id && ua.accountId === account.id)) {
        userAccountData.push({
          userId: superUser.id,
          accountId: account.id,
          role: faker.helpers.arrayElement(['admin', 'member']),
          status: 'active'
        });
      }
    }
  }

  // Add additional user-account relationships for remaining users
  const remainingUsers = users.slice(4); // Faker users
  for (let i = 0; i < 15; i++) {
    const user = faker.helpers.arrayElement(remainingUsers);
    const account = faker.helpers.arrayElement(accounts);

    // Avoid duplicate relationships
    if (!userAccountData.some(ua => ua.userId === user.id && ua.accountId === account.id)) {
      userAccountData.push({
        userId: user.id,
        accountId: account.id,
        role: faker.helpers.arrayElement(['admin', 'member']), // Fixed: removed 'viewer', it doesn't exist in schema
        status: faker.helpers.arrayElement(['active', 'pending', 'inactive'])
      });
    }
  }

  await db.insert(schema.userAccounts).values(userAccountData);
  console.log(`✅ Created ${userAccountData.length} user account relationships`);
}

/**
 * Inserts account settings for each provided account using generated settings data.
 *
 * @param accounts - The list of account objects to associate with new settings
 */
async function ensureAccountSettings(accounts: any[]) {
  console.log('⚙️ Seeding account settings...');

  const settingsData = accounts.map(account => ({
    accountId: account.id,
    settings: generateAccountSettings()
  }));

  await db.insert(schema.accountSettings).values(settingsData);
  console.log(`✅ Created ${settingsData.length} account settings`);
}

/**
 * Inserts user settings for each provided user with randomly generated values.
 *
 * @param users - Array of user objects to associate with new user settings
 */
async function ensureUserSettings(users: any[]) {
  console.log('👤 Seeding user settings...');

  const settingsData = users.map(user => ({
    userId: user.id,
    settings: generateUserSettings()
  }));

  await db.insert(schema.userSettings).values(settingsData);
  console.log(`✅ Created ${settingsData.length} user settings`);
}

/**
 * Ensures that a predefined set of product categories exists in the database, each linked to a random account.
 *
 * @param accounts - The list of account objects to associate with categories
 * @returns The array of created category records
 */
async function ensureCategories(accounts: any[]) {
  console.log('📂 Seeding categories...');

  const categoryNames = [
    'Electronics', 'Fashion', 'Home & Garden', 'Sports & Outdoors',
    'Beauty & Personal Care', 'Books & Media', 'Automotive', 'Food & Beverage',
    'Health & Wellness', 'Toys & Games', 'Pet Supplies', 'Office Supplies'
  ];

  const categoryData = categoryNames.map(name => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    name
  }));

  const categories = await db.insert(schema.categories).values(categoryData).returning();
  console.log(`✅ Created ${categories.length} categories`);
  return categories;
}

/**
 * Ensures that a set of predefined keywords with randomized include and exclude metadata exist for the provided accounts.
 *
 * Generates 20 keywords, each linked to a random account, and inserts them into the database with associated tone, word, and consideration fields for both inclusion and exclusion criteria.
 *
 * @param accounts - The list of account objects to associate keywords with
 * @returns The array of created keyword records
 */
async function ensureKeywords(accounts: any[]) {
  console.log('🔍 Seeding keywords...');

  const keywordTerms = [
    'summer sale', 'new arrival', 'best seller', 'limited edition', 'premium quality',
    'eco friendly', 'organic', 'handmade', 'vintage', 'modern design',
    'affordable', 'luxury', 'trending', 'seasonal', 'exclusive',
    'fast shipping', 'free returns', 'customer favorite', 'top rated', 'bestselling'
  ];

  const keywordData = keywordTerms.map(term => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    keyword: term,
    include: {
      tone: faker.helpers.arrayElement(['casual', 'professional', 'friendly', 'urgent']),
      word: faker.lorem.word(),
      consideration: faker.lorem.sentence()
    },
    exclude: {
      tone: faker.helpers.arrayElement(['aggressive', 'boring', 'confusing']),
      word: faker.lorem.word(),
      consideration: faker.lorem.sentence()
    }
  }));

  const keywords = await db.insert(schema.keywords).values(keywordData).returning();
  console.log(`✅ Created ${keywords.length} keywords`);
  return keywords;
}

/**
 * Creates 8 brands with randomized attributes and associates each with a random account.
 *
 * Each brand includes generated names, descriptions, colors, logos, notes, and product counts. The `primaryCredentialId` is set to null for all brands.
 *
 * @param accounts - The list of account objects to associate with the new brands
 * @returns An array of the created brand records
 */
async function ensureBrands(accounts: any[]) {
  console.log('🏷️ Seeding brands...');

  const brandData = Array.from({ length: 8 }, () => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    name: faker.company.name(),
    description: faker.company.catchPhrase(),
    colors: generateBrandColors(),
    logoAssetUrl: faker.image.urlLoremFlickr({ category: 'business' }),
    primaryCredentialId: null, // Will be set after creating credential sets
    notes: faker.lorem.sentence(),
    productCount: faker.number.int({ min: 5, max: 150 })
  }));

  const brands = await db.insert(schema.brands).values(brandData).returning();
  console.log(`✅ Created ${brands.length} brands`);
  return brands;
}

/**
 * Inserts a set of randomized credential sets, including both Walmart API and plugin credentials, each linked to a random account and admin user.
 *
 * Generates four Walmart API credential sets with varied credential fields and three plugin credential sets with plugin-specific credentials. Each credential set includes randomized status flags and metadata.
 *
 * @returns An array of the created credential set records.
 */
async function ensureCredentialSets(accounts: any[], users: any[]) {
  console.log('🔐 Seeding credential sets...');

  const credentialData = [];

  // Create 4 Walmart API credential sets
  for (let i = 0; i < 4; i++) {
    credentialData.push({
      accountId: faker.helpers.arrayElement(accounts).id,
      name: `${faker.company.name()} Walmart API`,
      type: faker.helpers.arrayElement(['wm_display', 'wm_supplier', 'wm_marketplace', 'wm_sponsored']),
      credentials: {
        advertiserId: faker.datatype.boolean({ probability: 0.8 }) ? faker.string.alphanumeric(12) : null,
        nonce: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(24) : null,
        state: faker.datatype.boolean({ probability: 0.5 }) ? faker.string.alphanumeric(16) : null,
        partnerId: faker.datatype.boolean({ probability: 0.8 }) ? faker.string.alphanumeric(10) : null,
        accessToken: faker.datatype.boolean({ probability: 0.7 }) ? faker.string.alphanumeric(32) : null,
        refreshToken: faker.datatype.boolean({ probability: 0.7 }) ? faker.string.alphanumeric(32) : null,
        expiresAt: faker.datatype.boolean({ probability: 0.7 }) ? faker.date.future().toISOString() : null
      },
      adminId: faker.helpers.arrayElement(users).id,
      isShared: faker.datatype.boolean({ probability: 0.8 }),
      isActive: faker.datatype.boolean({ probability: 0.9 })
    });
  }

  // Create 3 plugin credential sets for demo purposes
  const pluginNames = ['Analytics Dashboard', 'Social Media Manager', 'Inventory Sync'];
  for (const pluginName of pluginNames) {
    credentialData.push({
      accountId: faker.helpers.arrayElement(accounts).id,
      name: pluginName,
      type: 'plugin',
      credentials: {
        apiKey: faker.string.alphanumeric(32),
        clientId: faker.string.uuid(),
        clientSecret: faker.string.alphanumeric(64),
        webhookUrl: faker.internet.url(),
        isConnected: faker.datatype.boolean({ probability: 0.7 }),
        lastSync: faker.datatype.boolean({ probability: 0.6 }) ? faker.date.recent().toISOString() : null
      },
      adminId: faker.helpers.arrayElement(users).id,
      isShared: faker.datatype.boolean({ probability: 0.6 }),
      isActive: faker.datatype.boolean({ probability: 0.8 })
    });
  }

  const credentialSets = await db.insert(schema.credentialSets).values(credentialData).returning();
  console.log(`✅ Created ${credentialData.length} credential sets (${credentialData.filter(c => c.type === 'plugin').length} plugins)`);
  return credentialSets;
}

/**
 * Inserts Walmart as the sole retailer in the database and returns the created retailer record.
 *
 * @returns An array containing the Walmart retailer record.
 */
async function ensureRetailers() {
  console.log('🛒 Seeding retailers...');

  const retailerData = [
    {
      name: 'Walmart',
      url: 'https://walmart.com',
      slug: 'walmart',
      logoUrl: faker.image.urlLoremFlickr({ category: 'business' })
    }
  ];

  const retailers = await db.insert(schema.retailers).values(retailerData).returning();
  console.log(`✅ Created ${retailers.length} retailers (Walmart only)`);
  return retailers;
}

/**
 * Creates brand-category relationships by associating each brand with 1 to 3 randomly selected categories.
 *
 * Inserts new associations for each brand without checking for existing duplicates.
 */
async function ensureBrandCategories(brands: any[], categories: any[]) {
  console.log('🔗 Seeding brand categories...');

  const brandCategoryData = [];

  // Each brand gets 1-3 categories
  for (const brand of brands) {
    const numCategories = faker.number.int({ min: 1, max: 3 });
    const selectedCategories = faker.helpers.arrayElements(categories, numCategories);

    for (const category of selectedCategories) {
      brandCategoryData.push({
        brandId: brand.id,
        categoryId: category.id
      });
    }
  }

  await db.insert(schema.brandCategories).values(brandCategoryData);
  console.log(`✅ Created ${brandCategoryData.length} brand category relationships`);
}

/**
 * Creates brand-user relationships by assigning each brand to multiple users with specific roles.
 *
 * Ensures that superusers are assigned to 2–4 brands each with roles including 'owner', 'admin', or 'member', and that every brand has 1–3 additional users with roles of 'admin' or 'member'. Avoids duplicate brand-user assignments.
 */
async function ensureBrandUsers(brands: any[], users: any[]) {
  console.log('👥 Seeding brand users...');

  const brandUserData = [];
  const superUsers = users.slice(0, 4); // First 4 are superusers from config
  const remainingUsers = users.slice(4); // Faker users

  // Ensure superusers get access to multiple brands
  for (const superUser of superUsers) {
    // Give each superuser access to 2-4 brands
    const numBrands = faker.number.int({ min: 2, max: 4 });
    const selectedBrands = faker.helpers.arrayElements(brands, numBrands);

    for (const brand of selectedBrands) {
      brandUserData.push({
        brandId: brand.id,
        userId: superUser.id,
        role: faker.helpers.arrayElement(['owner', 'admin', 'member']) // Superusers can be owners
      });
    }
  }

  // Each brand gets additional users (1-3 more beyond superusers)
  for (const brand of brands) {
    const numAdditionalUsers = faker.number.int({ min: 1, max: 3 });
    const availableUsers = [...superUsers, ...remainingUsers];
    const selectedUsers = faker.helpers.arrayElements(availableUsers, numAdditionalUsers);

    for (const user of selectedUsers) {
      // Avoid duplicate relationships
      if (!brandUserData.some(bu => bu.brandId === brand.id && bu.userId === user.id)) {
        brandUserData.push({
          brandId: brand.id,
          userId: user.id,
          role: faker.helpers.arrayElement(['admin', 'member']) // Regular users get admin or member
        });
      }
    }
  }

  await db.insert(schema.brandUsers).values(brandUserData);
  console.log(`✅ Created ${brandUserData.length} brand user relationships`);
}

/**
 * Associates each user with one or two randomly selected credential sets by creating user-credential set relationships in the database.
 *
 * @param users - The list of users to assign credential sets to
 * @param credentialSets - The available credential sets for assignment
 */
async function ensureUserCredentialSets(users: any[], credentialSets: any[]) {
  console.log('🔐 Seeding user credential sets...');

  const userCredentialData = [];

  // Each user gets access to 1-2 credential sets
  for (const user of users) {
    const numCredSets = faker.number.int({ min: 1, max: 2 });
    const selectedCredSets = faker.helpers.arrayElements(credentialSets, numCredSets);

    for (const credSet of selectedCredSets) {
      userCredentialData.push({
        userId: user.id,
        credentialSetId: credSet.id
      });
    }
  }

  await db.insert(schema.userCredentialSets).values(userCredentialData);
  console.log(`✅ Created ${userCredentialData.length} user credential set relationships`);
}

/**
 * Creates credential set-retailer associations for all provided credential sets and retailers.
 *
 * For each credential set, establishes a relationship with every retailer in the list.
 */
async function ensureCredentialSetRetailers(credentialSets: any[], retailers: any[]) {
  console.log('🛒 Seeding credential set retailers...');

  const credRetailerData = [];

  // Each credential set gets access to all available retailers (since we only have Walmart now)
  for (const credSet of credentialSets) {
    for (const retailer of retailers) {
      credRetailerData.push({
        credentialSetId: credSet.id,
        retailerId: retailer.id
      });
    }
  }

  await db.insert(schema.credentialSetRetailers).values(credRetailerData);
  console.log(`✅ Created ${credRetailerData.length} credential set retailer relationships`);
}

/**
 * Ensures that every brand is associated with all available retailers by creating brand-retailer relationships in the database.
 *
 * @param brands - The list of brands to associate with retailers
 * @param retailers - The list of retailers to link with brands
 */
async function ensureBrandRetailers(brands: any[], retailers: any[]) {
  console.log('🛒 Seeding brand retailers...');

  const brandRetailerData = [];

  // Each brand is available on all available retailers (since we only have Walmart now)
  for (const brand of brands) {
    for (const retailer of retailers) {
      brandRetailerData.push({
        brandId: brand.id,
        retailerId: retailer.id
      });
    }
  }

  await db.insert(schema.brandRetailers).values(brandRetailerData);
  console.log(`✅ Created ${brandRetailerData.length} brand retailer relationships`);
}

/**
 * Associates each product with all available retailers by creating retailer-product relationships.
 *
 * For every product, creates a record linking it to each retailer in the database.
 */
async function ensureRetailerProducts(retailers: any[], products: any[]) {
  console.log('🛍️ Seeding retailer products...');

  const retailerProductData = [];

  // Each product is available on all available retailers (since we only have Walmart now)
  for (const product of products) {
    for (const retailer of retailers) {
      retailerProductData.push({
        retailerId: retailer.id,
        productId: product.productId
      });
    }
  }

  await db.insert(schema.retailerProducts).values(retailerProductData);
  console.log(`✅ Created ${retailerProductData.length} retailer product relationships`);
}

/**
 * Ensures that 25 asset records exist by inserting new assets with random URLs linked to random accounts.
 *
 * @param accounts - The list of account objects to associate with the assets
 * @returns The array of created asset records
 */
async function ensureAssets(accounts: any[]) {
  console.log('🖼️ Seeding assets...');

  const assetData = Array.from({ length: 25 }, () => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    url: faker.image.url()
  }));

  const assets = await db.insert(schema.assets).values(assetData).returning();
  console.log(`✅ Created ${assets.length} assets`);
  return assets;
}

/**
 * Ensures that a set of products exist in the database by inserting 30 products with randomized attributes linked to provided accounts and brands.
 *
 * @param accounts - The list of account objects to associate products with
 * @param brands - The list of brand objects to associate products with
 * @returns The array of created product records
 */
async function ensureProducts(accounts: any[], brands: any[]) {
  console.log('📦 Seeding products...');

  const productData = Array.from({ length: 30 }, () => ({
    productId: faker.string.alphanumeric(10),
    productTitle: faker.commerce.productName(),
    productType: faker.helpers.arrayElement(['physical', 'digital', 'service']),
    category: faker.commerce.department(),
    thumbnailUrl: faker.image.urlLoremFlickr({ category: 'product' }),
    shortDescription: faker.commerce.productDescription(),
    longDescription: faker.lorem.paragraphs(2),
    genAiDescription: faker.lorem.paragraph(),
    specifications: faker.helpers.arrayElements([
      'waterproof', 'wireless', 'rechargeable', 'portable', 'eco-friendly'
    ], { min: 1, max: 3 }),
    productHighlights: faker.helpers.arrayElements([
      'Best seller', 'Editor\'s choice', 'Customer favorite', 'New arrival'
    ], { min: 1, max: 2 }),
    classType: faker.helpers.arrayElement(['A', 'B', 'C']),
    upc: faker.string.numeric(12),
    images: Array.from({ length: faker.number.int({ min: 1, max: 4 }) }, () =>
      faker.image.urlLoremFlickr({ category: 'product' })
    ),
    customImages: [],
    accountId: faker.helpers.arrayElement(accounts).id,
    brandId: faker.helpers.arrayElement(brands).id,
    pdpSummary: faker.lorem.sentence()
  }));

  const products = await db.insert(schema.products).values(productData).returning();
  console.log(`✅ Created ${products.length} products`);
  return products;
}

/**
 * Inserts 1 to 4 image metadata records for each product, associating images with products and randomizing image details.
 *
 * @param products - The list of products to generate image metadata for
 */
async function ensureImageMetadata(products: any[]) {
  console.log('🖼️ Seeding image metadata...');

  const imageData = [];

  // Each product gets 1-4 images
  for (const product of products) {
    const numImages = faker.number.int({ min: 1, max: 4 });

    for (let i = 0; i < numImages; i++) {
      imageData.push({
        productId: product.productId,
        imageUrl: faker.image.urlLoremFlickr({ category: 'product' }),
        imageText: faker.lorem.sentence(),
        classification: faker.helpers.arrayElement(['product', 'lifestyle', 'other'] as const)
      });
    }
  }

  await db.insert(schema.imageMetadata).values(imageData);
  console.log(`✅ Created ${imageData.length} image metadata records`);
}

/**
 * Ensures that a set of projects exist in the database, creating 15 new projects with randomized data if needed.
 *
 * Each project is associated with a random brand, user, and retailer, and includes campaign and status information.
 *
 * @returns The array of created project records.
 */
async function ensureProjects(brands: any[], users: any[], retailers: any[]) {
  console.log('📋 Seeding projects...');

  const projectData = Array.from({ length: 15 }, () => ({
    brandId: faker.helpers.arrayElement(brands).id,
    name: faker.company.buzzPhrase(),
    wmCampaigns: {
      [faker.string.alphanumeric(10)]: {
        name: faker.company.catchPhrase(),
        adGroups: {
          [faker.string.alphanumeric(8)]: faker.commerce.department(),
          [faker.string.alphanumeric(8)]: faker.commerce.department()
        }
      }
    },
    retailerId: faker.helpers.arrayElement(retailers).id,
    createdBy: faker.helpers.arrayElement(users).id,
    currentStep: faker.number.int({ min: 0, max: 5 }),
    status: faker.helpers.arrayElement(['in_progress', 'in_review', 'published', 'inactive', 'archived'] as const),
  }));

  const projects = await db.insert(schema.projects).values(projectData).returning();
  console.log(`✅ Created ${projects.length} projects`);
  return projects;
}

/**
 * Inserts 35 generation records, each linking a random product, keyword, project, and brand.
 *
 * @param products - List of product objects to associate with generations
 * @param keywords - List of keyword objects to associate with generations
 * @param projects - List of project objects to associate with generations
 * @param brands - List of brand objects to associate with generations
 * @returns An array of the created generation records
 */
async function ensureGenerations(products: any[], keywords: any[], projects: any[], brands: any[]) {
  console.log('⚡ Seeding generations...');

  const generationData = Array.from({ length: 35 }, () => ({
    productId: faker.helpers.arrayElement(products).id,
    keywordId: faker.helpers.arrayElement(keywords).id,
    projectId: faker.helpers.arrayElement(projects).id,
    brandId: faker.helpers.arrayElement(brands).id
  }));

  const generations = await db.insert(schema.generations).values(generationData).returning();
  console.log(`✅ Created ${Array.isArray(generations) ? generations.length : 'unknown'} generations`);
  return Array.isArray(generations) ? generations : [];
}

/**
 * Ensures that a set of creative records exist by inserting 50 creatives with randomized data linked to provided brands, users, generations, and projects.
 *
 * @param brands - Array of brand objects to associate with creatives
 * @param users - Array of user objects to associate with creatives
 * @param generations - Array of generation objects to associate with creatives
 * @param projects - Array of project objects to associate with creatives
 * @returns An array of the created creative records
 */
async function ensureCreatives(brands: any[], users: any[], generations: any[], projects: any[]) {
  console.log('🎨 Seeding creatives...');

  const creativeData = Array.from({ length: 50 }, () => ({
    projectId: faker.helpers.arrayElement(projects).id,
    wmCreativeId: faker.string.alphanumeric(12),
    brandId: faker.helpers.arrayElement(brands).id,
    userId: faker.helpers.arrayElement(users).id,
    generationId: faker.helpers.arrayElement(generations).id,
    previewUrls: {
      thumbnail: faker.image.url(),
      fullSize: faker.image.url()
    },
    previewStatus: faker.helpers.arrayElement(['PENDING', 'COMPLETED', 'FAILED']),
    folderId: faker.datatype.boolean({ probability: 0.7 }) ? faker.string.uuid() : null,
    status: faker.helpers.arrayElement(['DRAFT', 'REVIEW', 'APPROVED', 'REJECTED']),
    keyword: faker.helpers.arrayElement(['sale', 'new', 'bestseller', 'premium']),
    adUnits: {
      banner: {
        width: faker.number.int({ min: 300, max: 1920 }),
        height: faker.number.int({ min: 250, max: 1080 }),
        format: faker.helpers.arrayElement(['jpeg', 'png', 'gif'])
      },
      video: {
        duration: faker.number.int({ min: 15, max: 60 }),
        format: faker.helpers.arrayElement(['mp4', 'webm'])
      }
    },
    reviewComments: Array.from({ length: faker.number.int({ min: 0, max: 3 }) }, () => ({
      comment: faker.lorem.sentence(),
      reviewer: faker.person.fullName(),
      timestamp: faker.date.recent().toISOString()
    }))
  }));

  const creatives = await db.insert(schema.creatives).values(creativeData).returning();
  console.log(`✅ Created ${creatives.length} creatives`);
  return creatives;
}

/**
 * Creates 1 to 3 generation version records for each provided generation, assigning randomized content and a creator from the user list.
 *
 * For every generation, generates multiple version entries with randomized unit fields, ad previews, preview image positions, and creator assignment. Returns all created generation version records.
 *
 * @param generations - The list of generation entities to create versions for
 * @param users - The list of users to randomly assign as creators of the versions
 * @returns The array of newly created generation version records
 */
async function ensureGenerationVersions(generations: any[], users: any[]) {
  console.log('🔄 Seeding generation versions...');

  const versionData = [];

  // Each generation gets 1-3 versions
  for (const generation of generations) {
    const numVersions = faker.number.int({ min: 1, max: 3 });

    for (let i = 0; i < numVersions; i++) {
      versionData.push({
        generationId: generation.id,
        sessionId: faker.string.uuid(),
        unitFields: {
          headline: faker.company.catchPhrase(),
          description: faker.lorem.sentence(),
          cta: faker.helpers.arrayElement(['Shop Now', 'Learn More', 'Buy Now', 'Discover'])
        },
        hasCompleted: faker.datatype.boolean({ probability: 0.7 }),
        createdBy: faker.helpers.arrayElement(users).id,
        version: i + 1,
        adPreviews: Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => ({
          id: faker.string.uuid(),
          url: faker.image.url(),
          size: faker.helpers.arrayElement(['300x250', '728x90', '320x50'])
        })),
        previewImagePositions: {
          primary: { x: 0, y: 0 },
          secondary: { x: 100, y: 100 }
        },
        applyToAll: faker.datatype.boolean({ probability: 0.3 })
      });
    }
  }

  const versions = await db.insert(schema.generationVersions).values(versionData).returning();
  console.log(`✅ Created ${Array.isArray(versions) ? versions.length : 'unknown'} generation versions`);
  return Array.isArray(versions) ? versions : [];
}

/**
 * Creates a proof record for each generation version, generating randomized titles, descriptions, ad unit IDs, and reviewer lists.
 *
 * @param generationVersions - Generation version objects to associate with new proofs
 * @returns An array of created proof records, or an empty array if no generation versions are provided
 */
async function ensureProofs(generationVersions: any[]) {
  console.log('📋 Seeding proofs...');

  if (!Array.isArray(generationVersions) || generationVersions.length === 0) {
    console.log('⚠️ No generation versions provided, skipping proof creation');
    return [];
  }

  const proofData = generationVersions.map(version => ({
    generationVersionId: version.id,
    title: faker.company.buzzPhrase(),
    emailDescription: faker.lorem.paragraph(),
    adUnitIds: faker.helpers.arrayElements([
      faker.string.alphanumeric(8),
      faker.string.alphanumeric(8),
      faker.string.alphanumeric(8)
    ], { min: 1, max: 3 }),
    reviewers: {
      internal: Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => ({
        id: faker.string.uuid(),
        name: faker.person.fullName(),
        email: faker.internet.email()
      })),
      external: Array.from({ length: faker.number.int({ min: 0, max: 2 }) }, () => ({
        id: faker.string.uuid(),
        name: faker.person.fullName(),
        email: faker.internet.email()
      }))
    }
  }));

  const proofs = await db.insert(schema.proofs).values(proofData).returning();
  console.log(`✅ Created ${Array.isArray(proofs) ? proofs.length : 'unknown'} proofs`);
  return Array.isArray(proofs) ? proofs : [];
}

/**
 * Ensures that each proof has 1 to 3 associated reviewers, creating reviewer records with randomized user assignments and statuses.
 *
 * @param proofs - The list of proof entities to assign reviewers to
 * @param users - The list of user entities from which reviewers and requesters are selected
 * @returns The array of created reviewer records
 */
async function ensureReviewers(proofs: any[], users: any[]) {
  console.log('👁️ Seeding reviewers...');

  const reviewerData = [];

  // Each proof gets 1-3 reviewers
  for (const proof of proofs) {
    const numReviewers = faker.number.int({ min: 1, max: 3 });

    for (let i = 0; i < numReviewers; i++) {
      const reviewer = faker.helpers.arrayElement(users);
      const requester = faker.helpers.arrayElement(users);

      reviewerData.push({
        proofId: proof.id,
        name: reviewer.name,
        email: reviewer.email,
        userId: reviewer.id,
        isExternal: faker.datatype.boolean({ probability: 0.2 }),
        status: faker.helpers.arrayElement(['pending', 'approved', 'rejected', 'needs_changes']),
        requestedBy: requester.id,
        lastRequestedAt: faker.date.recent()
      });
    }
  }

  const reviewers = await db.insert(schema.reviewers).values(reviewerData).returning();
  console.log(`✅ Created ${reviewers.length} reviewers`);
  return reviewers;
}

/**
 * Ensures that a set of external reviewers exists by inserting 12 randomly generated external reviewer records linked to accounts, users, and retailers.
 *
 * @returns The array of created external reviewer records.
 */
async function ensureExternalReviewers(accounts: any[], users: any[], retailers: any[]) {
  console.log('🔗 Seeding external reviewers...');

  const externalReviewerData = Array.from({ length: 12 }, () => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    jobTitle: faker.person.jobTitle(),
    name: faker.person.fullName(),
    email: faker.internet.email(),
    requestedByUserId: faker.helpers.arrayElement(users).id,
    retailerId: faker.helpers.arrayElement(retailers).id
  }));

  const externalReviewers = await db.insert(schema.externalReviewers).values(externalReviewerData).returning();
  console.log(`✅ Created ${externalReviewers.length} external reviewers`);
  return externalReviewers;
}

/**
 * Populates each proof with parent and reply comments to simulate realistic review discussions.
 *
 * For each proof, creates 1–5 parent comments from its reviewers, then adds reply comments to about 30% of those parents. Ensures no duplicate comments are seeded.
 */
async function ensureProofComments(proofs: any[], reviewers: any[]) {
  console.log('💬 Seeding proof comments...');

  const commentData = [];

  // Each proof gets 1-5 comments
  for (const proof of proofs) {
    const proofReviewers = reviewers.filter(r => r.proofId === proof.id);
    if (proofReviewers.length === 0) continue;

    const numComments = faker.number.int({ min: 1, max: 5 });

    for (let i = 0; i < numComments; i++) {
      const comment = {
        proofId: proof.id,
        reviewerId: faker.helpers.arrayElement(proofReviewers).id,
        comment: faker.lorem.paragraph(),
        replyToId: null as string | null
      };

      commentData.push(comment);
    }
  }

  // Insert parent comments first
  const insertedComments = await db.insert(schema.proofComments).values(commentData).returning();
  console.log(`✅ Created ${commentData.length} parent proof comments`);

  // Now add some reply comments using actual comment IDs
  const replyCommentData = [];
  const parentComments = faker.helpers.arrayElements(insertedComments, Math.floor(insertedComments.length * 0.3));

  for (const parentComment of parentComments) {
    const proofReviewers = reviewers.filter(r => r.proofId === parentComment.proofId);
    if (proofReviewers.length === 0) continue;

    replyCommentData.push({
      proofId: parentComment.proofId,
      reviewerId: faker.helpers.arrayElement(proofReviewers).id,
      comment: faker.lorem.sentence(),
      replyToId: parentComment.id
    });
  }

  if (replyCommentData.length > 0) {
    await db.insert(schema.proofComments).values(replyCommentData);
    console.log(`✅ Created ${replyCommentData.length} reply proof comments`);
  }
}

/**
 * Inserts 45 notifications with randomized messages and statuses, each linked to a random user and account.
 *
 * Ensures the notifications table contains a diverse set of test notifications for the provided users and accounts.
 */
async function ensureNotifications(users: any[], accounts: any[]) {
  console.log('🔔 Seeding notifications...');

  const notificationData = Array.from({ length: 45 }, () => ({
    userId: faker.helpers.arrayElement(users).id,
    accountId: faker.helpers.arrayElement(accounts).id,
    message: generateNotificationData(),
    status: faker.helpers.arrayElement(['read', 'unread'])
  }));

  await db.insert(schema.notifications).values(notificationData);
  console.log(`✅ Created ${notificationData.length} notifications`);
}

/**
 * Inserts eight invitation records with randomized data, associating each with a provided account and zero or more brands.
 *
 * Each invitation includes an account ID, a list of brand IDs (if available), an email address, a type ('request' or 'invite'), and a status ('pending', 'accepted', or 'rejected').
 */
async function ensureInvitations(accounts: any[], brands: any[]) {
  console.log('✉️ Seeding invitations...');

  // If no brands are provided, fall back to empty brandIds array
  const availableBrands = brands && brands.length > 0 ? brands : [];

  const invitationData = Array.from({ length: 8 }, () => ({
    accountId: faker.helpers.arrayElement(accounts).id,
    brandIds: availableBrands.length > 0
      ? faker.helpers.arrayElements(
          availableBrands.map(brand => brand.id),
          { min: 0, max: Math.min(3, availableBrands.length) }
        )
      : [],
    email: faker.internet.email(),
    type: faker.helpers.arrayElement(['request', 'invite'] as const),
    status: faker.helpers.arrayElement(['pending', 'accepted', 'rejected'] as const)
  }));

  await db.insert(schema.invitations).values(invitationData);
  console.log(`✅ Created ${invitationData.length} invitations`);
}

/**
 * Ensures that each brand has one or two associated brand guideline records with randomized metadata.
 *
 * Inserts brand guideline entries for each brand, assigning a random label, file name, asset URL, status, and uploader from the provided users.
 */
async function ensureBrandGuidelines(brands: any[], users: any[]) {
  console.log('📖 Seeding brand guidelines...');

  const guidelineData = [];

  // Each brand gets 1-2 guideline files
  for (const brand of brands) {
    const numGuidelines = faker.number.int({ min: 1, max: 2 });

    for (let i = 0; i < numGuidelines; i++) {
      guidelineData.push({
        brandId: brand.id,
        label: faker.helpers.arrayElement([
          'Brand Guidelines', 'Logo Usage', 'Color Palette', 'Typography Guide'
        ]),
        fileName: faker.system.fileName(),
        assetUrl: faker.internet.url(),
        status: faker.helpers.arrayElement(['active', 'archived'] as const),
        uploadedBy: faker.helpers.arrayElement(users).id
      });
    }
  }

  await db.insert(schema.brandGuidelines).values(guidelineData);
  console.log(`✅ Created ${guidelineData.length} brand guidelines`);
}

/**
 * Assigns a primary credential set to approximately 60% of brands, selecting a credential set from the same account for each brand.
 *
 * For each chosen brand, updates the brand's `primaryCredentialId` to reference a randomly selected credential set associated with the same account.
 */
async function updateBrandsWithCredentials(brands: any[], credentialSets: any[]) {
  console.log('🔗 Linking brands with credential sets...');

  // Update about 60% of brands with a primary credential set
  const brandsToUpdate = faker.helpers.arrayElements(brands, Math.ceil(brands.length * 0.6));

  for (const brand of brandsToUpdate) {
    // Find credential sets from the same account
    const accountCredSets = credentialSets.filter(cs => cs.accountId === brand.accountId);

    if (accountCredSets.length > 0) {
      const selectedCredSet = faker.helpers.arrayElement(accountCredSets);
      await db.update(schema.brands)
        .set({ primaryCredentialId: selectedCredSet.id })
        .where(eq(schema.brands.id, brand.id));
    }
  }

  console.log(`✅ Updated ${brandsToUpdate.length} brands with primary credential sets`);
}

/**
 * Creates 15 demo lead records for the beta program with realistic user, organization, and invitation data.
 */
async function ensureLeads() {
  console.log('🎯 Seeding leads...');

  const leadsData = Array.from({ length: 15 }, () => ({
    betaId: faker.string.uuid(),
    fullName: faker.person.fullName(),
    email: faker.internet.email(),
    organization: faker.company.name(),
    dssSpend: faker.number.int({ min: 1000, max: 50000 }),
    orgSize: faker.number.int({ min: 5, max: 500 }),
    role: faker.person.jobTitle(),
    inviteStatus: faker.helpers.arrayElement(['pending', 'accepted', 'rejected'] as const),
    betaKeys: faker.helpers.arrayElements(
      Array.from({ length: 5 }, () => faker.string.uuid()),
      { min: 0, max: 3 }
    ),
    sentKeys: Array.from({ length: faker.number.int({ min: 0, max: 2 }) }, () => ({
      key: faker.string.uuid(),
      email: faker.datatype.boolean({ probability: 0.7 }) ? faker.internet.email() : null
    }))
  }));

  await db.insert(schema.leads).values(leadsData);
  console.log(`✅ Created ${leadsData.length} leads`);
}