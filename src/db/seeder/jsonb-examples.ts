
// JSONB examples provided by the user
export const credentialExamples = [
  {
    "apiVersion": "v3",
    "clientType": "DSP_ADVERTISER",
    "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "environment": "production",
    "permissions": ["campaign_management", "reporting", "audience_management"],
    "refreshToken": "refresh_abc123...",
    "wmtPartnerId": "WMT_PARTNER_001",
    "tokenExpiresAt": "2025-06-18T21:35:15.723Z",
    "wmtAdvertiserId": "WMT_ADV_12345678"
  },
  {
    "apiVersion": "v3",
    "clientType": "MARKETPLACE_SELLER",
    "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "environment": "sandbox",
    "permissions": ["inventory_management", "order_management"],
    "refreshToken": "refresh_def456...",
    "wmtPartnerId": "WMT_PARTNER_002",
    "tokenExpiresAt": "2025-05-20T14:22:10.456Z",
    "wmtAdvertiserId": "WMT_ADV_87654321"
  },
  {
    "apiVersion": "v2",
    "clientType": "SUPPLIER",
    "accessToken": "legacy_token_xyz789...",
    "environment": "production",
    "permissions": ["catalog_management"],
    "wmtPartnerId": "WMT_PARTNER_003",
    "tokenExpiresAt": "2025-07-15T09:18:33.891Z"
  }
];

export const accountSettingsExamples = [
  {
    "theme": "light",
    "notifications": true,
    "preference1": "foo",
    "preference2": "goo"
  },
  {
    "theme": "dark",
    "notifications": false,
    "preference1": "bar",
    "preference2": "baz"
  },
  {
    "theme": "auto",
    "notifications": true,
    "preference1": "qux",
    "preference2": "quux"
  }
];

export const brandColorsExamples = [
  { primary: '#2563eb', secondary: '#7c3aed' },
  { primary: '#059669', secondary: '#0891b2' },
  { primary: '#dc2626', secondary: '#ea580c' },
  { primary: '#7c2d12', secondary: '#a16207' },
  { primary: '#1f2937', secondary: '#374151' }
];

export const reviewersExamples = [
  { internal: {}, external: {} },
  {
    internal: { "teamLead": "user123", "reviewer": "user456" },
    external: { "client": "<EMAIL>" }
  },
  {
    internal: { "reviewer": "user789", "approver": "user101" },
    external: {}
  }
];

export const keywordIncludeExcludeExamples = [
  {
    "tone": null,
    "word": null,
    "consideration": null
  },
  {
    "tone": "positive",
    "word": "quality",
    "consideration": "premium"
  },
  {
    "tone": "urgent",
    "word": "sale",
    "consideration": "discount"
  },
  {
    "tone": "neutral",
    "word": "features",
    "consideration": "comparison"
  }
];

export const wmCampaignsExamples = [
  {
    '123': {
      name: 'Holiday Campaign 2024',
      adGroups: { '123': 'Electronics Ad Group', '456': 'Toys Ad Group' }
    },
    '456': {
      name: 'Spring Sale',
      adGroups: { '789': 'Fashion Ad Group' }
    }
  },
  {
    '789': {
      name: 'Back to School',
      adGroups: { '111': 'School Supplies', '222': 'Backpacks', '333': 'Electronics' }
    }
  },
  {}
];
