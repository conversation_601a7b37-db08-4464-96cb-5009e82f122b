import 'dotenv/config';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { eq, inArray } from 'drizzle-orm';
import { faker } from '@faker-js/faker';
import * as schema from '../schema';
import * as relations from '../relations';

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is required');
}
const sql = neon(process.env.DATABASE_URL);
const db = drizzle(sql, { schema: { ...schema, ...relations } });

// Set faker seed for consistent results
faker.seed(100); // Different seed from main seeder

console.log('🎯 Starting realistic demo data enhancement...');

await enhanceWithRealisticData();

console.log('✅ Realistic demo data enhancement completed!');

/**
 * Enhances the database with interconnected, realistic demo data for brands, products, projects, creatives, review workflows, and notifications.
 *
 * Throws an error if there are fewer than 4 users or no accounts in the database to ensure minimum requirements for realistic scenarios.
 * Sequentially creates and links demo entities to simulate a comprehensive business environment for demonstration purposes.
 */
async function enhanceWithRealisticData() {
  try {
    // Query existing data first
    const existingUsers = await db.select().from(schema.users);
    const existingAccounts = await db.select().from(schema.accounts);
    const existingRetailers = await db.select().from(schema.retailers);
    const existingCategories = await db.select().from(schema.categories);
    const existingKeywords = await db.select().from(schema.keywords);
    const existingCredentialSets = await db.select().from(schema.credentialSets);

    if (existingUsers.length < 4) {
      throw new Error('Need at least 4 users for realistic scenarios');
    }

    if (existingAccounts.length < 1) {
      throw new Error('Need at least 1 account for realistic scenarios');
    }

    console.log(`Found ${existingUsers.length} users, ${existingAccounts.length} accounts`);

    // Create realistic demo data
    const demoBrands = await createRealisticBrands(existingAccounts, existingCredentialSets);
    await createBrandRelationships(demoBrands, existingCategories, existingUsers);
    const demoProducts = await createRealisticProducts(demoBrands, existingAccounts);
    const demoProjects = await createRealisticProjects(demoBrands, existingUsers, existingRetailers);
    const demoGenerations = await createRealisticGenerations(demoProducts, existingKeywords, demoProjects, demoBrands);
    const demoCreatives = await createRealisticCreatives(demoProjects, demoBrands, existingUsers, demoGenerations);
    await createRealisticReviewWorkflow(demoCreatives, existingUsers);
    await createContextualNotifications(existingUsers, existingAccounts, demoBrands);

    console.log('🎯 Demo enhancement completed successfully!');

  } catch (error) {
    console.error('❌ Demo enhancement failed:', error);
    throw error;
  }
}

/**
 * Creates and inserts four realistic demo brand records into the database.
 *
 * Each brand is associated with an account, assigned a set of branding color hex codes, a logo URL, a primary credential, descriptive notes, and a product count. Returns the array of inserted brand records.
 *
 * @param accounts - List of account records used to associate brands
 * @param credentialSets - List of credential set records for assigning primary credentials to brands
 * @returns The array of inserted brand records
 */
async function createRealisticBrands(accounts: any[], credentialSets: any[]) {
  console.log('🏷️ Creating realistic demo brands...');

  const demoBrandsData = [
    {
      accountId: accounts[0].id,
      name: "EcoLife Essentials",
      description: "Sustainable living products for conscious consumers",
      colors: ['#10B981', '#059669', '#6EE7B7'],
      logoAssetUrl: 'https://images.unsplash.com/photo-*************-64674bd600d8?w=200',
      primaryCredentialId: credentialSets.length > 0 ? credentialSets[0].id : null,
      notes: "Focus on sustainability messaging, earth tones, lifestyle imagery. Target eco-conscious millennials and Gen-Z.",
      productCount: 12
    },
    {
      accountId: accounts[0].id,
      name: "TechFlow Pro",
      description: "Professional-grade electronics and accessories",
      colors: ['#3B82F6', '#1E40AF', '#93C5FD'],
      logoAssetUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200',
      primaryCredentialId: credentialSets.length > 1 ? credentialSets[1].id : credentialSets[0]?.id,
      notes: "Modern, sleek design. Highlight premium quality and innovation. Appeal to tech professionals and early adopters.",
      productCount: 18
    },
    {
      accountId: accounts[0].id,
      name: "HomeStyle Living",
      description: "Curated home decor and furniture collection",
      colors: ['#F59E0B', '#D97706', '#FCD34D'],
      logoAssetUrl: 'https://images.unsplash.com/photo-*************-27b2c045efd7?w=200',
      primaryCredentialId: credentialSets.length > 2 ? credentialSets[2].id : credentialSets[0]?.id,
      notes: "Cozy, inviting imagery. Family-focused messaging. Emphasize comfort and style for modern homes.",
      productCount: 25
    },
    {
      accountId: accounts.length > 1 ? accounts[1].id : accounts[0].id,
      name: "FitLife Athletics",
      description: "Performance sportswear and fitness equipment",
      colors: ['#EF4444', '#DC2626', '#FCA5A5'],
      logoAssetUrl: 'https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=200',
      primaryCredentialId: credentialSets.length > 3 ? credentialSets[3].id : credentialSets[0]?.id,
      notes: "High-energy, motivational messaging. Target fitness enthusiasts and athletes. Emphasize performance and results.",
      productCount: 15
    }
  ];

  const demoBrands = await db.insert(schema.brands).values(demoBrandsData).returning();
  console.log(`✅ Created ${demoBrands.length} realistic demo brands`);
  return demoBrands;
}

/**
 * Establishes relationships between demo brands, categories, and users in the database.
 *
 * Associates each demo brand with a relevant category and assigns users to brands with specific roles and active status.
 */
async function createBrandRelationships(demoBrands: any[], categories: any[], users: any[]) {
  console.log('🔗 Creating brand relationships...');

  // Brand categories
  const brandCategoryData = [
    { brandId: demoBrands[0].id, categoryId: categories.find(c => c.name.includes('Home') || c.name.includes('Garden'))?.id || categories[0].id },
    { brandId: demoBrands[1].id, categoryId: categories.find(c => c.name.includes('Electronics'))?.id || categories[1].id },
    { brandId: demoBrands[2].id, categoryId: categories.find(c => c.name.includes('Home'))?.id || categories[0].id },
    { brandId: demoBrands[3].id, categoryId: categories.find(c => c.name.includes('Sports'))?.id || categories[2].id }
  ];

  await db.insert(schema.brandCategories).values(brandCategoryData);

  // Brand users with specific roles
  const brandUserData = [
    { brandId: demoBrands[0].id, userId: users[0].id, role: 'admin', isActive: true },
    { brandId: demoBrands[0].id, userId: users[1].id, role: 'editor', isActive: true },
    { brandId: demoBrands[1].id, userId: users[1].id, role: 'admin', isActive: true },
    { brandId: demoBrands[1].id, userId: users[2].id, role: 'editor', isActive: true },
    { brandId: demoBrands[2].id, userId: users[2].id, role: 'admin', isActive: true },
    { brandId: demoBrands[3].id, userId: users[3].id, role: 'admin', isActive: true }
  ];

  await db.insert(schema.brandUsers).values(brandUserData);
  console.log(`✅ Created brand relationships`);
}

/**
 * Creates and inserts realistic demo products for the specified brands, avoiding duplicates.
 *
 * Checks for existing products by product ID, inserts only new products with detailed attributes, and returns the combined list of existing and newly created products for further demo data seeding.
 *
 * @param demoBrands - The demo brand records to associate products with
 * @param accounts - The account records used to assign products
 * @returns An array of all relevant product records, including both existing and newly created products
 */
async function createRealisticProducts(demoBrands: any[], accounts: any[]) {
  console.log('📦 Creating realistic products...');

  const productSets = [
    // EcoLife Essentials products
    {
      brandId: demoBrands[0].id,
      products: [
        {
          productId: 'ECO001',
          productTitle: 'Bamboo Fiber Dinnerware Set - 16 Piece',
          productType: 'physical',
          category: 'Home & Kitchen',
          thumbnailUrl: 'https://images.unsplash.com/photo-*************-48f60103fc96?w=300',
          shortDescription: 'Complete eco-friendly dining set made from sustainable bamboo fiber',
          longDescription: 'Transform your dining experience with our 16-piece bamboo fiber dinnerware set. Crafted from sustainable bamboo, these dishes are naturally antimicrobial, dishwasher safe, and perfect for everyday use. Set includes 4 dinner plates, 4 salad plates, 4 bowls, and 4 cups.',
          genAiDescription: 'Sustainable bamboo dinnerware perfect for eco-conscious families who want style without compromising their environmental values.',
          specifications: ['biodegradable', 'dishwasher-safe', 'microwave-safe', 'antimicrobial'],
          productHighlights: ['Eco-friendly', 'Dishwasher safe', 'Complete 16-piece set', 'Antimicrobial'],
          classType: 'A',
          upc: '850123456789',
          pdpSummary: 'Complete bamboo dinnerware set for sustainable dining'
        },
        {
          productId: 'ECO002',
          productTitle: 'Organic Cotton Reusable Shopping Bags - 5 Pack',
          productType: 'physical',
          category: 'Home & Garden',
          thumbnailUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300',
          shortDescription: 'Durable organic cotton shopping bags with reinforced handles',
          longDescription: 'Say goodbye to plastic bags with our set of 5 premium organic cotton shopping bags. Each bag can hold up to 50 lbs and features reinforced handles and a spacious design. Machine washable and built to last for years of sustainable shopping.',
          genAiDescription: 'Premium reusable shopping bags that combine sustainability with style and durability.',
          specifications: ['organic-cotton', 'machine-washable', 'reinforced-handles', 'foldable'],
          productHighlights: ['Organic cotton', 'Machine washable', 'Holds 50 lbs', '5-pack value'],
          classType: 'B',
          upc: '850123456790',
          pdpSummary: 'Durable organic cotton shopping bags for sustainable shopping'
        }
      ]
    },
    // TechFlow Pro products
    {
      brandId: demoBrands[1].id,
      products: [
        {
          productId: 'TECH001',
          productTitle: 'TechFlow Pro Wireless Earbuds with ANC',
          productType: 'physical',
          category: 'Electronics',
          thumbnailUrl: 'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=300',
          shortDescription: 'Premium wireless earbuds with active noise cancellation',
          longDescription: 'Experience studio-quality sound with our flagship wireless earbuds. Featuring advanced active noise cancellation, 30-hour battery life, and IPX7 water resistance. Perfect for professionals, commuters, and audiophiles who demand the best.',
          genAiDescription: 'Professional-grade wireless earbuds engineered for superior sound quality and all-day comfort.',
          specifications: ['wireless', 'noise-cancelling', 'waterproof', '30hr-battery'],
          productHighlights: ['Active noise cancellation', '30-hour battery', 'IPX7 water resistant', 'Premium sound'],
          classType: 'A',
          upc: '850987654321',
          pdpSummary: 'Premium wireless earbuds with ANC and 30-hour battery'
        },
        {
          productId: 'TECH002',
          productTitle: 'Smart Home Hub with Voice Control',
          productType: 'physical',
          category: 'Electronics',
          thumbnailUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300',
          shortDescription: 'Central control hub for all your smart home devices',
          longDescription: 'Transform your home into a smart home with our advanced control hub. Compatible with over 1000+ devices, featuring voice control, automated routines, and energy monitoring. Easy setup and intuitive app control make home automation simple.',
          genAiDescription: 'The brain of your smart home - connecting and controlling all your devices seamlessly.',
          specifications: ['voice-control', 'wireless', 'app-controlled', 'energy-monitoring'],
          productHighlights: ['1000+ device compatibility', 'Voice control', 'Energy monitoring', 'Easy setup'],
          classType: 'A',
          upc: '850987654322',
          pdpSummary: 'Smart home hub with voice control and device automation'
        }
      ]
    }
    // Add more product sets for other brands as needed
  ];

  // Check which products already exist
  const allProductIds = productSets.flatMap(set => set.products.map(p => p.productId));
  const existingProducts = await db.select().from(schema.products)
    .where(inArray(schema.products.productId, allProductIds));

  const existingProductIds = new Set(existingProducts.map(p => p.productId));
  console.log(`Found ${existingProducts.length} existing products: ${Array.from(existingProductIds).join(', ')}`);

  // Prepare data for new products only
  const newProductData = [];
  for (const brandSet of productSets) {
    for (const product of brandSet.products) {
      if (!existingProductIds.has(product.productId)) {
        newProductData.push({
          ...product,
          accountId: accounts[0].id,
          brandId: brandSet.brandId,
          images: [product.thumbnailUrl],
          customImages: []
        });
      }
    }
  }

  // Insert only new products
  let newProducts: any[] = [];
  if (newProductData.length > 0) {
    newProducts = await db.insert(schema.products).values(newProductData).returning();
    console.log(`✅ Created ${newProducts.length} new realistic products`);
  } else {
    console.log(`✅ All realistic products already exist, skipping creation`);
  }

  // Return all products (existing + new) for the rest of the seeder
  const allDemoProducts = [...existingProducts, ...newProducts];
  console.log(`📦 Total realistic products available: ${allDemoProducts.length}`);
  return allDemoProducts;
}

/**
 * Creates and inserts realistic demo projects with workflow scenarios for each demo brand.
 *
 * Each project includes campaign and ad group structures, is associated with a brand, retailer, and creator user, and is assigned workflow status and timestamps to simulate real project lifecycles.
 *
 * @returns The array of created project records.
 */
async function createRealisticProjects(demoBrands: any[], users: any[], retailers: any[]) {
  console.log('📋 Creating realistic projects with workflow scenarios...');

  const now = new Date();
  const walmartRetailer = retailers.find(r => r.name === 'Walmart') || retailers[0];
  const amazonRetailer = retailers.find(r => r.name === 'Amazon') || retailers[1] || retailers[0];

  const realisticProjects = [
    {
      brandId: demoBrands[0].id, // EcoLife Essentials
      name: "Holiday Sustainable Gift Guide 2024",
      wmCampaigns: {
        "HOLIDAY2024_ECO": {
          name: "Sustainable Holiday Gifts",
          adGroups: {
            "DINNERWARE_AG": "Eco Dinnerware Collection",
            "BAGS_AG": "Reusable Shopping Solutions",
            "BUNDLES_AG": "Holiday Gift Bundles"
          }
        }
      },
      retailerId: walmartRetailer.id,
      createdBy: users[0].id,
      currentStep: 3, // Creative development phase
      status: 'in_progress' as const,
      createdAt: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      updatedAt: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000)  // 1 day ago
    },
    {
      brandId: demoBrands[1].id, // TechFlow Pro
      name: "TechFlow Pro Q4 Product Launch",
      wmCampaigns: {
        "EARBUDS_LAUNCH_Q4": {
          name: "Pro Earbuds Launch Campaign",
          adGroups: {
            "WIRELESS_AG": "Wireless Audio Category",
            "PREMIUM_AG": "Premium Electronics",
            "HOLIDAY_AG": "Holiday Tech Gifts"
          }
        }
      },
      retailerId: amazonRetailer.id,
      createdBy: users[1].id,
      currentStep: 5, // Completed
      status: 'published' as const,
      createdAt: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      updatedAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)   // 3 days ago
    },
    {
      brandId: demoBrands[2].id, // HomeStyle Living
      name: "Winter Home Comfort Campaign",
      wmCampaigns: {
        "WINTER_HOME_2024": {
          name: "Winter Home Essentials",
          adGroups: {
            "FURNITURE_AG": "Comfort Furniture",
            "DECOR_AG": "Winter Decor",
            "LIGHTING_AG": "Cozy Lighting"
          }
        }
      },
      retailerId: walmartRetailer.id,
      createdBy: users[2].id,
      currentStep: 2, // Early planning phase
      status: 'in_progress' as const,
      createdAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      updatedAt: new Date(now.getTime() - 6 * 60 * 60 * 1000)       // 6 hours ago
    },
    {
      brandId: demoBrands[3].id, // FitLife Athletics
      name: "New Year Fitness Resolution Campaign",
      wmCampaigns: {
        "NYR_FITNESS_2025": {
          name: "New Year New You",
          adGroups: {
            "EQUIPMENT_AG": "Home Fitness Equipment",
            "APPAREL_AG": "Workout Apparel",
            "ACCESSORIES_AG": "Fitness Accessories"
          }
        }
      },
      retailerId: amazonRetailer.id,
      createdBy: users[3].id,
      currentStep: 4, // Review phase
      status: 'in_review' as const,
      createdAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      updatedAt: new Date(now.getTime() - 4 * 60 * 60 * 1000)       // 4 hours ago
    }
  ];

  const demoProjects = await db.insert(schema.projects).values(realisticProjects).returning();
  console.log(`✅ Created ${demoProjects.length} realistic projects`);
  return demoProjects;
}

/**
 * Inserts realistic generation records linking products, keywords, projects, and brands to simulate product generation scenarios.
 *
 * @param demoProducts - Array of demo product records to associate with generations
 * @param keywords - Array of keyword records used for generation targeting
 * @param demoProjects - Array of demo project records to link generations to campaigns
 * @param demoBrands - Array of demo brand records for brand association
 * @returns Array of inserted generation records
 */
async function createRealisticGenerations(demoProducts: any[], keywords: any[], demoProjects: any[], demoBrands: any[]) {
  console.log('⚡ Creating realistic generations...');

  const generationData = [
    {
      productId: demoProducts[0].id, // Bamboo dinnerware - use UUID id, not string productId
      keywordId: keywords.find(k => k.keyword.includes('eco') || k.keyword.includes('sustainable'))?.id || keywords[0].id,
      projectId: demoProjects[0].id, // Holiday campaign
      brandId: demoBrands[0].id
    },
    {
      productId: demoProducts[1].id, // Shopping bags - use UUID id
      keywordId: keywords.find(k => k.keyword.includes('eco') || k.keyword.includes('organic'))?.id || keywords[1].id,
      projectId: demoProjects[0].id, // Holiday campaign
      brandId: demoBrands[0].id
    },
    {
      productId: demoProducts[2].id, // Earbuds - use UUID id
      keywordId: keywords.find(k => k.keyword.includes('premium') || k.keyword.includes('quality'))?.id || keywords[2].id,
      projectId: demoProjects[1].id, // TechFlow launch
      brandId: demoBrands[1].id
    },
    {
      productId: demoProducts[3].id, // Smart hub - use UUID id
      keywordId: keywords.find(k => k.keyword.includes('smart') || k.keyword.includes('modern'))?.id || keywords[3].id,
      projectId: demoProjects[1].id, // TechFlow launch
      brandId: demoBrands[1].id
    }
  ];

  const demoGenerations = await db.insert(schema.generations).values(generationData).returning();
  console.log(`✅ Created ${demoGenerations.length} realistic generations`);
  return demoGenerations;
}

/**
 * Inserts a set of realistic creative records with review workflows into the database.
 *
 * Each creative is associated with a project, brand, user, and generation, and includes campaign metadata, preview URLs, ad unit specifications, review comments, and workflow status. Returns the inserted creative records.
 *
 * @returns The array of inserted creative records.
 */
async function createRealisticCreatives(demoProjects: any[], demoBrands: any[], users: any[], demoGenerations: any[]) {
  console.log('🎨 Creating realistic creatives with review workflows...');

  const now = new Date();

  const realisticCreatives = [
    {
      projectId: demoProjects[0].id, // Holiday campaign
      wmCreativeId: "HOLIDAY_BANNER_ECO_001",
      brandId: demoBrands[0].id, // EcoLife
      userId: users[1].id, // Designer
      generationId: demoGenerations[0].id,
      previewUrls: {
        thumbnail: "https://images.unsplash.com/photo-*************-48f60103fc96?w=200",
        fullSize: "https://images.unsplash.com/photo-*************-48f60103fc96?w=800"
      },
      previewStatus: "COMPLETED",
      folderId: null,
      status: "REVIEW", // Currently in review
      keyword: "sustainable gifts",
      adUnits: {
        banner: { width: 728, height: 90, format: "png" },
        video: { duration: 30, format: "mp4" }
      },
      reviewComments: [
        {
          comment: "Love the green color scheme! Can we make the 'Shop Now' button more prominent? Also, the product image could be brighter.",
          reviewer: "Sarah Chen - Brand Manager",
          timestamp: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString()
        }
      ],
      createdAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      projectId: demoProjects[1].id, // TechFlow launch
      wmCreativeId: "EARBUDS_HERO_001",
      brandId: demoBrands[1].id, // TechFlow
      userId: users[2].id,
      generationId: demoGenerations[2].id,
      previewUrls: {
        thumbnail: "https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=200",
        fullSize: "https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=800"
      },
      previewStatus: "COMPLETED",
      folderId: null,
      status: "APPROVED", // Success story
      keyword: "wireless earbuds",
      adUnits: {
        banner: { width: 300, height: 250, format: "jpg" },
        video: { duration: 15, format: "mp4" }
      },
      reviewComments: [
        {
          comment: "Initial draft looks good, but let's emphasize the noise cancellation feature more prominently in the headline.",
          reviewer: "Mike Rodriguez - Creative Director",
          timestamp: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          comment: "Updated with 'Advanced Noise Cancellation' in the main headline. How does this look?",
          reviewer: "Alex Kim - Designer",
          timestamp: new Date(now.getTime() - 8 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          comment: "Perfect! This really highlights our key differentiator. The contrast is great and the CTA stands out. Approved for launch.",
          reviewer: "Mike Rodriguez - Creative Director",
          timestamp: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString()
        }
      ],
      createdAt: new Date(now.getTime() - 25 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    },
    {
      projectId: demoProjects[0].id, // Holiday campaign
      wmCreativeId: "HOLIDAY_BAGS_002",
      brandId: demoBrands[0].id, // EcoLife
      userId: users[1].id,
      generationId: demoGenerations[1].id,
      previewUrls: {
        thumbnail: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200",
        fullSize: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=800"
      },
      previewStatus: "COMPLETED",
      folderId: null,
      status: "DRAFT", // Still being worked on
      keyword: "reusable bags",
      adUnits: {
        banner: { width: 320, height: 50, format: "png" },
        video: { duration: 15, format: "mp4" }
      },
      reviewComments: [],
      createdAt: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(now.getTime() - 6 * 60 * 60 * 1000) // 6 hours ago
    },
    {
      projectId: demoProjects[2].id, // New Year fitness campaign
      wmCreativeId: "FITNESS_MOTIVATION_001",
      brandId: demoBrands[3].id, // FitLife Athletics
      userId: users[3].id,
      generationId: demoGenerations[3].id,
      previewUrls: {
        thumbnail: "https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=200",
        fullSize: "https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=800"
      },
      previewStatus: "COMPLETED",
      folderId: null,
      status: "REVIEW", // In review phase
      keyword: "fitness equipment",
      adUnits: {
        banner: { width: 728, height: 90, format: "jpg" },
        video: { duration: 30, format: "mp4" }
      },
      reviewComments: [
        {
          comment: "Great energy in this creative! The 'New Year New You' messaging is on point. Can we A/B test with a version that shows before/after results?",
          reviewer: "Lisa Park - Marketing Director",
          timestamp: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString()
        }
      ],
      createdAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(now.getTime() - 6 * 60 * 60 * 1000)
    }
  ];

  const demoCreatives = await db.insert(schema.creatives).values(realisticCreatives).returning();
  console.log(`✅ Created ${demoCreatives.length} realistic creatives`);
  return demoCreatives;
}

/**
 * Creates realistic review workflows for demo creatives, including generation versions, proofs, and reviewer assignments.
 *
 * For the first two demo creatives, this function generates detailed generation versions with unit fields and ad previews, creates associated proofs with internal reviewers, and assigns reviewer statuses to simulate a real-world creative review process.
 */
async function createRealisticReviewWorkflow(demoCreatives: any[], users: any[]) {
  console.log('👁️ Creating realistic review workflows...');

  // Create generation versions for the creatives that need detailed review workflow
  const versionData = demoCreatives.slice(0, 2).map((creative, index) => ({
    generationId: creative.generationId,
    sessionId: faker.string.uuid(),
    unitFields: {
      headline: index === 0
        ? "Sustainable Holiday Gifts That Make a Difference"
        : "Experience Premium Sound with Advanced Noise Cancellation",
      description: index === 0
        ? "Shop eco-friendly dinnerware and reusable bags for the conscious gift-giver"
        : "TechFlow Pro Wireless Earbuds - 30hr battery, IPX7 waterproof, studio-quality sound",
      cta: index === 0 ? "Shop Sustainable Gifts" : "Upgrade Your Audio"
    },
    hasCompleted: true,
    createdBy: users[index + 1].id,
    version: 1,
    adPreviews: [
      {
        id: faker.string.uuid(),
        url: creative.previewUrls.fullSize,
        size: "728x90"
      }
    ],
    previewImagePositions: {
      primary: { x: 0, y: 0 },
      secondary: { x: 200, y: 50 }
    },
    applyToAll: false
  }));

  const generationVersions = await db.insert(schema.generationVersions).values(versionData).returning();

  // Create proofs for these versions
  const proofData = generationVersions.map((version, index) => ({
    generationVersionId: version.id,
    title: index === 0
      ? "EcoLife Holiday Campaign - Banner Creative Review"
      : "TechFlow Pro Earbuds - Hero Banner Final Review",
    emailDescription: index === 0
      ? "Please review the holiday campaign banner for EcoLife Essentials. Focus on messaging clarity and visual appeal for the sustainable gift market."
      : "Final review for the TechFlow Pro earbuds hero banner. This will be the primary creative for our Q4 launch campaign.",
    adUnitIds: ["banner_728x90", "mobile_320x50"],
    reviewers: {
      internal: [
        {
          id: users[0].id,
          name: users[0].name,
          email: users[0].email
        }
      ],
      external: []
    }
  }));

  const proofs = await db.insert(schema.proofs).values(proofData).returning();

  // Create reviewers
  const reviewerData = proofs.map((proof, index) => ({
    proofId: proof.id,
    name: users[0].name,
    email: users[0].email,
    userId: users[0].id,
    isExternal: false,
    status: index === 0 ? 'pending' : 'approved',
    requestedBy: users[index + 1].id,
    lastRequestedAt: new Date(Date.now() - (index + 1) * 24 * 60 * 60 * 1000)
  }));

  await db.insert(schema.reviewers).values(reviewerData);

  console.log(`✅ Created review workflow for ${generationVersions.length} creatives`);
}

/**
 * Inserts a set of contextual notifications for demo users and accounts, simulating real-world events such as creative approvals, review requests, project updates, brand guideline uploads, and campaign performance milestones.
 */
async function createContextualNotifications(users: any[], accounts: any[], demoBrands: any[]) {
  console.log('🔔 Creating contextual notifications...');

  const now = new Date();

  const contextualNotifications = [
    {
      userId: users[0].id,
      accountId: accounts[0].id,
      title: "Creative Approved ✅",
      message: "Mike Rodriguez approved your 'TechFlow Pro Earbuds Hero Banner' creative",
      data: {
        type: "creative_approval",
        action: "approved",
        entityId: "EARBUDS_HERO_001",
        metadata: {
          creativeTitle: "TechFlow Pro Earbuds Hero Banner",
          approverName: "Mike Rodriguez",
          brandName: "TechFlow Pro"
        }
      },
      isRead: false,
      readAt: null,
      createdAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    },
    {
      userId: users[1].id,
      accountId: accounts[0].id,
      title: "Review Requested 📝",
      message: "New creative review requested for EcoLife Holiday Campaign",
      data: {
        type: "review_request",
        action: "requested",
        entityId: "HOLIDAY_BANNER_ECO_001",
        metadata: {
          creativeTitle: "Holiday Sustainable Gift Guide Banner",
          requesterName: "Sarah Chen",
          brandName: "EcoLife Essentials"
        }
      },
      isRead: false,
      readAt: null,
      createdAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      userId: users[0].id,
      accountId: accounts[0].id,
      title: "Project Updated 📋",
      message: "Holiday Sustainable Gift Guide project moved to creative development phase",
      data: {
        type: "project_update",
        action: "updated",
        entityId: "project_id_placeholder",
        metadata: {
          projectName: "Holiday Sustainable Gift Guide 2024",
          currentStep: 3,
          brandName: "EcoLife Essentials"
        }
      },
      isRead: true,
      readAt: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000),
      createdAt: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000)
    },
    {
      userId: users[2].id,
      accountId: accounts[0].id,
      title: "New Brand Guidelines 📖",
      message: "Updated brand guidelines uploaded for HomeStyle Living",
      data: {
        type: "brand_update",
        action: "uploaded",
        entityId: demoBrands[2].id,
        metadata: {
          fileName: "HomeStyle_Brand_Guidelines_v3.pdf",
          uploaderName: "Jennifer Wilson",
          brandName: "HomeStyle Living"
        }
      },
      isRead: false,
      readAt: null,
      createdAt: new Date(now.getTime() - 6 * 60 * 60 * 1000) // 6 hours ago
    },
    {
      userId: users[3].id,
      accountId: accounts[0].id,
      title: "Campaign Performance 📈",
      message: "FitLife New Year campaign is performing 23% above target CTR",
      data: {
        type: "performance_update",
        action: "milestone",
        entityId: "NYR_FITNESS_2025",
        metadata: {
          campaignName: "New Year Fitness Resolution Campaign",
          metric: "CTR",
          performance: "+23%",
          brandName: "FitLife Athletics"
        }
      },
      isRead: false,
      readAt: null,
      createdAt: new Date(now.getTime() - 4 * 60 * 60 * 1000) // 4 hours ago
    }
  ];

  await db.insert(schema.notifications).values(contextualNotifications);
  console.log(`✅ Created ${contextualNotifications.length} contextual notifications`);
}