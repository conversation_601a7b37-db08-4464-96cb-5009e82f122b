import 'dotenv/config';
import { clearDatabase } from './seeders/core';
import { ensureSuperUsers, ensureAccounts, ensureAccountSettings, ensureUserSettings, ensurePermissions } from './seeders/base';
import { 
  ensureBrands, 
  ensureCategories, 
  ensureKeywords, 
  ensureCredentialSets, 
  ensureRetailers,
  ensureRetailerSpecs,
  updateBrandsWithCredentials
} from './seeders/business';
import {
  ensureUserAccounts,
  ensureBrandCategories,
  ensureBrandUsers,
  ensureBrandRetailers,
  ensureUserCredentialSets,
  ensureCredentialSetRetailers
} from './seeders/relationships';
import {
  ensureAssets,
  ensureProducts,
  ensureRetailerProducts,
  ensureImageMetadata,
  ensureProjects
} from './seeders/content';
import { logger } from './seeders/utils';
import { ENV_CONFIG } from './seedConfig';

console.log('🌱 Starting seeding without workflow entities...');

// Validate environment
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

try {
  logger.startPhase('Database Preparation');
  
  if (!ENV_CONFIG.SKIP_CLEARING) {
    console.log('🧹 Clearing database...');
    await clearDatabase();
  }
  
  logger.endPhase();

  logger.startPhase('Base Entities');
  const users = await ensureSuperUsers();
  const accounts = await ensureAccounts(users);
  await ensureUserAccounts(users, accounts);
  await ensureAccountSettings(accounts);
  await ensureUserSettings(users);
  await ensurePermissions();
  logger.endPhase();

  logger.startPhase('Business Logic');
  const categories = await ensureCategories(accounts);
  const keywords = await ensureKeywords(accounts);
  const brands = await ensureBrands(accounts);
  const credentialSets = await ensureCredentialSets(accounts, users);
  const retailers = await ensureRetailers();
  await ensureRetailerSpecs(retailers);
  await updateBrandsWithCredentials(brands, credentialSets);
  logger.endPhase();

  logger.startPhase('Relationships');
  await ensureBrandCategories(brands, categories);
  await ensureBrandUsers(brands, users);
  await ensureUserCredentialSets(users, credentialSets);
  await ensureCredentialSetRetailers(credentialSets, retailers);
  await ensureBrandRetailers(brands, retailers);
  logger.endPhase();

  logger.startPhase('Content & Assets');
  const assets = await ensureAssets(accounts);
  const products = await ensureProducts(accounts, brands);
  await ensureRetailerProducts(retailers, products);
  await ensureImageMetadata(products);
  const projects = await ensureProjects(brands, users, retailers);
  logger.endPhase();

  const totalRecords = users.length + accounts.length + brands.length + products.length + projects.length + assets.length + 200; // Approximate
  logger.logSummary(totalRecords);

} catch (error) {
  logger.logError('Seeding without workflow failed', error instanceof Error ? error : new Error(String(error)));
  process.exit(1);
}

console.log('✅ Seeding without workflow entities completed successfully!');