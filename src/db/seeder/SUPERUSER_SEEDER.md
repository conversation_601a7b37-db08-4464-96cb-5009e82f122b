# Enhanced Super User Seeder

This seeder creates comprehensive relational data for all super users defined in `superUsers.json`. It ensures each super user has complete relationships across all database entities.

## What it does

For each super user in `superUsers.json`, this seeder:

1. **Creates 3 Accounts** - Each user gets 3 dedicated accounts with different types
2. **Assigns Account Roles** - User has different roles in each account:
   - Account 1: **Owner** role
   - Account 2: **Admin** role
   - Account 3: **Member** role
3. **Creates Brands** - Each account gets 1 dedicated brand (3 brands total per user)
4. **Assigns Brand Roles** - User gets corresponding roles in each brand:
   - Brand 1: **Owner** role
   - Brand 2: **Admin** role
   - Brand 3: **Member** role
5. **Associates with Retailers** - Each brand is linked to 2-4 retailers
6. **Creates Products** - Each brand gets 3-8 products
7. **Creates Projects** - Each brand gets 1-3 projects
8. **Sets up Credentials** - User gets access to credential sets

## Usage

### Prerequisites

1. Make sure you have a valid `DATABASE_URL` in your `.env` file
2. Run the main seeder first to create base data:
   ```bash
   npm run db:seed
   ```

### Running the Enhanced Seeder

```bash
npm run db:seed:superusers
```

### Expected Output

```
🦸‍♂️ Starting enhanced super user seeding...
Found 4 super users to enhance

👤 Setting up relationships for user: Geoffrey Hinton (<EMAIL>)
🏢 Creating 3 accounts for Geoffrey Hinton...
✅ Created 3 accounts for Geoffrey Hinton
🔗 Assigning Geoffrey Hinton to accounts with specific roles...
✅ Assigned Geoffrey Hinton to 3 accounts with roles: owner, admin, member
🏷️ Creating 1 brand for each of 3 accounts...
✅ Created 3 brands (1 per account)
🔗 Assigning Geoffrey Hinton to brands with appropriate roles...
✅ Assigned Geoffrey Hinton to 3 brands with roles: owner, admin, member
🛒 Associating 3 brands with retailers...
✅ Created 9 brand-retailer associations
📦 Creating products for 3 brands...
✅ Created 18 products
📊 Creating projects for 3 brands...
✅ Created 6 projects
🔐 Setting up credentials for Geoffrey Hinton...
✅ Assigned Geoffrey Hinton to 2 credential sets
✅ Completed setup for Geoffrey Hinton

[Repeats for each super user...]

✅ Enhanced super user seeding completed!
```

## What gets created

For each super user (using Geoffrey Hinton as example):

### Accounts
- **Geoffrey's Premium Agency** (agency) - User is owner
- **Geoffrey's Tech Solutions Inc** (3p) - User is admin
- **Geoffrey's Lifestyle Co** (1p) - User is member

### Brands
- **Geoffrey's Premium Brand** (linked to Premium Agency)
- **Geoffrey's Tech Solutions** (linked to Tech Solutions Inc)
- **Geoffrey's Lifestyle Co** (linked to Lifestyle Co)

### Relationships
- User → Account relationships with roles
- User → Brand relationships with corresponding roles
- Brand → Retailer relationships (each brand on 2-4 retailers)
- Brand → Product relationships (3-8 products per brand)
- Brand → Project relationships (1-3 projects per brand)
- User → Credential Set relationships

## Testing the Results

After running the seeder, you can test that `getUserBrandsByRetailer` now works:

1. Pick any retailer ID from the database
2. Call the API endpoint with a super user's ID
3. You should now get brands returned instead of empty data

Example query to verify:
```sql
SELECT
    u.name as user_name,
    u.email,
    b.name as brand_name,
    bu.role as brand_role,
    r.name as retailer_name
FROM users u
JOIN brandUsers bu ON u.id = bu.userId
JOIN brands b ON bu.brandId = b.id
JOIN brandRetailers br ON b.id = br.brandId
JOIN retailers r ON br.retailerId = r.id
WHERE u.email LIKE '%@%'
ORDER BY u.name, b.name, r.name;
```

## Notes

- This seeder is safe to run multiple times - it checks for existing data
- It runs after the main seeder, so it expects base entities (retailers, categories, etc.) to exist
- Each super user gets completely isolated data sets
- The seeder uses faker with a consistent seed for reproducible results