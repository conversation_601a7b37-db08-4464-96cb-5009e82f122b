import { SeederConfig } from './seeders/utils';

// Configuration for seeding quantities and relationships
export const SEED_CONFIG: SeederConfig = {
  userCount: 25, // Minimum users (4 super users + 21 demo users for accounts/brands)
  accountCount: 4, // One account per super user
  brandCount: 16, // Each super user gets 3+ brands, plus extras for demo users
  productCount: 30,
  projectCount: 15,
  generationCount: 35,
  creativeCount: 50,
  assetCount: 25,
  notificationCount: 45,
  invitationCount: 8,
  externalReviewerCount: 12
};

// Relationship configuration
export const RELATIONSHIP_CONFIG = {
  // Each super user owns exactly ONE account
  accountsPerSuperUser: 1,
  superUserAccountRole: 'owner',
  
  // Each account should have at least 5 demo users
  minDemoUsersPerAccount: 5,
  
  // Each super user should have at least 3 brands with different roles
  minBrandsPerSuperUser: 3,
  superUserBrandRoles: ['owner', 'admin', 'member'],
  
  // Each brand should have at least 5 demo users
  minDemoUsersPerBrand: 5,
  
  // Brand relationships
  categoriesPerBrand: { min: 1, max: 3 },
  retailersPerBrand: { min: 2, max: 6 },
  guidelinesPerBrand: { min: 1, max: 2 },
  
  // Product relationships
  imagesPerProduct: { min: 1, max: 4 },
  retailersPerProduct: { min: 2, max: 4 },
  
  // Credential relationships
  credentialSetsPerUser: { min: 1, max: 2 },
  retailersPerCredentialSet: { min: 2, max: 5 },
  
  // Project and generation relationships
  versionsPerGeneration: { min: 1, max: 3 },
  reviewersPerProof: { min: 1, max: 3 },
  commentsPerProof: { min: 1, max: 5 },
  
  // Brand credential linking
  brandsWithCredentialsPercentage: 0.6 // 60% of brands get primary credentials
};

// Environment configuration
export const ENV_CONFIG = {
  FORCE_CLEAR: process.env.FORCE_CLEAR === 'true',
  SKIP_CLEARING: process.env.SKIP_CLEARING === 'true'
};