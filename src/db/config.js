import { neon } from '@neondatabase/serverless'
import { drizzle } from 'drizzle-orm/neon-http'
import * as schema from './schema/index.js'
import { logger } from '../utils/logger.js'

// Enhanced Neon HTTP client with optimized connection pooling
const sql = neon(process.env.DATABASE_URL, {
  // Optimized connection pooling configuration
  connectionTimeoutMillis: 30000, // 30 seconds timeout
  idleTimeoutMillis: 30000, // 30 seconds idle timeout  
  maxLifetimeSeconds: 3600, // 1 hour max connection lifetime
  // Performance optimizations
  arrayMode: false, // Object mode for better dev experience
  fullResults: false, // Only return rows for better performance
  // Neon HTTP client automatically handles connection pooling
  // These settings optimize connection reuse and prevent timeouts
})

// Minimal connection logging
if (process.env.NODE_ENV === 'development' && process.env.LOG_LEVEL === 'DEBUG') {
  logger.debug('Database connection initialized', {
    provider: 'neon-serverless',
    connectionPooling: true
  });
}

// Create optimized Drizzle instance with enhanced configuration
export const db = drizzle(sql, { 
  schema,
  // Enhanced database configuration
  casing: 'snake_case',
  // Query logging only in debug mode
  ...(process.env.NODE_ENV === 'development' && process.env.LOG_LEVEL === 'DEBUG' && {
    logger: {
      logQuery: (query, params) => {
        logger.debug('DB Query', {
          query: query.substring(0, 80) + '...',
          params: params?.length || 0
        });
      }
    }
  })
})

// Database health check function
export async function checkDatabaseHealth() {
  try {
    const start = Date.now();
    await sql`SELECT 1 as health_check`;
    const duration = Date.now() - start;
    
    logger.info('Database health check passed', {
      duration_ms: duration,
      status: 'healthy'
    });
    
    return { healthy: true, duration };
  } catch (error) {
    logger.error('Database health check failed', error);
    return { healthy: false, error: error.message };
  }
}

// Export the sql client for direct queries if needed
export { sql }