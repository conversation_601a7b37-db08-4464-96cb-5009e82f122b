import { relations } from 'drizzle-orm';
import {
  accounts,
  accountSettings,
  assets,
  brandCategories,
  brandGuidelines,
  brandRetailers,
  brands,
  brandUsers,
  categories,
  creatives,
  credentialSetRetailers,
  credentialSets,
  externalReviewers,
  generations,
  generationVersions,
  imageMetadata,
  invitations,
  keywords,
  notifications,
  products,
  projects,
  proofComments,
  proofs,
  retailerProducts,
  retailers,
  reviewers,
  userAccounts,
  userCredentialSets,
  users,
  userSettings
} from './schema';

export const usersRelations = relations(users, ({ one, many }) => ({
  ownedAccounts: many(accounts, { relationName: 'owner' }),
  userAccounts: many(userAccounts),
  brandUsers: many(brandUsers),
  creatives: many(creatives),
  administeredCredentialSets: many(credentialSets, { relationName: 'admin' }),
  userCredentialSets: many(userCredentialSets),
  createdGenerationVersions: many(generationVersions),
  createdProjects: many(projects),
  createdReviewerEntries: many(reviewers, { relationName: 'creator' }),
  requestedReviewerEntries: many(reviewers, { relationName: 'requester' }),
  userSettings: one(userSettings, {
    fields: [users.id],
    references: [userSettings.userId]
  }),
  notifications: many(notifications),
  uploadedBrandGuidelines: many(brandGuidelines),
  requestedExternalReviewers: many(externalReviewers)
}));

export const accountsRelations = relations(accounts, ({ one, many }) => ({
  owner: one(users, {
    fields: [accounts.ownerId],
    references: [users.id],
    relationName: 'owner'
  }),
  userAccounts: many(userAccounts),
  settings: one(accountSettings, {
    fields: [accounts.id],
    references: [accountSettings.accountId]
  }),
  assets: many(assets),
  brands: many(brands),
  categories: many(categories),
  credentialSets: many(credentialSets),
  invitations: many(invitations),
  keywords: many(keywords),
  notifications: many(notifications),
  products: many(products),
  externalReviewers: many(externalReviewers)
}));

export const userAccountsRelations = relations(userAccounts, ({ one }) => ({
  user: one(users, {
    fields: [userAccounts.userId],
    references: [users.id]
  }),
  account: one(accounts, {
    fields: [userAccounts.accountId],
    references: [accounts.id]
  })
}));

export const accountSettingsRelations = relations(accountSettings, ({ one }) => ({
  account: one(accounts, {
    fields: [accountSettings.accountId],
    references: [accounts.id]
  })
}));

export const assetsRelations = relations(assets, ({ one }) => ({
  account: one(accounts, {
    fields: [assets.accountId],
    references: [accounts.id]
  })
}));

export const brandsRelations = relations(brands, ({ one, many }) => ({
  account: one(accounts, {
    fields: [brands.accountId],
    references: [accounts.id]
  }),
  primaryCredentialSet: one(credentialSets, {
    fields: [brands.primaryCredentialId],
    references: [credentialSets.id]
  }),
  brandCategories: many(brandCategories),
  brandUsers: many(brandUsers),
  brandGuidelines: many(brandGuidelines),
  creatives: many(creatives),
  generations: many(generations),
  products: many(products),
  projects: many(projects),
  brandRetailers: many(brandRetailers)
}));

export const brandUsersRelations = relations(brandUsers, ({ one }) => ({
  brand: one(brands, {
    fields: [brandUsers.brandId],
    references: [brands.id]
  }),
  user: one(users, {
    fields: [brandUsers.userId],
    references: [users.id]
  })
}));

export const brandCategoriesRelations = relations(brandCategories, ({ one }) => ({
  brand: one(brands, {
    fields: [brandCategories.brandId],
    references: [brands.id]
  }),
  category: one(categories, {
    fields: [brandCategories.categoryId],
    references: [categories.id]
  })
}));

export const categoriesRelations = relations(categories, ({ one, many }) => ({
  account: one(accounts, {
    fields: [categories.accountId],
    references: [accounts.id]
  }),
  brandCategories: many(brandCategories)
}));

export const brandGuidelinesRelations = relations(brandGuidelines, ({ one }) => ({
  brand: one(brands, {
    fields: [brandGuidelines.brandId],
    references: [brands.id]
  }),
  uploader: one(users, {
    fields: [brandGuidelines.uploadedBy],
    references: [users.id]
  })
}));

export const credentialSetsRelations = relations(credentialSets, ({ one, many }) => ({
  account: one(accounts, {
    fields: [credentialSets.accountId],
    references: [accounts.id]
  }),
  admin: one(users, {
    fields: [credentialSets.adminId],
    references: [users.id],
    relationName: 'admin'
  }),
  userCredentialSets: many(userCredentialSets),
  credentialSetRetailers: many(credentialSetRetailers)
}));

export const userCredentialSetsRelations = relations(userCredentialSets, ({ one }) => ({
  user: one(users, {
    fields: [userCredentialSets.userId],
    references: [users.id]
  }),
  credentialSet: one(credentialSets, {
    fields: [userCredentialSets.credentialSetId],
    references: [credentialSets.id]
  })
}));

export const retailersRelations = relations(retailers, ({ many }) => ({
  credentialSetRetailers: many(credentialSetRetailers),
  brandRetailers: many(brandRetailers),
  retailerProducts: many(retailerProducts),
  externalReviewers: many(externalReviewers)
}));

export const credentialSetRetailersRelations = relations(credentialSetRetailers, ({ one }) => ({
  credentialSet: one(credentialSets, {
    fields: [credentialSetRetailers.credentialSetId],
    references: [credentialSets.id]
  }),
  retailer: one(retailers, {
    fields: [credentialSetRetailers.retailerId],
    references: [retailers.id]
  })
}));

export const brandRetailersRelations = relations(brandRetailers, ({ one }) => ({
  brand: one(brands, {
    fields: [brandRetailers.brandId],
    references: [brands.id]
  }),
  retailer: one(retailers, {
    fields: [brandRetailers.retailerId],
    references: [retailers.id]
  })
}));

export const retailerProductsRelations = relations(retailerProducts, ({ one }) => ({
  retailer: one(retailers, {
    fields: [retailerProducts.retailerId],
    references: [retailers.id]
  }),
  product: one(products, {
    fields: [retailerProducts.productId],
    references: [products.productId]
  })
}));

export const productsRelations = relations(products, ({ one, many }) => ({
  account: one(accounts, {
    fields: [products.accountId],
    references: [accounts.id]
  }),
  brand: one(brands, {
    fields: [products.brandId],
    references: [brands.id]
  }),
  generations: many(generations),
  imageMetadata: many(imageMetadata),
  retailerProducts: many(retailerProducts)
}));

export const imageMetadataRelations = relations(imageMetadata, ({ one }) => ({
  product: one(products, {
    fields: [imageMetadata.productId],
    references: [products.productId]
  })
}));

export const keywordsRelations = relations(keywords, ({ one, many }) => ({
  account: one(accounts, {
    fields: [keywords.accountId],
    references: [accounts.id]
  }),
  generations: many(generations)
}));

export const projectsRelations = relations(projects, ({ one, many }) => ({
  brand: one(brands, {
    fields: [projects.brandId],
    references: [brands.id]
  }),
  creator: one(users, {
    fields: [projects.createdBy],
    references: [users.id]
  }),
  generations: many(generations),
  creatives: many(creatives)
}));

export const generationsRelations = relations(generations, ({ one, many }) => ({
  product: one(products, {
    fields: [generations.productId],
    references: [products.productId]
  }),
  keyword: one(keywords, {
    fields: [generations.keywordId],
    references: [keywords.id]
  }),
  project: one(projects, {
    fields: [generations.projectId],
    references: [projects.id]
  }),
  brand: one(brands, {
    fields: [generations.brandId],
    references: [brands.id]
  }),
  creatives: many(creatives),
  currentVersion: one(generationVersions, {
    fields: [generations.generationVersionId],
    references: [generationVersions.id]
  }),
  versions: many(generationVersions)
}));

export const creativesRelations = relations(creatives, ({ one }) => ({
  brand: one(brands, {
    fields: [creatives.brandId],
    references: [brands.id]
  }),
  user: one(users, {
    fields: [creatives.userId],
    references: [users.id]
  }),
  generation: one(generations, {
    fields: [creatives.generationId],
    references: [generations.id]
  }),
  project: one(projects, {
    fields: [creatives.projectId],
    references: [projects.id]
  })
}));

export const generationVersionsRelations = relations(generationVersions, ({ one, many }) => ({
  generation: one(generations, {
    fields: [generationVersions.generationId],
    references: [generations.id]
  }),
  creator: one(users, {
    fields: [generationVersions.createdBy],
    references: [users.id]
  }),
  proofs: many(proofs)
}));

export const proofsRelations = relations(proofs, ({ one, many }) => ({
  generationVersion: one(generationVersions, {
    fields: [proofs.generationVersionId],
    references: [generationVersions.id]
  }),
  reviewers: many(reviewers),
  comments: many(proofComments)
}));

export const reviewersRelations = relations(reviewers, ({ one }) => ({
  proof: one(proofs, {
    fields: [reviewers.proofId],
    references: [proofs.id]
  }),
  user: one(users, {
    fields: [reviewers.userId],
    references: [users.id],
    relationName: 'creator'
  }),
  requestedByUser: one(users, {
    fields: [reviewers.requestedBy],
    references: [users.id],
    relationName: 'requester'
  })
}));

export const proofCommentsRelations = relations(proofComments, ({ one, many }) => ({
  proof: one(proofs, {
    fields: [proofComments.proofId],
    references: [proofs.id]
  }),
  reviewer: one(reviewers, {
    fields: [proofComments.reviewerId],
    references: [reviewers.id]
  }),
  replyTo: one(proofComments, {
    fields: [proofComments.replyToId],
    references: [proofComments.id],
    relationName: 'replies'
  }),
  replies: many(proofComments, {
    relationName: 'replies'
  })
}));

export const invitationsRelations = relations(invitations, ({ one }) => ({
  account: one(accounts, {
    fields: [invitations.accountId],
    references: [accounts.id]
  })
}));

export const notificationsRelations = relations(notifications, ({ one }) => ({
  user: one(users, {
    fields: [notifications.userId],
    references: [users.id]
  }),
  account: one(accounts, {
    fields: [notifications.accountId],
    references: [accounts.id]
  })
}));

export const userSettingsRelations = relations(userSettings, ({ one }) => ({
  user: one(users, {
    fields: [userSettings.userId],
    references: [users.id]
  })
}));

export const externalReviewersRelations = relations(externalReviewers, ({ one }) => ({
  account: one(accounts, {
    fields: [externalReviewers.accountId],
    references: [accounts.id]
  }),
  requestedByUser: one(users, {
    fields: [externalReviewers.requestedByUserId],
    references: [users.id]
  }),
  retailer: one(retailers, {
    fields: [externalReviewers.retailerId],
    references: [retailers.id]
  })
}));
