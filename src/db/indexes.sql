-- Database Index Optimizations for ADVID Server
-- Run these indexes to improve query performance

-- Foreign Key Indexes (Critical for JOIN performance)
CREATE INDEX IF NOT EXISTS idx_brands_account_id ON brands(account_id);
CREATE INDEX IF NOT EXISTS idx_generations_project_id ON generations(project_id);
CREATE INDEX IF NOT EXISTS idx_generations_keyword_id ON generations(keyword_id);
CREATE INDEX IF NOT EXISTS idx_brand_users_user_id ON brand_users(user_id);
CREATE INDEX IF NOT EXISTS idx_brand_users_brand_id ON brand_users(brand_id);
CREATE INDEX IF NOT EXISTS idx_user_accounts_user_id ON user_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_accounts_account_id ON user_accounts(account_id);
CREATE INDEX IF NOT EXISTS idx_products_account_id ON products(account_id);
CREATE INDEX IF NOT EXISTS idx_creatives_brand_id ON creatives(brand_id);
CREATE INDEX IF NOT EXISTS idx_keywords_account_id ON keywords(account_id);
CREATE INDEX IF NOT EXISTS idx_retailers_account_id ON retailers(account_id);

-- Composite Indexes for Common Query Patterns
CREATE INDEX IF NOT EXISTS idx_user_accounts_user_id_account_id ON user_accounts(user_id, account_id);
CREATE INDEX IF NOT EXISTS idx_brand_users_user_id_brand_id ON brand_users(user_id, brand_id);
CREATE INDEX IF NOT EXISTS idx_generations_project_id_keyword_id ON generations(project_id, keyword_id);
CREATE INDEX IF NOT EXISTS idx_creatives_brand_id_status ON creatives(brand_id, status);
CREATE INDEX IF NOT EXISTS idx_generations_brand_id_status ON generations(brand_id, status);

-- Query-Specific Indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_auth0_id ON users(auth0_id);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON invitations(token);
CREATE INDEX IF NOT EXISTS idx_creative_projects_account_id ON creative_projects(account_id);
CREATE INDEX IF NOT EXISTS idx_creative_projects_brand_id ON creative_projects(brand_id);

-- Timestamp-based indexes for filtering and sorting
CREATE INDEX IF NOT EXISTS idx_generations_created_at ON generations(created_at);
CREATE INDEX IF NOT EXISTS idx_creatives_created_at ON creatives(created_at);
CREATE INDEX IF NOT EXISTS idx_creative_projects_created_at ON creative_projects(created_at);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Full-text search indexes (if using PostgreSQL text search)
CREATE INDEX IF NOT EXISTS idx_brands_name_search ON brands USING GIN(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_products_name_search ON products USING GIN(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_keywords_keyword_search ON keywords USING GIN(to_tsvector('english', keyword));

-- Partial indexes for common filtered queries
CREATE INDEX IF NOT EXISTS idx_users_active ON users(id) WHERE active = true;
CREATE INDEX IF NOT EXISTS idx_generations_active ON generations(id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_creatives_active ON creatives(id) WHERE status = 'active';

-- Index for authorization service performance
CREATE INDEX IF NOT EXISTS idx_user_accounts_permissions ON user_accounts(user_id, account_id, role);

-- Additional indexes for commonly queried fields (BAF-52)
-- accountId indexes
CREATE INDEX IF NOT EXISTS idx_assets_account_id ON assets(account_id);
CREATE INDEX IF NOT EXISTS idx_credential_sets_account_id ON credential_sets(account_id);
CREATE INDEX IF NOT EXISTS idx_account_settings_account_id ON account_settings(account_id);

-- userId indexes
CREATE INDEX IF NOT EXISTS idx_generation_versions_created_by ON generation_versions(created_by);
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON projects(created_by);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_credential_sets_user_id ON user_credential_sets(user_id);
CREATE INDEX IF NOT EXISTS idx_reviewers_user_id ON reviewers(user_id);

-- Primary key indexes (automatically created but explicitly listed for documentation)
CREATE INDEX IF NOT EXISTS idx_users_id ON users(id);
CREATE INDEX IF NOT EXISTS idx_projects_id ON projects(id);
CREATE INDEX IF NOT EXISTS idx_brands_id ON brands(id);
CREATE INDEX IF NOT EXISTS idx_generations_id ON generations(id);

-- brandId indexes
CREATE INDEX IF NOT EXISTS idx_brand_guidelines_brand_id ON brand_guidelines(brand_id);
CREATE INDEX IF NOT EXISTS idx_brand_categories_brand_id ON brand_categories(brand_id);
CREATE INDEX IF NOT EXISTS idx_brand_retailers_brand_id ON brand_retailers(brand_id);

-- generationId indexes
CREATE INDEX IF NOT EXISTS idx_generation_versions_generation_id ON generation_versions(generation_id);

-- Additional foreign key indexes
CREATE INDEX IF NOT EXISTS idx_proofs_generation_version_id ON proofs(generation_version_id);
CREATE INDEX IF NOT EXISTS idx_proof_comments_proof_id ON proof_comments(proof_id);
CREATE INDEX IF NOT EXISTS idx_proof_comments_reviewer_id ON proof_comments(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_image_metadata_product_id ON image_metadata(product_id);
CREATE INDEX IF NOT EXISTS idx_credential_set_retailers_credential_set_id ON credential_set_retailers(credential_set_id);
CREATE INDEX IF NOT EXISTS idx_credential_set_retailers_retailer_id ON credential_set_retailers(retailer_id);
CREATE INDEX IF NOT EXISTS idx_user_credential_sets_credential_set_id ON user_credential_sets(credential_set_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_account_id ON notifications(account_id);
CREATE INDEX IF NOT EXISTS idx_external_reviewers_user_id ON external_reviewers(user_id);
CREATE INDEX IF NOT EXISTS idx_external_reviewers_account_id ON external_reviewers(account_id);
CREATE INDEX IF NOT EXISTS idx_external_reviewers_brand_id ON external_reviewers(brand_id);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_generation_versions_generation_id_version ON generation_versions(generation_id, version);
CREATE INDEX IF NOT EXISTS idx_proof_comments_proof_id_created_at ON proof_comments(proof_id, created_at);
CREATE INDEX IF NOT EXISTS idx_assets_account_id_created_at ON assets(account_id, created_at);

-- Partial indexes for active/status filtering
CREATE INDEX IF NOT EXISTS idx_user_credential_sets_active ON user_credential_sets(user_id, credential_set_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_notifications_unread ON notifications(user_id, created_at) WHERE is_read = false;

-- Performance indexes for timestamp-based queries
CREATE INDEX IF NOT EXISTS idx_generation_versions_created_at ON generation_versions(created_at);
CREATE INDEX IF NOT EXISTS idx_proofs_created_at ON proofs(created_at);
CREATE INDEX IF NOT EXISTS idx_proof_comments_created_at ON proof_comments(created_at);
CREATE INDEX IF NOT EXISTS idx_assets_created_at ON assets(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- Notify completion
SELECT 'Database indexes created successfully!' AS status;