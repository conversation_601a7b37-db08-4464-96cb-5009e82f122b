import pkg from "pg";
const { Client, Pool } = pkg;

/**
 * Creates a new PostgreSQL client connection using SERVICE_DB environment variable
 * @returns {Client} PostgreSQL client instance
 */
export function createServiceDbClient() {
  if (!process.env.SERVICE_DB) {
    throw new Error("SERVICE_DB environment variable is not set");
  }

  return new Client({
    connectionString: process.env.SERVICE_DB,
  });
}

/**
 * Creates a PostgreSQL connection pool using SERVICE_DB environment variable
 * Use this for better performance with multiple connections
 * @returns {Pool} PostgreSQL pool instance
 */
export function createServiceDbPool() {
  if (!process.env.SERVICE_DB) {
    throw new Error("SERVICE_DB environment variable is not set");
  }

  return new Pool({
    connectionString: process.env.SERVICE_DB,
    max: 10, // Maximum number of clients in the pool
    idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
    connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
  });
}

/**
 * Execute a query using a single client connection
 * @param {string} query - SQL query string
 * @param {Array} params - Query parameters
 * @returns {Promise<Object>} Query result
 */
export async function executeServiceDbQuery(query, params = []) {
  const client = createServiceDbClient();

  try {
    await client.connect();
    const result = await client.query(query, params);
    return result;
  } finally {
    await client.end();
  }
}

// Create a singleton pool instance for reuse
let serviceDbPool = null;

/**
 * Get or create the service database pool
 * @returns {Pool} PostgreSQL pool instance
 */
export function getServiceDbPool() {
  if (!serviceDbPool) {
    serviceDbPool = createServiceDbPool();
  }
  return serviceDbPool;
}

/**
 * Execute a query using the connection pool (recommended for multiple queries)
 * @param {string} query - SQL query string
 * @param {Array} params - Query parameters
 * @returns {Promise<Object>} Query result
 */
export async function executeServiceDbPoolQuery(query, params = []) {
  const pool = getServiceDbPool();
  return await pool.query(query, params);
}
