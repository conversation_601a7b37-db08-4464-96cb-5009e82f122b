import { Queue } from 'bullmq';
import IORedis from 'ioredis';
import { logger } from '../utils/logger.js';

/**
 * Email Job Types
 * These constants define the valid email job types for the queue system
 */
export const EMAIL_JOB_TYPES = {
  INVITATION: 'invitation',
  BETA_INVITATION: 'beta-invitation',
  PASSWORD_RESET: 'password-reset',
  CUSTOM: 'custom',
} as const;

/**
 * Email Queue Service
 *
 * Uses a separate Redis connection for email queue processing to avoid
 * conflicts with the main server's session storage Redis instance.
 *
 * Environment Variables:
 * - QUEUE_REDIS_URL: Dedicated Redis URL for email queue (separate instance)
 *   If not provided, the service will operate in "disabled" mode and log warnings
 *   instead of processing emails through the queue.
 *
 * Railway Setup:
 * 1. Create separate Redis instance for email queue
 * 2. Set QUEUE_REDIS_URL to the new Redis instance URL
 * 3. Email service uses same Redis instance for queue processing
 */

// Job types matching the email service
interface BaseEmailJob {
  to: string;
  timestamp: number;
  retryCount?: number;
}

interface InvitationEmailJob extends BaseEmailJob {
  type: typeof EMAIL_JOB_TYPES.INVITATION;
  invitationType: 'request' | 'invite';
  invitationId: string;
  organizationName?: string;
  senderName?: string;
}

interface BetaInvitationEmailJob extends BaseEmailJob {
  type: typeof EMAIL_JOB_TYPES.BETA_INVITATION;
  senderName: string;
  senderEmail: string;
  betaCode: string;
  personalMessage?: string;
}

interface PasswordResetEmailJob extends BaseEmailJob {
  type: typeof EMAIL_JOB_TYPES.PASSWORD_RESET;
  resetUrl: string;
}

interface CustomEmailJob extends BaseEmailJob {
  type: typeof EMAIL_JOB_TYPES.CUSTOM;
  subject: string;
  heading?: string;
  content: string;
  buttonText?: string;
  buttonLink?: string;
  footerText?: string;
  fromName?: string;
  replyTo?: string;
}

type EmailJob = InvitationEmailJob | BetaInvitationEmailJob | PasswordResetEmailJob | CustomEmailJob;

/**
 * Validation helper functions
 */
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

const validateNonEmptyString = (value: any, fieldName: string): void => {
  if (typeof value !== 'string' || value.trim().length === 0) {
    throw new Error(`${fieldName} is required and must be a non-empty string`);
  }
};

const validateEmailAddress = (email: any, fieldName: string = 'email'): void => {
  validateNonEmptyString(email, fieldName);
  if (!validateEmail(email.trim())) {
    throw new Error(`${fieldName} must be a valid email address`);
  }
};

class EmailQueueService {
  private queue: Queue | null = null;
  private redis: IORedis | null = null;
  private isDisabled: boolean = false;
  private connectionFailureCount: number = 0;
  private maxConnectionFailures: number = 3;

  constructor() {
    // Use separate Redis URL for email queue to avoid conflicts with session storage
    const queueRedisUrl = process.env.QUEUE_REDIS_URL;

    if (!queueRedisUrl) {
      logger.warn('QUEUE_REDIS_URL environment variable not set. Email queue service running in disabled mode - emails will be logged but not queued.');
      this.isDisabled = true;
      return;
    }

    try {
      this.redis = new IORedis(queueRedisUrl, {
        // Configure for Railway's internal networking and IPv6 support
        family: 0, // Auto-detect IPv4/IPv6 (use 6 to force IPv6)
        enableReadyCheck: true,
        maxRetriesPerRequest: null, // Allow infinite retries for queue operations
        lazyConnect: true, // Don't connect immediately
        connectTimeout: 5000, // Reduced from 10 seconds
        commandTimeout: 3000, // Reduced from 5 seconds
        enableOfflineQueue: false, // Fail-fast behavior for immediate feedback
        keepAlive: 30000, // 30 seconds
        // Circuit breaker configuration - disable automatic reconnection after failures
        autoResubscribe: false,
        autoResendUnfulfilledCommands: false,
        // Production-ready retry strategy with exponential backoff
        retryStrategy: (times: number) => {
          const delay = Math.min(Math.exp(times) * 1000, 20000);
          logger.debug('Redis retry attempt', { attempt: times, delayMs: delay });
          return delay;
        },
        // Reconnect on specific errors like READONLY
        reconnectOnError: (err: Error) => {
          const targetErrors = ['READONLY', 'ECONNRESET', 'ENOTFOUND'];
          const shouldReconnect = targetErrors.some(target => err.message.includes(target));
          if (shouldReconnect) {
            logger.debug('Reconnecting Redis due to error', { error: err.message });
            return true;
          }
          return false;
        },
      });

      // Add event handlers for Redis connection debugging
      this.redis.on('error', (error: Error) => {
        this.connectionFailureCount++;

        // Check for DNS resolution failures (ENOTFOUND)
        const isDnsError = 'code' in error && (error as any).code === 'ENOTFOUND';
        const isConnectionError = 'code' in error && ['ECONNREFUSED', 'ECONNRESET', 'ETIMEDOUT'].includes((error as any).code);

        if (isDnsError) {
          logger.error('DNS resolution failed for Redis hostname', error, {
            connectionFailureCount: this.connectionFailureCount,
            maxFailures: this.maxConnectionFailures
          });

          // Disable service after too many DNS failures
          if (this.connectionFailureCount >= this.maxConnectionFailures) {
            logger.error('Too many DNS failures, disabling email queue service', error, {
              failures: this.connectionFailureCount,
              maxFailures: this.maxConnectionFailures
            });
            this.disableService();
            return;
          }
        } else if (isConnectionError) {
          logger.error('Redis connection error - server may be down or unreachable', error, {
            errorCode: 'code' in error ? (error as any).code : undefined,
            address: 'address' in error ? (error as any).address : undefined,
            port: 'port' in error ? (error as any).port : undefined
          });
        } else {
          logger.error('Redis error', error, {
            errorCode: 'code' in error ? (error as any).code : undefined,
            address: 'address' in error ? (error as any).address : undefined
          });
        }

        logger.warn('Email queue connection failure count updated', {
          failures: this.connectionFailureCount,
          maxFailures: this.maxConnectionFailures
        });
      });

      this.redis.on('connect', () => {
        logger.debug('Redis connection established');
        // Reset failure count on successful connection
        this.connectionFailureCount = 0;
      });

      this.redis.on('ready', () => {
        logger.debug('Redis ready for commands');
      });

      this.redis.on('reconnecting', (delayMs: number) => {
        logger.debug('Redis reconnecting', { delayMs });
      });

      this.redis.on('close', () => {
        logger.debug('Redis connection closed');
      });

      this.redis.on('end', () => {
        logger.debug('Redis connection ended');
      });

      this.queue = new Queue('email-processing', {
        connection: this.redis,
        defaultJobOptions: {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: 100,
          removeOnFail: 50,
        },
      });

      // Add critical error event listener for queue instance
      this.queue.on('error', (err: Error) => {
        logger.error('Email queue error', err);
        // Increment connection failure count on queue errors
        this.connectionFailureCount++;
        
        // Check if we should disable the service
        if (this.connectionFailureCount >= this.maxConnectionFailures) {
          logger.error('Queue errors exceeded threshold, disabling service', err, {
            failures: this.connectionFailureCount,
            maxFailures: this.maxConnectionFailures
          });
          this.disableService();
        }
      });

      if (process.env.LOG_LEVEL === 'DEBUG') {
        logger.debug('EmailQueue service initialized successfully');
      }
    } catch (error) {
      logger.error('Failed to initialize Redis connection, falling back to disabled mode', error);
      this.isDisabled = true;
      this.redis = null;
      this.queue = null;
    }
  }

  private logDisabledWarning(operation: string, recipient: string): void {
    logger.warn('Email queue disabled - email logged but not queued', {
      operation,
      recipient,
      note: 'Set QUEUE_REDIS_URL to enable email queueing'
    });
  }

  private async disableService(): Promise<void> {
    console.log('[EmailQueue] Disabling email queue service due to connection failures');
    this.isDisabled = true;

    try {
      // Close existing connections gracefully
      if (this.queue) {
        // Remove queue event listeners to prevent memory leaks
        this.queue.removeAllListeners();
        await this.queue.close();
        this.queue = null;
      }
      if (this.redis) {
        // Remove all event listeners to prevent memory leaks
        this.redis.removeAllListeners();
        await this.redis.disconnect();
        this.redis = null;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[EmailQueue] Error while disabling service:', errorMessage);
    }

    console.log('[EmailQueue] Service disabled. All future email operations will log warnings instead of queueing.');
  }

  async addInvitationEmail(jobData: Omit<InvitationEmailJob, 'type' | 'timestamp'>): Promise<void> {
    // Validate required fields regardless of queue status
    validateEmailAddress(jobData.to, 'recipient email');
    validateNonEmptyString(jobData.invitationId, 'invitationId');

    // Validate invitationType enum
    if (!['request', 'invite'].includes(jobData.invitationType)) {
      throw new Error('invitationType must be either "request" or "invite"');
    }

    // Validate optional fields if provided
    if (jobData.organizationName !== undefined) {
      validateNonEmptyString(jobData.organizationName, 'organizationName');
    }
    if (jobData.senderName !== undefined) {
      validateNonEmptyString(jobData.senderName, 'senderName');
    }

    if (this.isDisabled || !this.queue) {
      this.logDisabledWarning('Invitation email', jobData.to);
      return;
    }

    const job: InvitationEmailJob = {
      ...jobData,
      type: EMAIL_JOB_TYPES.INVITATION,
      timestamp: Date.now(),
    };

    try {
      const result = await this.queue.add('invitation-email', job);
      console.log(`[EmailQueue] Invitation email job queued for ${jobData.to} (ID: ${result.id})`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[EmailQueue] Failed to queue invitation email for ${jobData.to}:`, errorMessage);
      throw error;
    }
  }

  async addBetaInvitationEmail(jobData: Omit<BetaInvitationEmailJob, 'type' | 'timestamp'>): Promise<void> {
    // Validate required fields regardless of queue status
    validateEmailAddress(jobData.to, 'recipient email');
    validateEmailAddress(jobData.senderEmail, 'sender email');
    validateNonEmptyString(jobData.senderName, 'senderName');
    validateNonEmptyString(jobData.betaCode, 'betaCode');

    // Validate optional fields if provided
    if (jobData.personalMessage !== undefined) {
      validateNonEmptyString(jobData.personalMessage, 'personalMessage');
    }

    if (this.isDisabled || !this.queue) {
      this.logDisabledWarning('Beta invitation email', jobData.to);
      return;
    }

    const job: BetaInvitationEmailJob = {
      ...jobData,
      type: EMAIL_JOB_TYPES.BETA_INVITATION,
      timestamp: Date.now(),
    };

    try {
      const result = await this.queue.add('beta-invitation-email', job);
      console.log(`[EmailQueue] Beta invitation email job queued for ${jobData.to} (ID: ${result.id})`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[EmailQueue] Failed to queue beta invitation email for ${jobData.to}:`, errorMessage);
      throw error;
    }
  }

  async addPasswordResetEmail(jobData: Omit<PasswordResetEmailJob, 'type' | 'timestamp'>): Promise<void> {
    // Validate required fields regardless of queue status
    validateEmailAddress(jobData.to, 'recipient email');
    validateNonEmptyString(jobData.resetUrl, 'resetUrl');

    // Validate URL format
    if (!validateUrl(jobData.resetUrl)) {
      throw new Error('resetUrl must be a valid URL');
    }

    if (this.isDisabled || !this.queue) {
      this.logDisabledWarning('Password reset email', jobData.to);
      return;
    }

    const job: PasswordResetEmailJob = {
      ...jobData,
      type: EMAIL_JOB_TYPES.PASSWORD_RESET,
      timestamp: Date.now(),
    };

    try {
      const result = await this.queue.add('password-reset-email', job);
      console.log(`[EmailQueue] Password reset email job queued for ${jobData.to} (ID: ${result.id})`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[EmailQueue] Failed to queue password reset email for ${jobData.to}:`, errorMessage);
      throw error;
    }
  }

  async addCustomEmail(jobData: Omit<CustomEmailJob, 'type' | 'timestamp'>): Promise<void> {
    // Validate required fields regardless of queue status
    validateEmailAddress(jobData.to, 'recipient email');
    validateNonEmptyString(jobData.subject, 'subject');
    validateNonEmptyString(jobData.content, 'content');

    // Validate optional fields if provided
    if (jobData.heading !== undefined) {
      validateNonEmptyString(jobData.heading, 'heading');
    }
    if (jobData.buttonText !== undefined) {
      validateNonEmptyString(jobData.buttonText, 'buttonText');
    }
    if (jobData.buttonLink !== undefined) {
      validateNonEmptyString(jobData.buttonLink, 'buttonLink');
      if (!validateUrl(jobData.buttonLink)) {
        throw new Error('buttonLink must be a valid URL');
      }
    }
    if (jobData.footerText !== undefined) {
      validateNonEmptyString(jobData.footerText, 'footerText');
    }
    if (jobData.fromName !== undefined) {
      validateNonEmptyString(jobData.fromName, 'fromName');
    }
    if (jobData.replyTo !== undefined) {
      validateEmailAddress(jobData.replyTo, 'replyTo email');
    }

    // Validate button fields consistency
    if (jobData.buttonText && !jobData.buttonLink) {
      throw new Error('buttonLink is required when buttonText is provided');
    }
    if (jobData.buttonLink && !jobData.buttonText) {
      throw new Error('buttonText is required when buttonLink is provided');
    }

    if (this.isDisabled || !this.queue) {
      this.logDisabledWarning('Custom email', jobData.to);
      return;
    }

    const job: CustomEmailJob = {
      ...jobData,
      type: EMAIL_JOB_TYPES.CUSTOM,
      timestamp: Date.now(),
    };

    try {
      const result = await this.queue.add('custom-email', job);
      console.log(`[EmailQueue] Custom email job queued for ${jobData.to} (ID: ${result.id})`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[EmailQueue] Failed to queue custom email for ${jobData.to}:`, errorMessage);
      throw error;
    }
  }

  async getQueueStats() {
    if (this.isDisabled || !this.queue) {
      console.warn('[EmailQueue] getQueueStats - Queue disabled. Returning zero stats.');
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        disabled: true,
        connectionFailureCount: this.connectionFailureCount,
        maxConnectionFailures: this.maxConnectionFailures
      };
    }

    try {
      const [waiting, active, completed, failed] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getCompleted(),
        this.queue.getFailed(),
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        disabled: false,
        connectionFailureCount: this.connectionFailureCount,
        maxConnectionFailures: this.maxConnectionFailures
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[EmailQueue] Failed to get queue stats:', errorMessage);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        disabled: true,
        error: errorMessage
      };
    }
  }

  async closeQueue(): Promise<void> {
    if (this.isDisabled) {
      console.log('[EmailQueue] Queue already disabled, nothing to close.');
      return;
    }

    try {
      if (this.queue) {
        // Remove event listeners to prevent memory leaks
        this.queue.removeAllListeners();
        await this.queue.close();
        console.log('[EmailQueue] Queue closed successfully');
      }
      if (this.redis) {
        // Remove event listeners to prevent memory leaks
        this.redis.removeAllListeners();
        await this.redis.disconnect();
        console.log('[EmailQueue] Redis connection closed successfully');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[EmailQueue] Error closing queue/redis:', errorMessage);
    }
  }

  // Helper method to check if the service is operational
  isEnabled(): boolean {
    return !this.isDisabled && this.queue !== null && this.redis !== null;
  }

  // Test Redis connection and provide debugging info
  async testConnection(): Promise<{ success: boolean; info: any }> {
    if (this.isDisabled || !this.redis) {
      return { success: false, info: { error: 'Service disabled or Redis not initialized' } };
    }

    try {
      // Test basic connectivity
      const pong = await this.redis.ping();

      // Get connection info
      const info = await this.redis.info('server');

      return {
        success: true,
        info: {
          ping: pong,
          serverInfo: info,
          status: this.redis.status,
          options: {
            host: this.redis.options.host,
            port: this.redis.options.port,
            family: this.redis.options.family,
            db: this.redis.options.db,
          }
        }
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        info: {
          error: errorMessage,
          status: this.redis.status,
          options: {
            host: this.redis.options.host,
            port: this.redis.options.port,
            family: this.redis.options.family,
            db: this.redis.options.db,
          }
        }
      };
    }
  }
}

export const emailQueue = new EmailQueueService();
export type { InvitationEmailJob, BetaInvitationEmailJob, PasswordResetEmailJob, CustomEmailJob };