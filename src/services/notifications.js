import { notifications } from '../db/schema/notifications.js'
import { AuthService } from './auth.service.js'
import { db } from '../db/index.js'

// Store active WebSocket connections
const activeConnections = []

export async function notificationService(fastify) {
  // Register WebSocket route for notifications
  fastify.get('/ws/notifications', { websocket: true }, wsHandler)

  // Helper function to send notification to specific user
  fastify.decorate('sendNotification', async (userId, message) => {
    try {
      // Store notification in database
      await fastify.db.insert(notifications).values({
        user_id: userId,
        message,
        status: 'unread'
      })

      // Send to connected websocket if user is online
      const userConnections = activeConnections.filter(conn => conn.userId === userId)
      userConnections.forEach(conn => {
        try {
          conn.socket.send(JSON.stringify(message))
        } catch (err) {
          console.error(`Failed to send message to user ${userId}:`, err)
          // Remove dead connection
          const index = activeConnections.findIndex(c => c.socket === conn.socket)
          if (index !== -1) activeConnections.splice(index, 1)
        }
      })
    } catch (err) {
      console.error('Error in sendNotification:', err)
    }
  })
}

const wsHandler = (socket, request) => {
  const { userId } = request.query || {}

  if (!userId) {
    socket.close(1008, 'Authentication required')
    return
  }

  // Store new connection
  activeConnections.push({
    userId,
    socket
  })

  // Handle connection close
  socket.on('close', () => {
    const index = activeConnections.findIndex(
      conn => conn.userId === userId && conn.socket === socket
    )
    if (index !== -1) {
      activeConnections.splice(index, 1)
    }
  })

  // Handle incoming messages (optional)
  socket.on('message', async (message) => {
    console.log('message', message)
  })
}

// Helper function to get authenticated user ID from request
async function getUserIdFromRequest(request) {
  // Get user info from the session attached by verifyAuth
  const sessionUser = request.user;

  if (!sessionUser || !sessionUser.auth0Id) {
    throw new Error('User not found in session');
  }

  return sessionUser.auth0Id; // Return the auth0Id (sub) from the session user
}

// Helper function to send message to user(s)
export async function sendMessageToUsers(userIds, message) {
  try {
    // Convert to array if single userId provided
    const targetUsers = Array.isArray(userIds) ? userIds : [userIds]

    // Save notifications to database for each user
    await Promise.all(targetUsers.map(userId =>
      db.insert(notifications).values({
        user_id: userId,
        message,
        status: 'unread'
      })
    ))

    // Find all connections for the target users
    const targetConnections = activeConnections.filter(conn =>
      targetUsers.includes(conn.userId)
    )

    let successCount = 0
    // Send message to each connection
    for (const conn of targetConnections) {
      try {
        conn.socket.send(JSON.stringify(message))
        successCount++
      } catch (err) {
        console.error(`Failed to send message to user:`, err)
        // Remove dead connection
        const index = activeConnections.findIndex(c => c.socket === conn.socket)
        if (index !== -1) activeConnections.splice(index, 1)
      }
    }

    return successCount // Return number of successful sends
  } catch (err) {
    console.error('Error in sendMessageToUsers:', err)
    return 0
  }
}