import { eq, sql } from 'drizzle-orm';
import { db } from '../db/index.js';
import { leads } from '../db/schema/leads.js';
import { users } from '../db/schema/users.js';

/**
 * Service for handling beta code generation and verification
 */
export class BetaService {

  /**
   * Check if a beta code is valid and return the associated lead
   * @param {string} betaCode - The beta code to check
   * @param {string} [email] - Optional email to verify against the lead
   * @returns {Promise<Object>} The lead information
   */
  static async findLeadByBetaCode(betaCode, email) {
    if (!betaCode) {
      throw new Error('Beta code is required');
    }
    if (!email) {
      throw new Error('Email is required');
    }

    // Normalize email to lowercase
    const normalizedEmail = email.toLowerCase();

    // Since betaCode is now the same as betaId, use it directly
    const betaId = betaCode;

    // First, search for the lead with the matching betaId
    let lead = await db
      .select()
      .from(leads)
      .where(eq(leads.betaId, betaId))
      .limit(1);

    // If no lead found by betaId, search through beta_keys of all leads
    if (!lead || lead.length === 0) {
      // Get all leads that have beta_keys
      const allLeads = await db
        .select()
        .from(leads)
        .where(
          // Only get leads where beta_keys is not null
          // This is a performance optimization to avoid checking all leads
          sql`${leads.betaKeys} IS NOT NULL`
        );

      // Find a lead that has the betaCode in its beta_keys array
      const matchingLead = allLeads.find(lead =>
        Array.isArray(lead.betaKeys) && lead.betaKeys.includes(betaId)
      );

      if (matchingLead) {
        // If we found a matching lead, use it
        lead = matchingLead;
      } else {
        return null;
      }
    } else {
      // Extract the lead from the array if it's an array
      if (Array.isArray(lead)) {
        lead = lead[0];
      }
    }

    let leadIsSameAsUser = false;

    if (normalizedEmail && normalizedEmail === lead.email) {
      leadIsSameAsUser = true;
      console.log('using user\'s own beta key');
    } else if (normalizedEmail) {
      console.log('invitee using one of user\'s beta keys');
    }

    lead.isSameAsUser = leadIsSameAsUser;

    return lead;
  }

  /**
   * Check if a beta key has been used for signup
   * @param {string} betaKeyId - The beta key ID to check
   * @returns {Promise<boolean>} True if the beta key has been used, false otherwise
   */
  static async isBetaKeyUsed(betaKeyId) {
    if (!betaKeyId) {
      throw new Error('Beta key ID is required');
    }

    // First, check if there's a lead with this betaKeyId as their betaId
    const leadWithBetaId = await db
      .select()
      .from(leads)
      .where(eq(leads.betaId, betaKeyId))
      .limit(1);

    if (leadWithBetaId && leadWithBetaId.length > 0) {
      return true;
    }
  }
}