import axios from 'axios';

export class AuthService {
  private static AUTH0_DOMAIN = process.env.AUTH0_DOMAIN;

  /**
   * Fetches user information from Auth0 using the provided access token
   * @param token - The Auth0 access token
   * @returns The user information from Auth0
   */
  static async getUserInfo(token: string) {
    try {
      const response = await axios.get(`https://${this.AUTH0_DOMAIN}/userinfo`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching user info:', error);
      throw error;
    }
  }
} 