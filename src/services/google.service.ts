import { Storage } from "@google-cloud/storage";
import dotenv from "dotenv";

dotenv.config();

// Check if required environment variables are present
const requiredEnvVars = [
  "GOOGLE_CLOUD_PROJECT_ID",
  "GOOGLE_CLOUD_CLIENT_EMAIL",
  "GOOGLE_CLOUD_PRIVATE_KEY",
];

requiredEnvVars.forEach((varName) => {
  if (!process.env[varName]) {
    console.error(`Missing required environment variable: ${varName}`);
  }
});

// Initialize storage client using environment variables
export const storage = new Storage({
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  credentials: {
    client_email: process.env.GOOGLE_CLOUD_CLIENT_EMAIL,
    // Make sure to handle newlines in the private key
    private_key: process.env.GOOGLE_CLOUD_PRIVATE_KEY!.replace(/\\n/g, "\n"),
  },
});

/**
 * Returns the MIME type corresponding to the file extension in the given filename.
 *
 * If the extension is not recognized, defaults to 'application/octet-stream'.
 *
 * @param fileName - The name of the file whose MIME type is to be determined
 * @returns The MIME type string for the file
 */
function getMimeType(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  const mimeTypes: { [key: string]: string } = {
    'pdf': 'application/pdf',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    'txt': 'text/plain',
    'json': 'application/json',
    'csv': 'text/csv',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  };
  
  return mimeTypes[extension || ''] || 'application/octet-stream';
}

/**
 * Ensures a file exists in the specified Google Cloud Storage bucket by uploading it if necessary, then returns its public URL.
 *
 * @param bucketName - The name of the Google Cloud Storage bucket
 * @param buffer - The file data to upload if the file does not already exist
 * @param fileName - The name of the file to check or upload
 * @returns The public URL of the file in the bucket
 */
export async function getOrUploadFile(bucketName: string, buffer: Buffer, fileName: string) {
  try {
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(fileName);

    // Check if file exists
    const [exists] = await file.exists();

    if (!exists) {
      // If file doesn't exist, upload it
      const stream = file.createWriteStream({
        metadata: {
          contentType: getMimeType(fileName),
        },
      });

      await new Promise((resolve, reject) => {
        stream.on("error", (err) => reject(err));
        stream.on("finish", resolve);
        stream.end(buffer);
      });
    }

    // Return public URL
    return `https://storage.googleapis.com/${bucketName}/${fileName}`;
    
  } catch (error) {
    console.error("Error in getOrUploadFile:", error);
    throw error;
  }
}
