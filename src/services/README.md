# Services README

This directory contains reusable services that encapsulate business logic and interactions with external systems.

## `notifications.js` - Real-Time Notification Service

This service implements real-time user notifications using WebSockets. It allows the server to push messages to connected clients instantly.

### Architecture

The service uses a WebSocket endpoint to establish persistent connections with clients. An in-memory array (`activeConnections`) on the server stores the active connections, mapping each socket to a `userId`.

When a notification needs to be sent (e.g., from another service or controller), the `sendNotification` function is called. This function first persists the notification in the database and then checks the in-memory array for any active connections for the recipient. If a connection is found, the message is sent over the socket in real-time.

```mermaid
sequenceDiagram
    participant Client
    participant Server
    participant DB as Database

    Client->>Server: HTTP Request to /ws/notifications (Upgrade to WebSocket)
    Note over Client,Server: Connection is authenticated via JWT
    Server-->>Client: WebSocket Connection Established
    Server->>Server: Store connection and userId in `activeConnections` array

    participant OtherService as Another Service/Controller
    OtherService->>Server: Call `fastify.sendNotification(userId, message)`
    Server->>+DB: INSERT INTO notifications (userId, message)
    DB-->>-Server: Notification Saved
    Server->>Server: Find socket for userId in `activeConnections`
    Server-->>Client: PUSH message over WebSocket
```

### Key Components

-   **`GET /ws/notifications`**: The WebSocket endpoint. A client connects to this endpoint to listen for notifications. The `userId` must be provided as a query parameter for authentication.
-   **`fastify.decorate('sendNotification', ...)`**: This function decorates the main Fastify instance with a `sendNotification` method. This makes it easy for any other part of the application to send a notification to a specific user.
-   **Connection Management:** The service handles the lifecycle of WebSocket connections, including storing them on connection, removing them on close, and handling errors gracefully.
-   **Database Persistence:** All notifications are saved to the database, ensuring that users who are offline will see their notifications when they next log in and fetch their notification history.

### Scalability Considerations

The current implementation uses an in-memory array to store active connections. This is suitable for a single-server deployment. For a multi-server, load-balanced environment, this connection store would need to be migrated to a shared, external service like **Redis (using its Pub/Sub capabilities)**. This would allow any server instance to publish a notification that could be delivered to a user connected to any other server instance.
