import { db } from '../db/index.js'
import { users, userAccounts, brandUsers, brands, accounts } from '../db/schema/index.js'
import { eq, and, or, sql } from 'drizzle-orm'

/**
 * Authorization service for role-based access control
 * Handles account-level and brand-level permission checking
 */
export class AuthorizationService {
  
  /**
   * Get user's role for a specific account
   */
  async getUserAccountRole(userId, accountId) {
    try {
      const [userAccount] = await db
        .select({ role: userAccounts.role, status: userAccounts.status })
        .from(userAccounts)
        .where(and(
          eq(userAccounts.userId, userId),
          eq(userAccounts.accountId, accountId)
        ))
        .limit(1)
      
      return userAccount?.status === 'active' ? userAccount.role : null
    } catch (error) {
      console.error('Error getting user account role:', error)
      return null
    }
  }

  /**
   * Get user's role for a specific brand
   */
  async getUserBrandRole(userId, brandId) {
    try {
      const [brandUser] = await db
        .select({ role: brandUsers.role })
        .from(brandUsers)
        .where(and(
          eq(brandUsers.userId, userId),
          eq(brandUsers.brandId, brandId)
        ))
        .limit(1)
      
      return brandUser?.role || null
    } catch (error) {
      console.error('Error getting user brand role:', error)
      return null
    }
  }

  /**
   * Get user's account role for the account that owns a specific brand
   */
  async getUserAccountRoleForBrand(userId, brandId) {
    try {
      const [result] = await db
        .select({ 
          role: userAccounts.role,
          status: userAccounts.status 
        })
        .from(userAccounts)
        .innerJoin(brands, eq(userAccounts.accountId, brands.accountId))
        .where(and(
          eq(userAccounts.userId, userId),
          eq(brands.id, brandId)
        ))
        .limit(1)
      
      return result?.status === 'active' ? result.role : null
    } catch (error) {
      console.error('Error getting user account role for brand:', error)
      return null
    }
  }

  /**
   * Check if user can manage account users (add/remove/modify roles)
   */
  async canManageAccountUsers(userId, accountId) {
    const role = await this.getUserAccountRole(userId, accountId)
    return ['owner', 'admin'].includes(role)
  }

  /**
   * Check if user can view account members
   */
  async canViewAccountMembers(userId, accountId) {
    const role = await this.getUserAccountRole(userId, accountId)
    return ['owner', 'admin', 'member'].includes(role)
  }

  /**
   * Check if user can manage brand users (add/remove/modify roles)
   * Can manage if:
   * - User is brand owner/admin
   * - OR user is account owner/admin for the brand's account
   */
  async canManageBrandUsers(userId, brandId) {
    // Check brand-level role
    const brandRole = await this.getUserBrandRole(userId, brandId)
    if (['owner', 'admin'].includes(brandRole)) return true
    
    // Check account-level role for brand's account
    const accountRole = await this.getUserAccountRoleForBrand(userId, brandId)
    return ['owner', 'admin'].includes(accountRole)
  }

  /**
   * Check if user can upload brand guidelines
   * Can upload if:
   * - User is brand owner/admin
   * - OR user is account owner/admin for the brand's account
   */
  async canUploadBrandGuidelines(userId, brandId) {
    // Check brand-level role
    const brandRole = await this.getUserBrandRole(userId, brandId)
    if (['owner', 'admin'].includes(brandRole)) return true
    
    // Check account-level role for brand's account
    const accountRole = await this.getUserAccountRoleForBrand(userId, brandId)
    return ['owner', 'admin'].includes(accountRole)
  }

  /**
   * Check if user can view brand (any role is sufficient)
   */
  async canViewBrand(userId, brandId) {
    // Check brand-level role
    const brandRole = await this.getUserBrandRole(userId, brandId)
    if (brandRole) return true
    
    // Check account-level role for brand's account
    const accountRole = await this.getUserAccountRoleForBrand(userId, brandId)
    return ['owner', 'admin', 'member'].includes(accountRole)
  }

  /**
   * Check if user can leave a brand
   * Cannot leave if they're the last owner or admin
   */
  async canLeaveBrand(userId, brandId) {
    const userRole = await this.getUserBrandRole(userId, brandId)
    if (!userRole) return false
    
    // If member, can always leave
    if (userRole === 'member') return true
    
    // If owner or admin, check if there are other owners/admins
    try {
      const adminCount = await db
        .select({ count: 'count(*)' })
        .from(brandUsers)
        .where(and(
          eq(brandUsers.brandId, brandId),
          or(eq(brandUsers.role, 'owner'), eq(brandUsers.role, 'admin'))
        ))
      
      return adminCount[0]?.count > 1
    } catch (error) {
      console.error('Error checking brand admin count:', error)
      return false
    }
  }

  /**
   * Get all accounts that a user has access to
   * Returns accounts where user has active membership
   */
  async getUserAccessibleAccounts(userId) {
    try {
      const accessibleAccounts = await db
        .select({
          id: accounts.id,
          name: accounts.name,
          ownerId: accounts.ownerId,
          status: accounts.status,
          logoUrl: accounts.logoUrl,
          type: accounts.type,
          onboardingStep: accounts.onboardingStep,
          stripeCustomerId: accounts.stripeCustomerId,
          validSubscription: accounts.validSubscription,
          hasPaymentMethod: accounts.hasPaymentMethod,
          createdAt: accounts.createdAt,
          updatedAt: accounts.updatedAt,
          userRole: userAccounts.role,
          userStatus: userAccounts.status
        })
        .from(userAccounts)
        .innerJoin(accounts, eq(userAccounts.accountId, accounts.id))
        .where(and(
          eq(userAccounts.userId, userId),
          eq(userAccounts.status, 'active')
        ))
      
      return accessibleAccounts
    } catch (error) {
      console.error('Error getting user accessible accounts:', error)
      return []
    }
  }

  /**
   * Check if user has access to a specific account
   */
  async canAccessAccount(userId, accountId) {
    if (!userId || !accountId) {
      return false;
    }

    const role = await this.getUserAccountRole(userId, accountId)
    return role !== null
  }

  /**
   * Check if user can modify account (admin/owner only)
   */
  async canModifyAccount(userId, accountId) {
    if (!userId || !accountId) {
      return false;
    }

    const role = await this.getUserAccountRole(userId, accountId)
    return ['owner', 'admin'].includes(role)
  }

  /**
   * Check if user can delete account (owner only)
   */
  async canDeleteAccount(userId, accountId) {
    if (!userId || !accountId) {
      return false;
    }
    
    const role = await this.getUserAccountRole(userId, accountId)
    return role === 'owner'
  }

  /**
   * Get all brands that a user has access to
   * Returns brands where user has direct brand access OR account access
   */
  async getUserAccessibleBrands(userId) {
    try {
      // Get brands where user has direct brand access
      const directBrandAccess = await db
        .select({
          id: brands.id,
          name: brands.name,
          accountId: brands.accountId,
          accessType: sql`'direct'`.as('accessType'),
          brandRole: brandUsers.role,
          accountRole: sql`null`.as('accountRole')
        })
        .from(brandUsers)
        .innerJoin(brands, eq(brandUsers.brandId, brands.id))
        .where(eq(brandUsers.userId, userId))

      // Get brands where user has account access (but not direct brand access)
      const accountBrandAccess = await db
        .select({
          id: brands.id,
          name: brands.name,
          accountId: brands.accountId,
          accessType: sql`'account'`.as('accessType'),
          brandRole: sql`null`.as('brandRole'),
          accountRole: userAccounts.role
        })
        .from(userAccounts)
        .innerJoin(brands, eq(userAccounts.accountId, brands.accountId))
        .leftJoin(brandUsers, and(
          eq(brandUsers.brandId, brands.id),
          eq(brandUsers.userId, userId)
        ))
        .where(and(
          eq(userAccounts.userId, userId),
          eq(userAccounts.status, 'active'),
          sql`${brandUsers.userId} IS NULL` // Only get brands where user doesn't have direct access
        ))

      // Combine and return all accessible brands
      return [...directBrandAccess, ...accountBrandAccess]
    } catch (error) {
      console.error('Error getting user accessible brands:', error)
      return []
    }
  }
}

// Export singleton instance
export const authService = new AuthorizationService()