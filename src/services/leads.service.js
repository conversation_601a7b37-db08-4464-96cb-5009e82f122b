import { eq } from 'drizzle-orm';
import { db } from '../db/index.js';
import { leads } from '../db/schema/leads.js';
import { v4 as uuidv4 } from 'uuid'; // Make sure to install this package if not already installed

/**
 * Service for handling lead-related operations
 */
export class LeadsService {
  /**
   * Create a new lead in the database
   * @param {Object} leadData - The lead data
   * @param {string} leadData.fullName - Full name of the lead
   * @param {string} leadData.email - Email of the lead
   * @param {string} leadData.organization - Organization of the lead
   * @param {string} [leadData.source] - Source of the lead (adobe, shoptalk, online) - not stored in DB
   * @returns {Promise<Object>} The created lead
   */
  static async createLead(leadData) {
    // Validate required fields
    if (!leadData.fullName || !leadData.email || !leadData.organization) {
      throw new Error('Missing required fields: fullName, email, and organization are required');
    }

    // Normalize email to lowercase
    const normalizedEmail = leadData.email.toLowerCase();

    // Check if lead with this email or betaId already exists
    const existingLead = await db
      .select()
      .from(leads)
      .where(
        leadData.betaId
          ? eq(leads.betaId, leadData.betaId)
          : eq(leads.email, normalizedEmail)
      )
      .limit(1);

    if (existingLead && existingLead.length > 0) {
      console.log('lead with this email or betaId already exists (TODO: use existing lead)')
      throw new Error('A lead with this email already exists');
    }

    // Use provided betaId or generate a new one
    const betaId = leadData.betaId || uuidv4();

    // Generate two additional beta keys for friends
    const friendBetaKey1 = uuidv4();
    const friendBetaKey2 = uuidv4();

    // Create an array of beta keys
    const betaKeys = [friendBetaKey1, friendBetaKey2];

    console.log('Generated beta keys for friends:', betaKeys);

    // Create the lead in the database with normalized email
    const insertValues = {
      fullName: leadData.fullName,
      email: normalizedEmail,
      organization: leadData.organization,
      betaId,
      betaKeys,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Add optional fields if present
    if (leadData.source) {
      insertValues.source = leadData.source;
    }

    if (leadData.inviteStatus) {
      insertValues.inviteStatus = leadData.inviteStatus;
    }

    const [newLead] = await db
      .insert(leads)
      .values(insertValues)
      .returning();

    // Return the lead
    return newLead;
  }

  /**
   * Update a lead by email
   * @param {string} email - The email of the lead to update
   * @param {Object} updateData - The data to update
   * @param {string} [updateData.inviteStatus] - The new invite status
   * @returns {Promise<Object>} The updated lead
   */
  static async updateLead(email, updateData) {
    if (!email) {
      throw new Error('Email is required to update a lead');
    }

    // Normalize email to lowercase
    const normalizedEmail = email.toLowerCase();

    // Prepare update data
    const updates = {};

    if (updateData.inviteStatus) {
      updates.inviteStatus = updateData.inviteStatus;
    }

    // Always update the updatedAt timestamp
    updates.updatedAt = new Date();

    // Update the lead
    const result = await db
      .update(leads)
      .set(updates)
      .where(eq(leads.email, normalizedEmail))
      .returning();

    if (!result || result.length === 0) {
      throw new Error(`No lead found with email: ${email}`);
    }

    return result[0];
  }

  /**
   * Track when a beta key is sent to a friend
   * @param {string} email - The email of the lead
   * @param {string} betaKey - The beta key that was sent
   * @param {string} friendEmail - The email of the friend who received the key
   * @returns {Promise<Object>} The updated lead
   */
  static async trackSentBetaKey(email, betaKey, friendEmail) {
    if (!email || !betaKey || !friendEmail) {
      throw new Error('Email, beta key, and friend email are required');
    }

    // Normalize both emails to lowercase
    const normalizedEmail = email.toLowerCase();
    const normalizedFriendEmail = friendEmail.toLowerCase();

    // Get the lead
    const lead = await this.getLeadByEmail(email);

    if (!lead) {
      throw new Error(`No lead found with email: ${email}`);
    }

    // Check if the beta key belongs to this lead
    if (!lead.betaKeys || !lead.betaKeys.includes(betaKey)) {
      throw new Error('This beta key does not belong to this lead');
    }

    // Check if this key has already been sent
    const sentKeys = lead.sentKeys || [];
    const existingSentKey = sentKeys.find(sk => sk.key === betaKey);

    if (existingSentKey) {
      throw new Error('This beta key has already been sent');
    }

    // Add the sent key record
    const updatedSentKeys = [
      ...sentKeys,
      { key: betaKey, email: normalizedFriendEmail, sentAt: new Date() }
    ];

    // Update the lead
    const result = await db
      .update(leads)
      .set({
        sentKeys: updatedSentKeys,
        updatedAt: new Date()
      })
      .where(eq(leads.email, normalizedEmail))
      .returning();

    return result[0];
  }

  /**
   * Get a lead by email
   * @param {string} email - The email to search for
   * @returns {Promise<Object|null>} The lead or null if not found
   */
  static async getLeadByEmail(email) {
    if (!email) {
      throw new Error('Email is required');
    }

    // Normalize email to lowercase
    const normalizedEmail = email.toLowerCase();

    const result = await db
      .select()
      .from(leads)
      .where(eq(leads.email, normalizedEmail))
      .limit(1);

    return result.length > 0 ? result[0] : null;
  }
}