/**
 * Standardized logging utility for ADVID Server
 * Provides structured logging with consistent formatting and security
 */

interface LogContext {
  [key: string]: any;
}

interface SecurityContext {
  userId?: string;
  auth0Id?: string;
  sessionId?: string;
  ip?: string;
  userAgent?: string;
}

enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

class Logger {
  private serviceName: string;
  private logLevel: LogLevel;

  constructor(serviceName: string = 'advid-server') {
    this.serviceName = serviceName;
    this.logLevel = this.getLogLevel();
  }

  private getLogLevel(): LogLevel {
    const level = process.env.LOG_LEVEL?.toUpperCase() || (process.env.NODE_ENV === 'development' ? 'WARN' : 'INFO');
    return LogLevel[level as keyof typeof LogLevel] ?? LogLevel.INFO;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private formatLog(level: string, message: string, context?: LogContext): object {
    const timestamp = new Date().toISOString();
    
    return {
      timestamp,
      level,
      service: this.serviceName,
      message,
      ...this.sanitizeContext(context),
      ...(process.env.NODE_ENV === 'development' && { 
        stack: new Error().stack?.split('\n')[3]?.trim() 
      })
    };
  }

  private sanitizeContext(context?: LogContext): LogContext {
    if (!context) return {};

    const sanitized = { ...context };
    
    // Remove or mask sensitive data
    const sensitiveKeys = [
      'password', 'secret', 'token', 'apiKey', 'api_key',
      'authorization', 'cookie', 'session', 'auth',
      'creditCard', 'ssn', 'socialSecurity'
    ];

    for (const key in sanitized) {
      const lowerKey = key.toLowerCase();
      if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
      
      // Mask email addresses (keep domain for debugging)
      if (typeof sanitized[key] === 'string' && sanitized[key].includes('@')) {
        const email = sanitized[key] as string;
        const [local, domain] = email.split('@');
        sanitized[key] = `${local.substring(0, 2)}***@${domain}`;
      }
    }

    return sanitized;
  }

  private output(logObject: object): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(JSON.stringify(logObject, null, 2));
    } else {
      console.log(JSON.stringify(logObject));
    }
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.output(this.formatLog('DEBUG', message, context));
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.INFO)) {
      this.output(this.formatLog('INFO', message, context));
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.WARN)) {
      this.output(this.formatLog('WARN', message, context));
    }
  }

  error(message: string, error?: Error | unknown, context?: LogContext): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const errorContext = {
        ...context,
        error: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error
      };
      this.output(this.formatLog('ERROR', message, errorContext));
    }
  }

  fatal(message: string, error?: Error | unknown, context?: LogContext): void {
    const errorContext = {
      ...context,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error
    };
    this.output(this.formatLog('FATAL', message, errorContext));
  }

  // Security-focused logging methods
  security(message: string, context?: SecurityContext): void {
    this.info(`[SECURITY] ${message}`, context);
  }

  auth(message: string, context?: SecurityContext): void {
    this.info(`[AUTH] ${message}`, context);
  }

  performance(message: string, duration?: number, context?: LogContext): void {
    this.info(`[PERFORMANCE] ${message}`, { 
      duration_ms: duration,
      ...context 
    });
  }

  audit(action: string, context?: SecurityContext & { resource?: string }): void {
    this.info(`[AUDIT] ${action}`, context);
  }

  // Create child logger with additional context
  child(context: LogContext): Logger {
    const childLogger = new Logger(this.serviceName);
    const originalFormatLog = childLogger.formatLog.bind(childLogger);
    
    childLogger.formatLog = (level: string, message: string, additionalContext?: LogContext) => {
      return originalFormatLog(level, message, { ...context, ...additionalContext });
    };
    
    return childLogger;
  }
}

// Export singleton instance
export const logger = new Logger();

// Export class for creating custom instances
export { Logger };

// Legacy console.log replacement (for gradual migration)
export const log = {
  debug: (message: string, ...args: any[]) => logger.debug(message, { args }),
  info: (message: string, ...args: any[]) => logger.info(message, { args }),
  warn: (message: string, ...args: any[]) => logger.warn(message, { args }),
  error: (message: string, ...args: any[]) => {
    const error = args.find(arg => arg instanceof Error);
    const context = args.filter(arg => !(arg instanceof Error));
    logger.error(message, error, { args: context });
  }
};