/**
 * Environment variable validation utility
 * Validates required environment variables and provides secure defaults where appropriate
 */

import { logger } from './logger.js';

interface EnvConfig {
  DATABASE_URL: string;
  AUTH0_DOMAIN: string;
  AUTH0_CLIENT_ID: string;
  AUTH0_CLIENT_SECRET: string;
  AUTH0_AUDIENCE: string;
  JWT_SECRET: string;
  SESSION_SECRET: string;
  REDIS_URL: string;
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;
  
  // Optional variables with defaults
  QUEUE_REDIS_URL?: string; // Optional - email queue falls back to disabled mode if not set
  COOKIE_SECRET?: string;
  RESEND_API_KEY?: string;
  AUTH0_PASSWORD_RESET_REDIRECT_URL?: string;
  AUTH0_DB_CONNECTION_ID?: string;
}

const requiredVars = [
  'DATABASE_URL',
  'AUTH0_DOMAIN',
  'AUTH0_CLIENT_ID',
  'AUTH0_CLIENT_SECRET',
  'AUTH0_AUDIENCE',
  'JWT_SECRET',
  'SESSION_SECRET',
  'REDIS_URL'
] as const;

const securityCriticalVars = [
  'JWT_SECRET',
  'SESSION_SECRET',
  'AUTH0_CLIENT_SECRET'
] as const;

/**
 * Validate a single environment variable
 */
function validateEnvVar(name: string, value: string | undefined, isRequired: boolean = true): string {
  if (!value || value.trim() === '') {
    if (isRequired) {
      throw new Error(`Environment variable ${name} is required but not set`);
    }
    return '';
  }

  // Security validation for critical variables
  if (securityCriticalVars.includes(name as any)) {
    if (value.length < 32) {
      throw new Error(`Environment variable ${name} must be at least 32 characters for security`);
    }
    
    // Check for weak/common secrets
    const weakPatterns = [
      /^(secret|password|key|token|jwt)$/i,
      /^(test|dev|example|sample)$/i,
      /^(123|abc|000)/,
      /^(.)\1{10,}$/ // Repeated characters
    ];
    
    if (weakPatterns.some(pattern => pattern.test(value))) {
      throw new Error(`Environment variable ${name} appears to use a weak/default value`);
    }
  }

  // URL validation for database and Redis URLs
  if (name.includes('URL') || name.includes('_URL')) {
    try {
      new URL(value);
    } catch {
      throw new Error(`Environment variable ${name} must be a valid URL`);
    }
  }

  // Domain validation for Auth0
  if (name === 'AUTH0_DOMAIN') {
    if (!value.includes('.') || value.includes('://')) {
      throw new Error(`AUTH0_DOMAIN should be a domain name (e.g., 'your-tenant.auth0.com'), not a full URL`);
    }
  }

  return value.trim();
}

/**
 * Validate all environment variables
 */
export function validateEnvironment(): EnvConfig {
  const errors: string[] = [];
  
  // Only log validation in debug mode or if there are errors

  // Check all required variables
  for (const varName of requiredVars) {
    try {
      validateEnvVar(varName, process.env[varName], true);
    } catch (error) {
      errors.push(error instanceof Error ? error.message : String(error));
    }
  }

  // Validate NODE_ENV
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv && !['development', 'production', 'test'].includes(nodeEnv)) {
    errors.push('NODE_ENV must be one of: development, production, test');
  }

  // Validate PORT
  const port = process.env.PORT;
  if (port && (isNaN(Number(port)) || Number(port) < 1 || Number(port) > 65535)) {
    errors.push('PORT must be a valid port number (1-65535)');
  }

  if (errors.length > 0) {
    logger.error('❌ Environment validation failed', null, { errors });
    throw new Error(`Environment validation failed: ${errors.length} error(s) found`);
  }

  if (process.env.LOG_LEVEL === 'DEBUG') {
    logger.debug('✅ Environment validation passed');
  }

  return {
    DATABASE_URL: validateEnvVar('DATABASE_URL', process.env.DATABASE_URL),
    AUTH0_DOMAIN: validateEnvVar('AUTH0_DOMAIN', process.env.AUTH0_DOMAIN),
    AUTH0_CLIENT_ID: validateEnvVar('AUTH0_CLIENT_ID', process.env.AUTH0_CLIENT_ID),
    AUTH0_CLIENT_SECRET: validateEnvVar('AUTH0_CLIENT_SECRET', process.env.AUTH0_CLIENT_SECRET),
    AUTH0_AUDIENCE: validateEnvVar('AUTH0_AUDIENCE', process.env.AUTH0_AUDIENCE),
    JWT_SECRET: validateEnvVar('JWT_SECRET', process.env.JWT_SECRET),
    SESSION_SECRET: validateEnvVar('SESSION_SECRET', process.env.SESSION_SECRET),
    REDIS_URL: validateEnvVar('REDIS_URL', process.env.REDIS_URL),
    NODE_ENV: (nodeEnv as EnvConfig['NODE_ENV']) || 'development',
    PORT: port ? Number(port) : 3000,
    
    // Optional variables
    QUEUE_REDIS_URL: validateEnvVar('QUEUE_REDIS_URL', process.env.QUEUE_REDIS_URL, false),
    COOKIE_SECRET: validateEnvVar('COOKIE_SECRET', process.env.COOKIE_SECRET, false),
    RESEND_API_KEY: validateEnvVar('RESEND_API_KEY', process.env.RESEND_API_KEY, false),
    AUTH0_PASSWORD_RESET_REDIRECT_URL: validateEnvVar('AUTH0_PASSWORD_RESET_REDIRECT_URL', process.env.AUTH0_PASSWORD_RESET_REDIRECT_URL, false),
    AUTH0_DB_CONNECTION_ID: validateEnvVar('AUTH0_DB_CONNECTION_ID', process.env.AUTH0_DB_CONNECTION_ID, false)
  };
}

/**
 * Check if running in production
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * Get a secure random string for testing/development
 */
export function generateSecureSecret(length: number = 64): string {
  const crypto = require('crypto');
  return crypto.randomBytes(length).toString('hex');
}