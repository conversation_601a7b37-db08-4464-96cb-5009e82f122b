import { db, sql } from '../db/config.js'

async function dbPlugin(fastify, options) {
  // Add database instance to fastify
  fastify.decorate('db', db)
  fastify.decorate('sql', sql)

  // Add hook to check database connection
  fastify.addHook('onReady', async () => {
    let retries = 5
    while (retries > 0) {
      try {
        const result = await sql('SELECT 1 as test')
        console.log('Database connection established:', result[0])
        fastify.log.info('Database connection successful')
        return
      } catch (error) {
        retries--
        if (retries === 0) {
          console.error('Database connection failed after all retries:', error)
          throw error
        }
        console.log(`Database connection attempt failed. Retrying... (${retries} attempts left)`)
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
  })

  // Note: HTTP clients don't need explicit closing like connection pools
}

export default dbPlugin