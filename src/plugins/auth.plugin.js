import fp from 'fastify-plugin'
import crypto from 'crypto'
import { logger } from '../utils/logger.js'

async function authPlugin(fastify, options) {
  logger.info('Registering strict token-only auth plugin')

  // Create a reusable authentication function: Requires a valid Bear<PERSON> token authenticated via Redis
  const verifyAuth = async (request, reply) => {
    const authHeader = request.headers.authorization;

    // Check for N8N secret first
    if (authHeader === `Bear<PERSON> ${process.env.N8N_SECRET}`) {
      request.log.info('N8N secret authenticated successfully');
      return; // Allow the request to proceed
    }

    if(process.env.WORKER_SECRET && authHeader === `Bearer ${process.env.WORKER_SECRET}`) {
      request.log.info('Worker secret authenticated successfully');
      return; // Allow the request to proceed
    }

    // 1. Check for Bearer token in Authorization header
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        request.log.warn('Authentication check failed: No Bearer token provided.');
        return reply.code(401).send({ error: 'Unauthorized', details: 'Bearer token required.' });
    }

    const bearerToken = authHeader.substring(7); // Extract token

    // 2. Attempt to authenticate using the bearer token via Redis lookup
    try {
      if (!fastify.redis) {
        request.log.error('Redis client (fastify.redis) is not available for token auth.');
        return reply.code(500).send({ error: 'Internal Server Error', details: 'Redis connection not configured.' });
      }

      // WARNING: KEYS scan is inefficient. Use an index in production.
      const sessionKeys = await fastify.redis.keys('sess:*');
      console.log('sessionKeys', sessionKeys);
      let userFoundFromToken = false;

      for (const key of sessionKeys) {
        const sessionDataString = await fastify.redis.get(key);
        if (sessionDataString) {
          try {
            const sessionData = JSON.parse(sessionDataString);
            //console.log('sessionData', sessionData);
            // Use the user's updated logic for checking session data structure
            if (sessionData && sessionData.auth0Token && sessionData.auth0Token === bearerToken) {
              if (sessionData.user) {
                request.user = sessionData.user; // Assign user from token's session data
                request.log.info({ userId: request.user.id }, 'Bearer token authenticated successfully via Redis lookup.');
                userFoundFromToken = true;
                break; // Token authenticated, stop searching
              } else {
                request.log.warn({ sessionKey: key }, 'Found matching session for token, but user data is missing.');
              }
            }
          } catch (parseError) {
            request.log.error({ sessionKey: key, error: parseError.message }, 'Failed to parse session data during token auth.');
          }
        }
      }

      if (userFoundFromToken) {
        return; // Proceed with request, user set from token data
      } else {
        // Token was provided, but no matching session/user found in Redis
        request.log.warn('Authentication check failed: Bearer token provided but no matching session/user found in Redis.');
        return reply.code(401).send({ error: 'Unauthorized', details: 'Invalid or expired Bearer token.' });
      }

    } catch (redisError) {
      request.log.error({ error: redisError.message }, 'Error during Redis lookup for bearer token authentication.');
      return reply.code(500).send({ error: 'Internal Server Error', details: 'Failed to process token authentication.' });
    }
    // No fallback to session - if we reach here something is wrong, but the logic above covers all paths.
  }

  // Decorate fastify instance with the new auth function
  fastify.decorate('verifyAuth', verifyAuth)

  logger.info('Strict token-only auth plugin registered successfully')
}

// Export the plugin wrapped with fastify-plugin
export default fp(authPlugin, {
  name: 'auth-plugin',
  // Dependencies might only need Redis now, but keep session/cookie if routes
  // might *conditionally* use session elsewhere, or if session setup is required by other plugins.
  dependencies: ['@fastify/cookie', '@fastify/session'] // Review if session/cookie are still needed
})