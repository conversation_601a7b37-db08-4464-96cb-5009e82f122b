# Plugins README

This directory contains custom Fastify plugins that add core functionalities and decorate the Fastify instance for use throughout the application.

---

## `auth.plugin.js` - Strict Token Authentication

This plugin provides the primary authentication strategy for the application, enforcing strict bearer token authentication for all protected routes.

### Architecture & Strategy

The plugin implements a token-based authentication flow that relies on Redis as the session store. It does **not** validate JWTs on every request. Instead, it uses the provided bearer token as a key to look up an existing and valid user session in Redis. This approach is stateful but highly performant, as it avoids cryptographic operations on every API call.

The authentication flow is as follows:

1.  **Login:** A user authenticates (likely via Auth0), and upon successful login, the server creates a session in Redis. The key for this session is derived from the user's bearer token (preferably a SHA256 hash of it for security).
2.  **Request:** The client application sends the bearer token in the `Authorization` header for all subsequent requests to protected endpoints.
3.  **Verification:** The `verifyAuth` decorator, provided by this plugin, intercepts the request.
4.  **Redis Lookup:** It extracts the token, hashes it, and looks for a corresponding session key in Redis (e.g., `sess:token:<hashed_token>`).
5.  **User Hydration:** If a session is found, the user data stored within that session is parsed and attached to the `request` object as `request.user`. The request is then allowed to proceed.
6.  **Failure:** If no `Authorization` header is present, or if no session is found in Redis for the given token, the plugin immediately replies with a `401 Unauthorized` error and stops the request from proceeding further.

```mermaid
sequenceDiagram
    participant Client
    participant Server as AdVid Server
    participant Redis

    Client->>+Server: Request with "Authorization: Bearer <token>"
    Server->>Server: `verifyAuth` decorator runs
    Server->>Server: Hash the <token>
    Server->>+Redis: GET `sess:token:<hashed_token>`
    Redis-->>-Server: Return session data (or nil)
    alt Session Found
        Server->>Server: Parse session, attach `request.user`
        Server->>Server: Pass request to controller
    else Session Not Found
        Server-->>-Client: 401 Unauthorized
    end
```

### Usage

The plugin decorates the Fastify instance with a `verifyAuth` function. To protect a route, simply add it to the `onRequest` lifecycle hook:

```javascript
fastify.get('/some-protected-route', {
  onRequest: [fastify.verifyAuth],
  handler: async (request, reply) => {
    // request.user is now available
    return { user: request.user };
  }
});
```

---

## `db.plugin.js` - Database Connector

This plugin manages the application's database connection and integrates it seamlessly with the Fastify instance.

### Architecture & Strategy

The plugin's primary responsibility is to make the Drizzle ORM instance available throughout the application.

1.  **Decorator:** It uses Fastify's `decorate` method to attach the database instance as `fastify.db` and the Drizzle `sql` template tag as `fastify.sql`. This provides a single, consistent point of access to the database from any part of the application (e.g., route handlers, other services).
2.  **Startup Health Check:** It uses the `onReady` lifecycle hook to verify the database connection when the server starts. This is a critical reliability feature that ensures the application doesn't start in a broken state if the database is unavailable.
3.  **Retry Logic:** The health check includes a retry mechanism (5 attempts over 10 seconds). This makes the application more resilient, especially in containerized environments where the database container might start more slowly than the application container.

### Usage

Once the plugin is registered, you can access the database instance from any request handler or hook:

```javascript
fastify.get('/users', async (request, reply) => {
  // Access the db instance via the request.server object
  const allUsers = await request.server.db.select().from(users);
  return allUsers;
});
```
